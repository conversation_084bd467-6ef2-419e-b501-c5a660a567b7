/**
 * AdminBuddy Firebase Cloud Functions
 * Automated task generation, expiry management, and notifications
 */

import { onSchedule } from "firebase-functions/v2/scheduler";
import { onCall } from "firebase-functions/v2/https";
import * as logger from "firebase-functions/logger";
import { db } from "./firebase-admin";

// Export Stripe functions
export {
  createCheckoutSession,
  stripeWebhook,
  manualUpdateSubscription,
} from "./stripe";

// Collection names
const COLLECTIONS = {
  LOCATIONS: "adminbuddy_locations",
  ROUTINES: "adminbuddy_routines",
  ROUTINE_TASKS: "adminbuddy_routine_tasks",
  LOCATION_ROUTINE_SCHEDULES: "adminbuddy_location_routine_schedules",
  DAILY_TASKS: "adminbuddy_daily_tasks",
  AD_HOC_TASKS: "adminbuddy_ad_hoc_tasks",
  TENANT_ROLES: "adminbuddy_tenant_roles",
  USER_PROFILES: "user_profiles",
};

// Types
interface DailyTask {
  id?: string;
  tenantId: string;
  routineTaskId: string;
  locationId: string;
  date: string;
  title: string;
  description?: string;
  priority: "low" | "medium" | "high";
  requiredRole?: string;
  dueTime?: string;
  visibleFromTime?: string; // HH:MM format, when task becomes visible in kiosk
  expiryType: "next_report" | "set_datetime" | "no_expiry";
  expiryDateTime?: string;
  status: "pending" | "completed" | "expired";
  createdAt: string;
}

interface LocationRoutineSchedule {
  id?: string;
  tenantId: string;
  locationId: string;
  routineId: string;
  cadence: "daily" | "weekly" | "biweekly" | "monthly" | "quarterly" | "yearly";
  daysOfWeek: number[]; // 0=Sunday, 1=Monday, etc. (used for weekly+ cadences)
  visibleFromTime?: string; // HH:MM format, when tasks become visible (optional)
  createdAt: string;
}

interface RoutineTask {
  id?: string;
  tenantId: string;
  routineId: string;
  title: string;
  description?: string;
  priority: "low" | "medium" | "high";
  requiredRole?: string;
  dueTime?: string;
  expiryType: "next_report" | "set_datetime" | "no_expiry";
  expiryDateTime?: string;
  createdAt: string;
}

// Helper function to check if a schedule should run on a given date
function shouldScheduleRun(
  schedule: LocationRoutineSchedule,
  date: string,
  skipDayCheck = false
): boolean {
  if (skipDayCheck) return true;

  const dateObj = new Date(date + "T00:00:00");
  const dayOfWeek = dateObj.getDay();

  switch (schedule.cadence) {
  case "daily":
    return true; // Daily tasks run every day

  case "weekly":
    return schedule.daysOfWeek.includes(dayOfWeek);

  case "biweekly": {
    // Run every 2 weeks on specified days
    // Use epoch week number to determine if it's an "on" week
    const epochTime = dateObj.getTime();
    const weeksSinceEpoch = Math.floor(epochTime / (1000 * 60 * 60 * 24 * 7));
    const isBiweeklyWeek = weeksSinceEpoch % 2 === 0;
    return isBiweeklyWeek && schedule.daysOfWeek.includes(dayOfWeek);
  }

  case "monthly": {
    // Run on the first occurrence of specified days each month
    const firstOfMonth = new Date(
      dateObj.getFullYear(),
      dateObj.getMonth(),
      1
    );
    const daysInMonth = new Date(
      dateObj.getFullYear(),
      dateObj.getMonth() + 1,
      0
    ).getDate();

    for (const targetDay of schedule.daysOfWeek) {
      // Find first occurrence of this day in the month
      const firstOccurrence = new Date(firstOfMonth);
      while (
        firstOccurrence.getDay() !== targetDay &&
          firstOccurrence.getDate() <= daysInMonth
      ) {
        firstOccurrence.setDate(firstOccurrence.getDate() + 1);
      }

      if (firstOccurrence.getDate() === dateObj.getDate()) {
        return true;
      }
    }
    return false;
  }

  case "quarterly": {
    // Run on the first occurrence of specified days each quarter
    const quarter = Math.floor(dateObj.getMonth() / 3);
    const firstOfQuarter = new Date(dateObj.getFullYear(), quarter * 3, 1);
    const lastOfQuarter = new Date(dateObj.getFullYear(), quarter * 3 + 3, 0);

    for (const targetDay of schedule.daysOfWeek) {
      const firstOccurrence = new Date(firstOfQuarter);
      while (
        firstOccurrence.getDay() !== targetDay &&
          firstOccurrence <= lastOfQuarter
      ) {
        firstOccurrence.setDate(firstOccurrence.getDate() + 1);
      }

      if (
        firstOccurrence.getDate() === dateObj.getDate() &&
          firstOccurrence.getMonth() === dateObj.getMonth()
      ) {
        return true;
      }
    }
    return false;
  }

  case "yearly": {
    // Run on the first occurrence of specified days each year
    const firstOfYear = new Date(dateObj.getFullYear(), 0, 1);
    const lastOfYear = new Date(dateObj.getFullYear(), 11, 31);

    for (const targetDay of schedule.daysOfWeek) {
      const firstOccurrence = new Date(firstOfYear);
      while (
        firstOccurrence.getDay() !== targetDay &&
          firstOccurrence <= lastOfYear
      ) {
        firstOccurrence.setDate(firstOccurrence.getDate() + 1);
      }

      if (
        firstOccurrence.getDate() === dateObj.getDate() &&
          firstOccurrence.getMonth() === dateObj.getMonth()
      ) {
        return true;
      }
    }
    return false;
  }

  default:
    logger.warn(`Unknown cadence: ${schedule.cadence}, defaulting to weekly`);
    return schedule.daysOfWeek.includes(dayOfWeek);
  }
}

// Helper function to generate daily tasks for a specific tenant and date
async function generateDailyTasksForTenant(
  tenantId: string,
  date: string,
  scheduleId?: string,
  skipDayCheck = false
): Promise<number> {
  try {
    logger.info(`📅 Generating daily tasks for tenant ${tenantId} on ${date}`);

    // Get schedules for this tenant (all or specific one)
    let schedulesSnapshot;

    if (scheduleId) {
      logger.info(`🎯 Generating tasks for specific schedule: ${scheduleId}`);
      // Get the specific schedule document
      const scheduleDoc = await db
        .collection(COLLECTIONS.LOCATION_ROUTINE_SCHEDULES)
        .doc(scheduleId)
        .get();

      if (!scheduleDoc.exists) {
        logger.info(`⚠️ Schedule ${scheduleId} not found`);
        return 0;
      }

      const scheduleData = scheduleDoc.data();
      if (scheduleData?.tenantId !== tenantId) {
        logger.info(`⚠️ Schedule ${scheduleId} belongs to different tenant`);
        return 0;
      }

      // Create a mock snapshot with just this document
      schedulesSnapshot = {
        docs: [scheduleDoc],
        empty: false,
      };
    } else {
      logger.info("📋 Generating tasks for all active schedules");
      schedulesSnapshot = await db
        .collection(COLLECTIONS.LOCATION_ROUTINE_SCHEDULES)
        .where("tenantId", "==", tenantId)
        .get();
    }

    logger.info(
      `🔍 Found ${schedulesSnapshot.docs.length} schedules for tenant ${tenantId}`
    );

    if (schedulesSnapshot.empty) {
      logger.info(`⚠️ No active schedules found for tenant ${tenantId}`);
      return 0;
    }

    // Get day of week for the date (0=Sunday, 1=Monday, etc.)
    const dateObj = new Date(date + "T00:00:00");
    const dayOfWeek = dateObj.getDay();

    // Get all routine tasks for this tenant
    const routineTasksSnapshot = await db
      .collection(COLLECTIONS.ROUTINE_TASKS)
      .where("tenantId", "==", tenantId)
      .get();

    logger.info(
      `🔍 Found ${routineTasksSnapshot.docs.length} active routine tasks for tenant ${tenantId}`
    );

    const routineTasks = routineTasksSnapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as RoutineTask[];

    // Log routine IDs for debugging
    const routineIds = [...new Set(routineTasks.map((rt) => rt.routineId))];
    logger.info(
      `🔍 Routine tasks found for routines: ${routineIds.join(", ")}`
    );

    if (routineTasks.length === 0) {
      logger.info(`⚠️ No routine tasks found for tenant ${tenantId}`);
      return 0;
    }

    let tasksCreated = 0;

    // Process each schedule
    for (const scheduleDoc of schedulesSnapshot.docs) {
      const schedule = {
        id: scheduleDoc.id,
        ...scheduleDoc.data(),
      } as LocationRoutineSchedule;

      logger.info(
        `🔍 Processing schedule ${schedule.id} for routine ${schedule.routineId} on day ${dayOfWeek}`
      );
      logger.info(`🔍 Schedule days: [${schedule.daysOfWeek.join(", ")}]`);

      // Check if this schedule should run today using new cadence logic
      if (!shouldScheduleRun(schedule, date, skipDayCheck)) {
        logger.info(
          `⏭️ Skipping schedule ${schedule.id} - not scheduled for ${date} (cadence: ${schedule.cadence})`
        );
        continue;
      }

      if (skipDayCheck) {
        logger.info(
          "🎯 Skipping day-of-week check - generating tasks regardless of schedule days"
        );
      }

      // Get routine tasks for this routine
      const routineTasksForSchedule = routineTasks.filter(
        (rt) => rt.routineId === schedule.routineId
      );

      logger.info(
        `🔍 Found ${routineTasksForSchedule.length} routine tasks for routine ${schedule.routineId}`
      );

      if (routineTasksForSchedule.length === 0) {
        logger.info(
          `⚠️ No routine tasks found for routine ${schedule.routineId} in schedule ${schedule.id}`
        );
        continue;
      }

      // Create daily tasks for each routine task
      for (const routineTask of routineTasksForSchedule) {
        // Check if task already exists for this date
        const existingTaskSnapshot = await db
          .collection(COLLECTIONS.DAILY_TASKS)
          .where("tenantId", "==", tenantId)
          .where("routineTaskId", "==", routineTask.id)
          .where("locationId", "==", schedule.locationId)
          .where("date", "==", date)
          .get();

        if (!existingTaskSnapshot.empty) {
          logger.info(
            `⏭️ Task already exists: ${routineTask.title} at location ${schedule.locationId}`
          );
          continue;
        }

        // Calculate due time and expiry
        let dueTime = routineTask.dueTime;
        let expiryDateTime = routineTask.expiryDateTime;

        // If routine has visible time and task has relative time, calculate absolute time
        if (schedule.visibleFromTime && routineTask.dueTime?.startsWith("+")) {
          // Handle relative time like "+30min"
          const minutes = parseInt(
            routineTask.dueTime.replace("+", "").replace("min", "")
          );
          const [startHour, startMinute] = schedule.visibleFromTime
            .split(":")
            .map(Number);
          const totalMinutes = startHour * 60 + startMinute + minutes;
          const newHour = Math.floor(totalMinutes / 60);
          const newMinute = totalMinutes % 60;
          dueTime = `${newHour.toString().padStart(2, "0")}:${newMinute
            .toString()
            .padStart(2, "0")}`;
        }

        // Handle expiry date calculation
        if (routineTask.expiryType === "set_datetime") {
          if (routineTask.expiryDateTime) {
            // If specific datetime is provided, use it with the target date
            expiryDateTime = `${date}T${routineTask.expiryDateTime}:00`;
          } else {
            // If set_datetime but no specific datetime, default to end of day
            expiryDateTime = `${date}T23:59:59`;
          }
        }

        // Create daily task - filter out undefined values for Firestore
        const dailyTaskData: any = {
          tenantId,
          routineTaskId: routineTask.id!,
          locationId: schedule.locationId,
          date,
          title: routineTask.title,
          priority: routineTask.priority,
          expiryType: routineTask.expiryType,
          status: "pending",
          createdAt: new Date().toISOString(),
        };

        // Add visibleFromTime if schedule has it (for kiosk visibility control)
        if (schedule.visibleFromTime) {
          dailyTaskData.visibleFromTime = schedule.visibleFromTime;
        }

        // Only add optional fields if they are not undefined
        if (routineTask.description !== undefined) {
          dailyTaskData.description = routineTask.description;
        }
        if (routineTask.requiredRole !== undefined) {
          dailyTaskData.requiredRole = routineTask.requiredRole;
        }
        if (dueTime !== undefined) {
          dailyTaskData.dueTime = dueTime;
        }
        if (expiryDateTime !== undefined) {
          dailyTaskData.expiryDateTime = expiryDateTime;
        }

        logger.info(
          `📝 Creating task with data: ${JSON.stringify({
            tenantId,
            date,
            title: routineTask.title,
            locationId: schedule.locationId,
            collection: COLLECTIONS.DAILY_TASKS,
            hasDescription: routineTask.description !== undefined,
            hasRequiredRole: routineTask.requiredRole !== undefined,
            hasDueTime: dueTime !== undefined,
            hasExpiryDateTime: expiryDateTime !== undefined,
          })}`
        );

        const docRef = await db
          .collection(COLLECTIONS.DAILY_TASKS)
          .add(dailyTaskData);
        tasksCreated++;

        logger.info(
          `✅ Created daily task: ${routineTask.title} for ${schedule.locationId} with ID: ${docRef.id}`
        );
        logger.info(
          `✅ Task saved to collection: ${COLLECTIONS.DAILY_TASKS} with tenantId: ${tenantId} and date: ${date}`
        );
      }
    }

    logger.info(
      `🎉 Generated ${tasksCreated} daily tasks for tenant ${tenantId} on ${date}`
    );
    return tasksCreated;
  } catch (error) {
    logger.error(
      `❌ Error generating daily tasks for tenant ${tenantId}:`,
      error
    );
    throw error;
  }
}

// Helper function to expire overdue tasks
async function expireOverdueTasks(): Promise<number> {
  try {
    logger.info("⏰ Checking for overdue tasks to expire");
    const now = new Date();

    // Get all pending tasks that should be expired
    const tasksSnapshot = await db
      .collection(COLLECTIONS.DAILY_TASKS)
      .where("status", "==", "pending")
      .get();

    let expiredCount = 0;
    const batch = db.batch();

    for (const taskDoc of tasksSnapshot.docs) {
      const task = { id: taskDoc.id, ...taskDoc.data() } as DailyTask;
      let shouldExpire = false;

      if (task.expiryType === "next_report") {
        // Tasks with "next_report" expiry should be handled by the reporting system
        // They expire when included in a report, not by time
        // This function should not auto-expire these tasks
        shouldExpire = false;
      } else if (task.expiryType === "set_datetime" && task.expiryDateTime) {
        // Expire if past the specific datetime
        const expiryDate = new Date(task.expiryDateTime);
        if (now > expiryDate) {
          shouldExpire = true;
        }
      }

      if (shouldExpire) {
        batch.update(taskDoc.ref, {
          status: "expired",
          expiredAt: now.toISOString(),
        });
        expiredCount++;
        logger.info(`⏰ Expiring task: ${task.title} (${task.date})`);
      }
    }

    // Also check ad-hoc tasks
    const adHocTasksSnapshot = await db
      .collection(COLLECTIONS.AD_HOC_TASKS)
      .where("status", "==", "pending")
      .get();

    for (const taskDoc of adHocTasksSnapshot.docs) {
      const task = { id: taskDoc.id, ...taskDoc.data() } as any;
      let shouldExpire = false;

      if (task.expiryType === "next_report") {
        // Tasks with "next_report" expiry should be handled by the reporting system
        // They expire when included in a report, not by time
        // This function should not auto-expire these tasks
        shouldExpire = false;
      } else if (task.expiryType === "set_datetime" && task.expiryDateTime) {
        // Expire if past the specific datetime
        const expiryDate = new Date(task.expiryDateTime);
        if (now > expiryDate) {
          shouldExpire = true;
        }
      }

      if (shouldExpire) {
        batch.update(taskDoc.ref, {
          status: "expired",
          expiredAt: now.toISOString(),
        });
        expiredCount++;
        logger.info(`⏰ Expiring ad-hoc task: ${task.title} (${task.dueDate})`);
      }
    }

    if (expiredCount > 0) {
      await batch.commit();
      logger.info(`✅ Expired ${expiredCount} overdue tasks`);
    } else {
      logger.info("✅ No overdue tasks found");
    }

    return expiredCount;
  } catch (error) {
    logger.error("❌ Error expiring overdue tasks:", error);
    throw error;
  }
}

// Scheduled function: Generate daily tasks every day at midnight
export const generateDailyTasks = onSchedule(
  {
    schedule: "0 0 * * *", // Every day at midnight
    timeZone: "America/Toronto", // Adjust to your timezone
    memory: "256MiB",
  },
  async (_event) => {
    try {
      logger.info("🌅 Starting daily task generation job");
      const today = new Date().toISOString().split("T")[0];

      // Get all active tenants (users with profiles)
      const userProfilesSnapshot = await db
        .collection(COLLECTIONS.USER_PROFILES)
        .get();

      if (userProfilesSnapshot.empty) {
        logger.info("⚠️ No user profiles found");
        return;
      }

      const tenantIds = new Set<string>();
      userProfilesSnapshot.docs.forEach((doc) => {
        const profile = doc.data();
        if (profile.tenantId) {
          tenantIds.add(profile.tenantId);
        }
      });

      logger.info(`🏢 Found ${tenantIds.size} active tenants`);

      let totalTasksCreated = 0;

      // Generate tasks for each tenant
      for (const tenantId of tenantIds) {
        try {
          const tasksCreated = await generateDailyTasksForTenant(
            tenantId,
            today
          );
          totalTasksCreated += tasksCreated;
        } catch (error) {
          logger.error(
            `❌ Failed to generate tasks for tenant ${tenantId}:`,
            error
          );
          // Continue with other tenants
        }
      }

      logger.info(
        `🎉 Daily task generation complete! Created ${totalTasksCreated} tasks across ${tenantIds.size} tenants`
      );
    } catch (error) {
      logger.error("❌ Daily task generation job failed:", error);
      throw error;
    }
  }
);

// Scheduled function: Expire overdue tasks daily (was every 15 minutes)
export const expireTasks = onSchedule(
  {
    schedule: "0 2 * * *", // Daily at 2 AM (reduced from every 15 minutes for cost optimization)
    timeZone: "America/Toronto", // Adjust to your timezone
    memory: "128MiB",
  },
  async (_event) => {
    try {
      logger.info("⏰ Starting task expiry job");
      const expiredCount = await expireOverdueTasks();
      logger.info(`✅ Task expiry job complete! Expired ${expiredCount} tasks`);
    } catch (error) {
      logger.error("❌ Task expiry job failed:", error);
      throw error;
    }
  }
);

// Callable function: Manual task generation for testing
export const manualGenerateTasks = onCall(
  {
    memory: "256MiB",
  },
  async (request) => {
    try {
      const { tenantId, date, scheduleId } = request.data;
      const requestId = `req-${Date.now()}-${Math.random()
        .toString(36)
        .substr(2, 5)}`;

      if (!tenantId) {
        throw new Error("tenantId is required");
      }

      const targetDate = date || new Date().toISOString().split("T")[0];

      if (scheduleId) {
        logger.info(
          `🔧 [${requestId}] Manual task generation requested for tenant ` +
            `${tenantId} on ${targetDate} for schedule ${scheduleId}`
        );
      } else {
        logger.info(
          `🔧 [${requestId}] Manual task generation requested for tenant ${tenantId} on ${targetDate} for all schedules`
        );
      }

      const tasksCreated = await generateDailyTasksForTenant(
        tenantId,
        targetDate,
        scheduleId,
        true // Skip day-of-week check for manual generation
      );

      logger.info(
        `🎉 [${requestId}] Returning response: ${tasksCreated} tasks created`
      );

      return {
        success: true,
        tasksCreated,
        date: targetDate,
        tenantId,
        requestId,
        message: `Generated ${tasksCreated} tasks for ${targetDate}`,
      };
    } catch (error) {
      logger.error("❌ Manual task generation failed:", error);
      throw error;
    }
  }
);

// Callable function: Manual task expiry for testing
export const manualExpireTasks = onCall(
  {
    memory: "128MiB",
  },
  async (_request) => {
    try {
      logger.info("🔧 Manual task expiry requested");

      const expiredCount = await expireOverdueTasks();

      return {
        success: true,
        expiredCount,
        message: `Expired ${expiredCount} overdue tasks`,
      };
    } catch (error) {
      logger.error("❌ Manual task expiry failed:", error);
      throw error;
    }
  }
);

// Callable function: Get function status and stats
export const getFunctionStats = onCall(
  {
    memory: "128MiB",
  },
  async (_request) => {
    try {
      const today = new Date().toISOString().split("T")[0];

      // Get task counts
      const [dailyTasksSnapshot, adHocTasksSnapshot, userProfilesSnapshot] =
        await Promise.all([
          db
            .collection(COLLECTIONS.DAILY_TASKS)
            .where("date", "==", today)
            .get(),
          db.collection(COLLECTIONS.AD_HOC_TASKS).get(),
          db.collection(COLLECTIONS.USER_PROFILES).get(),
        ]);

      const dailyTasks = dailyTasksSnapshot.docs.map((doc) => doc.data());
      const adHocTasks = adHocTasksSnapshot.docs.map((doc) => doc.data());

      const tenantIds = new Set<string>();
      userProfilesSnapshot.docs.forEach((doc) => {
        const profile = doc.data();
        if (profile.tenantId) {
          tenantIds.add(profile.tenantId);
        }
      });

      return {
        success: true,
        stats: {
          activeTenants: tenantIds.size,
          todaysTasks: {
            total: dailyTasks.length,
            pending: dailyTasks.filter((t) => t.status === "pending").length,
            completed: dailyTasks.filter((t) => t.status === "completed")
              .length,
            expired: dailyTasks.filter((t) => t.status === "expired").length,
          },
          adHocTasks: {
            total: adHocTasks.length,
            pending: adHocTasks.filter((t) => t.status === "pending").length,
            completed: adHocTasks.filter((t) => t.status === "completed")
              .length,
            expired: adHocTasks.filter((t) => t.status === "expired").length,
          },
          lastUpdated: new Date().toISOString(),
        },
      };
    } catch (error) {
      logger.error("❌ Failed to get function stats:", error);
      throw error;
    }
  }
);
