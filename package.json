{"name": "adminbuddy-site", "version": "1.0.0", "private": true, "dependencies": {"@aws-sdk/client-s3": "^3.817.0", "@aws-sdk/client-sts": "^3.817.0", "@aws-sdk/s3-request-presigner": "^3.817.0", "@stripe/stripe-js": "^7.3.1", "@types/react-beautiful-dnd": "^13.1.8", "chart.js": "^4.4.9", "cross-env": "^7.0.3", "date-fns": "^2.29.0", "eslint": "^8.57.1", "firebase": "^11.8.1", "react": "^18.0.0", "react-beautiful-dnd": "^13.1.1", "react-calendar": "^6.0.0", "react-chartjs-2": "^5.3.0", "react-dom": "^18.0.0", "react-router-dom": "^6.0.0", "react-scripts": "^5.0.1"}, "scripts": {"start": "GENERATE_SOURCEMAP=false BROWSER=none react-scripts start", "start:win": "react-scripts start", "build": "NODE_ENV=production react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "deploy": "npm run build && firebase deploy", "deploy:hosting": "npm run build && firebase deploy --only hosting", "deploy:functions": "firebase deploy --only functions", "deploy:rules": "firebase deploy --only firestore:rules", "firebase:emulators": "firebase emulators:start", "firebase:logs": "firebase functions:log", "clean": "rm -rf build node_modules/.cache", "clean:install": "npm run clean && npm install"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/node": "^16.18.0", "@types/react": "^18.0.0", "@types/react-calendar": "^4.1.0", "@types/react-dom": "^18.0.0", "@types/react-router-dom": "^5.3.3", "typescript": "^4.9.5"}}