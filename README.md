# AdminBuddy - Task Coordination for Small Businesses

A modern, responsive React SaaS application for task coordination and management in shift-based small businesses, built with TypeScript and Firebase.

## 🚀 Quick Start

```bash
# Clone the repository
git clone https://github.com/dmacdo02/bitbybit.git
cd bitbybit

# Install dependencies
npm install

# Start development server
npm start

# Windows users:
npm run start:win

# Open http://localhost:3000
```

## 🏗️ Architecture

### Tech Stack
- **Frontend**: React 18 with TypeScript
- **Backend**: Firebase (Firestore, Auth, Functions, Hosting)
- **Routing**: React Router v6
- **Styling**: CSS Modules + Global CSS
- **State Management**: React Context + Local State
- **Build Tool**: Create React App (CRA)
- **Authentication**: Firebase Auth with REST API
- **Database**: Firestore with multi-tenant architecture

### Project Structure
```
src/
├── components/          # Reusable UI components
│   ├── ui/             # Basic UI elements (buttons, inputs, etc.)
│   ├── layout/         # Layout components (header, navigation, etc.)
│   ├── auth/           # Authentication components
│   └── dashboard/      # Dashboard-specific components
├── pages/              # Page-level components
├── contexts/           # React Context providers
├── services/           # API services and Firebase integration
├── utils/              # Utility functions
├── styles/             # Global styles and CSS modules
├── config/             # Configuration files
└── types/              # TypeScript type definitions
```

## �� Firebase Deployment

```bash
# Build and deploy everything
npm run deploy

# Deploy only hosting
npm run deploy:hosting

# Deploy only functions
npm run deploy:functions

# Deploy only Firestore rules
npm run deploy:rules
```

## 🛠️ Development Commands

```bash
# Development
npm start                    # Start dev server
npm test                     # Run tests
npm run build               # Production build

# Firebase
npm run firebase:emulators  # Start Firebase emulators
npm run firebase:logs       # View function logs

# Maintenance
npm run clean               # Clean build cache
npm run clean:install      # Clean install dependencies
```

## 🏢 Multi-Tenant SaaS Features

- **Tenant Isolation**: Complete data separation between businesses
- **Role-Based Access**: Owner, Manager, and Tablet personas
- **Trial Management**: 14-day free trial with subscription management
- **Task Automation**: Scheduled task generation and expiry
- **Real-time Updates**: Live task status updates across devices
- **Kiosk Mode**: Simplified interface for task completion

## 🔐 Security

- **Firebase Security Rules**: Tenant-based data isolation
- **Protected Routes**: Role-based access control
- **Authentication**: Secure token-based auth with refresh
- **Input Validation**: Comprehensive data validation
- **HTTPS Only**: Secure communication in production

## 📱 Responsive Design

- **Mobile-First**: Optimized for tablets and phones
- **Touch-Friendly**: Large buttons and intuitive gestures
- **Offline-Ready**: Basic functionality without internet
- **Fast Loading**: Optimized bundle sizes and caching

## 🚀 Production Deployment

AdminBuddy is deployed on Firebase Hosting with:
- **Global CDN**: Fast loading worldwide
- **Automatic SSL**: HTTPS by default
- **Custom Domain**: adminbuddy.app
- **Serverless Functions**: Automated task management
- **Real-time Database**: Live updates across devices

## 📊 Monitoring & Analytics

- **Performance Monitoring**: Real-time performance metrics
- **Error Tracking**: Comprehensive error logging
- **Usage Analytics**: User behavior insights
- **Health Checks**: System status monitoring

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is proprietary software. All rights reserved.

## 📞 Support

For support, email: <EMAIL>
