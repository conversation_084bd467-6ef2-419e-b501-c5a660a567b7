// Quick script to check what expiry types are actually being used
const { initializeApp } = require('firebase/app');
const { getFirestore, collection, getDocs, query, where } = require('firebase/firestore');

// Firebase config (using your existing config)
const firebaseConfig = {
  apiKey: "AIzaSyCDtTEuXAROJTlBmVe4GZggjDu_nWHsB_o",
  authDomain: "adminbuddy.firebaseapp.com",
  projectId: "adminbuddy",
  storageBucket: "adminbuddy.firebasestorage.app",
  messagingSenderId: "1074586257734",
  appId: "1:1074586257734:web:b8b8b8b8b8b8b8b8b8b8b8"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

async function checkExpiryTypes() {
  try {
    console.log('🔍 Checking expiry types in daily tasks...');
    
    // Get all daily tasks
    const dailyTasksRef = collection(db, 'adminbuddy_daily_tasks');
    const dailySnapshot = await getDocs(dailyTasksRef);
    
    const expiryTypeCounts = {
      'no_expiry': 0,
      'next_report': 0,
      'set_datetime': 0,
      'undefined': 0
    };
    
    const statusCounts = {
      'pending': 0,
      'completed': 0,
      'expired': 0
    };
    
    let setDatetimeTasks = [];
    let expiredTasks = [];
    
    dailySnapshot.docs.forEach(doc => {
      const data = doc.data();
      
      // Count expiry types
      const expiryType = data.expiryType || 'undefined';
      expiryTypeCounts[expiryType] = (expiryTypeCounts[expiryType] || 0) + 1;
      
      // Count statuses
      const status = data.status || 'undefined';
      statusCounts[status] = (statusCounts[status] || 0) + 1;
      
      // Collect set_datetime tasks
      if (expiryType === 'set_datetime') {
        setDatetimeTasks.push({
          id: doc.id,
          title: data.title,
          expiryDateTime: data.expiryDateTime,
          status: data.status,
          date: data.date
        });
      }
      
      // Collect expired tasks
      if (status === 'expired') {
        expiredTasks.push({
          id: doc.id,
          title: data.title,
          expiryType: data.expiryType,
          expiredAt: data.expiredAt,
          date: data.date
        });
      }
    });
    
    console.log('\n📊 DAILY TASKS ANALYSIS:');
    console.log('Total daily tasks:', dailySnapshot.docs.length);
    console.log('\nExpiry Type Breakdown:');
    Object.entries(expiryTypeCounts).forEach(([type, count]) => {
      console.log(`  ${type}: ${count}`);
    });
    
    console.log('\nStatus Breakdown:');
    Object.entries(statusCounts).forEach(([status, count]) => {
      console.log(`  ${status}: ${count}`);
    });
    
    console.log('\n🎯 SET_DATETIME TASKS:');
    if (setDatetimeTasks.length > 0) {
      setDatetimeTasks.forEach(task => {
        console.log(`  - ${task.title} (${task.date})`);
        console.log(`    Expiry: ${task.expiryDateTime || 'Not set'}`);
        console.log(`    Status: ${task.status}`);
      });
    } else {
      console.log('  No tasks with set_datetime expiry type found');
    }
    
    console.log('\n⏰ EXPIRED TASKS:');
    if (expiredTasks.length > 0) {
      expiredTasks.forEach(task => {
        console.log(`  - ${task.title} (${task.date})`);
        console.log(`    Expiry Type: ${task.expiryType}`);
        console.log(`    Expired At: ${task.expiredAt || 'Not set'}`);
      });
    } else {
      console.log('  No expired tasks found');
    }
    
    // Now check ad-hoc tasks
    console.log('\n🔍 Checking expiry types in ad-hoc tasks...');
    
    const adHocTasksRef = collection(db, 'adminbuddy_ad_hoc_tasks');
    const adHocSnapshot = await getDocs(adHocTasksRef);
    
    const adHocExpiryTypeCounts = {
      'no_expiry': 0,
      'next_report': 0,
      'set_datetime': 0,
      'undefined': 0
    };
    
    const adHocStatusCounts = {
      'pending': 0,
      'completed': 0,
      'expired': 0
    };
    
    let adHocSetDatetimeTasks = [];
    let adHocExpiredTasks = [];
    
    adHocSnapshot.docs.forEach(doc => {
      const data = doc.data();
      
      // Count expiry types
      const expiryType = data.expiryType || 'undefined';
      adHocExpiryTypeCounts[expiryType] = (adHocExpiryTypeCounts[expiryType] || 0) + 1;
      
      // Count statuses
      const status = data.status || 'undefined';
      adHocStatusCounts[status] = (adHocStatusCounts[status] || 0) + 1;
      
      // Collect set_datetime tasks
      if (expiryType === 'set_datetime') {
        adHocSetDatetimeTasks.push({
          id: doc.id,
          title: data.title,
          expiryDateTime: data.expiryDateTime,
          status: data.status,
          dueDate: data.dueDate
        });
      }
      
      // Collect expired tasks
      if (status === 'expired') {
        adHocExpiredTasks.push({
          id: doc.id,
          title: data.title,
          expiryType: data.expiryType,
          expiredAt: data.expiredAt,
          dueDate: data.dueDate
        });
      }
    });
    
    console.log('\n📊 AD-HOC TASKS ANALYSIS:');
    console.log('Total ad-hoc tasks:', adHocSnapshot.docs.length);
    console.log('\nExpiry Type Breakdown:');
    Object.entries(adHocExpiryTypeCounts).forEach(([type, count]) => {
      console.log(`  ${type}: ${count}`);
    });
    
    console.log('\nStatus Breakdown:');
    Object.entries(adHocStatusCounts).forEach(([status, count]) => {
      console.log(`  ${status}: ${count}`);
    });
    
    console.log('\n🎯 SET_DATETIME AD-HOC TASKS:');
    if (adHocSetDatetimeTasks.length > 0) {
      adHocSetDatetimeTasks.forEach(task => {
        console.log(`  - ${task.title} (${task.dueDate})`);
        console.log(`    Expiry: ${task.expiryDateTime || 'Not set'}`);
        console.log(`    Status: ${task.status}`);
      });
    } else {
      console.log('  No ad-hoc tasks with set_datetime expiry type found');
    }
    
    console.log('\n⏰ EXPIRED AD-HOC TASKS:');
    if (adHocExpiredTasks.length > 0) {
      adHocExpiredTasks.forEach(task => {
        console.log(`  - ${task.title} (${task.dueDate})`);
        console.log(`    Expiry Type: ${task.expiryType}`);
        console.log(`    Expired At: ${task.expiredAt || 'Not set'}`);
      });
    } else {
      console.log('  No expired ad-hoc tasks found');
    }
    
    // Summary
    const totalSetDatetime = setDatetimeTasks.length + adHocSetDatetimeTasks.length;
    const totalExpired = expiredTasks.length + adHocExpiredTasks.length;
    
    console.log('\n🎯 SUMMARY:');
    console.log(`Total tasks with set_datetime expiry: ${totalSetDatetime}`);
    console.log(`Total expired tasks: ${totalExpired}`);
    console.log(`Memory issues could be hiding expiry attempts: ${totalSetDatetime > 0 ? 'POSSIBLE' : 'UNLIKELY'}`);
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

checkExpiryTypes();
