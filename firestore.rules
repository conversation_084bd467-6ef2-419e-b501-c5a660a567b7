rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {

    // AdminBuddy Collections - Tenant-based Security Rules
    // Users can only access data for their own tenant

    // Helper function to get user's tenant ID
    function getUserTenantId() {
      return get(/databases/$(database)/documents/user_profiles/$(request.auth.uid)).data.tenantId;
    }

    // Helper function to check if user is authenticated and has tenant access
    function hasValidTenantAccess(tenantId) {
      return request.auth != null &&
             exists(/databases/$(database)/documents/user_profiles/$(request.auth.uid)) &&
             getUserTenantId() == tenantId;
    }

    // User Profiles (SaaS Multi-Tenant) - Users can only access their own profile
    match /user_profiles/{userId} {
      // Users can only read/write their own profile
      allow read, write: if request.auth != null && request.auth.uid == userId;

      // Allow creation during signup (users creating their own profile)
      allow create: if request.auth != null && request.auth.uid == userId;

      // Allow owners to create and manage kiosk user profiles for their tenant
      allow create, update, delete: if request.auth != null &&
                   exists(/databases/$(database)/documents/user_profiles/$(request.auth.uid)) &&
                   get(/databases/$(database)/documents/user_profiles/$(request.auth.uid)).data.role == "owner" &&
                   get(/databases/$(database)/documents/user_profiles/$(request.auth.uid)).data.tenantId == request.resource.data.tenantId &&
                   request.resource.data.role == "kiosk";

      // NO GLOBAL LISTING - users cannot see other users' profiles
    }

    // Tenants - Users can only access their own tenant data
    match /tenants/{tenantId} {
      // Users can only access their own tenant
      allow read, write: if request.auth != null &&
                        exists(/databases/$(database)/documents/user_profiles/$(request.auth.uid)) &&
                        get(/databases/$(database)/documents/user_profiles/$(request.auth.uid)).data.tenantId == tenantId;
      // Allow creation only for their own tenant
      allow create: if request.auth != null &&
                   exists(/databases/$(database)/documents/user_profiles/$(request.auth.uid)) &&
                   get(/databases/$(database)/documents/user_profiles/$(request.auth.uid)).data.tenantId == tenantId;
    }

    // Locations - Tenant-specific access
    match /adminbuddy_locations/{locationId} {
      allow read: if hasValidTenantAccess(resource.data.tenantId);
      // Allow reading locations for kiosk username lookup (needed before authentication)
      allow read: if request.auth == null && resource.data.kioskUsername != null;
      allow create: if request.auth != null &&
                   exists(/databases/$(database)/documents/user_profiles/$(request.auth.uid)) &&
                   getUserTenantId() == request.resource.data.tenantId;
      allow update: if hasValidTenantAccess(resource.data.tenantId) &&
                   getUserTenantId() == request.resource.data.tenantId;
      allow delete: if hasValidTenantAccess(resource.data.tenantId);
      allow list: if request.auth != null &&
                 exists(/databases/$(database)/documents/user_profiles/$(request.auth.uid));
      // Allow querying locations by kiosk username for login (unauthenticated)
      allow list: if request.auth == null;
    }

    // Routines - Tenant-specific access
    match /adminbuddy_routines/{routineId} {
      allow read: if hasValidTenantAccess(resource.data.tenantId);
      allow create: if request.auth != null &&
                   exists(/databases/$(database)/documents/user_profiles/$(request.auth.uid)) &&
                   getUserTenantId() == request.resource.data.tenantId;
      allow update: if hasValidTenantAccess(resource.data.tenantId) &&
                   getUserTenantId() == request.resource.data.tenantId;
      allow delete: if hasValidTenantAccess(resource.data.tenantId);
      allow list: if request.auth != null &&
                 exists(/databases/$(database)/documents/user_profiles/$(request.auth.uid));
    }

    // Routine Tasks - Tenant-specific access
    match /adminbuddy_routine_tasks/{taskId} {
      allow read: if hasValidTenantAccess(resource.data.tenantId);
      allow create: if request.auth != null &&
                   exists(/databases/$(database)/documents/user_profiles/$(request.auth.uid)) &&
                   getUserTenantId() == request.resource.data.tenantId;
      allow update: if hasValidTenantAccess(resource.data.tenantId) &&
                   getUserTenantId() == request.resource.data.tenantId;
      allow delete: if hasValidTenantAccess(resource.data.tenantId);
      allow list: if request.auth != null &&
                 exists(/databases/$(database)/documents/user_profiles/$(request.auth.uid));
    }

    // Location Routine Schedules - Tenant-specific access
    match /adminbuddy_location_routine_schedules/{scheduleId} {
      allow read: if hasValidTenantAccess(resource.data.tenantId);
      allow create: if request.auth != null &&
                   exists(/databases/$(database)/documents/user_profiles/$(request.auth.uid)) &&
                   getUserTenantId() == request.resource.data.tenantId;
      allow update: if hasValidTenantAccess(resource.data.tenantId) &&
                   getUserTenantId() == request.resource.data.tenantId;
      allow delete: if hasValidTenantAccess(resource.data.tenantId);
      allow list: if request.auth != null &&
                 exists(/databases/$(database)/documents/user_profiles/$(request.auth.uid));
    }

    // Daily Tasks - Tenant-specific access
    match /adminbuddy_daily_tasks/{taskId} {
      allow read: if hasValidTenantAccess(resource.data.tenantId);
      allow create: if request.auth != null &&
                   exists(/databases/$(database)/documents/user_profiles/$(request.auth.uid)) &&
                   getUserTenantId() == request.resource.data.tenantId;
      allow update: if hasValidTenantAccess(resource.data.tenantId) &&
                   getUserTenantId() == request.resource.data.tenantId;
      allow delete: if hasValidTenantAccess(resource.data.tenantId);
      allow list: if request.auth != null &&
                 exists(/databases/$(database)/documents/user_profiles/$(request.auth.uid));
    }

    // Ad-hoc Tasks - Tenant-specific access
    match /adminbuddy_ad_hoc_tasks/{taskId} {
      allow read: if hasValidTenantAccess(resource.data.tenantId);
      allow create: if request.auth != null &&
                   exists(/databases/$(database)/documents/user_profiles/$(request.auth.uid)) &&
                   getUserTenantId() == request.resource.data.tenantId;
      allow update: if hasValidTenantAccess(resource.data.tenantId) &&
                   getUserTenantId() == request.resource.data.tenantId;
      allow delete: if hasValidTenantAccess(resource.data.tenantId);
      allow list: if request.auth != null &&
                 exists(/databases/$(database)/documents/user_profiles/$(request.auth.uid));
    }

    // Tenant Roles - Tenant-specific access
    match /adminbuddy_tenant_roles/{roleId} {
      allow read: if hasValidTenantAccess(resource.data.tenantId);
      allow create: if request.auth != null &&
                   exists(/databases/$(database)/documents/user_profiles/$(request.auth.uid)) &&
                   getUserTenantId() == request.resource.data.tenantId;
      allow update: if hasValidTenantAccess(resource.data.tenantId) &&
                   getUserTenantId() == request.resource.data.tenantId;
      allow delete: if hasValidTenantAccess(resource.data.tenantId);
      allow list: if request.auth != null &&
                 exists(/databases/$(database)/documents/user_profiles/$(request.auth.uid));
    }

    // Watchlist Tasks - Tenant-specific access
    match /adminbuddy_watchlist_tasks/{taskId} {
      allow read: if hasValidTenantAccess(resource.data.tenantId);
      allow create: if request.auth != null &&
                   exists(/databases/$(database)/documents/user_profiles/$(request.auth.uid)) &&
                   getUserTenantId() == request.resource.data.tenantId;
      allow update: if hasValidTenantAccess(resource.data.tenantId) &&
                   getUserTenantId() == request.resource.data.tenantId;
      allow delete: if hasValidTenantAccess(resource.data.tenantId);
      allow list: if request.auth != null &&
                 exists(/databases/$(database)/documents/user_profiles/$(request.auth.uid));
    }

    // Watchlist Checks - Tenant-specific access
    match /adminbuddy_watchlist_checks/{checkId} {
      allow read: if hasValidTenantAccess(resource.data.tenantId);
      allow create: if request.auth != null &&
                   exists(/databases/$(database)/documents/user_profiles/$(request.auth.uid)) &&
                   getUserTenantId() == request.resource.data.tenantId;
      allow update: if hasValidTenantAccess(resource.data.tenantId) &&
                   getUserTenantId() == request.resource.data.tenantId;
      allow delete: if hasValidTenantAccess(resource.data.tenantId);
      allow list: if request.auth != null &&
                 exists(/databases/$(database)/documents/user_profiles/$(request.auth.uid));
    }

    // Location Reports - Tenant-specific access
    match /adminbuddy_location_reports/{reportId} {
      allow read: if hasValidTenantAccess(resource.data.tenantId);
      allow create: if request.auth != null &&
                   exists(/databases/$(database)/documents/user_profiles/$(request.auth.uid)) &&
                   getUserTenantId() == request.resource.data.tenantId;
      allow update: if hasValidTenantAccess(resource.data.tenantId) &&
                   getUserTenantId() == request.resource.data.tenantId;
      allow delete: if hasValidTenantAccess(resource.data.tenantId);
      allow list: if request.auth != null &&
                 exists(/databases/$(database)/documents/user_profiles/$(request.auth.uid));
    }

    // Legacy Collections (for backward compatibility) - Tenant-specific access
    match /adminbuddy_tasks/{document} {
      allow read: if hasValidTenantAccess(resource.data.tenantId);
      allow create: if request.auth != null &&
                   exists(/databases/$(database)/documents/user_profiles/$(request.auth.uid)) &&
                   getUserTenantId() == request.resource.data.tenantId;
      allow update: if hasValidTenantAccess(resource.data.tenantId) &&
                   getUserTenantId() == request.resource.data.tenantId;
      allow delete: if hasValidTenantAccess(resource.data.tenantId);
      allow list: if request.auth != null &&
                 exists(/databases/$(database)/documents/user_profiles/$(request.auth.uid));
    }

    match /adminbuddy_location_routines/{document} {
      allow read: if hasValidTenantAccess(resource.data.tenantId);
      allow create: if request.auth != null &&
                   exists(/databases/$(database)/documents/user_profiles/$(request.auth.uid)) &&
                   getUserTenantId() == request.resource.data.tenantId;
      allow update: if hasValidTenantAccess(resource.data.tenantId) &&
                   getUserTenantId() == request.resource.data.tenantId;
      allow delete: if hasValidTenantAccess(resource.data.tenantId);
      allow list: if request.auth != null &&
                 exists(/databases/$(database)/documents/user_profiles/$(request.auth.uid));
    }

    match /adminbuddy_task_completions/{document} {
      allow read: if hasValidTenantAccess(resource.data.tenantId);
      allow create: if request.auth != null &&
                   exists(/databases/$(database)/documents/user_profiles/$(request.auth.uid)) &&
                   getUserTenantId() == request.resource.data.tenantId;
      allow update: if hasValidTenantAccess(resource.data.tenantId) &&
                   getUserTenantId() == request.resource.data.tenantId;
      allow delete: if hasValidTenantAccess(resource.data.tenantId);
      allow list: if request.auth != null &&
                 exists(/databases/$(database)/documents/user_profiles/$(request.auth.uid));
    }
  }
}