# AdminBuddy Developer Guide

## 🚀 Quick Commands

```bash
# Development
npm start                    # Start dev server (http://localhost:3000)
npm test                     # Run tests
npm run build               # Production build

# Firebase Deployment
npm run deploy              # Build and deploy everything
npm run deploy:hosting      # Deploy only hosting
npm run deploy:functions    # Deploy only functions
npm run deploy:rules        # Deploy only Firestore rules

# Firebase Development
npm run firebase:emulators  # Start Firebase emulators
npm run firebase:logs       # View function logs

# Testing Production
npx serve -s build -p 3001  # Test production build locally
```

## 📁 File Organization

### Creating New Components

```
src/components/category/ComponentName/
├── ComponentName.tsx        # Main component
├── ComponentName.css       # Styles
├── index.ts                # Export
└── ComponentName.test.tsx  # Tests (future)
```

### Creating New Pages

```
src/pages/PageName/
├── PageName.tsx            # Main page component
├── PageName.css           # Page-specific styles
└── index.js               # Simple export
```

## 🎯 Code Patterns

### Component Template

```typescript
import React from "react";
import { usePerformance } from "../../hooks";
import "./ComponentName.css";

interface ComponentNameProps {
  title: string;
  children?: React.ReactNode;
}

const ComponentName: React.FC<ComponentNameProps> = ({ title, children }) => {
  usePerformance({
    componentName: "ComponentName",
    threshold: 16, // Adjust based on complexity
  });

  return (
    <div className="component-name">
      <h2>{title}</h2>
      {children}
    </div>
  );
};

export default ComponentName;
```

### Page Template

```typescript
import React from "react";
import { usePerformance } from "../../hooks";
import ErrorBoundary from "../../components/ui/ErrorBoundary";
import "./PageName.css";

const PageName: React.FC = () => {
  usePerformance({
    componentName: "PageName",
    threshold: 30, // Pages can be slower
  });

  return (
    <ErrorBoundary>
      <div className="page-name">
        <h1>Page Title</h1>
        {/* Page content */}
      </div>
    </ErrorBoundary>
  );
};

export default PageName;
```

### Custom Hook Template

```typescript
import { useState, useEffect } from "react";

interface UseCustomHookReturn {
  data: any[];
  loading: boolean;
  error: string | null;
}

export const useCustomHook = (): UseCustomHookReturn => {
  const [data, setData] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Hook logic here
  }, []);

  return { data, loading, error };
};
```

## 🎨 CSS Guidelines

### Naming Convention (BEM)

```css
/* Block */
.component-name {
}

/* Element */
.component-name__element {
}

/* Modifier */
.component-name--modifier {
}
.component-name__element--modifier {
}
```

### Responsive Design

```css
/* Mobile first */
.component {
  padding: 1rem;
}

/* Tablet */
@media (min-width: 768px) {
  .component {
    padding: 2rem;
  }
}

/* Desktop */
@media (min-width: 1024px) {
  .component {
    padding: 3rem;
  }
}
```

### Animation Classes

```css
/* Use existing animation utilities */
.animate-fade-in        /* Fade in animation */
/* Fade in animation */
.animate-fade-in-up     /* Fade in from bottom */
.animate-slide-in-left  /* Slide in from left */
.hover-lift             /* Hover lift effect */
.hover-scale; /* Hover scale effect */
```

## 🔧 Performance Guidelines

### Component Performance Thresholds

```typescript
// Simple components
threshold: 16; // 60fps target

// Data-heavy components
threshold: 25; // Allow more time

// Page components
threshold: 30; // Pages can be slower

// Complex data operations
threshold: 50; // Heavy operations
```

### Optimization Techniques

```typescript
// Memoize expensive components
const ExpensiveComponent = React.memo(({ data }) => {
  // Component logic
});

// Lazy load pages
const LazyPage = React.lazy(() => import("./pages/LazyPage"));

// Preload images
const { isLoaded } = useImagePreload(imageUrl);

// Intersection observer for animations
const { ref, hasIntersected } = useIntersectionObserver({
  threshold: 0.1,
  triggerOnce: true,
});
```

## 🛠️ Common Tasks

### Adding a New Route

1. **Add to constants:**

   ```typescript
   // src/constants/index.ts
   export const ROUTES = {
     NEW_PAGE: "/new-page",
   };
   ```

2. **Add to App.tsx:**

   ```typescript
   const NewPage = React.lazy(() => import("./pages/NewPage"));

   // In Routes
   <Route path={ROUTES.NEW_PAGE} element={<NewPage />} />;
   ```

3. **Add to navigation:**
   ```typescript
   // src/constants/index.ts
   export const NAVIGATION_ITEMS = [{ to: ROUTES.NEW_PAGE, label: "New Page" }];
   ```

### Adding Performance Monitoring

```typescript
import { usePerformance } from "../hooks";

// In component
usePerformance({
  componentName: "ComponentName",
  threshold: 20,
  logToConsole: true, // Optional, defaults to true in dev
});
```

### Adding Responsive Behavior

```typescript
import { useViewport } from "../hooks";

const { isMobile, isTablet, isDesktop } = useViewport();

return (
  <div className={`component ${isMobile ? "mobile" : ""}`}>
    {isMobile ? <MobileView /> : <DesktopView />}
  </div>
);
```

### Adding Animations

```typescript
import { useIntersectionObserver, useAnimation } from "../hooks";

const { ref, hasIntersected } = useIntersectionObserver({
  threshold: 0.1,
  triggerOnce: true,
});

const { animationClass } = useAnimation("fade-in-up", hasIntersected ? 0 : 0);

return (
  <div ref={ref} className={`component ${animationClass}`}>
    Content
  </div>
);
```

## 🐛 Debugging

### Performance Issues

```typescript
// Check component render times
usePerformance({
  componentName: "SlowComponent",
  threshold: 1, // Very low threshold to catch issues
});

// Check in browser console for warnings
```

### State Issues

```typescript
// Add debug logging
useEffect(() => {
  console.log("State changed:", { state, props });
}, [state, props]);
```

### CSS Issues

```css
/* Add debug borders */
.debug * {
  border: 1px solid red !important;
}

/* Check responsive breakpoints */
.debug::before {
  content: "Mobile";
}

@media (min-width: 768px) {
  .debug::before {
    content: "Tablet";
  }
}

@media (min-width: 1024px) {
  .debug::before {
    content: "Desktop";
  }
}
```

## 📋 Checklist for New Features

### Before Committing

- [ ] TypeScript types defined
- [ ] Performance monitoring added
- [ ] Responsive design tested
- [ ] Accessibility checked
- [ ] Error boundaries in place
- [ ] Loading states handled
- [ ] Production build tested
- [ ] Console errors cleared
- [ ] Mobile tested
- [ ] Performance targets met

### Testing Checklist

- [ ] Development mode works
- [ ] Production build works
- [ ] Mobile responsive
- [ ] Keyboard navigation
- [ ] Screen reader friendly
- [ ] Performance dashboard shows metrics
- [ ] No console errors
- [ ] All links work
- [ ] Images load properly
- [ ] Animations respect reduced motion
