// Firebase-based instructor authentication service
import {
  signInWithEmailAndPassword,
  signOut,
  onAuthStateChanged,
  User,
} from "firebase/auth";
import { auth, db } from "../config/firebase";
import { doc, getDoc } from "firebase/firestore";
import { getInstructorByEmail } from "./instructorManagementService";

export interface InstructorUser {
  id: string;
  email: string;
  name: string;
  role: "instructor";
  assignedCourses: string[]; // Course IDs the instructor teaches
  loginTime: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

// Cache for instructor data to avoid repeated database calls
let instructorCache: { [email: string]: InstructorUser } = {};

export const instructorLogin = async (
  credentials: LoginCredentials
): Promise<{ success: boolean; user?: InstructorUser; error?: string }> => {
  try {
    // First authenticate with Firebase
    const userCredential = await signInWithEmailAndPassword(
      auth,
      credentials.email,
      credentials.password
    );
    const firebaseUser = userCredential.user;

    // Check if user is an authorized instructor in database
    console.log("🔍 Looking up instructor account for:", credentials.email);
    const instructorAccount = await getInstructorByEmail(credentials.email);
    console.log("📋 Found instructor account:", instructorAccount);

    if (!instructorAccount) {
      await signOut(auth); // Sign out if not authorized
      return {
        success: false,
        error: "No instructor account found for this email address",
      };
    }

    if (instructorAccount.status !== "active") {
      await signOut(auth); // Sign out if not authorized
      return {
        success: false,
        error: `Account status is "${instructorAccount.status}". Please contact an administrator.`,
      };
    }

    const user: InstructorUser = {
      id: instructorAccount.id,
      email: instructorAccount.email,
      name: instructorAccount.name,
      role: "instructor",
      assignedCourses: instructorAccount.assignedCourses,
      loginTime: new Date().toISOString(),
    };

    // Cache the instructor data
    instructorCache[credentials.email] = user;

    return { success: true, user };
  } catch (error: any) {
    let errorMessage = "Login failed";

    switch (error.code) {
      case "auth/user-not-found":
        errorMessage = "No account found with this email";
        break;
      case "auth/wrong-password":
        errorMessage = "Incorrect password";
        break;
      case "auth/invalid-email":
        errorMessage = "Invalid email address";
        break;
      case "auth/too-many-requests":
        errorMessage = "Too many failed attempts. Please try again later";
        break;
      default:
        errorMessage = error.message || "Login failed";
    }

    return { success: false, error: errorMessage };
  }
};

export const instructorLogout = async (): Promise<void> => {
  try {
    // Clear cache
    instructorCache = {};
    await signOut(auth);
  } catch (error) {
    console.error("Logout error:", error);
  }
};

export const getInstructorSession = (): InstructorUser | null => {
  const user = auth.currentUser;
  if (!user || !user.email) {
    return null;
  }

  // Return cached instructor data if available
  const cachedInstructor = instructorCache[user.email];
  if (cachedInstructor) {
    return cachedInstructor;
  }

  return null;
};

export const isInstructorAuthenticated = (): boolean => {
  const user = auth.currentUser;
  return (
    user !== null &&
    user.email !== null &&
    instructorCache[user.email] !== undefined
  );
};

// Listen for auth state changes
export const onInstructorAuthStateChanged = (
  callback: (user: InstructorUser | null) => void
): (() => void) => {
  return onAuthStateChanged(auth, async (firebaseUser) => {
    console.log(
      "🔄 Auth state changed, Firebase user:",
      firebaseUser?.email || "null"
    );

    if (firebaseUser && firebaseUser.email) {
      // Check cache first
      const cachedInstructor = instructorCache[firebaseUser.email];
      if (cachedInstructor) {
        console.log("📋 Using cached instructor data:", cachedInstructor.name);
        callback(cachedInstructor);
        return;
      }

      // Check database if not in cache
      try {
        console.log(
          "🔍 Looking up instructor in database:",
          firebaseUser.email
        );
        const instructorAccount = await getInstructorByEmail(
          firebaseUser.email
        );
        console.log("📋 Database lookup result:", instructorAccount);

        if (instructorAccount && instructorAccount.status === "active") {
          const instructorUser: InstructorUser = {
            id: instructorAccount.id,
            email: instructorAccount.email,
            name: instructorAccount.name,
            role: "instructor",
            assignedCourses: instructorAccount.assignedCourses,
            loginTime: new Date().toISOString(),
          };
          instructorCache[firebaseUser.email] = instructorUser;
          console.log("✅ Instructor authenticated:", instructorUser.name);
          callback(instructorUser);
        } else {
          console.log("❌ Instructor not found or not active");
          callback(null);
        }
      } catch (error) {
        console.error("❌ Error checking instructor auth state:", error);
        callback(null);
      }
    } else {
      console.log("❌ No Firebase user");
      callback(null);
    }
  });
};

// Instructor route protection
export const requireInstructorAuth = (): InstructorUser => {
  const user = getInstructorSession();
  if (!user) {
    throw new Error("Instructor authentication required");
  }
  return user;
};

// Debug function to check instructor data
export const debugInstructorData = async (instructorId: string) => {
  try {
    console.log("🔍 DEBUG: Checking instructor data for:", instructorId);

    const instructorRef = doc(db, "instructors", instructorId);
    const instructorDoc = await getDoc(instructorRef);

    if (!instructorDoc.exists()) {
      console.log("❌ DEBUG: Instructor document not found");
      return;
    }

    const data = instructorDoc.data();
    console.log("📋 DEBUG: Full instructor document:", data);
    console.log("📋 DEBUG: Assigned courses:", data.assignedCourses);
    console.log("📋 DEBUG: Document ID:", instructorDoc.id);
    console.log("📋 DEBUG: Data keys:", Object.keys(data));
  } catch (error) {
    console.error("❌ DEBUG: Error checking instructor data:", error);
  }
};

// Get courses assigned to instructor (using course-centric approach)
export const getInstructorCourses = async (
  instructorId: string
): Promise<any[]> => {
  try {
    console.log("🔍 Getting courses for instructor:", instructorId);

    // Get instructor data to verify session
    const instructorUser = getInstructorSession();
    if (!instructorUser) {
      console.log("❌ No instructor session");
      return [];
    }

    // Debug instructor data
    await debugInstructorData(instructorId);

    console.log(
      "🔍 Using course-centric approach: finding courses where instructorId =",
      instructorId
    );

    // Import query functions
    const { collection, query, where, getDocs } = await import(
      "firebase/firestore"
    );

    // Query courses collection for courses assigned to this instructor
    const coursesRef = collection(db, "courses");
    const q = query(coursesRef, where("instructorId", "==", instructorId));

    console.log("🔍 Executing Firestore query for assigned courses...");
    const snapshot = await getDocs(q);

    const assignedCourses = snapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as any[];

    console.log(
      `📚 Found ${assignedCourses.length} courses assigned to instructor`
    );

    if (assignedCourses.length > 0) {
      console.log(
        "📚 Assigned courses:",
        assignedCourses.map((c) => ({
          id: c.id,
          slug: c.slug,
          title: c.title,
          instructorId: c.instructorId,
          status: c.status,
        }))
      );
    } else {
      console.log("📚 No courses assigned to instructor");
      console.log(
        "💡 HINT: Use the Course Assignment tab in admin portal to assign courses"
      );
    }

    return assignedCourses;
  } catch (error) {
    console.error("Error getting instructor courses:", error);
    console.log("💡 TROUBLESHOOTING:");
    console.log("   1. Check Firestore security rules for courses collection");
    console.log("   2. Verify instructor ID is correct");
    console.log(
      "   3. Make sure courses are assigned via Course Assignment tab"
    );
    return [];
  }
};

// Get course capacities for instructor's assigned courses only
export const getInstructorCourseCapacities = async (): Promise<any[]> => {
  try {
    console.log("🔍 Getting course capacities for instructor...");

    // Get instructor data to verify session
    const instructorUser = getInstructorSession();
    if (!instructorUser) {
      console.log("❌ No instructor session");
      return [];
    }

    console.log(
      "📋 Getting capacities for courses assigned to instructor:",
      instructorUser.id
    );

    // Get assigned courses using the new secure method
    const assignedCourses = await getInstructorCourses(instructorUser.id);

    // Get course capacities only for assigned courses
    const { getCourseCapacity } = await import(
      "./firebaseCourseCapacityService"
    );
    const capacities = [];

    for (const course of assignedCourses) {
      try {
        console.log(
          `🔍 Getting capacity for assigned course: ${course.title} (${course.id})`
        );

        console.log(
          `📋 Found course: ${course.title} (slug: ${course.slug}, id: ${course.id})`
        );

        // Try to get capacity using the document ID (capacity documents use document ID)
        let capacity = await getCourseCapacity(course.id);

        if (capacity) {
          console.log(`✅ Got capacity for ${course.title}:`, {
            courseId: capacity.courseId,
            enrolled: capacity.currentEnrollment,
            registrations: capacity.registrations.length,
          });
          capacities.push(capacity);
        } else {
          console.log(`❌ No capacity found for course: ${course.title}`);
        }
      } catch (error) {
        console.error(
          `❌ Error getting capacity for course ${course.title} (${course.id}):`,
          error
        );
        // Continue with other courses even if one fails
      }
    }

    console.log(
      "📊 Found capacities for assigned courses:",
      capacities.map((c) => c.courseTitle)
    );
    return capacities;
  } catch (error) {
    console.error("Error getting instructor course capacities:", error);
    return [];
  }
};

// Get registrations only for instructor's assigned courses
export const getInstructorCourseRegistrations = async (
  courseId: string
): Promise<any[]> => {
  try {
    console.log("🔍 Getting registrations for instructor course:", courseId);

    // Get instructor data to verify access
    const instructorUser = getInstructorSession();
    if (!instructorUser) {
      console.log("❌ No instructor session");
      return [];
    }

    // Get assigned courses to verify instructor has access to this course
    const assignedCourses = await getInstructorCourses(instructorUser.id);

    // Find the course and verify instructor has access
    const course = assignedCourses.find(
      (c) => c.slug === courseId || c.id === courseId
    );

    if (!course) {
      console.log(
        `❌ Course not found or instructor not assigned: ${courseId}`
      );
      return [];
    }

    console.log(`✅ Instructor authorized for course: ${course.title}`);
    console.log(
      `🔍 Course details: ID="${course.id}", slug="${course.slug}", title="${course.title}", instructorId="${course.instructorId}"`
    );
    console.log(`🔍 About to query registrations for courseId: "${course.id}"`);

    // Get registrations for this specific course only
    const { getCourseRegistrations } = await import(
      "./firebaseCourseCapacityService"
    );

    // Try both document ID and slug to find registrations
    console.log(
      `🔍 Trying to get registrations with document ID: ${course.id}`
    );
    let registrations = await getCourseRegistrations(course.id);

    console.log(
      `📊 Found ${registrations.length} registrations with document ID`
    );
    if (registrations.length > 0) {
      console.log(
        "📋 Registrations found with document ID:",
        registrations.map((r) => ({
          id: r.id,
          name: `${r.firstName} ${r.lastName}`,
          courseId: r.courseId,
          enrollmentStatus: r.enrollmentStatus,
        }))
      );
      console.log(
        "📋 Found registrations:",
        registrations
          .map((r) => `${r.firstName} ${r.lastName} (${r.email})`)
          .join(", ")
      );
    }

    if (registrations.length === 0) {
      console.log(
        `🔍 No registrations found with document ID, trying slug: ${course.slug}`
      );
      registrations = await getCourseRegistrations(course.slug);
      console.log(`📊 Found ${registrations.length} registrations with slug`);
      if (registrations.length > 0) {
        console.log(
          "📋 Registrations found with slug:",
          registrations.map((r) => ({
            id: r.id,
            name: `${r.firstName} ${r.lastName}`,
            courseId: r.courseId,
            enrollmentStatus: r.enrollmentStatus,
          }))
        );
      }
    }

    // Final filter to ensure we only return registrations that match this specific course
    // and exclude cancelled registrations
    const filteredRegistrations = registrations.filter((r) => {
      const courseMatches =
        r.courseId === course.id || r.courseId === course.slug;
      const notCancelled = r.paymentStatus !== "cancelled";

      if (!courseMatches) {
        console.log(
          `  ❌ Registration "${r.firstName} ${r.lastName}": wrong course (${r.courseId})`
        );
      }
      if (!notCancelled) {
        console.log(
          `  ❌ Registration "${r.firstName} ${r.lastName}": cancelled status`
        );
      }

      return courseMatches && notCancelled;
    });

    // Deduplicate registrations by email (keep most recent)
    const emailMap = new Map<string, any>();

    filteredRegistrations.forEach((registration) => {
      const email = registration.email.toLowerCase().trim();
      const existing = emailMap.get(email);

      if (!existing) {
        emailMap.set(email, registration);
      } else {
        // Keep the most recent registration
        const existingDate = new Date(existing.registrationDate);
        const currentDate = new Date(registration.registrationDate);

        if (currentDate > existingDate) {
          console.log(
            `🔄 Replacing duplicate registration for ${registration.email} with newer one`
          );
          emailMap.set(email, registration);
        }
      }
    });

    const deduplicatedRegistrations = Array.from(emailMap.values());

    console.log(
      `✅ Final deduplicated registrations for course ${course.title}: ${deduplicatedRegistrations.length} (was ${filteredRegistrations.length})`
    );
    console.log(
      "📋 Final registration details:",
      deduplicatedRegistrations.map((r) => ({
        id: r.id,
        name: `${r.firstName} ${r.lastName}`,
        email: r.email,
        courseId: r.courseId,
        enrollmentStatus: r.enrollmentStatus,
        registrationDate: r.registrationDate,
      }))
    );

    return deduplicatedRegistrations;
  } catch (error) {
    console.error("Error getting instructor course registrations:", error);
    return [];
  }
};
