// AdminBuddy Location Management Service
import {
  collection,
  doc,
  getDoc,
  getDocs,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
} from "firebase/firestore";
import { db, COLLECTIONS } from "../config/firebase";
import { Location } from "../types";

// Create a new location
export const createLocation = async (
  locationData: Omit<Location, "id" | "createdAt" | "updatedAt">
): Promise<string> => {
  try {
    console.log("📍 Creating new location:", locationData.name);
    
    const now = new Date().toISOString();
    const docRef = await addDoc(collection(db, COLLECTIONS.LOCATIONS), {
      ...locationData,
      createdAt: now,
      updatedAt: now,
    });

    console.log(`✅ Location created with ID: ${docRef.id}`);
    return docRef.id;
  } catch (error) {
    console.error("❌ Error creating location:", error);
    throw error;
  }
};

// Get location by ID
export const getLocation = async (locationId: string): Promise<Location | null> => {
  try {
    const locationRef = doc(db, COLLECTIONS.LOCATIONS, locationId);
    const locationDoc = await getDoc(locationRef);
    
    if (!locationDoc.exists()) {
      return null;
    }
    
    return { id: locationDoc.id, ...locationDoc.data() } as Location;
  } catch (error) {
    console.error("❌ Error getting location:", error);
    return null;
  }
};

// Get locations by tenant
export const getLocationsByTenant = async (tenantId: string): Promise<Location[]> => {
  try {
    console.log("🔍 Fetching locations for tenant:", tenantId);
    const locationsRef = collection(db, COLLECTIONS.LOCATIONS);
    const q = query(
      locationsRef,
      where("tenantId", "==", tenantId),
      where("isActive", "==", true),
      orderBy("name", "asc")
    );
    const snapshot = await getDocs(q);

    const locations = snapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as Location[];

    console.log(`📊 Found ${locations.length} locations for tenant`);
    return locations;
  } catch (error) {
    console.error("❌ Error fetching locations:", error);
    return [];
  }
};

// Update location
export const updateLocation = async (
  locationId: string,
  updates: Partial<Omit<Location, "id" | "createdAt" | "updatedAt">>
): Promise<boolean> => {
  try {
    console.log(`📝 Updating location: ${locationId}`);
    
    const locationRef = doc(db, COLLECTIONS.LOCATIONS, locationId);
    await updateDoc(locationRef, {
      ...updates,
      updatedAt: new Date().toISOString(),
    });

    console.log(`✅ Location updated successfully`);
    return true;
  } catch (error) {
    console.error("❌ Error updating location:", error);
    return false;
  }
};

// Delete location (soft delete)
export const deleteLocation = async (locationId: string): Promise<boolean> => {
  try {
    console.log(`🗑️ Deleting location: ${locationId}`);
    
    // Soft delete by setting isActive to false
    return await updateLocation(locationId, { isActive: false });
  } catch (error) {
    console.error("❌ Error deleting location:", error);
    return false;
  }
};

// Get locations for a manager
export const getManagerLocations = async (
  tenantId: string,
  managerLocationIds: string[]
): Promise<Location[]> => {
  try {
    if (managerLocationIds.length === 0) {
      return [];
    }

    console.log("🔍 Fetching manager locations:", managerLocationIds);
    
    // Get all locations for the tenant first, then filter
    const allLocations = await getLocationsByTenant(tenantId);
    const managerLocations = allLocations.filter(location => 
      managerLocationIds.includes(location.id)
    );

    console.log(`📊 Found ${managerLocations.length} locations for manager`);
    return managerLocations;
  } catch (error) {
    console.error("❌ Error fetching manager locations:", error);
    return [];
  }
};

// Assign manager to location
export const assignManagerToLocation = async (
  locationId: string,
  managerId: string
): Promise<boolean> => {
  try {
    console.log(`👤 Assigning manager ${managerId} to location ${locationId}`);
    
    const location = await getLocation(locationId);
    if (!location) {
      throw new Error("Location not found");
    }

    const updatedManagerIds = [...(location.managerIds || [])];
    if (!updatedManagerIds.includes(managerId)) {
      updatedManagerIds.push(managerId);
    }

    return await updateLocation(locationId, { managerIds: updatedManagerIds });
  } catch (error) {
    console.error("❌ Error assigning manager to location:", error);
    return false;
  }
};

// Remove manager from location
export const removeManagerFromLocation = async (
  locationId: string,
  managerId: string
): Promise<boolean> => {
  try {
    console.log(`👤 Removing manager ${managerId} from location ${locationId}`);
    
    const location = await getLocation(locationId);
    if (!location) {
      throw new Error("Location not found");
    }

    const updatedManagerIds = (location.managerIds || []).filter(
      id => id !== managerId
    );

    return await updateLocation(locationId, { managerIds: updatedManagerIds });
  } catch (error) {
    console.error("❌ Error removing manager from location:", error);
    return false;
  }
};
