// Firebase-based admin authentication service
import {
  signInWithEmailAndPassword,
  signOut,
  onAuthStateChanged,
  User,
} from "firebase/auth";
import { auth, ADMIN_CONFIG } from "../config/firebase";

export interface AdminUser {
  email: string;
  name: string;
  role: "admin";
  loginTime: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export const adminLogin = async (
  credentials: LoginCredentials
): Promise<{ success: boolean; user?: AdminUser; error?: string }> => {
  try {
    // Check if email is authorized admin
    if (credentials.email !== ADMIN_CONFIG.ADMIN_EMAIL) {
      return { success: false, error: "Unauthorized email address" };
    }

    const userCredential = await signInWithEmailAndPassword(
      auth,
      credentials.email,
      credentials.password
    );
    const firebaseUser = userCredential.user;

    const user: AdminUser = {
      email: firebaseUser.email || "",
      name: "<PERSON>", // You can store this in Firestore if needed
      role: "admin",
      loginTime: new Date().toISOString(),
    };

    return { success: true, user };
  } catch (error: any) {
    let errorMessage = "Login failed";

    switch (error.code) {
      case "auth/user-not-found":
        errorMessage = "No account found with this email";
        break;
      case "auth/wrong-password":
        errorMessage = "Incorrect password";
        break;
      case "auth/invalid-email":
        errorMessage = "Invalid email address";
        break;
      case "auth/too-many-requests":
        errorMessage = "Too many failed attempts. Please try again later";
        break;
      default:
        errorMessage = error.message || "Login failed";
    }

    return { success: false, error: errorMessage };
  }
};

export const adminLogout = async (): Promise<void> => {
  try {
    await signOut(auth);
  } catch (error) {
    console.error("Logout error:", error);
  }
};

export const getAdminSession = (): AdminUser | null => {
  const user = auth.currentUser;
  if (!user || user.email !== ADMIN_CONFIG.ADMIN_EMAIL) {
    return null;
  }

  return {
    email: user.email,
    name: "Derek MacDonald",
    role: "admin",
    loginTime: new Date().toISOString(),
  };
};

export const isAdminAuthenticated = (): boolean => {
  const user = auth.currentUser;
  return user !== null && user.email === ADMIN_CONFIG.ADMIN_EMAIL;
};

// Listen for auth state changes
export const onAdminAuthStateChanged = (
  callback: (user: AdminUser | null) => void
): (() => void) => {
  return onAuthStateChanged(auth, (firebaseUser) => {
    if (firebaseUser && firebaseUser.email === ADMIN_CONFIG.ADMIN_EMAIL) {
      const adminUser: AdminUser = {
        email: firebaseUser.email,
        name: "Derek MacDonald",
        role: "admin",
        loginTime: new Date().toISOString(),
      };
      callback(adminUser);
    } else {
      callback(null);
    }
  });
};

// Admin route protection
export const requireAdminAuth = (): AdminUser => {
  const user = getAdminSession();
  if (!user) {
    throw new Error("Admin authentication required");
  }
  return user;
};
