import { storage } from '../config/firebase';
import { ref, uploadBytes, getDownloadURL, deleteObject } from 'firebase/storage';

export interface ImageUploadResult {
  success: boolean;
  url?: string;
  error?: string;
}

export interface ImageUploadProgress {
  progress: number;
  isUploading: boolean;
}

/**
 * Upload an image to Firebase Storage
 * @param file - The image file to upload
 * @param folder - The folder path in storage (e.g., 'courses', 'calendar')
 * @param filename - Optional custom filename (will generate if not provided)
 * @returns Promise with upload result
 */
export const uploadImage = async (
  file: File,
  folder: string = 'courses',
  filename?: string
): Promise<ImageUploadResult> => {
  try {
    // Validate file type
    if (!file.type.startsWith('image/')) {
      return {
        success: false,
        error: 'Please select a valid image file (JPG, PNG, GIF, WebP)'
      };
    }

    // Validate file size (5MB limit)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      return {
        success: false,
        error: 'Image size must be less than 5MB'
      };
    }

    // Generate filename if not provided
    const timestamp = Date.now();
    const extension = file.name.split('.').pop()?.toLowerCase() || 'jpg';
    const finalFilename = filename || `${timestamp}.${extension}`;
    
    // Create storage reference
    const storageRef = ref(storage, `${folder}/${finalFilename}`);
    
    // Upload file
    console.log(`📤 Uploading image to: ${folder}/${finalFilename}`);
    const snapshot = await uploadBytes(storageRef, file);
    
    // Get download URL
    const downloadURL = await getDownloadURL(snapshot.ref);
    
    console.log('✅ Image uploaded successfully:', downloadURL);
    
    return {
      success: true,
      url: downloadURL
    };
    
  } catch (error) {
    console.error('❌ Image upload failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Upload failed'
    };
  }
};

/**
 * Delete an image from Firebase Storage
 * @param imageUrl - The full download URL of the image to delete
 * @returns Promise with deletion result
 */
export const deleteImage = async (imageUrl: string): Promise<boolean> => {
  try {
    // Extract the storage path from the download URL
    const url = new URL(imageUrl);
    const pathMatch = url.pathname.match(/\/o\/(.+)\?/);
    
    if (!pathMatch) {
      console.warn('Could not extract storage path from URL:', imageUrl);
      return false;
    }
    
    const storagePath = decodeURIComponent(pathMatch[1]);
    const storageRef = ref(storage, storagePath);
    
    await deleteObject(storageRef);
    console.log('✅ Image deleted successfully:', storagePath);
    
    return true;
  } catch (error) {
    console.error('❌ Image deletion failed:', error);
    return false;
  }
};

/**
 * Validate image file before upload
 * @param file - The file to validate
 * @returns Validation result with error message if invalid
 */
export const validateImageFile = (file: File): { valid: boolean; error?: string } => {
  // Check file type
  if (!file.type.startsWith('image/')) {
    return {
      valid: false,
      error: 'Please select a valid image file (JPG, PNG, GIF, WebP)'
    };
  }
  
  // Check file size (5MB limit)
  const maxSize = 5 * 1024 * 1024; // 5MB
  if (file.size > maxSize) {
    return {
      valid: false,
      error: 'Image size must be less than 5MB'
    };
  }
  
  // Check dimensions (optional - could add max width/height check)
  return { valid: true };
};

/**
 * Generate a storage-friendly filename from a course title
 * @param title - The course title
 * @param extension - File extension (default: 'jpg')
 * @returns Clean filename suitable for storage
 */
export const generateCourseImageFilename = (title: string, extension: string = 'jpg'): string => {
  const timestamp = Date.now();
  const cleanTitle = title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .trim();
  
  return `${cleanTitle}-${timestamp}.${extension}`;
};

/**
 * Resize image on client side before upload (optional optimization)
 * @param file - The image file to resize
 * @param maxWidth - Maximum width in pixels
 * @param maxHeight - Maximum height in pixels
 * @param quality - JPEG quality (0-1)
 * @returns Promise with resized file
 */
export const resizeImage = (
  file: File,
  maxWidth: number = 1200,
  maxHeight: number = 800,
  quality: number = 0.8
): Promise<File> => {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();
    
    img.onload = () => {
      // Calculate new dimensions
      let { width, height } = img;
      
      if (width > maxWidth) {
        height = (height * maxWidth) / width;
        width = maxWidth;
      }
      
      if (height > maxHeight) {
        width = (width * maxHeight) / height;
        height = maxHeight;
      }
      
      // Set canvas dimensions
      canvas.width = width;
      canvas.height = height;
      
      // Draw and compress
      ctx?.drawImage(img, 0, 0, width, height);
      
      canvas.toBlob(
        (blob) => {
          if (blob) {
            const resizedFile = new File([blob], file.name, {
              type: file.type,
              lastModified: Date.now()
            });
            resolve(resizedFile);
          } else {
            resolve(file); // Fallback to original
          }
        },
        file.type,
        quality
      );
    };
    
    img.src = URL.createObjectURL(file);
  });
};

/**
 * Storage folder constants
 */
export const STORAGE_FOLDERS = {
  COURSES: 'courses',
  CALENDAR: 'calendar',
  GENERAL: 'general'
} as const;
