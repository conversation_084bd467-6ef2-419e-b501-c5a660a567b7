// Backward compatibility wrapper for Firebase course capacity service
// This file delegates all operations to firebaseCourseCapacityService.ts

export type {
  CourseCapacity,
  CourseRegistration,
} from "./firebaseCourseCapacityService";

// Re-export all Firebase functions for backward compatibility
export {
  initializeCourseCapacities,
  getCourseCapacity,
  getAllCourseCapacities,
  updateCourseCapacity,
  addRegistrationToCapacity,
  getCourseRegistrations,
  getAllRegistrations,
  deleteRegistration,
  updatePaymentStatus,
  getRegistrationStats,
  onCourseCapacityChanged,
  onRegistrationsChanged,
} from "./firebaseCourseCapacityService";
