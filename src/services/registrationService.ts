import { RegistrationData } from "../components/course/RegistrationForm";
import {
  addRegistrationToCapacity,
  getCourseCapacity,
  CourseRegistration,
} from "./firebaseCourseCapacityService";

// Local interface for creating registrations (before timestamp is added)
interface RegistrationForCreation extends RegistrationData {
  courseId: string;
  courseTitle: string;
  coursePrice: string;
  registrationDate: string;
  registrationId: string;
  paymentStatus: "pending" | "paid" | "refunded" | "late";
  paymentDate?: string;
  paymentNotes?: string;
  enrollmentStatus: "enrolled" | "waitlist";
}

export interface RegistrationResponse {
  success: boolean;
  registrationId?: string;
  message: string;
  error?: string;
}

// Registration service now uses Firebase for data storage
// Email notifications can be added later via Firebase Functions or other services

export const submitRegistration = async (
  courseId: string,
  courseTitle: string,
  coursePrice: string,
  formData: RegistrationData
): Promise<RegistrationResponse> => {
  try {
    // Check course capacity to determine messaging
    const capacity = await getCourseCapacity(courseId);
    const isWaitlist = capacity && capacity.status === "full";

    const registrationId = generateRegistrationId();
    const registrationDate = new Date().toISOString();

    const registration: RegistrationForCreation = {
      ...formData,
      courseId,
      courseTitle,
      coursePrice,
      registrationDate,
      registrationId,
      paymentStatus: "pending",
      enrollmentStatus: isWaitlist ? "waitlist" : "enrolled",
    };

    // Store registration locally for confirmation page
    localStorage.setItem("lastRegistration", JSON.stringify(registration));

    // Save to Firebase
    await addRegistrationToCapacity(registration);

    return {
      success: true,
      registrationId,
      message: isWaitlist
        ? "You've been added to the waitlist! We'll contact you if a spot becomes available."
        : "Registration submitted successfully! Your registration has been saved and we'll be in touch with next steps.",
    };
  } catch (error) {
    console.error("Registration submission error:", error);
    return {
      success: false,
      message:
        "Failed to save registration. Please try again or contact us directly.",
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
};

export const getLastRegistration = (): RegistrationForCreation | null => {
  try {
    const stored = localStorage.getItem("lastRegistration");
    return stored ? JSON.parse(stored) : null;
  } catch {
    return null;
  }
};

export const clearLastRegistration = (): void => {
  localStorage.removeItem("lastRegistration");
};

function generateRegistrationId(): string {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substr(2, 5);
  return `REG-${timestamp}-${random}`.toUpperCase();
}

// Registration service is now fully integrated with Firebase
// Email notifications can be implemented later using:
// - Firebase Functions with email providers (SendGrid, Mailgun, etc.)
// - Third-party services triggered by Firebase webhooks
// - Manual email workflows from the admin dashboard
