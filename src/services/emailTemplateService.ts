/**
 * Email template service for managing email templates in Firebase
 */

import {
  collection,
  doc,
  getDocs,
  getDoc,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  orderBy,
  where,
  Timestamp,
} from 'firebase/firestore';
import { db } from '../config/firebase';
import {
  EmailTemplate,
  CreateEmailTemplateData,
  UpdateEmailTemplateData,
  DEFAULT_EMAIL_TEMPLATES,
  EmailTemplateCategory,
} from '../types/emailTemplate';

const COLLECTION_NAME = 'emailTemplates';

/**
 * Get all email templates
 */
export const getAllEmailTemplates = async (): Promise<EmailTemplate[]> => {
  try {
    const templatesRef = collection(db, COLLECTION_NAME);
    const q = query(templatesRef, orderBy('category'), orderBy('name'));
    const snapshot = await getDocs(q);
    
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate?.()?.toISOString() || doc.data().createdAt,
      updatedAt: doc.data().updatedAt?.toDate?.()?.toISOString() || doc.data().updatedAt,
    })) as EmailTemplate[];
  } catch (error) {
    console.error('Error fetching email templates:', error);
    throw new Error('Failed to fetch email templates');
  }
};

/**
 * Get email templates by category
 */
export const getEmailTemplatesByCategory = async (category: EmailTemplateCategory): Promise<EmailTemplate[]> => {
  try {
    const templatesRef = collection(db, COLLECTION_NAME);
    const q = query(
      templatesRef,
      where('category', '==', category),
      where('isActive', '==', true),
      orderBy('name')
    );
    const snapshot = await getDocs(q);
    
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate?.()?.toISOString() || doc.data().createdAt,
      updatedAt: doc.data().updatedAt?.toDate?.()?.toISOString() || doc.data().updatedAt,
    })) as EmailTemplate[];
  } catch (error) {
    console.error('Error fetching email templates by category:', error);
    throw new Error('Failed to fetch email templates');
  }
};

/**
 * Get a single email template by ID
 */
export const getEmailTemplate = async (id: string): Promise<EmailTemplate | null> => {
  try {
    const templateRef = doc(db, COLLECTION_NAME, id);
    const snapshot = await getDoc(templateRef);
    
    if (!snapshot.exists()) {
      return null;
    }
    
    return {
      id: snapshot.id,
      ...snapshot.data(),
      createdAt: snapshot.data().createdAt?.toDate?.()?.toISOString() || snapshot.data().createdAt,
      updatedAt: snapshot.data().updatedAt?.toDate?.()?.toISOString() || snapshot.data().updatedAt,
    } as EmailTemplate;
  } catch (error) {
    console.error('Error fetching email template:', error);
    throw new Error('Failed to fetch email template');
  }
};

/**
 * Create a new email template
 */
export const createEmailTemplate = async (
  templateData: CreateEmailTemplateData,
  createdBy: string = 'admin'
): Promise<string> => {
  try {
    const now = Timestamp.now();
    const template = {
      ...templateData,
      isActive: true,
      createdAt: now,
      updatedAt: now,
      createdBy,
    };
    
    const docRef = await addDoc(collection(db, COLLECTION_NAME), template);
    return docRef.id;
  } catch (error) {
    console.error('Error creating email template:', error);
    throw new Error('Failed to create email template');
  }
};

/**
 * Update an existing email template
 */
export const updateEmailTemplate = async (templateData: UpdateEmailTemplateData): Promise<void> => {
  try {
    const { id, ...updateData } = templateData;
    const templateRef = doc(db, COLLECTION_NAME, id);
    
    await updateDoc(templateRef, {
      ...updateData,
      updatedAt: Timestamp.now(),
    });
  } catch (error) {
    console.error('Error updating email template:', error);
    throw new Error('Failed to update email template');
  }
};

/**
 * Delete an email template
 */
export const deleteEmailTemplate = async (id: string): Promise<void> => {
  try {
    const templateRef = doc(db, COLLECTION_NAME, id);
    await deleteDoc(templateRef);
  } catch (error) {
    console.error('Error deleting email template:', error);
    throw new Error('Failed to delete email template');
  }
};

/**
 * Toggle email template active status
 */
export const toggleEmailTemplateStatus = async (id: string, isActive: boolean): Promise<void> => {
  try {
    const templateRef = doc(db, COLLECTION_NAME, id);
    await updateDoc(templateRef, {
      isActive,
      updatedAt: Timestamp.now(),
    });
  } catch (error) {
    console.error('Error toggling email template status:', error);
    throw new Error('Failed to update email template status');
  }
};

/**
 * Initialize default email templates (run once)
 */
export const initializeDefaultEmailTemplates = async (): Promise<void> => {
  try {
    const existingTemplates = await getAllEmailTemplates();
    
    // Only create defaults if no templates exist
    if (existingTemplates.length === 0) {
      console.log('Creating default email templates...');
      
      for (const template of DEFAULT_EMAIL_TEMPLATES) {
        await createEmailTemplate(template, 'system');
      }
      
      console.log('Default email templates created successfully');
    }
  } catch (error) {
    console.error('Error initializing default email templates:', error);
    throw new Error('Failed to initialize default email templates');
  }
};

/**
 * Replace variables in email template
 */
export const replaceEmailVariables = (
  template: string,
  variables: Record<string, string>
): string => {
  let result = template;
  
  Object.entries(variables).forEach(([key, value]) => {
    const regex = new RegExp(key.replace(/[{}]/g, '\\$&'), 'g');
    result = result.replace(regex, value || '');
  });
  
  return result;
};

/**
 * Preview email with sample data
 */
export const previewEmailTemplate = (template: EmailTemplate): { subject: string; body: string } => {
  const sampleData: Record<string, string> = {
    '{{studentName}}': 'John Smith',
    '{{firstName}}': 'John',
    '{{lastName}}': 'Smith',
    '{{courseName}}': 'Introduction to Programming with Scratch',
    '{{coursePrice}}': '$299',
    '{{startDate}}': 'January 15, 2024',
    '{{endDate}}': 'February 19, 2024',
    '{{paymentStatus}}': 'Pending',
    '{{enrollmentStatus}}': 'Enrolled',
    '{{waitlistPosition}}': '3',
    '{{parentEmail}}': '<EMAIL>',
    '{{phone}}': '(*************',
    '{{emergencyContact}}': 'Jane Smith - (*************',
    '{{adminName}}': 'Derek MacDonald',
    '{{adminEmail}}': '<EMAIL>',
    '{{companyName}}': 'Bit by Bit',
    '{{currentDate}}': new Date().toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }),
  };
  
  return {
    subject: replaceEmailVariables(template.subject, sampleData),
    body: replaceEmailVariables(template.body, sampleData),
  };
};
