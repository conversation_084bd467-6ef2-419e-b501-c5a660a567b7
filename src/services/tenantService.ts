// AdminBuddy Tenant Management Service
import {
  collection,
  doc,
  getDoc,
  getDocs,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
} from "firebase/firestore";
import { db, COLLECTIONS } from "../config/firebase";
import { Tenant, User } from "../types";

// Create a new tenant (business)
export const createTenant = async (
  tenantData: Omit<Tenant, "id" | "createdAt" | "updatedAt">
): Promise<string> => {
  try {
    console.log("🏢 Creating new tenant:", tenantData.name);
    
    const now = new Date().toISOString();
    const docRef = await addDoc(collection(db, COLLECTIONS.TENANTS), {
      ...tenantData,
      createdAt: now,
      updatedAt: now,
    });

    console.log(`✅ Tenant created with ID: ${docRef.id}`);
    return docRef.id;
  } catch (error) {
    console.error("❌ Error creating tenant:", error);
    throw error;
  }
};

// Get tenant by ID
export const getTenant = async (tenantId: string): Promise<Tenant | null> => {
  try {
    const tenantRef = doc(db, COLLECTIONS.TENANTS, tenantId);
    const tenantDoc = await getDoc(tenantRef);
    
    if (!tenantDoc.exists()) {
      return null;
    }
    
    return { id: tenantDoc.id, ...tenantDoc.data() } as Tenant;
  } catch (error) {
    console.error("❌ Error getting tenant:", error);
    return null;
  }
};

// Update tenant
export const updateTenant = async (
  tenantId: string,
  updates: Partial<Omit<Tenant, "id" | "createdAt" | "updatedAt">>
): Promise<boolean> => {
  try {
    console.log(`📝 Updating tenant: ${tenantId}`);
    
    const tenantRef = doc(db, COLLECTIONS.TENANTS, tenantId);
    await updateDoc(tenantRef, {
      ...updates,
      updatedAt: new Date().toISOString(),
    });

    console.log(`✅ Tenant updated successfully`);
    return true;
  } catch (error) {
    console.error("❌ Error updating tenant:", error);
    return false;
  }
};

// Get all tenants (admin only)
export const getAllTenants = async (): Promise<Tenant[]> => {
  try {
    console.log("🔍 Fetching all tenants...");
    const tenantsRef = collection(db, COLLECTIONS.TENANTS);
    const q = query(tenantsRef, orderBy("createdAt", "desc"));
    const snapshot = await getDocs(q);

    const tenants = snapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as Tenant[];

    console.log(`📊 Found ${tenants.length} tenants`);
    return tenants;
  } catch (error) {
    console.error("❌ Error fetching tenants:", error);
    return [];
  }
};

// Delete tenant (admin only - careful!)
export const deleteTenant = async (tenantId: string): Promise<boolean> => {
  try {
    console.log(`🗑️ Deleting tenant: ${tenantId}`);
    
    // TODO: Add cascade delete for all tenant data
    // - Users
    // - Locations  
    // - Task Groups
    // - Tasks
    // - Completions
    // - Handoffs
    
    const tenantRef = doc(db, COLLECTIONS.TENANTS, tenantId);
    await deleteDoc(tenantRef);
    
    console.log(`✅ Tenant deleted successfully`);
    return true;
  } catch (error) {
    console.error("❌ Error deleting tenant:", error);
    return false;
  }
};

// Get tenant settings
export const getTenantSettings = async (tenantId: string) => {
  const tenant = await getTenant(tenantId);
  return tenant?.settings || {
    timezone: "America/Los_Angeles",
    dateFormat: "MM/DD/YYYY",
    timeFormat: "12h" as const,
  };
};

// Update tenant settings
export const updateTenantSettings = async (
  tenantId: string,
  settings: Tenant["settings"]
): Promise<boolean> => {
  return updateTenant(tenantId, { settings });
};
