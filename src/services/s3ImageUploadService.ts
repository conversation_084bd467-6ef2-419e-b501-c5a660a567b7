import {
  S3Client,
  PutObjectCommand,
  DeleteObjectCommand,
} from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import {
  STSClient,
  AssumeRoleWithWebIdentityCommand,
} from "@aws-sdk/client-sts";
import { auth } from "../config/firebase";

export interface S3UploadResult {
  success: boolean;
  url?: string;
  error?: string;
}

export interface S3UploadProgress {
  progress: number;
  isUploading: boolean;
}

// AWS Configuration from environment
const AWS_CONFIG = {
  region: process.env.REACT_APP_AWS_REGION || "ca-central-1",
  bucketName: process.env.REACT_APP_AWS_BUCKET_NAME || "bitbybit-course-images",
  roleArn: process.env.REACT_APP_AWS_ROLE_ARN || "",
};

/**
 * Get temporary AWS credentials using Firebase ID token
 */
async function getAWSCredentials() {
  try {
    // Get current Firebase user and ID token
    const user = auth.currentUser;
    if (!user) {
      throw new Error("User not authenticated with Firebase");
    }

    const idToken = await user.getIdToken();

    // Create STS client
    const stsClient = new STSClient({ region: AWS_CONFIG.region });

    // Assume role with web identity
    const command = new AssumeRoleWithWebIdentityCommand({
      RoleArn: AWS_CONFIG.roleArn,
      RoleSessionName: `bitbybit-admin-${Date.now()}`,
      WebIdentityToken: idToken,
    });

    const response = await stsClient.send(command);

    if (!response.Credentials) {
      throw new Error("Failed to get AWS credentials");
    }

    return {
      accessKeyId: response.Credentials.AccessKeyId!,
      secretAccessKey: response.Credentials.SecretAccessKey!,
      sessionToken: response.Credentials.SessionToken!,
    };
  } catch (error) {
    console.error("Error getting AWS credentials:", error);
    throw new Error(
      `Failed to get AWS credentials: ${
        error instanceof Error ? error.message : "Unknown error"
      }`
    );
  }
}

/**
 * Create S3 client with temporary credentials
 */
async function createS3Client() {
  const credentials = await getAWSCredentials();

  return new S3Client({
    region: AWS_CONFIG.region,
    credentials,
  });
}

/**
 * Upload an image to S3
 * @param file - The image file to upload
 * @param folder - The folder path in S3 (e.g., 'courses', 'calendar')
 * @param filename - Optional custom filename (will generate if not provided)
 * @returns Promise with upload result
 */
export const uploadImageToS3 = async (
  file: File,
  folder: string = "courses",
  filename?: string
): Promise<S3UploadResult> => {
  try {
    // Validate file type
    if (!file.type.startsWith("image/")) {
      return {
        success: false,
        error: "Please select a valid image file (JPG, PNG, GIF, WebP)",
      };
    }

    // Validate file size (5MB limit)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      return {
        success: false,
        error: "Image size must be less than 5MB",
      };
    }

    // Generate filename if not provided
    const timestamp = Date.now();
    const extension = file.name.split(".").pop()?.toLowerCase() || "jpg";
    const finalFilename = filename || `${timestamp}.${extension}`;
    const key = `${folder}/${finalFilename}`;

    console.log(`📤 Uploading image to S3: ${key}`);
    console.log(`📊 File details:`, {
      name: file.name,
      size: file.size,
      type: file.type,
    });

    // Create S3 client with temporary credentials
    const s3Client = await createS3Client();

    // Convert file to ArrayBuffer for browser compatibility
    console.log("🔄 Converting file to buffer...");
    const fileBuffer = await file.arrayBuffer();
    const uint8Array = new Uint8Array(fileBuffer);
    console.log(`✅ File converted to buffer: ${uint8Array.length} bytes`);

    // Upload file to S3
    console.log("📤 Starting S3 upload...");
    const command = new PutObjectCommand({
      Bucket: AWS_CONFIG.bucketName,
      Key: key,
      Body: uint8Array,
      ContentType: file.type,
      CacheControl: "max-age=31536000", // 1 year cache
    });

    const uploadResult = await s3Client.send(command);
    console.log("✅ S3 upload completed:", uploadResult.ETag);

    // Construct public URL
    const publicUrl = `https://${AWS_CONFIG.bucketName}.s3.${AWS_CONFIG.region}.amazonaws.com/${key}`;

    console.log("✅ Image uploaded successfully:", publicUrl);

    return {
      success: true,
      url: publicUrl,
    };
  } catch (error) {
    console.error("❌ S3 upload failed:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Upload failed",
    };
  }
};

/**
 * Delete an image from S3
 * @param imageUrl - The full URL of the image to delete
 * @returns Promise with deletion result
 */
export const deleteImageFromS3 = async (imageUrl: string): Promise<boolean> => {
  try {
    // Extract the key from the URL
    const url = new URL(imageUrl);
    const key = url.pathname.substring(1); // Remove leading slash

    if (!key) {
      console.warn("Could not extract key from URL:", imageUrl);
      return false;
    }

    console.log(`🗑️ Deleting image from S3: ${key}`);

    // Create S3 client with temporary credentials
    const s3Client = await createS3Client();

    // Delete object from S3
    const command = new DeleteObjectCommand({
      Bucket: AWS_CONFIG.bucketName,
      Key: key,
    });

    await s3Client.send(command);
    console.log("✅ Image deleted successfully:", key);

    return true;
  } catch (error) {
    console.error("❌ S3 deletion failed:", error);
    return false;
  }
};

/**
 * Validate image file before upload
 * @param file - The file to validate
 * @returns Validation result with error message if invalid
 */
export const validateImageFile = (
  file: File
): { valid: boolean; error?: string } => {
  // Check file type
  if (!file.type.startsWith("image/")) {
    return {
      valid: false,
      error: "Please select a valid image file (JPG, PNG, GIF, WebP)",
    };
  }

  // Check file size (5MB limit)
  const maxSize = 5 * 1024 * 1024; // 5MB
  if (file.size > maxSize) {
    return {
      valid: false,
      error: "Image size must be less than 5MB",
    };
  }

  return { valid: true };
};

/**
 * Generate a storage-friendly filename from a course title
 * @param title - The course title
 * @param extension - File extension (default: 'jpg')
 * @returns Clean filename suitable for storage
 */
export const generateCourseImageFilename = (
  title: string,
  extension: string = "jpg"
): string => {
  const timestamp = Date.now();
  const cleanTitle = title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, "") // Remove special characters
    .replace(/\s+/g, "-") // Replace spaces with hyphens
    .replace(/-+/g, "-") // Replace multiple hyphens with single
    .trim();

  return `${cleanTitle}-${timestamp}.${extension}`;
};

/**
 * Storage folder constants
 */
export const S3_FOLDERS = {
  COURSES: "courses",
  CALENDAR: "calendar",
  GENERAL: "general",
} as const;

/**
 * Test S3 connection and permissions
 */
export const testS3Connection = async (): Promise<{
  success: boolean;
  error?: string;
}> => {
  try {
    console.log("🔍 Testing S3 connection...");

    // Try to get AWS credentials
    await getAWSCredentials();
    console.log("✅ AWS credentials obtained successfully");

    // Try to create S3 client
    await createS3Client();
    console.log("✅ S3 client created successfully");

    return { success: true };
  } catch (error) {
    console.error("❌ S3 connection test failed:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Connection test failed",
    };
  }
};
