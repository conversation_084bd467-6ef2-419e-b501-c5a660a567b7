import {
  collection,
  doc,
  getDocs,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  orderBy,
  where,
  Timestamp,
} from "firebase/firestore";
import { db, COLLECTIONS } from "../config/firebase";

const CATEGORIES_COLLECTION = "categories";

export interface Category {
  id: string;
  name: string;
  type: "subject" | "difficulty";
  description?: string;
  createdAt: string;
  updatedAt: string;
}

// No default categories - completely clean slate

export const getAllCategories = async (): Promise<Category[]> => {
  try {
    console.log("🔍 Fetching all categories...");
    const categoriesRef = collection(db, CATEGORIES_COLLECTION);
    const q = query(categoriesRef, orderBy("type"), orderBy("name"));
    const snapshot = await getDocs(q);

    const categories = snapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as Category[];

    console.log(`📚 Found ${categories.length} categories`);
    return categories;
  } catch (error) {
    console.error("Error fetching categories:", error);
    return [];
  }
};

export const createCategory = async (
  categoryData: Omit<Category, "id" | "createdAt" | "updatedAt">
): Promise<string> => {
  try {
    console.log("📝 Creating new category:", categoryData.name);

    // Check if category already exists
    const categoriesRef = collection(db, CATEGORIES_COLLECTION);
    const existingQuery = query(
      categoriesRef,
      where("name", "==", categoryData.name)
    );
    const existingSnapshot = await getDocs(existingQuery);

    if (!existingSnapshot.empty) {
      throw new Error(`Category "${categoryData.name}" already exists`);
    }

    const now = new Date().toISOString();
    const docRef = await addDoc(categoriesRef, {
      ...categoryData,
      createdAt: now,
      updatedAt: now,
    });

    console.log(`✅ Category created with ID: ${docRef.id}`);
    return docRef.id;
  } catch (error) {
    console.error("Error creating category:", error);
    throw error;
  }
};

export const updateCategory = async (
  categoryId: string,
  updates: Partial<Omit<Category, "id" | "createdAt" | "updatedAt">>
): Promise<boolean> => {
  try {
    console.log(`📝 Updating category: ${categoryId}`);

    // If updating name, check for duplicates
    if (updates.name) {
      const categoriesRef = collection(db, CATEGORIES_COLLECTION);
      const existingQuery = query(
        categoriesRef,
        where("name", "==", updates.name)
      );
      const existingSnapshot = await getDocs(existingQuery);
      const existingDoc = existingSnapshot.docs.find(
        (doc) => doc.id !== categoryId
      );

      if (existingDoc) {
        throw new Error(`Category "${updates.name}" already exists`);
      }
    }

    const now = new Date().toISOString();
    await updateDoc(doc(db, CATEGORIES_COLLECTION, categoryId), {
      ...updates,
      updatedAt: now,
    });

    console.log(`✅ Category updated successfully`);
    return true;
  } catch (error) {
    console.error("Error updating category:", error);
    throw error;
  }
};

export const deleteCategory = async (categoryId: string): Promise<boolean> => {
  try {
    console.log(`🗑️ Deleting category: ${categoryId}`);

    await deleteDoc(doc(db, CATEGORIES_COLLECTION, categoryId));
    console.log(`✅ Category deleted successfully`);
    return true;
  } catch (error) {
    console.error("Error deleting category:", error);
    throw error;
  }
};

export const getCategoriesByType = async (
  type: "subject" | "difficulty"
): Promise<Category[]> => {
  try {
    const categoriesRef = collection(db, CATEGORIES_COLLECTION);
    const q = query(categoriesRef, where("type", "==", type), orderBy("name"));
    const snapshot = await getDocs(q);

    return snapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as Category[];
  } catch (error) {
    console.error(`Error fetching ${type} categories:`, error);
    return [];
  }
};
