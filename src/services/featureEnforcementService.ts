// AdminBuddy Feature Enforcement Service
import { UserProfile } from "./adminBuddyFirebaseService";
import { getTrialStatus, canAccessFeatures } from "./trialService";
import { getSubscriptionSummary } from "./subscriptionService";

export interface FeatureAccess {
  canCreateLocations: boolean;
  canCreateTasks: boolean;
  canGenerateTasks: boolean;
  canViewReports: boolean;
  canAccessKiosk: boolean;
  canManageUsers: boolean;
  maxLocations: number;
  reason?: string;
}

/**
 * Check what features a user can access based on trial/subscription status
 */
export const checkFeatureAccess = async (
  userProfile: UserProfile | null,
  currentLocationCount: number = 0
): Promise<FeatureAccess> => {
  if (!userProfile) {
    return {
      canCreateLocations: false,
      canCreateTasks: false,
      canGenerateTasks: false,
      canViewReports: false,
      canAccessKiosk: false,
      canManageUsers: false,
      maxLocations: 0,
      reason: "No user profile found",
    };
  }

  const trialStatus = getTrialStatus(userProfile);
  const hasAccess = canAccessFeatures(userProfile);

  // If trial expired and no subscription, block everything except billing
  if (!hasAccess) {
    return {
      canCreateLocations: false,
      canCreateTasks: false,
      canGenerateTasks: false,
      canViewReports: false,
      canAccessKiosk: false,
      canManageUsers: false,
      maxLocations: 0,
      reason: "Trial expired. Please subscribe to continue using AdminBuddy.",
    };
  }

  // If user has active subscription, check subscription limits
  if (trialStatus.hasActiveSubscription) {
    try {
      const subscriptionSummary = await getSubscriptionSummary(
        userProfile.tenantId
      );
      const maxLocations = subscriptionSummary.locationCount || 1;

      return {
        canCreateLocations: currentLocationCount < maxLocations,
        canCreateTasks: true,
        canGenerateTasks: true,
        canViewReports: true,
        canAccessKiosk: true,
        canManageUsers: true,
        maxLocations,
        reason:
          currentLocationCount >= maxLocations
            ? `Subscription allows ${maxLocations} location${
                maxLocations !== 1 ? "s" : ""
              }. Upgrade to add more.`
            : undefined,
      };
    } catch (error) {
      console.error("Error checking subscription limits:", error);
      // Fallback to allowing basic access
      return {
        canCreateLocations: true,
        canCreateTasks: true,
        canGenerateTasks: true,
        canViewReports: true,
        canAccessKiosk: true,
        canManageUsers: true,
        maxLocations: 1,
      };
    }
  }

  // User is in trial - limit to 1 location, allow other features
  const trialLocationLimit = 1;
  return {
    canCreateLocations: currentLocationCount < trialLocationLimit,
    canCreateTasks: true,
    canGenerateTasks: true,
    canViewReports: true,
    canAccessKiosk: true,
    canManageUsers: true,
    maxLocations: trialLocationLimit,
    reason:
      currentLocationCount >= trialLocationLimit
        ? `Trial accounts are limited to ${trialLocationLimit} location. Subscribe to add more locations.`
        : trialStatus.daysRemaining <= 3
        ? `Trial expires in ${trialStatus.daysRemaining} day${
            trialStatus.daysRemaining !== 1 ? "s" : ""
          }. Subscribe to continue.`
        : undefined,
  };
};

/**
 * Check if user can create a new location
 */
export const canCreateLocation = async (
  userProfile: UserProfile | null,
  currentLocationCount: number
): Promise<{ allowed: boolean; reason?: string }> => {
  const access = await checkFeatureAccess(userProfile, currentLocationCount);

  return {
    allowed: access.canCreateLocations,
    reason: access.reason,
  };
};

/**
 * Redirect to billing if trial expired
 */
export const enforceTrialAccess = (
  userProfile: UserProfile | null
): boolean => {
  if (!userProfile) {
    return false;
  }

  const hasAccess = canAccessFeatures(userProfile);

  if (!hasAccess) {
    // Store current URL for redirect after subscription
    const currentUrl = window.location.pathname + window.location.search;
    localStorage.setItem("adminbuddy_redirect_after_subscription", currentUrl);

    // Redirect to billing
    window.location.href = "/billing";
    return false;
  }

  return true;
};

/**
 * Show feature blocked modal/message
 */
export const showFeatureBlockedMessage = (reason: string): void => {
  alert(`Feature Unavailable\n\n${reason}\n\nClick OK to go to billing page.`);
  window.location.href = "/billing";
};

/**
 * Get user-friendly access message for UI
 */
export const getAccessMessage = async (
  userProfile: UserProfile | null,
  currentLocationCount: number = 0
): Promise<string | null> => {
  const access = await checkFeatureAccess(userProfile, currentLocationCount);
  return access.reason || null;
};
