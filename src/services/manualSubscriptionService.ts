// Manual Subscription Update Service for Testing
import {
  getDocument,
  updateDocument,
  createDocument,
  queryDocuments,
} from "./firestoreRestApi";

interface ManualSubscriptionData {
  tenantId: string;
  subscriptionId?: string;
  locationCount?: number;
  status?: "active" | "canceled" | "past_due" | "unpaid" | "incomplete";
}

/**
 * Manually update subscription status for testing purposes
 * This bypasses Stripe webhooks and directly updates the database
 */
export const manualUpdateSubscription = async (
  data: ManualSubscriptionData
) => {
  try {
    console.log("🔧 Manually updating subscription:", data);

    const {
      tenantId,
      locationCount = 3,
      status = "active",
      subscriptionId = `manual_${Date.now()}`,
    } = data;

    // Update tenant document
    await updateTenantSubscription(tenantId, {
      subscriptionId,
      status,
      locationCount,
    });

    // Update user profiles for this tenant
    await updateUserSubscriptionStatus(tenantId, status === "active");

    console.log("✅ Manual subscription update successful");
    return {
      success: true,
      tenantId,
      status,
      locationCount,
    };
  } catch (error) {
    console.error("❌ Manual subscription update failed:", error);
    throw error;
  }
};

/**
 * Update tenant subscription data directly in Firestore using REST API
 */
const updateTenantSubscription = async (
  tenantId: string,
  data: { subscriptionId: string; status: string; locationCount: number }
) => {
  try {
    const updateData = {
      subscriptionId: data.subscriptionId,
      subscriptionStatus: data.status,
      subscriptionCurrentPeriodEnd: new Date(
        Date.now() + 30 * 24 * 60 * 60 * 1000
      ).toISOString(), // 30 days from now
      subscriptionLocationCount: data.locationCount,
      lastPaymentDate:
        data.status === "active" ? new Date().toISOString() : undefined,
      updatedAt: new Date().toISOString(),
    };

    // Remove undefined values
    Object.keys(updateData).forEach((key) => {
      if (updateData[key as keyof typeof updateData] === undefined) {
        delete updateData[key as keyof typeof updateData];
      }
    });

    try {
      // Try to get existing tenant document
      const existingTenant = await getDocument("tenants", tenantId);

      if (existingTenant) {
        // Update existing tenant
        await updateDocument("tenants", tenantId, updateData);
        console.log("✅ Tenant subscription updated");
      } else {
        throw new Error("Tenant not found");
      }
    } catch (error) {
      // If tenant doesn't exist, create it
      console.log("Tenant doesn't exist, creating new tenant...");

      const newTenantData = {
        id: tenantId,
        name: "New Business",
        plan: "basic",
        isActive: true,
        createdAt: new Date().toISOString(),
        settings: {
          timezone: "America/Toronto",
          dateFormat: "MM/DD/YYYY",
          timeFormat: "12h" as const,
        },
        ...updateData,
      };

      await createDocument("tenants", newTenantData, tenantId);
      console.log("✅ Tenant created with subscription data");
    }
  } catch (error) {
    console.error("❌ Error updating tenant subscription:", error);
    throw error;
  }
};

/**
 * Update user profiles subscription status using REST API
 */
const updateUserSubscriptionStatus = async (
  tenantId: string,
  hasActiveSubscription: boolean
) => {
  try {
    // Find all user profiles for this tenant
    const userProfiles = await queryDocuments("user_profiles", [
      { field: "tenantId", operator: "==", value: tenantId },
    ]);

    if (!userProfiles || userProfiles.length === 0) {
      console.warn("No user profiles found for tenant:", tenantId);
      return;
    }

    // Update all user profiles
    const updateData = {
      hasActiveSubscription,
      lastLoginAt: new Date().toISOString(),
    };

    // Update each user profile individually (REST API doesn't have batch operations)
    const updatePromises = userProfiles.map(async (profile: any) => {
      if (profile.id) {
        return updateDocument("user_profiles", profile.id, updateData);
      }
    });

    await Promise.all(updatePromises);
    console.log(`✅ Updated ${userProfiles.length} user profiles`);
  } catch (error) {
    console.error("❌ Error updating user subscription status:", error);
    throw error;
  }
};

/**
 * Fix corrupted user profile by restoring essential fields
 */
export const fixUserProfile = async (
  userId: string,
  email: string,
  tenantId: string
) => {
  try {
    console.log("🔧 Fixing corrupted user profile...");

    const fixData = {
      email,
      tenantId,
      role: "owner",
      displayName: email.split("@")[0],
      uid: userId,
      createdAt: new Date().toISOString(),
      trialStartDate: new Date().toISOString(),
      trialEndDate: new Date(
        Date.now() + 14 * 24 * 60 * 60 * 1000
      ).toISOString(), // 14 days from now
      hasActiveSubscription: true, // Keep the subscription active
      lastLoginAt: new Date().toISOString(),
    };

    await updateDocument("user_profiles", userId, fixData);
    console.log("✅ User profile fixed successfully");

    return fixData;
  } catch (error) {
    console.error("❌ Error fixing user profile:", error);
    throw error;
  }
};

/**
 * Quick function to activate subscription for current user
 */
export const activateSubscriptionForTenant = async (
  tenantId: string,
  locationCount: number = 3
) => {
  return manualUpdateSubscription({
    tenantId,
    locationCount,
    status: "active",
    subscriptionId: `manual_${Date.now()}`,
  });
};

/**
 * Quick function to cancel subscription for current user
 */
export const cancelSubscriptionForTenant = async (tenantId: string) => {
  return manualUpdateSubscription({
    tenantId,
    locationCount: 1,
    status: "canceled",
  });
};
