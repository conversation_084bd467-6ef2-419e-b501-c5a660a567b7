import {
  createLocation,
  createRoutine,
  createRoutineTask,
  createLocationRoutineSchedule,
  createTenantRole,
} from "./adminBuddyFirebaseService";

interface SampleDataOptions {
  tenantId: string;
  businessType?: "coffee_shop" | "restaurant" | "retail" | "office";
}

export const createSampleData = async ({
  tenantId,
  businessType = "coffee_shop",
}: SampleDataOptions) => {
  console.log(`🏗️ Creating sample data for ${businessType}...`);

  try {
    const sampleData = getSampleDataTemplate(businessType);
    const createdData: any = {
      locations: [],
      roles: [],
      routines: [],
      routineTasks: [],
      schedules: [],
    };

    // Step 1: Create locations
    console.log("📍 Creating sample locations...");
    for (const locationData of sampleData.locations) {
      const location = await createLocation({
        ...locationData,
        tenantId,
      });
      createdData.locations.push(location);
      console.log(`✅ Created location: ${locationData.name}`);
    }

    // Step 2: Create employee roles
    console.log("👥 Creating sample employee roles...");
    for (const roleData of sampleData.roles) {
      const role = await createTenantRole({
        ...roleData,
        tenantId,
      });
      createdData.roles.push(role);
      console.log(`✅ Created role: ${roleData.name}`);
    }

    // Step 3: Create routines
    console.log("🔄 Creating sample routines...");
    for (const routineData of sampleData.routines) {
      const routine = await createRoutine({
        ...routineData,
        tenantId,
      });
      createdData.routines.push(routine);
      console.log(`✅ Created routine: ${routineData.name}`);
    }

    // Step 4: Create routine tasks
    console.log("📋 Creating sample routine tasks...");
    for (const taskData of sampleData.routineTasks) {
      // Find the routine ID by name
      const routine = createdData.routines.find(
        (r: any) => r.name === taskData.routineName
      );
      if (routine) {
        const { routineName, ...taskDataWithoutRoutineName } = taskData;
        const task = await createRoutineTask({
          ...taskDataWithoutRoutineName,
          routineId: routine.id,
          tenantId,
        });
        createdData.routineTasks.push(task);
        console.log(`✅ Created task: ${taskData.title}`);
      }
    }

    // Step 5: Create schedules
    console.log("📅 Creating sample schedules...");
    for (const scheduleData of sampleData.schedules) {
      // Find the routine and location IDs by name
      const routine = createdData.routines.find(
        (r: any) => r.name === scheduleData.routineName
      );
      const location = createdData.locations.find(
        (l: any) => l.name === scheduleData.locationName
      );

      if (routine && location) {
        const { routineName, locationName, ...scheduleDataWithoutNames } =
          scheduleData;
        const schedule = await createLocationRoutineSchedule({
          ...scheduleDataWithoutNames,
          routineId: routine.id,
          locationId: location.id,
          tenantId,
        });
        createdData.schedules.push(schedule);
        console.log(`✅ Created schedule: ${routine.name} at ${location.name}`);
      }
    }

    console.log("🎉 Sample data creation completed successfully!");
    return createdData;
  } catch (error) {
    console.error("❌ Error creating sample data:", error);
    throw error;
  }
};

const getSampleDataTemplate = (businessType: string) => {
  switch (businessType) {
    case "coffee_shop":
      return getCoffeeShopSampleData();
    case "restaurant":
      return getRestaurantSampleData();
    case "retail":
      return getRetailSampleData();
    case "office":
      return getOfficeSampleData();
    default:
      return getCoffeeShopSampleData();
  }
};

const getCoffeeShopSampleData = () => ({
  locations: [
    {
      name: "Downtown Coffee Shop",
      address: "123 Main Street, Downtown",
      description: "Our flagship location in the heart of downtown",
    },
  ],
  roles: [
    {
      name: "Manager",
      description: "Store manager responsible for operations",
      color: "#0a2d69",
      permissions: [],
    },
    {
      name: "Barista",
      description: "Coffee preparation and customer service",
      color: "#10b981",
      permissions: [],
    },
    {
      name: "Cashier",
      description: "Point of sale and customer transactions",
      color: "#f59e0b",
      permissions: [],
    },
    {
      name: "Cleaner",
      description: "Cleaning and maintenance tasks",
      color: "#8b5cf6",
      permissions: [],
    },
  ],
  routines: [
    {
      name: "Opening Store",
      description: "Tasks to complete when opening the store each morning",
      color: "#10b981",
    },
    {
      name: "Closing Store",
      description: "End-of-day tasks to secure and clean the store",
      color: "#ef4444",
    },
    {
      name: "Weekly Deep Clean",
      description: "Comprehensive cleaning tasks performed weekly",
      color: "#8b5cf6",
    },
  ],
  routineTasks: [
    // Opening Store Tasks
    {
      routineName: "Opening Store",
      title: "Turn on equipment",
      description: "Start espresso machine, grinders, and other equipment",
      priority: "high" as const,
      estimatedMinutes: 10,
      roleId: null,
      expiryType: "no_expiry" as const,
    },
    {
      routineName: "Opening Store",
      title: "Check inventory levels",
      description: "Verify coffee beans, milk, and supplies are stocked",
      priority: "high" as const,
      estimatedMinutes: 5,
      roleId: null,
      expiryType: "no_expiry" as const,
    },
    {
      routineName: "Opening Store",
      title: "Set up cash register",
      description: "Count starting cash and prepare POS system",
      priority: "high" as const,
      estimatedMinutes: 5,
      roleId: null,
      expiryType: "no_expiry" as const,
    },
    {
      routineName: "Opening Store",
      title: "Clean and sanitize surfaces",
      description: "Wipe down counters, tables, and customer areas",
      priority: "medium" as const,
      estimatedMinutes: 15,
      roleId: null,
      expiryType: "no_expiry" as const,
    },
    {
      routineName: "Opening Store",
      title: "Unlock doors and flip sign",
      description: "Open for business and turn on 'Open' sign",
      priority: "high" as const,
      estimatedMinutes: 2,
      roleId: null,
      expiryType: "no_expiry" as const,
    },

    // Closing Store Tasks
    {
      routineName: "Closing Store",
      title: "Lock doors and flip sign",
      description: "Secure entrance and turn off 'Open' sign",
      priority: "high" as const,
      estimatedMinutes: 2,
      roleId: null,
      expiryType: "no_expiry" as const,
    },
    {
      routineName: "Closing Store",
      title: "Count cash register",
      description: "Complete cash count and prepare deposit",
      priority: "high" as const,
      estimatedMinutes: 15,
      roleId: null,
      expiryType: "no_expiry" as const,
    },
    {
      routineName: "Closing Store",
      title: "Clean equipment",
      description: "Clean espresso machine, grinders, and brewing equipment",
      priority: "high" as const,
      estimatedMinutes: 20,
      roleId: null,
      expiryType: "no_expiry" as const,
    },
    {
      routineName: "Closing Store",
      title: "Sanitize all surfaces",
      description: "Deep clean counters, tables, and customer seating",
      priority: "medium" as const,
      estimatedMinutes: 25,
      roleId: null,
      expiryType: "no_expiry" as const,
    },
    {
      routineName: "Closing Store",
      title: "Take out trash",
      description: "Empty all trash bins and take to dumpster",
      priority: "medium" as const,
      estimatedMinutes: 10,
      roleId: null,
      expiryType: "no_expiry" as const,
    },
    {
      routineName: "Closing Store",
      title: "Turn off equipment",
      description: "Safely shut down all equipment and lights",
      priority: "high" as const,
      estimatedMinutes: 5,
      roleId: null,
      expiryType: "no_expiry" as const,
    },

    // Weekly Deep Clean Tasks
    {
      routineName: "Weekly Deep Clean",
      title: "Deep clean espresso machine",
      description: "Descale and thoroughly clean espresso machine",
      priority: "high" as const,
      estimatedMinutes: 45,
      roleId: null,
      expiryType: "next_report" as const,
    },
    {
      routineName: "Weekly Deep Clean",
      title: "Clean grinder burrs",
      description: "Remove and clean coffee grinder burrs",
      priority: "high" as const,
      estimatedMinutes: 30,
      roleId: null,
      expiryType: "next_report" as const,
    },
    {
      routineName: "Weekly Deep Clean",
      title: "Mop floors thoroughly",
      description: "Deep mop all floor areas including behind equipment",
      priority: "medium" as const,
      estimatedMinutes: 30,
      roleId: null,
      expiryType: "next_report" as const,
    },
    {
      routineName: "Weekly Deep Clean",
      title: "Clean windows and mirrors",
      description: "Clean all glass surfaces inside and outside",
      priority: "low" as const,
      estimatedMinutes: 20,
      roleId: null,
      expiryType: "next_report" as const,
    },
    {
      routineName: "Weekly Deep Clean",
      title: "Organize storage areas",
      description: "Tidy and organize back-of-house storage",
      priority: "low" as const,
      estimatedMinutes: 25,
      roleId: null,
      expiryType: "next_report" as const,
    },
  ],
  schedules: [
    {
      routineName: "Opening Store",
      locationName: "Downtown Coffee Shop",
      cadence: "daily" as const,
      visibleFromTime: "07:00",
      daysOfWeek: [1, 2, 3, 4, 5, 6, 0], // Monday through Sunday
    },
    {
      routineName: "Closing Store",
      locationName: "Downtown Coffee Shop",
      cadence: "daily" as const,
      visibleFromTime: "20:00",
      daysOfWeek: [1, 2, 3, 4, 5, 6, 0], // Monday through Sunday
    },
    {
      routineName: "Weekly Deep Clean",
      locationName: "Downtown Coffee Shop",
      cadence: "weekly" as const,
      visibleFromTime: "19:00",
      daysOfWeek: [0], // Sunday only
    },
  ],
});

// Additional business type templates can be added here
const getRestaurantSampleData = () => {
  // TODO: Implement restaurant sample data
  return getCoffeeShopSampleData(); // Fallback for now
};

const getRetailSampleData = () => {
  // TODO: Implement retail sample data
  return getCoffeeShopSampleData(); // Fallback for now
};

const getOfficeSampleData = () => {
  // TODO: Implement office sample data
  return getCoffeeShopSampleData(); // Fallback for now
};
