// Instructor management service for admin
import {
  collection,
  doc,
  setDoc,
  getDoc,
  getDocs,
  query,
  orderBy,
  updateDoc,
  deleteDoc,
  where,
} from "firebase/firestore";
import {
  createUserWithEmailAndPassword,
  deleteUser,
  signInWithEmailAndPassword,
  sendPasswordResetEmail,
  fetchSignInMethodsForEmail,
  sendSignInLinkToEmail,
} from "firebase/auth";
import { auth, db } from "../config/firebase";
import { getAdminSession } from "./adminService";

export interface InstructorAccount {
  id: string;
  email: string;
  name: string;
  phone?: string;
  assignedCourses: string[]; // Course IDs
  status: "active" | "inactive" | "pending" | "invited";
  createdAt: string;
  updatedAt: string;
  createdBy: string; // Admin who created the account
  invitedAt?: string; // When invitation was sent
  activatedAt?: string; // When instructor completed signup
}

export interface CreateInstructorData {
  email: string;
  name: string;
  phone?: string;
  assignedCourses: string[];
}

const COLLECTIONS = {
  INSTRUCTORS: "instructors",
};

// Send instructor invitation
export const sendInstructorInvitation = async (
  instructorData: CreateInstructorData,
  adminEmail: string
): Promise<{ success: boolean; error?: string; instructorId?: string }> => {
  try {
    console.log("📧 Sending instructor invitation to:", instructorData.email);

    // Check if email is already in use
    const signInMethods = await fetchSignInMethodsForEmail(
      auth,
      instructorData.email
    );
    if (signInMethods.length > 0) {
      return {
        success: false,
        error: "An account with this email already exists",
      };
    }

    // Create instructor document with "invited" status
    // Use a temporary ID that will be replaced when they accept the invitation
    const instructorId = `instructor_${Date.now()}_${Math.random()
      .toString(36)
      .substr(2, 9)}`;
    const now = new Date().toISOString();

    const instructorDoc: InstructorAccount = {
      id: instructorId,
      email: instructorData.email,
      name: instructorData.name,
      phone: instructorData.phone,
      assignedCourses: instructorData.assignedCourses,
      status: "invited",
      createdAt: now,
      updatedAt: now,
      createdBy: adminEmail,
      invitedAt: now,
    };

    console.log("📝 Creating instructor invitation record...");
    await setDoc(doc(db, COLLECTIONS.INSTRUCTORS, instructorId), instructorDoc);

    // For now, we'll just create the invitation record and show a success message
    // In production, you would integrate with an email service like SendGrid, Mailgun, etc.
    console.log(
      "📧 Invitation record created. In production, send email to:",
      instructorData.email
    );
    console.log(
      "🔗 Setup link:",
      `${window.location.origin}/instructor/setup?instructorId=${instructorId}`
    );

    console.log("✅ Instructor invitation sent successfully");
    return { success: true, instructorId };
  } catch (error: any) {
    console.error("❌ Error sending instructor invitation:", error);

    let errorMessage = "Failed to send instructor invitation";
    switch (error.code) {
      case "auth/email-already-in-use":
        errorMessage = "An account with this email already exists";
        break;
      case "auth/invalid-email":
        errorMessage = "Invalid email address";
        break;
      case "permission-denied":
        errorMessage = "Missing or insufficient permissions.";
        break;
      default:
        errorMessage = error.message || "Failed to send instructor invitation";
    }

    return { success: false, error: errorMessage };
  }
};

// Activate instructor account after they set their password
export const activateInstructorAccount = async (
  instructorId: string
): Promise<boolean> => {
  try {
    console.log("✅ Activating instructor account:", instructorId);

    const instructorRef = doc(db, COLLECTIONS.INSTRUCTORS, instructorId);
    await updateDoc(instructorRef, {
      status: "active",
      activatedAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    });

    console.log("✅ Instructor account activated successfully");
    return true;
  } catch (error) {
    console.error("❌ Error activating instructor account:", error);
    return false;
  }
};

// Resend invitation to instructor
export const resendInstructorInvitation = async (
  instructorEmail: string
): Promise<{ success: boolean; error?: string }> => {
  try {
    console.log("📧 Resending invitation to:", instructorEmail);

    // Find the instructor record
    const instructorsRef = collection(db, COLLECTIONS.INSTRUCTORS);
    const q = query(instructorsRef, where("email", "==", instructorEmail));
    const snapshot = await getDocs(q);

    if (snapshot.empty) {
      return { success: false, error: "Instructor not found" };
    }

    const instructorDoc = snapshot.docs[0];
    const instructorData = instructorDoc.data();

    // For now, just log the resend action
    // In production, you would integrate with an email service
    console.log("📧 Resending invitation to:", instructorEmail);
    console.log(
      "🔗 Setup link:",
      `${window.location.origin}/instructor/setup?instructorId=${instructorDoc.id}`
    );

    // Update the invitation timestamp
    await updateDoc(instructorDoc.ref, {
      invitedAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    });

    console.log("✅ Invitation resent successfully");
    return { success: true };
  } catch (error: any) {
    console.error("❌ Error resending invitation:", error);
    return {
      success: false,
      error: error.message || "Failed to resend invitation",
    };
  }
};

// Legacy function name for backward compatibility
export const createInstructorAccount = sendInstructorInvitation;

// Get all instructor accounts
export const getAllInstructors = async (): Promise<InstructorAccount[]> => {
  try {
    console.log("📋 Fetching all instructors...");
    const instructorsRef = collection(db, COLLECTIONS.INSTRUCTORS);
    const q = query(instructorsRef, orderBy("createdAt", "desc"));
    const snapshot = await getDocs(q);

    const instructors = snapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as InstructorAccount[];

    console.log(`👥 Found ${instructors.length} instructors`);
    return instructors;
  } catch (error) {
    console.error("Error getting instructors:", error);
    return [];
  }
};

// Get instructor by ID
export const getInstructorById = async (
  instructorId: string
): Promise<InstructorAccount | null> => {
  try {
    const instructorRef = doc(db, COLLECTIONS.INSTRUCTORS, instructorId);
    const instructorDoc = await getDoc(instructorRef);

    if (instructorDoc.exists()) {
      return {
        id: instructorDoc.id,
        ...instructorDoc.data(),
      } as InstructorAccount;
    }
    return null;
  } catch (error) {
    console.error("Error getting instructor:", error);
    return null;
  }
};

// Update instructor account
export const updateInstructorAccount = async (
  instructorId: string,
  updateData: Partial<Omit<InstructorAccount, "id" | "createdAt" | "createdBy">>
): Promise<boolean> => {
  try {
    console.log(`📝 Updating instructor ${instructorId}...`);
    const instructorRef = doc(db, COLLECTIONS.INSTRUCTORS, instructorId);

    const updatedData = {
      ...updateData,
      updatedAt: new Date().toISOString(),
    };

    await updateDoc(instructorRef, updatedData);
    console.log("✅ Instructor updated successfully");
    return true;
  } catch (error) {
    console.error("Error updating instructor:", error);
    return false;
  }
};

// Delete instructor account
export const deleteInstructorAccount = async (
  instructorId: string
): Promise<boolean> => {
  try {
    console.log(`🗑️ Deleting instructor ${instructorId}...`);

    // Delete from Firestore
    await deleteDoc(doc(db, COLLECTIONS.INSTRUCTORS, instructorId));

    // Note: We don't delete the Firebase Auth user here because we can't do that
    // from the client side. In production, this would be done via Cloud Functions
    // or Admin SDK on the server side.

    console.log("✅ Instructor deleted successfully");
    return true;
  } catch (error) {
    console.error("Error deleting instructor:", error);
    return false;
  }
};

// Get instructor by email (for login validation)
export const getInstructorByEmail = async (
  email: string
): Promise<InstructorAccount | null> => {
  try {
    const instructorsRef = collection(db, COLLECTIONS.INSTRUCTORS);
    const q = query(instructorsRef, where("email", "==", email));
    const snapshot = await getDocs(q);

    if (!snapshot.empty) {
      const doc = snapshot.docs[0];
      return {
        id: doc.id,
        ...doc.data(),
      } as InstructorAccount;
    }
    return null;
  } catch (error) {
    console.error("Error getting instructor by email:", error);
    return null;
  }
};

// Assign courses to instructor
export const assignCoursesToInstructor = async (
  instructorId: string,
  courseIds: string[]
): Promise<boolean> => {
  try {
    console.log(`📚 Assigning courses to instructor ${instructorId}...`);
    return await updateInstructorAccount(instructorId, {
      assignedCourses: courseIds,
    });
  } catch (error) {
    console.error("Error assigning courses:", error);
    return false;
  }
};

// Get instructors for a specific course
export const getInstructorsForCourse = async (
  courseId: string
): Promise<InstructorAccount[]> => {
  try {
    const instructorsRef = collection(db, COLLECTIONS.INSTRUCTORS);
    const q = query(
      instructorsRef,
      where("assignedCourses", "array-contains", courseId)
    );
    const snapshot = await getDocs(q);

    return snapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as InstructorAccount[];
  } catch (error) {
    console.error("Error getting instructors for course:", error);
    return [];
  }
};
