// Firebase-based course capacity management service
import {
  collection,
  doc,
  getDoc,
  getDocs,
  setDoc,
  updateDoc,
  deleteDoc,
  query,
  orderBy,
  where,
  onSnapshot,
  Timestamp,
  addDoc,
  writeBatch,
} from "firebase/firestore";
import { db, COLLECTIONS } from "../config/firebase";
import courseData from "../data/courseData";

export interface CourseRegistration {
  id?: string; // Firebase document ID (added when retrieved from Firestore)
  registrationId: string;
  courseId: string;
  courseTitle: string;
  coursePrice: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  experienceLevel: string;
  specialRequirements: string;
  emergencyContactName: string;
  emergencyContactPhone: string;
  emergencyContactRelationship: string;
  registrationDate: string;
  timestamp: Timestamp;
  paymentStatus: "pending" | "paid" | "refunded" | "late" | "cancelled";
  paymentDate?: string;
  paymentNotes?: string;
  enrollmentStatus: "enrolled" | "waitlist";
  instructorNotes?: string;
  courseCompleted?: boolean;
  completionDate?: string;
}

export interface CourseCapacity {
  courseId: string;
  courseTitle: string;
  maxStudents: number;
  currentEnrollment: number;
  waitlistCount: number;
  status: "open" | "full" | "waitlist" | "closed";
  registrations: CourseRegistration[];
}

// Default capacity settings for existing courses (using actual course slugs)
const DEFAULT_CAPACITIES: Record<
  string,
  { title: string; maxStudents: number }
> = {
  "coding-foundations-kids": {
    title: "Coding Foundations (Ages 9–14)",
    maxStudents: 8,
  },
  "web-foundations-teens-adults": {
    title: "Web Development Foundations (Teens & Adults)",
    maxStudents: 8,
  },
  "weather-app-project": {
    title: "Project: Build a Weather App",
    maxStudents: 6,
  },
  "personal-finance-tracker": {
    title: "Project: Personal Finance Tracker",
    maxStudents: 6,
  },
  "social-media-dashboard": {
    title: "Project: Social Media Dashboard",
    maxStudents: 6,
  },
  "mobile-app-project": {
    title: "Project: Mobile App from Idea to App Store",
    maxStudents: 6,
  },
  "ai-chatbot-project": {
    title: "Project: AI-Powered Chatbot",
    maxStudents: 6,
  },
  "game-development-project": {
    title: "Project: Create Your Own Video Game",
    maxStudents: 8,
  },
};

// Utility function to determine if payment should be marked as late
export const getEffectivePaymentStatus = (
  registration: CourseRegistration
): "pending" | "paid" | "refunded" | "late" | "cancelled" => {
  // If already paid, refunded, cancelled, or manually marked as late, return as-is
  if (registration.paymentStatus !== "pending") {
    return registration.paymentStatus;
  }

  // Find the course start date
  const course = courseData.find((c) => c.slug === registration.courseId);
  if (!course) {
    return registration.paymentStatus; // Can't determine, return original
  }

  // Parse the course start date
  const startDate = new Date(course.startDate);
  const today = new Date();

  // If course has started and payment is still pending, mark as late
  if (today > startDate) {
    return "late";
  }

  return registration.paymentStatus;
};

export const initializeCourseCapacities = async (): Promise<void> => {
  try {
    for (const [courseId, courseInfo] of Object.entries(DEFAULT_CAPACITIES)) {
      const capacityRef = doc(db, COLLECTIONS.COURSE_CAPACITIES, courseId);
      const capacityDoc = await getDoc(capacityRef);

      if (!capacityDoc.exists()) {
        await setDoc(capacityRef, {
          courseId,
          courseTitle: courseInfo.title,
          maxStudents: courseInfo.maxStudents,
          currentEnrollment: 0,
          status: "open",
          lastUpdated: Timestamp.now(),
        });
      }
    }
  } catch (error) {
    console.error("Error initializing course capacities:", error);
  }
};

export const getCourseCapacity = async (
  courseId: string
): Promise<CourseCapacity | null> => {
  try {
    const capacityRef = doc(db, COLLECTIONS.COURSE_CAPACITIES, courseId);
    const capacityDoc = await getDoc(capacityRef);

    if (!capacityDoc.exists()) {
      // Initialize if doesn't exist
      const defaultInfo = DEFAULT_CAPACITIES[courseId];
      if (defaultInfo) {
        await setDoc(capacityRef, {
          courseId,
          courseTitle: defaultInfo.title,
          maxStudents: defaultInfo.maxStudents,
          currentEnrollment: 0,
          status: "open",
          lastUpdated: Timestamp.now(),
        });

        return {
          courseId,
          courseTitle: defaultInfo.title,
          maxStudents: defaultInfo.maxStudents,
          currentEnrollment: 0,
          waitlistCount: 0,
          status: "open",
          registrations: [],
        };
      }
      return null;
    }

    const data = capacityDoc.data();
    const registrations = await getCourseRegistrations(courseId);
    // Exclude cancelled registrations from capacity calculations
    const activeRegistrations = registrations.filter(
      (r) => r.paymentStatus !== "cancelled"
    );
    const enrolledCount = activeRegistrations.filter(
      (r) => r.enrollmentStatus === "enrolled"
    ).length;
    const waitlistCount = activeRegistrations.filter(
      (r) => r.enrollmentStatus === "waitlist"
    ).length;

    return {
      courseId: data.courseId,
      courseTitle: data.courseTitle,
      maxStudents: data.maxStudents,
      currentEnrollment: enrolledCount,
      waitlistCount,
      status: enrolledCount >= data.maxStudents ? "full" : "open",
      registrations,
    };
  } catch (error) {
    console.error("Error getting course capacity:", error);
    return null;
  }
};

export const getAllCourseCapacities = async (): Promise<CourseCapacity[]> => {
  try {
    console.log("🏫 Fetching all course capacities...");
    const capacitiesRef = collection(db, COLLECTIONS.COURSE_CAPACITIES);
    const snapshot = await getDocs(capacitiesRef);

    const capacities: CourseCapacity[] = [];
    console.log(
      "📚 Available course capacity documents:",
      snapshot.docs.map((doc) => doc.id)
    );

    for (const doc of snapshot.docs) {
      const data = doc.data();
      console.log(`🔍 Checking registrations for course: "${data.courseId}"`);
      const registrations = await getCourseRegistrations(data.courseId);
      // Exclude cancelled registrations from capacity calculations
      const activeRegistrations = registrations.filter(
        (r) => r.paymentStatus !== "cancelled"
      );
      const enrolledCount = activeRegistrations.filter(
        (r) => r.enrollmentStatus === "enrolled"
      ).length;
      const waitlistCount = activeRegistrations.filter(
        (r) => r.enrollmentStatus === "waitlist"
      ).length;

      capacities.push({
        courseId: data.courseId,
        courseTitle: data.courseTitle,
        maxStudents: data.maxStudents,
        currentEnrollment: enrolledCount,
        waitlistCount,
        status: enrolledCount >= data.maxStudents ? "full" : "open",
        registrations,
      });
    }

    console.log(
      "📊 Final course capacities:",
      capacities.map((c) => ({
        courseId: c.courseId,
        title: c.courseTitle,
        enrolled: c.currentEnrollment,
        max: c.maxStudents,
      }))
    );

    return capacities;
  } catch (error) {
    console.error("Error getting all course capacities:", error);
    return [];
  }
};

export const updateCourseCapacity = async (
  courseId: string,
  maxStudents: number
): Promise<boolean> => {
  try {
    const capacityRef = doc(db, COLLECTIONS.COURSE_CAPACITIES, courseId);
    await updateDoc(capacityRef, {
      maxStudents,
      lastUpdated: Timestamp.now(),
    });
    return true;
  } catch (error) {
    console.error("Error updating course capacity:", error);
    return false;
  }
};

export const addRegistrationToCapacity = async (
  registration: Omit<CourseRegistration, "timestamp">
): Promise<void> => {
  try {
    console.log("🔥 Adding registration to Firebase:", registration);
    const registrationsRef = collection(db, COLLECTIONS.REGISTRATIONS);
    const docRef = await addDoc(registrationsRef, {
      ...registration,
      timestamp: Timestamp.now(),
    });
    console.log("✅ Registration added to Firebase with ID:", docRef.id);
  } catch (error) {
    console.error("❌ Error adding registration:", error);
  }
};

export const getCourseRegistrations = async (
  courseId: string
): Promise<CourseRegistration[]> => {
  try {
    console.log("🔍 Looking for registrations with courseId:", courseId);

    // First try with the provided courseId
    const registrationsRef = collection(db, COLLECTIONS.REGISTRATIONS);
    const q = query(
      registrationsRef,
      where("courseId", "==", courseId),
      orderBy("timestamp", "asc")
    );
    const snapshot = await getDocs(q);

    let registrations = snapshot.docs.map((doc) => ({
      ...(doc.data() as any),
      id: doc.id,
    })) as CourseRegistration[];

    console.log(
      `📊 Found ${registrations.length} registrations for courseId "${courseId}"`
    );

    // If no registrations found and courseId looks like a document ID, try to find by slug
    if (registrations.length === 0 && courseId.length > 10) {
      console.log(
        "🔍 No registrations found with document ID, trying to find course slug..."
      );

      // Get the course to find its slug
      const coursesRef = collection(db, COURSES_COLLECTION);
      const courseDoc = await getDoc(doc(coursesRef, courseId));

      if (courseDoc.exists()) {
        const courseData = courseDoc.data();
        const courseSlug = courseData.slug;

        if (courseSlug && courseSlug !== courseId) {
          console.log(`🔍 Trying with course slug: ${courseSlug}`);

          const slugQuery = query(
            registrationsRef,
            where("courseId", "==", courseSlug),
            orderBy("timestamp", "asc")
          );
          const slugSnapshot = await getDocs(slugQuery);

          registrations = slugSnapshot.docs.map((doc) => ({
            ...(doc.data() as any),
            id: doc.id,
          })) as CourseRegistration[];

          console.log(
            `📊 Found ${registrations.length} registrations for course slug "${courseSlug}"`
          );
        }
      }
    }

    if (registrations.length > 0) {
      console.log(
        "📝 Sample registration courseIds:",
        registrations.map((r) => r.courseId)
      );
    }

    return registrations;
  } catch (error) {
    console.error("Error getting course registrations:", error);
    return [];
  }
};

export const getAllRegistrations = async (): Promise<CourseRegistration[]> => {
  try {
    console.log("🔍 Fetching all registrations from Firebase...");
    const registrationsRef = collection(db, COLLECTIONS.REGISTRATIONS);
    const q = query(registrationsRef, orderBy("timestamp", "desc"));
    const snapshot = await getDocs(q);

    const registrations = snapshot.docs.map((doc) => ({
      ...(doc.data() as any),
      id: doc.id,
    })) as CourseRegistration[];

    console.log("📊 Found registrations:", registrations.length, registrations);
    return registrations;
  } catch (error) {
    console.error("❌ Error getting all registrations:", error);
    return [];
  }
};

export const deleteRegistration = async (
  registrationId: string
): Promise<boolean> => {
  try {
    const registrationRef = doc(db, COLLECTIONS.REGISTRATIONS, registrationId);
    await deleteDoc(registrationRef);
    return true;
  } catch (error) {
    console.error("Error deleting registration:", error);
    return false;
  }
};

export const updatePaymentStatus = async (
  documentId: string,
  paymentStatus: "pending" | "paid" | "refunded" | "late" | "cancelled",
  paymentNotes?: string
): Promise<boolean> => {
  try {
    console.log(
      `💰 Updating payment status for document ${documentId} to ${paymentStatus}`
    );
    const registrationRef = doc(db, COLLECTIONS.REGISTRATIONS, documentId);

    const updateData: any = {
      paymentStatus,
      lastUpdated: Timestamp.now(),
    };

    if (paymentStatus === "paid") {
      updateData.paymentDate = new Date().toISOString();
    }

    if (paymentNotes) {
      updateData.paymentNotes = paymentNotes;
    }

    await updateDoc(registrationRef, updateData);
    console.log(`✅ Payment status updated successfully`);
    return true;
  } catch (error) {
    console.error("Error updating payment status:", error);
    return false;
  }
};

export const updateEnrollmentStatus = async (
  documentId: string,
  enrollmentStatus: "enrolled" | "waitlist"
): Promise<boolean> => {
  try {
    console.log(
      `📝 Updating enrollment status for document ${documentId} to ${enrollmentStatus}`
    );
    const registrationRef = doc(db, COLLECTIONS.REGISTRATIONS, documentId);

    await updateDoc(registrationRef, {
      enrollmentStatus,
      lastUpdated: Timestamp.now(),
    });

    console.log(`✅ Enrollment status updated successfully`);
    return true;
  } catch (error) {
    console.error("Error updating enrollment status:", error);
    return false;
  }
};

export const updateInstructorNotes = async (
  documentId: string,
  instructorNotes: string,
  courseCompleted?: boolean
): Promise<boolean> => {
  try {
    console.log(`📝 Updating instructor notes for document ${documentId}`);
    const registrationRef = doc(db, COLLECTIONS.REGISTRATIONS, documentId);

    const updateData: any = {
      instructorNotes,
      lastUpdated: Timestamp.now(),
    };

    if (courseCompleted !== undefined) {
      updateData.courseCompleted = courseCompleted;
      if (courseCompleted) {
        updateData.completionDate = new Date().toISOString();
      }
    }

    await updateDoc(registrationRef, updateData);
    console.log(`✅ Instructor notes updated successfully`);
    return true;
  } catch (error) {
    console.error("Error updating instructor notes:", error);
    return false;
  }
};

export const updateRegistrationDetails = async (
  registrationId: string,
  updatedData: Partial<CourseRegistration>
): Promise<boolean> => {
  try {
    console.log(
      `📝 Updating registration details for document ${registrationId}`
    );
    const registrationRef = doc(db, COLLECTIONS.REGISTRATIONS, registrationId);

    // Remove undefined fields and prepare update data
    const updateData = Object.fromEntries(
      Object.entries(updatedData).filter(([_, value]) => value !== undefined)
    );

    // Add timestamp
    updateData.lastUpdated = Timestamp.now();

    await updateDoc(registrationRef, updateData);
    console.log(`✅ Registration details updated successfully`);
    return true;
  } catch (error) {
    console.error("Error updating registration details:", error);
    return false;
  }
};

export const getRegistrationStats = async () => {
  try {
    const registrations = await getAllRegistrations();
    const capacities = await getAllCourseCapacities();

    const totalRegistrations = registrations.length;
    const totalCapacity = capacities.reduce(
      (sum, course) => sum + course.maxStudents,
      0
    );
    const totalEnrolled = capacities.reduce(
      (sum, course) => sum + course.currentEnrollment,
      0
    );

    // Payment status breakdown and revenue calculation
    const paymentStats = registrations.reduce(
      (stats, reg) => {
        const status = reg.paymentStatus || "pending";
        stats[status] = (stats[status] || 0) + 1;
        return stats;
      },
      { pending: 0, paid: 0, refunded: 0 } as Record<string, number>
    );

    // Calculate total revenue from paid registrations
    const totalRevenue = registrations
      .filter((reg) => reg.paymentStatus === "paid")
      .reduce((sum, reg) => {
        // Extract numeric value from price string (e.g., "$299" -> 299)
        const price = reg.coursePrice || "0";
        const numericPrice = parseFloat(price.replace(/[^0-9.]/g, "")) || 0;
        return sum + numericPrice;
      }, 0);

    // Calculate waitlist counts for each course (excluding cancelled registrations)
    const courseBreakdownWithWaitlist = capacities.map((course) => {
      const courseRegistrations = registrations.filter(
        (reg) =>
          reg.courseId === course.courseId && reg.paymentStatus !== "cancelled"
      );
      const waitlistCount = courseRegistrations.filter(
        (reg) => reg.enrollmentStatus === "waitlist"
      ).length;

      return {
        courseId: course.courseId,
        courseTitle: course.courseTitle,
        enrolled: course.currentEnrollment,
        capacity: course.maxStudents,
        waitlist: waitlistCount,
        utilizationRate: (course.currentEnrollment / course.maxStudents) * 100,
        status: course.status,
      };
    });

    return {
      totalRegistrations,
      totalCapacity,
      totalEnrolled,
      utilizationRate:
        totalCapacity > 0 ? (totalEnrolled / totalCapacity) * 100 : 0,
      totalRevenue,
      paymentStats,
      recentRegistrations: registrations.slice(0, 10),
      courseBreakdown: courseBreakdownWithWaitlist,
    };
  } catch (error) {
    console.error("Error getting registration stats:", error);
    return {
      totalRegistrations: 0,
      totalCapacity: 0,
      totalEnrolled: 0,
      utilizationRate: 0,
      totalRevenue: 0,
      paymentStats: { pending: 0, paid: 0, refunded: 0 },
      recentRegistrations: [],
      courseBreakdown: [],
    };
  }
};

// Real-time listeners
export const onCourseCapacityChanged = (
  courseId: string,
  callback: (capacity: CourseCapacity | null) => void
) => {
  const capacityRef = doc(db, COLLECTIONS.COURSE_CAPACITIES, courseId);
  return onSnapshot(capacityRef, async (doc) => {
    if (doc.exists()) {
      const data = doc.data();
      const registrations = await getCourseRegistrations(courseId);
      // Exclude cancelled registrations from capacity calculations
      const activeRegistrations = registrations.filter(
        (r) => r.paymentStatus !== "cancelled"
      );
      const enrolledCount = activeRegistrations.filter(
        (r) => r.enrollmentStatus === "enrolled"
      ).length;
      const waitlistCount = activeRegistrations.filter(
        (r) => r.enrollmentStatus === "waitlist"
      ).length;

      callback({
        courseId: data.courseId,
        courseTitle: data.courseTitle,
        maxStudents: data.maxStudents,
        currentEnrollment: enrolledCount,
        waitlistCount,
        status: enrolledCount >= data.maxStudents ? "full" : "open",
        registrations,
      });
    } else {
      callback(null);
    }
  });
};

export const onRegistrationsChanged = (
  callback: (registrations: CourseRegistration[]) => void
) => {
  const registrationsRef = collection(db, COLLECTIONS.REGISTRATIONS);
  const q = query(registrationsRef, orderBy("timestamp", "desc"));

  return onSnapshot(q, (snapshot) => {
    const registrations = snapshot.docs.map((doc) => ({
      ...(doc.data() as any),
      id: doc.id,
    })) as CourseRegistration[];
    callback(registrations);
  });
};

export interface UserHistory {
  email: string;
  firstName: string;
  lastName: string;
  registrations: CourseRegistration[];
  totalCourses: number;
  completedCourses: number;
  lastActivity: string;
}

export interface Course {
  id: string;
  slug: string;
  title: string;
  description: string;
  startDate: string; // Will be a date string from calendar picker
  schedule: {
    daysOfWeek: string[]; // e.g., ["Monday", "Wednesday"]
    time: string; // e.g., "10:00 AM"
    duration: number; // Duration in minutes
  };
  price: string;
  outcomes: string[];
  ageRange: string;
  maxStudents: number;
  status: "active" | "archived" | "draft";
  createdAt: string;
  updatedAt: string;
  category: string;
  categories?: string[]; // Multiple structured categories/labels
  tags: string[];
  displayOrder: number;
  instructorId?: string;
  location?: string;
}

export const getUserHistory = async (
  email: string
): Promise<UserHistory | null> => {
  try {
    console.log(`🔍 Fetching user history for email: ${email}`);
    const registrationsRef = collection(db, COLLECTIONS.REGISTRATIONS);
    const q = query(
      registrationsRef,
      where("email", "==", email),
      orderBy("timestamp", "desc")
    );
    const snapshot = await getDocs(q);

    if (snapshot.empty) {
      return null;
    }

    const registrations = snapshot.docs.map((doc) => ({
      ...(doc.data() as any),
      id: doc.id,
    })) as CourseRegistration[];

    const firstRegistration = registrations[0];
    const completedCourses = registrations.filter(
      (reg) => reg.courseCompleted
    ).length;

    return {
      email,
      firstName: firstRegistration.firstName,
      lastName: firstRegistration.lastName,
      registrations,
      totalCourses: registrations.length,
      completedCourses,
      lastActivity: firstRegistration.registrationDate,
    };
  } catch (error) {
    console.error("Error getting user history:", error);
    return null;
  }
};

export const getAllUserHistories = async (): Promise<UserHistory[]> => {
  try {
    console.log("🔍 Fetching all user histories...");
    const registrations = await getAllRegistrations();

    // Group registrations by email
    const userMap = new Map<string, CourseRegistration[]>();

    registrations.forEach((registration) => {
      const email = registration.email.toLowerCase().trim();
      if (!userMap.has(email)) {
        userMap.set(email, []);
      }
      userMap.get(email)!.push(registration);
    });

    // Convert to UserHistory objects
    const userHistories: UserHistory[] = [];

    userMap.forEach((userRegistrations, email) => {
      // Sort by timestamp descending (most recent first)
      userRegistrations.sort(
        (a, b) =>
          new Date(b.registrationDate).getTime() -
          new Date(a.registrationDate).getTime()
      );

      const firstRegistration = userRegistrations[0];
      const completedCourses = userRegistrations.filter(
        (reg) => reg.courseCompleted
      ).length;

      userHistories.push({
        email,
        firstName: firstRegistration.firstName,
        lastName: firstRegistration.lastName,
        registrations: userRegistrations,
        totalCourses: userRegistrations.length,
        completedCourses,
        lastActivity: firstRegistration.registrationDate,
      });
    });

    // Sort by last activity (most recent first)
    userHistories.sort(
      (a, b) =>
        new Date(b.lastActivity).getTime() - new Date(a.lastActivity).getTime()
    );

    console.log(`📊 Found ${userHistories.length} unique users`);
    return userHistories;
  } catch (error) {
    console.error("Error getting all user histories:", error);
    return [];
  }
};

// Course Management Functions
const COURSES_COLLECTION = "courses";

export const getAllCourses = async (): Promise<Course[]> => {
  try {
    console.log("🔍 Fetching all courses...");
    const coursesRef = collection(db, COURSES_COLLECTION);
    // Don't order by displayOrder in the query since it might not exist for all courses
    const q = query(coursesRef, orderBy("createdAt", "desc"));
    const snapshot = await getDocs(q);

    const courses = snapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as Course[];

    // Sort by displayOrder, then by createdAt for courses without displayOrder
    courses.sort((a, b) => {
      if (a.displayOrder !== undefined && b.displayOrder !== undefined) {
        return a.displayOrder - b.displayOrder;
      }
      if (a.displayOrder !== undefined && b.displayOrder === undefined) {
        return -1;
      }
      if (a.displayOrder === undefined && b.displayOrder !== undefined) {
        return 1;
      }
      // Both undefined, sort by createdAt (newest first)
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    });

    console.log(`📚 Found ${courses.length} courses`);
    return courses;
  } catch (error) {
    console.error("Error getting all courses:", error);
    return [];
  }
};

export const createCourse = async (
  courseData: Omit<Course, "id" | "createdAt" | "updatedAt">
): Promise<string> => {
  try {
    console.log("📝 Creating new course...");
    const coursesRef = collection(db, COURSES_COLLECTION);
    const now = new Date().toISOString();

    // Get the highest displayOrder and add 1
    const existingCourses = await getAllCourses();
    const maxDisplayOrder = Math.max(
      ...existingCourses.map((c) => c.displayOrder || 0),
      0
    );

    const newCourse = {
      ...courseData,
      displayOrder: courseData.displayOrder || maxDisplayOrder + 1,
      createdAt: now,
      updatedAt: now,
    };

    const docRef = await addDoc(coursesRef, newCourse);

    // Also create a course capacity document
    await setDoc(doc(db, COLLECTIONS.COURSE_CAPACITIES, docRef.id), {
      courseId: docRef.id,
      courseTitle: courseData.title,
      maxStudents: courseData.maxStudents,
      status: courseData.status === "active" ? "open" : "closed",
    });

    console.log(`✅ Course created with ID: ${docRef.id}`);
    return docRef.id;
  } catch (error) {
    console.error("Error creating course:", error);
    throw error;
  }
};

export const updateCourse = async (
  courseId: string,
  updates: Partial<Course>
): Promise<boolean> => {
  try {
    console.log(`📝 Updating course ${courseId}...`);
    const courseRef = doc(db, COURSES_COLLECTION, courseId);

    const updateData = {
      ...updates,
      updatedAt: new Date().toISOString(),
    };

    await updateDoc(courseRef, updateData);

    // Update course capacity if title or maxStudents changed
    if (updates.title || updates.maxStudents !== undefined) {
      const capacityRef = doc(db, COLLECTIONS.COURSE_CAPACITIES, courseId);
      const capacityUpdates: any = {};

      if (updates.title) {
        capacityUpdates.courseTitle = updates.title;
      }
      if (updates.maxStudents !== undefined) {
        capacityUpdates.maxStudents = updates.maxStudents;
      }

      await updateDoc(capacityRef, capacityUpdates);
    }

    console.log(`✅ Course ${courseId} updated successfully`);
    return true;
  } catch (error) {
    console.error("Error updating course:", error);
    return false;
  }
};

export const archiveCourse = async (courseId: string): Promise<boolean> => {
  try {
    console.log(`📦 Archiving course ${courseId}...`);
    await updateCourse(courseId, { status: "archived" });

    // Update capacity status to closed
    const capacityRef = doc(db, COLLECTIONS.COURSE_CAPACITIES, courseId);
    await updateDoc(capacityRef, { status: "closed" });

    console.log(`✅ Course ${courseId} archived successfully`);
    return true;
  } catch (error) {
    console.error("Error archiving course:", error);
    return false;
  }
};

export const duplicateCourse = async (
  courseId: string,
  newTitle?: string
): Promise<string> => {
  try {
    console.log(`📋 Duplicating course ${courseId}...`);
    const courseRef = doc(db, COURSES_COLLECTION, courseId);
    const courseSnap = await getDoc(courseRef);

    if (!courseSnap.exists()) {
      throw new Error("Course not found");
    }

    const originalCourse = courseSnap.data() as Course;
    const duplicatedCourse = {
      ...originalCourse,
      title: newTitle || `${originalCourse.title} (Copy)`,
      status: "draft" as const,
    };

    // Remove the original id, createdAt, and updatedAt
    delete (duplicatedCourse as any).id;
    delete (duplicatedCourse as any).createdAt;
    delete (duplicatedCourse as any).updatedAt;

    const newCourseId = await createCourse(duplicatedCourse);
    console.log(`✅ Course duplicated with ID: ${newCourseId}`);
    return newCourseId;
  } catch (error) {
    console.error("Error duplicating course:", error);
    throw error;
  }
};

export const updateCourseDisplayOrder = async (
  courseId: string,
  newDisplayOrder: number
): Promise<boolean> => {
  try {
    console.log(
      `📝 Updating display order for course ${courseId} to ${newDisplayOrder}`
    );
    const courseRef = doc(db, COURSES_COLLECTION, courseId);

    await updateDoc(courseRef, {
      displayOrder: newDisplayOrder,
      updatedAt: new Date().toISOString(),
    });

    console.log(`✅ Course display order updated successfully`);
    return true;
  } catch (error) {
    console.error("Error updating course display order:", error);
    return false;
  }
};

export const reorderCourses = async (
  courseOrders: { id: string; displayOrder: number }[]
): Promise<boolean> => {
  try {
    console.log(`📝 Reordering ${courseOrders.length} courses...`);

    // Update all courses in a batch
    const batch = writeBatch(db);
    const now = new Date().toISOString();

    courseOrders.forEach(({ id, displayOrder }) => {
      const courseRef = doc(db, COURSES_COLLECTION, id);
      batch.update(courseRef, {
        displayOrder,
        updatedAt: now,
      });
    });

    await batch.commit();
    console.log(`✅ Successfully reordered ${courseOrders.length} courses`);
    return true;
  } catch (error) {
    console.error("Error reordering courses:", error);
    return false;
  }
};

// Migration function to add displayOrder to existing courses
export const migrateCourseDisplayOrder = async (): Promise<boolean> => {
  try {
    console.log("🔄 Migrating courses to add displayOrder...");
    const coursesRef = collection(db, COURSES_COLLECTION);
    const q = query(coursesRef, orderBy("createdAt", "asc"));
    const snapshot = await getDocs(q);

    const batch = writeBatch(db);
    const now = new Date().toISOString();

    snapshot.docs.forEach((doc, index) => {
      const data = doc.data();
      if (data.displayOrder === undefined) {
        batch.update(doc.ref, {
          displayOrder: index + 1,
          updatedAt: now,
        });
      }
    });

    await batch.commit();
    console.log(
      `✅ Successfully migrated ${snapshot.docs.length} courses with displayOrder`
    );
    return true;
  } catch (error) {
    console.error("Error migrating course display order:", error);
    return false;
  }
};
