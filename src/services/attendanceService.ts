// Attendance tracking service
import {
  collection,
  doc,
  setDoc,
  getDoc,
  getDocs,
  query,
  where,
  orderBy,
  Timestamp,
  updateDoc,
  addDoc,
} from "firebase/firestore";
import { db } from "../config/firebase";
import { CourseSession, SessionAttendanceRecord } from "../types";
import { getCourseSessionsService, updateSession } from "./sessionService";

export interface AttendanceRecord {
  id?: string;
  courseId: string;
  courseTitle: string;
  studentId: string; // Registration ID
  studentName: string;
  studentEmail: string;
  sessionDate: string; // ISO date string
  sessionNumber: number; // 1, 2, 3, etc.
  status: "present" | "absent" | "late" | "excused";
  notes?: string;
  instructorId: string;
  instructorName: string;
  createdAt: string;
  updatedAt: string;
}

export interface ClassSession {
  id?: string;
  courseId: string;
  courseTitle: string;
  sessionNumber: number;
  sessionDate: string;
  sessionTitle: string;
  instructorId: string;
  instructorName: string;
  attendanceRecords: AttendanceRecord[];
  createdAt: string;
  updatedAt: string;
}

export interface StudentProgress {
  studentId: string;
  studentName: string;
  studentEmail: string;
  courseId: string;
  courseTitle: string;
  totalSessions: number;
  attendedSessions: number;
  attendanceRate: number;
  progressNotes: string;
  skillsAssessment: {
    technical: number; // 1-5 scale
    collaboration: number;
    problemSolving: number;
    creativity: number;
  };
  lastUpdated: string;
}

const COLLECTIONS = {
  ATTENDANCE: "attendance",
  SESSIONS: "class_sessions",
  STUDENT_PROGRESS: "student_progress",
};

// Create or update attendance record
export const markAttendance = async (
  attendanceData: Omit<AttendanceRecord, "id" | "createdAt" | "updatedAt">
): Promise<boolean> => {
  try {
    console.log("📝 Marking attendance:", attendanceData);

    const attendanceRef = collection(db, COLLECTIONS.ATTENDANCE);
    const now = new Date().toISOString();

    // Create unique ID based on course, student, and session
    const recordId = `${attendanceData.courseId}-${attendanceData.studentId}-${attendanceData.sessionNumber}`;

    const record: AttendanceRecord = {
      ...attendanceData,
      id: recordId,
      createdAt: now,
      updatedAt: now,
    };

    await setDoc(doc(attendanceRef, recordId), record);
    console.log("✅ Attendance marked successfully");
    return true;
  } catch (error) {
    console.error("Error marking attendance:", error);
    return false;
  }
};

// Get attendance records for a course session
export const getSessionAttendance = async (
  courseId: string,
  sessionNumber: number
): Promise<AttendanceRecord[]> => {
  try {
    const attendanceRef = collection(db, COLLECTIONS.ATTENDANCE);
    const q = query(
      attendanceRef,
      where("courseId", "==", courseId),
      where("sessionNumber", "==", sessionNumber),
      orderBy("studentName", "asc")
    );

    const snapshot = await getDocs(q);
    return snapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as AttendanceRecord[];
  } catch (error) {
    console.error("Error getting session attendance:", error);
    return [];
  }
};

// Get all attendance records for a student in a course
export const getStudentAttendance = async (
  courseId: string,
  studentId: string
): Promise<AttendanceRecord[]> => {
  try {
    const attendanceRef = collection(db, COLLECTIONS.ATTENDANCE);
    const q = query(
      attendanceRef,
      where("courseId", "==", courseId),
      where("studentId", "==", studentId),
      orderBy("sessionNumber", "asc")
    );

    const snapshot = await getDocs(q);
    return snapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as AttendanceRecord[];
  } catch (error) {
    console.error("Error getting student attendance:", error);
    return [];
  }
};

// Get attendance summary for a course
export const getCourseAttendanceSummary = async (
  courseId: string
): Promise<{
  totalSessions: number;
  students: {
    studentId: string;
    studentName: string;
    attendanceRate: number;
    totalPresent: number;
    totalAbsent: number;
  }[];
}> => {
  try {
    const attendanceRef = collection(db, COLLECTIONS.ATTENDANCE);
    const q = query(
      attendanceRef,
      where("courseId", "==", courseId),
      orderBy("sessionNumber", "asc")
    );

    const snapshot = await getDocs(q);
    const records = snapshot.docs.map((doc) =>
      doc.data()
    ) as AttendanceRecord[];

    // Group by student
    const studentMap = new Map<
      string,
      {
        studentId: string;
        studentName: string;
        present: number;
        absent: number;
        total: number;
      }
    >();

    let maxSessionNumber = 0;

    records.forEach((record) => {
      maxSessionNumber = Math.max(maxSessionNumber, record.sessionNumber);

      if (!studentMap.has(record.studentId)) {
        studentMap.set(record.studentId, {
          studentId: record.studentId,
          studentName: record.studentName,
          present: 0,
          absent: 0,
          total: 0,
        });
      }

      const student = studentMap.get(record.studentId)!;
      student.total++;

      if (record.status === "present" || record.status === "late") {
        student.present++;
      } else {
        student.absent++;
      }
    });

    const students = Array.from(studentMap.values()).map((student) => ({
      studentId: student.studentId,
      studentName: student.studentName,
      attendanceRate:
        student.total > 0 ? (student.present / student.total) * 100 : 0,
      totalPresent: student.present,
      totalAbsent: student.absent,
    }));

    return {
      totalSessions: maxSessionNumber,
      students,
    };
  } catch (error) {
    console.error("Error getting course attendance summary:", error);
    return { totalSessions: 0, students: [] };
  }
};

// Update student progress notes
export const updateStudentProgress = async (
  progressData: StudentProgress
): Promise<boolean> => {
  try {
    console.log("📈 Updating student progress:", progressData);

    const progressRef = doc(
      db,
      COLLECTIONS.STUDENT_PROGRESS,
      `${progressData.courseId}-${progressData.studentId}`
    );

    const updatedProgress = {
      ...progressData,
      lastUpdated: new Date().toISOString(),
    };

    await setDoc(progressRef, updatedProgress);
    console.log("✅ Student progress updated successfully");
    return true;
  } catch (error) {
    console.error("Error updating student progress:", error);
    return false;
  }
};

// Get student progress
export const getStudentProgress = async (
  courseId: string,
  studentId: string
): Promise<StudentProgress | null> => {
  try {
    const progressRef = doc(
      db,
      COLLECTIONS.STUDENT_PROGRESS,
      `${courseId}-${studentId}`
    );
    const progressDoc = await getDoc(progressRef);

    if (progressDoc.exists()) {
      return progressDoc.data() as StudentProgress;
    }
    return null;
  } catch (error) {
    console.error("Error getting student progress:", error);
    return null;
  }
};

// ========================================
// NEW SESSION-BASED ATTENDANCE FUNCTIONS
// ========================================

// Mark attendance for a student in a specific session
export const markSessionAttendance = async (
  sessionId: string,
  studentId: string,
  attendanceData: Omit<SessionAttendanceRecord, "markedAt" | "markedBy">,
  instructorId: string
): Promise<boolean> => {
  try {
    console.log("📝 Marking session attendance:", {
      sessionId,
      studentId,
      attendanceData,
    });

    // Get the current session
    const sessionRef = doc(db, "courseSessions", sessionId);
    const sessionDoc = await getDoc(sessionRef);

    if (!sessionDoc.exists()) {
      console.error("Session not found:", sessionId);
      return false;
    }

    const session = sessionDoc.data() as CourseSession;

    // Create the attendance record, filtering out undefined values
    const attendanceRecord: SessionAttendanceRecord = {
      studentId: attendanceData.studentId,
      studentName: attendanceData.studentName,
      studentEmail: attendanceData.studentEmail,
      status: attendanceData.status,
      markedAt: new Date().toISOString(),
      markedBy: instructorId,
    };

    // Only add notes if they exist and are not empty
    if (attendanceData.notes && attendanceData.notes.trim() !== "") {
      attendanceRecord.notes = attendanceData.notes.trim();
    }

    // Update the session with the attendance record
    const updatedAttendance = {
      ...session.attendance,
      [studentId]: attendanceRecord,
    };

    await updateSession(sessionId, {
      attendance: updatedAttendance,
    });

    console.log("✅ Session attendance marked successfully");
    return true;
  } catch (error) {
    console.error("Error marking session attendance:", error);
    return false;
  }
};

// Get attendance for a specific session
export const getSessionAttendanceRecords = async (
  sessionId: string
): Promise<SessionAttendanceRecord[]> => {
  try {
    const sessionRef = doc(db, "courseSessions", sessionId);
    const sessionDoc = await getDoc(sessionRef);

    if (!sessionDoc.exists()) {
      return [];
    }

    const session = sessionDoc.data() as CourseSession;

    if (!session.attendance) {
      return [];
    }

    // Convert attendance object to array
    return Object.values(session.attendance);
  } catch (error) {
    console.error("Error getting session attendance:", error);
    return [];
  }
};

// Get all attendance records for a student across all sessions in a course
export const getStudentSessionAttendance = async (
  courseId: string,
  studentId: string
): Promise<
  { session: CourseSession; attendance?: SessionAttendanceRecord }[]
> => {
  try {
    console.log(
      `🔍 Getting session attendance for student ${studentId} in course ${courseId}`
    );

    // Get all sessions for the course
    const sessions = await getCourseSessionsService(courseId);

    // Map sessions with attendance data for the specific student
    const studentAttendance = sessions.map((session) => ({
      session,
      attendance: session.attendance?.[studentId],
    }));

    console.log(
      `📊 Found ${studentAttendance.length} sessions for student attendance`
    );
    return studentAttendance;
  } catch (error) {
    console.error("Error getting student session attendance:", error);
    return [];
  }
};

// Get attendance summary for a course using session-based data
export const getCourseSessionAttendanceSummary = async (
  courseId: string
): Promise<{
  totalSessions: number;
  students: {
    studentId: string;
    studentName: string;
    attendanceRate: number;
    totalPresent: number;
    totalAbsent: number;
    totalSessions: number;
  }[];
}> => {
  try {
    console.log(
      `🔍 Getting session-based attendance summary for course ${courseId}`
    );

    // Get all sessions for the course
    const sessions = await getCourseSessionsService(courseId);

    // Collect all unique students and their attendance
    const studentMap = new Map<
      string,
      {
        studentId: string;
        studentName: string;
        present: number;
        absent: number;
        total: number;
      }
    >();

    sessions.forEach((session) => {
      if (session.attendance) {
        Object.values(session.attendance).forEach((record) => {
          if (!studentMap.has(record.studentId)) {
            studentMap.set(record.studentId, {
              studentId: record.studentId,
              studentName: record.studentName,
              present: 0,
              absent: 0,
              total: 0,
            });
          }

          const student = studentMap.get(record.studentId)!;
          student.total++;

          if (record.status === "present" || record.status === "late") {
            student.present++;
          } else {
            student.absent++;
          }
        });
      }
    });

    const students = Array.from(studentMap.values()).map((student) => ({
      studentId: student.studentId,
      studentName: student.studentName,
      attendanceRate:
        student.total > 0 ? (student.present / student.total) * 100 : 0,
      totalPresent: student.present,
      totalAbsent: student.absent,
      totalSessions: student.total,
    }));

    console.log(
      `📊 Session-based attendance summary: ${sessions.length} sessions, ${students.length} students`
    );

    return {
      totalSessions: sessions.length,
      students,
    };
  } catch (error) {
    console.error("Error getting course session attendance summary:", error);
    return { totalSessions: 0, students: [] };
  }
};
