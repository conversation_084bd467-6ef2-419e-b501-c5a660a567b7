import {
  collection,
  doc,
  getDocs,
  getDoc,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  Timestamp,
} from "firebase/firestore";
import { db } from "../config/firebase";
import { CourseSession } from "../types";

const COLLECTIONS = {
  SESSIONS: "courseSessions",
} as const;

// Create a new session
export const createSession = async (
  courseId: string,
  sessionData: Omit<CourseSession, "id" | "createdAt" | "updatedAt">
): Promise<string> => {
  try {
    const now = new Date().toISOString();
    const sessionDoc = await addDoc(collection(db, COLLECTIONS.SESSIONS), {
      ...sessionData,
      courseId,
      createdAt: now,
      updatedAt: now,
    });

    console.log("✅ Session created:", sessionDoc.id);
    return sessionDoc.id;
  } catch (error) {
    console.error("❌ Error creating session:", error);
    throw error;
  }
};

// Get all sessions for a course
export const getCourseSessionsService = async (
  courseId: string
): Promise<CourseSession[]> => {
  try {
    console.log("🔍 Fetching sessions for course:", courseId);

    const sessionsRef = collection(db, COLLECTIONS.SESSIONS);
    const q = query(
      sessionsRef,
      where("courseId", "==", courseId),
      orderBy("sessionNumber", "asc")
    );

    const snapshot = await getDocs(q);
    const sessions = snapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as CourseSession[];

    console.log(`📊 Found ${sessions.length} sessions for course ${courseId}`);
    return sessions;
  } catch (error) {
    console.error("❌ Error fetching course sessions:", error);
    return [];
  }
};

// Get sessions for an instructor
export const getInstructorSessions = async (
  instructorId: string,
  courseId?: string
): Promise<CourseSession[]> => {
  try {
    console.log(
      `🔍 Fetching sessions for instructor: ${instructorId}${
        courseId ? ` and course: ${courseId}` : ""
      }`
    );

    if (courseId) {
      // Get sessions for specific course - try both courseId and slug
      let courseSessions = await getCourseSessionsService(courseId);
      console.log(
        `📊 Found ${courseSessions.length} sessions for course ${courseId}`
      );

      // If no sessions found, try the opposite identifier type
      if (courseSessions.length === 0) {
        console.log(
          `🔍 No sessions found for ${courseId}, trying to find alternate identifier`
        );
        const { getAllCourses } = await import(
          "./firebaseCourseCapacityService"
        );
        const allCourses = await getAllCourses();

        // Try to find course by slug or document ID
        const course = allCourses.find(
          (c) => c.slug === courseId || c.id === courseId
        );
        if (course) {
          // If we searched by slug, try document ID
          if (course.slug === courseId && course.id !== courseId) {
            console.log(
              `🔍 Found course document ID ${course.id} for slug ${courseId}`
            );
            courseSessions = await getCourseSessionsService(course.id);
            console.log(
              `📊 Found ${courseSessions.length} sessions using document ID ${course.id}`
            );
          }
          // If we searched by document ID, try slug
          else if (course.id === courseId && course.slug !== courseId) {
            console.log(
              `🔍 Found course slug ${course.slug} for document ID ${courseId}`
            );
            courseSessions = await getCourseSessionsService(course.slug);
            console.log(
              `📊 Found ${courseSessions.length} sessions using slug ${course.slug}`
            );
          }
        } else {
          console.log(`❌ No course found for identifier ${courseId}`);
        }
      }

      return courseSessions;
    }

    // Get instructor's assigned courses using the instructor service
    const { getInstructorCourses } = await import("./instructorService");
    const instructorCourses = await getInstructorCourses(instructorId);

    console.log(
      `📚 Instructor has ${instructorCourses.length} assigned courses:`,
      instructorCourses.map((c) => ({ id: c.id, slug: c.slug, title: c.title }))
    );

    // Get sessions for all instructor courses
    const allSessions: CourseSession[] = [];
    for (const course of instructorCourses) {
      console.log(
        `🔍 Getting sessions for course: ${course.title} (slug: ${course.slug}, id: ${course.id})`
      );

      // Try to get sessions using the course document ID first
      let courseSessions = await getCourseSessionsService(course.id);
      console.log(
        `📊 Found ${courseSessions.length} sessions using document ID ${course.id}`
      );

      // If no sessions found with document ID, try with slug
      if (courseSessions.length === 0 && course.slug !== course.id) {
        console.log(
          `🔍 No sessions found with document ID, trying slug ${course.slug}`
        );
        courseSessions = await getCourseSessionsService(course.slug);
        console.log(
          `📊 Found ${courseSessions.length} sessions using slug ${course.slug}`
        );
      }

      // Add course info to sessions
      const sessionsWithCourse = courseSessions.map((session) => ({
        ...session,
        courseTitle: course.title,
        courseSlug: course.slug,
      }));
      allSessions.push(...sessionsWithCourse);
    }

    // Sort by date
    allSessions.sort(
      (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()
    );

    console.log(
      `📊 Found ${allSessions.length} total sessions for instructor ${instructorId}`
    );
    return allSessions;
  } catch (error) {
    console.error("❌ Error fetching instructor sessions:", error);
    return [];
  }
};

// Update a session
export const updateSession = async (
  sessionId: string,
  updates: Partial<CourseSession>
): Promise<void> => {
  try {
    const sessionRef = doc(db, COLLECTIONS.SESSIONS, sessionId);
    await updateDoc(sessionRef, {
      ...updates,
      updatedAt: new Date().toISOString(),
    });

    console.log("✅ Session updated:", sessionId);
  } catch (error) {
    console.error("❌ Error updating session:", error);
    throw error;
  }
};

// Delete a session
export const deleteSession = async (sessionId: string): Promise<void> => {
  try {
    const sessionRef = doc(db, COLLECTIONS.SESSIONS, sessionId);
    await deleteDoc(sessionRef);

    console.log("✅ Session deleted:", sessionId);
  } catch (error) {
    console.error("❌ Error deleting session:", error);
    throw error;
  }
};

// Get upcoming sessions (public view)
export const getUpcomingSessions = async (
  limit?: number
): Promise<CourseSession[]> => {
  try {
    console.log("🔍 Fetching upcoming sessions");

    const sessionsRef = collection(db, COLLECTIONS.SESSIONS);
    const today = new Date().toISOString().split("T")[0];

    const q = query(
      sessionsRef,
      where("date", ">=", today),
      where("status", "==", "scheduled"),
      orderBy("date", "asc"),
      orderBy("startTime", "asc")
    );

    const snapshot = await getDocs(q);
    let sessions = snapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as CourseSession[];

    if (limit) {
      sessions = sessions.slice(0, limit);
    }

    console.log(`📊 Found ${sessions.length} upcoming sessions`);
    return sessions;
  } catch (error) {
    console.error("❌ Error fetching upcoming sessions:", error);
    return [];
  }
};

// Get all sessions with course information for customer calendar
export const getAllSessionsWithCourseInfo = async (): Promise<
  CourseSession[]
> => {
  try {
    console.log(
      "🔍 getAllSessionsWithCourseInfo: Fetching all sessions with course info for customer calendar"
    );

    const sessionsRef = collection(db, COLLECTIONS.SESSIONS);

    // Get all scheduled and completed sessions for active courses
    // We want to show both current/future sessions and past completed sessions
    const q = query(
      sessionsRef,
      where("status", "in", ["scheduled", "completed"]),
      orderBy("date", "asc"),
      orderBy("startTime", "asc")
    );

    const snapshot = await getDocs(q);
    const sessions = snapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as CourseSession[];

    console.log(
      `📊 getAllSessionsWithCourseInfo: Found ${sessions.length} scheduled and completed sessions`
    );

    if (sessions.length > 0) {
      console.log("🔍 Sample session data:", sessions[0]);
      console.log(
        "🔍 All session courseIds:",
        sessions.map((s) => ({
          id: s.id,
          courseId: s.courseId,
          date: s.date,
          status: s.status,
        }))
      );
    }

    // Get course information for each session
    const { getAllCourses } = await import("./firebaseCourseCapacityService");
    const allCourses = await getAllCourses();

    console.log(
      `📚 getAllSessionsWithCourseInfo: Found ${allCourses.length} total courses`
    );

    // Filter to only active courses that customers can see
    const activeCourses = allCourses.filter(
      (course) => course.status === "active"
    );

    console.log(
      `✅ getAllSessionsWithCourseInfo: Found ${activeCourses.length} active courses`
    );

    // Debug session courseIds and course identifiers
    console.log(
      "🔍 getAllSessionsWithCourseInfo: Session courseIds:",
      sessions.map((s) => s.courseId)
    );
    console.log(
      "🔍 getAllSessionsWithCourseInfo: Active course IDs:",
      activeCourses.map((c) => ({ id: c.id, slug: c.slug }))
    );

    // Enrich sessions with course information
    const sessionsWithCourseInfo = sessions
      .map((session) => {
        console.log(
          `🔍 Looking for course for session ${session.id} with courseId: ${session.courseId}`
        );

        const course = activeCourses.find(
          (c) => c.id === session.courseId || c.slug === session.courseId
        );

        if (!course) {
          console.warn(
            `❌ Course not found for session ${session.id} with courseId ${session.courseId}`
          );
          console.warn(
            "Available courses:",
            activeCourses.map((c) => ({
              id: c.id,
              slug: c.slug,
              title: c.title,
            }))
          );
          return null;
        }

        console.log(
          `✅ Found course ${course.title} for session ${session.id}`
        );

        return {
          ...session,
          courseTitle: course.title,
          courseSlug: course.slug,
          courseCategory: course.category, // Legacy single category
          courseCategories: course.categories, // New structured categories array
          coursePrice: course.price,
          courseAgeRange: course.ageRange,
          courseSchedule: course.schedule,
        };
      })
      .filter(Boolean) as CourseSession[];

    console.log(
      `📊 Found ${sessionsWithCourseInfo.length} sessions with course info`
    );
    return sessionsWithCourseInfo;
  } catch (error) {
    console.error("❌ Error fetching sessions with course info:", error);
    return [];
  }
};

// Utility functions
export const formatSessionTime = (
  startTime: string,
  endTime: string
): string => {
  const formatTime = (time: string) => {
    const [hours, minutes] = time.split(":");
    const hour = parseInt(hours);
    const ampm = hour >= 12 ? "PM" : "AM";
    const displayHour = hour % 12 || 12;
    return `${displayHour}:${minutes} ${ampm}`;
  };

  return `${formatTime(startTime)} - ${formatTime(endTime)}`;
};

export const formatSessionDate = (date: string): string => {
  return new Date(date).toLocaleDateString("en-US", {
    weekday: "long",
    year: "numeric",
    month: "long",
    day: "numeric",
  });
};

// Auto-generate sessions based on course schedule
export const autoGenerateSessions = async (
  courseId: string,
  course: {
    startDate: string;
    schedule: {
      daysOfWeek: string[];
      time: string;
      duration: number;
    };
    title: string;
  },
  sessionCount: number = 6
): Promise<string[]> => {
  try {
    console.log(
      `🔄 Auto-generating ${sessionCount} sessions for course:`,
      course.title
    );

    const sessions: Omit<CourseSession, "id" | "createdAt" | "updatedAt">[] =
      [];
    const startDate = new Date(course.startDate);

    // Map day names to numbers (0 = Sunday, 1 = Monday, etc.)
    const dayMap: { [key: string]: number } = {
      Sunday: 0,
      Monday: 1,
      Tuesday: 2,
      Wednesday: 3,
      Thursday: 4,
      Friday: 5,
      Saturday: 6,
    };

    // Get the target days of week as numbers
    const targetDays = course.schedule.daysOfWeek
      .map((day) => dayMap[day])
      .filter((day) => day !== undefined);

    if (targetDays.length === 0) {
      throw new Error("No valid days of week found in course schedule");
    }

    // Calculate end time
    const [hours, minutes] = course.schedule.time.split(":").map(Number);
    const startTime = `${hours.toString().padStart(2, "0")}:${minutes
      .toString()
      .padStart(2, "0")}`;

    const endDate = new Date(startDate);
    endDate.setHours(hours, minutes + course.schedule.duration);
    const endTime = `${endDate.getHours().toString().padStart(2, "0")}:${endDate
      .getMinutes()
      .toString()
      .padStart(2, "0")}`;

    // Generate sessions
    let currentDate = new Date(startDate);
    let sessionNumber = 1;

    // Find the first occurrence of a target day
    while (
      !targetDays.includes(currentDate.getDay()) &&
      sessionNumber <= sessionCount
    ) {
      currentDate.setDate(currentDate.getDate() + 1);
    }

    while (sessionNumber <= sessionCount) {
      if (targetDays.includes(currentDate.getDay())) {
        sessions.push({
          courseId,
          sessionNumber,
          date: currentDate.toISOString().split("T")[0], // YYYY-MM-DD format
          startTime,
          endTime,
          topic: `Session ${sessionNumber}`,
          description: "",
          status: "scheduled",
        });
        sessionNumber++;
      }

      // Move to next day
      currentDate.setDate(currentDate.getDate() + 1);

      // Safety check to prevent infinite loop
      if (
        currentDate.getTime() >
        startDate.getTime() + 365 * 24 * 60 * 60 * 1000
      ) {
        console.warn(
          "Stopped generating sessions after 1 year to prevent infinite loop"
        );
        break;
      }
    }

    // Create all sessions in Firebase
    const sessionIds: string[] = [];
    for (const sessionData of sessions) {
      const sessionId = await createSession(courseId, sessionData);
      sessionIds.push(sessionId);
    }

    console.log(
      `✅ Successfully created ${sessionIds.length} sessions for course ${course.title}`
    );
    return sessionIds;
  } catch (error) {
    console.error("❌ Error auto-generating sessions:", error);
    throw error;
  }
};

// Generate default sessions for a course (legacy function - keeping for compatibility)
export const generateDefaultSessions = (
  startDate: string,
  dayTime: string,
  sessionCount: number = 6,
  duration: number = 90
): Omit<CourseSession, "id" | "createdAt" | "updatedAt">[] => {
  const sessions: Omit<CourseSession, "id" | "createdAt" | "updatedAt">[] = [];

  // Parse dayTime (e.g., "Saturdays at 10:00 AM")
  const timeMatch = dayTime.match(/(\d{1,2}):(\d{2})\s*(AM|PM)/i);
  if (!timeMatch) {
    console.error("Could not parse dayTime:", dayTime);
    return sessions;
  }

  let hour = parseInt(timeMatch[1]);
  const minute = parseInt(timeMatch[2]);
  const ampm = timeMatch[3].toUpperCase();

  if (ampm === "PM" && hour !== 12) hour += 12;
  if (ampm === "AM" && hour === 12) hour = 0;

  const startTime = `${hour.toString().padStart(2, "0")}:${minute
    .toString()
    .padStart(2, "0")}`;
  const endHour = hour + Math.floor(duration / 60);
  const endMinute = minute + (duration % 60);
  const endTime = `${endHour.toString().padStart(2, "0")}:${endMinute
    .toString()
    .padStart(2, "0")}`;

  // Generate sessions
  const sessionDate = new Date(startDate);

  for (let i = 0; i < sessionCount; i++) {
    sessions.push({
      courseId: "", // Will be set by the calling function
      sessionNumber: i + 1,
      date: sessionDate.toISOString().split("T")[0],
      startTime,
      endTime,
      status: "scheduled",
    });

    // Add 7 days for next session (assuming weekly)
    sessionDate.setDate(sessionDate.getDate() + 7);
  }

  return sessions;
};

// Debug function to list all sessions in the database
export const getAllSessionsDebug = async (): Promise<CourseSession[]> => {
  try {
    console.log("🔍 DEBUG: Fetching ALL sessions from database");

    const sessionsRef = collection(db, COLLECTIONS.SESSIONS);
    const snapshot = await getDocs(sessionsRef);
    const sessions = snapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as CourseSession[];

    console.log(
      `📊 DEBUG: Found ${sessions.length} total sessions in database:`
    );
    sessions.forEach((session) => {
      console.log(
        `  - Session ${session.sessionNumber} for courseId: ${session.courseId} (${session.date})`
      );
    });

    return sessions;
  } catch (error) {
    console.error("❌ Error fetching all sessions:", error);
    return [];
  }
};
