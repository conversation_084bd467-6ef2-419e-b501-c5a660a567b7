/* Import additional stylesheets */
@import "./animations.css";
@import "./responsive.css";

/* Global styles and CSS reset */
html,
body,
#root {
  height: 100%;
  margin: 0;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen",
    "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue",
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f9f9f9;
  color: #0a2d69;
  line-height: 1.6;
}

* {
  box-sizing: border-box;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  margin-top: 0;
  line-height: 1.2;
}

p {
  margin-top: 0;
}

/* Links */
a {
  color: inherit;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

/* Lists */
ul,
ol {
  margin-top: 0;
}

/* Images */
img {
  max-width: 100%;
  height: auto;
}

/* Focus styles for accessibility */
:focus {
  outline: 2px solid #ffa300;
  outline-offset: 2px;
}

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Animations */
@keyframes instructorSpin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Reports Tab Styling */
.reports-tab {
  padding: 0;
}

.reports-tab .tab-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.reports-tab .tab-title h2 {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
  font-size: 1.5rem;
  font-weight: 600;
}

.reports-tab .tab-title p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

.reports-tab .tab-header-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.reports-tab .location-filter {
  padding: 0.5rem 1rem;
  border: 1px solid #ddd;
  border-radius: 6px;
  background: white;
  font-size: 0.9rem;
  min-width: 150px;
}

.reports-tab .location-filter:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

/* Empty State */
.reports-tab .empty-state {
  text-align: center;
  padding: 3rem 2rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.reports-tab .empty-state__icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.reports-tab .empty-state h3 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
  font-size: 1.25rem;
}

.reports-tab .empty-state p {
  margin: 0 0 2rem 0;
  color: #666;
  font-size: 1rem;
}

.reports-tab .empty-state__help {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 6px;
  text-align: left;
  max-width: 400px;
  margin: 0 auto;
}

.reports-tab .empty-state__help h4 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
  font-size: 1rem;
}

.reports-tab .empty-state__help ol {
  margin: 0;
  padding-left: 1.5rem;
}

.reports-tab .empty-state__help li {
  margin-bottom: 0.5rem;
  color: #555;
}

/* Reports List */
.reports-tab .reports-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Report Card */
.reports-tab .report-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: box-shadow 0.2s ease;
}

.reports-tab .report-card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Report Card Header */
.reports-tab .report-card__header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 1.5rem;
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  color: white;
}

.reports-tab .report-info h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.reports-tab .report-date {
  margin: 0 0 0.25rem 0;
  font-size: 0.9rem;
  opacity: 0.9;
}

.reports-tab .report-period {
  margin: 0;
  font-size: 0.85rem;
  opacity: 0.8;
}

.reports-tab .report-stats {
  display: flex;
  gap: 1rem;
}

.reports-tab .stat {
  text-align: center;
  min-width: 60px;
}

.reports-tab .stat-number {
  display: block;
  font-size: 1.5rem;
  font-weight: bold;
  line-height: 1;
}

.reports-tab .stat-label {
  display: block;
  font-size: 0.75rem;
  opacity: 0.8;
  margin-top: 0.25rem;
}

/* Report Notes */
.reports-tab .report-notes {
  padding: 1rem 1.5rem;
  background: #f8f9fa;
  border-left: 4px solid #3498db;
}

.reports-tab .report-notes h4 {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
  font-size: 0.9rem;
  font-weight: 600;
}

.reports-tab .report-notes p {
  margin: 0;
  color: #555;
  font-style: italic;
  line-height: 1.4;
}

/* Report Details */
.reports-tab .report-details {
  padding: 1.5rem;
}

.reports-tab .report-section {
  margin-bottom: 2rem;
}

.reports-tab .report-section:last-child {
  margin-bottom: 0;
}

.reports-tab .report-section h4 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
  font-size: 1rem;
  font-weight: 600;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #ecf0f1;
}

/* Collapsible Headers */
.reports-tab .collapsible-header {
  transition: all 0.2s ease;
  user-select: none;
  padding: 0.5rem;
  margin: -0.5rem -0.5rem 1rem -0.5rem;
  border-radius: 6px;
}

.reports-tab .collapsible-header:hover {
  background: #f8f9fa;
}

.reports-tab .expand-icon {
  font-size: 0.8rem;
  color: #666;
  transition: transform 0.2s ease;
}

.reports-tab .collapsible-header:hover .expand-icon {
  color: #3498db;
}

/* Task Lists */
.reports-tab .task-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.reports-tab .task-item {
  display: flex;
  flex-direction: column;
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #ecf0f1;
  gap: 0.5rem;
}

.reports-tab .task-main-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.reports-tab .task-item--pending {
  border-left-color: #f39c12;
  background: #fef9e7;
}

.reports-tab .task-item--expired {
  border-left-color: #e74c3c;
  background: #fdf2f2;
}

.reports-tab .task-item--completed {
  border-left-color: #27ae60;
  background: #f0f9f0;
}

.reports-tab .task-title {
  font-weight: 500;
  color: #2c3e50;
  flex: 1;
}

/* Task Notes Styling */
.reports-tab .task-notes {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  padding: 0.5rem;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 4px;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.reports-tab .task-notes-label {
  font-size: 0.75rem;
  font-weight: 600;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.reports-tab .task-notes-text {
  font-size: 0.85rem;
  color: #555;
  line-height: 1.4;
  font-style: italic;
}

.reports-tab .task-routine {
  color: #666;
  font-size: 0.85rem;
  margin-left: 0.5rem;
}

.reports-tab .task-time {
  color: #666;
  font-size: 0.85rem;
  margin-left: auto;
}

.reports-tab .task-status {
  font-size: 0.85rem;
  font-weight: 500;
  margin-left: auto;
}

.reports-tab .task-status--pending {
  color: #f39c12;
}

.reports-tab .task-status--expired {
  color: #e74c3c;
}

.reports-tab .no-tasks {
  text-align: center;
  color: #666;
  font-style: italic;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 6px;
  margin: 0;
}

/* Report Footer */
.reports-tab .report-footer {
  padding: 1rem 1.5rem;
  background: #f8f9fa;
  border-top: 1px solid #ecf0f1;
}

.reports-tab .reported-by {
  color: #666;
  font-size: 0.85rem;
}

/* Loading Spinner */
.reports-tab .loading-spinner {
  text-align: center;
  padding: 3rem;
  color: #666;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
  .reports-tab .tab-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .reports-tab .report-card__header {
    flex-direction: column;
    gap: 1rem;
  }

  .reports-tab .report-stats {
    justify-content: space-around;
  }

  .reports-tab .task-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .reports-tab .task-time,
  .reports-tab .task-status {
    margin-left: 0;
  }
}
