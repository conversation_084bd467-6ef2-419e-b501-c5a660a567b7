import React from 'react';
import { usePerformance } from '../hooks';

// Example 1: Basic monitoring
const MyComponent: React.FC = () => {
  // This will automatically log render times to console
  usePerformance({
    componentName: 'MyComponent',
    logToConsole: true,
    threshold: 10, // Warn if render takes more than 10ms
  });

  return <div>My monitored component</div>;
};

// Example 2: Custom threshold monitoring
const HeavyComponent: React.FC = () => {
  usePerformance({
    componentName: 'HeavyComponent',
    threshold: 50, // Higher threshold for complex components
  });

  // Simulate heavy computation
  const heavyCalculation = () => {
    let result = 0;
    for (let i = 0; i < 1000000; i++) {
      result += Math.random();
    }
    return result;
  };

  return <div>Result: {heavyCalculation()}</div>;
};

// Example 3: Production monitoring (logs disabled)
const ProductionComponent: React.FC = () => {
  usePerformance({
    componentName: 'ProductionComponent',
    logToConsole: process.env.NODE_ENV === 'development', // Only log in dev
  });

  return <div>Production ready component</div>;
};

export { MyComponent, HeavyComponent, ProductionComponent };
