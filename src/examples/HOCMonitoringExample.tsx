import React from 'react';
import { withPerformanceMonitoring } from '../components/hoc';

// Original component
const MySlowComponent: React.FC<{ data: any[] }> = ({ data }) => {
  return (
    <div>
      {data.map((item, index) => (
        <div key={index}>{item.name}</div>
      ))}
    </div>
  );
};

// Wrapped with performance monitoring
const MonitoredSlowComponent = withPerformanceMonitoring(
  MySlowComponent,
  'MySlowComponent'
);

// Usage
const App: React.FC = () => {
  const sampleData = Array.from({ length: 1000 }, (_, i) => ({
    name: `Item ${i}`,
  }));

  return (
    <div>
      <h1>Performance Monitoring Example</h1>
      <MonitoredSlowComponent data={sampleData} />
    </div>
  );
};

export default App;
