import { Course } from "../types";

const courseData: Course[] = [
  // Foundation Courses - Building Essential Skills
  {
    id: "coding-foundations-kids",
    slug: "coding-foundations-kids",
    title: "Coding Foundations (Ages 9–14)",
    description:
      "Perfect first step into programming! Learn fundamental concepts through visual programming and interactive projects. No typing skills needed - just curiosity!",
    startDate: "2025-03-15",
    schedule: {
      daysOfWeek: ["Saturday"],
      time: "10:00",
      duration: 90,
    },
    price: "$249",
    outcomes: [
      "Understand core programming concepts: loops, conditions, variables",
      "Create interactive stories and simple games",
      "Develop logical thinking and problem-solving skills",
      "Build confidence with technology and creative expression",
      "Learn debugging techniques and persistence",
    ],
    ageRange: "Ages 9–14",
    maxStudents: 8,
    status: "active",
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z",
    category: "Programming Languages",
    tags: ["beginner", "kids", "scratch", "visual-programming"],
    displayOrder: 1,
    location: "Bit by Bit Learning Center",
  },
  {
    id: "web-foundations-teens-adults",
    slug: "web-foundations-teens-adults",
    title: "Web Development Foundations (Teens & Adults)",
    description:
      "Build your first real website from scratch! Learn HTML, CSS, and JavaScript through hands-on projects. Perfect for career changers, students, or anyone curious about web development.",
    startDate: "2025-03-22",
    schedule: {
      daysOfWeek: ["Thursday"],
      time: "19:00",
      duration: 120,
    },
    price: "$299",
    outcomes: [
      "Understand how websites work and are structured",
      "Write semantic HTML and modern CSS",
      "Add interactivity with JavaScript",
      "Deploy your website live on the internet",
      "Gain confidence to continue learning independently",
    ],
    ageRange: "Ages 14+ (Teens & Adults)",
    maxStudents: 8,
    status: "active",
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z",
    category: "Web Development",
    tags: ["beginner", "teens", "adults", "html", "css", "javascript"],
    displayOrder: 2,
    location: "Bit by Bit Learning Center",
  },

  // Project-Based Courses - Learn by Building Real Things
  {
    id: "weather-app-project",
    slug: "weather-app-project",
    title: "Project: Build a Weather App",
    description:
      "Create a real-time weather application that pulls live data from the internet! Perfect introduction to APIs, data handling, and building something people actually use.",
    startDate: "2025-04-05",
    schedule: {
      daysOfWeek: ["Friday"],
      time: "18:30",
      duration: 90,
    },
    price: "$199",
    outcomes: [
      "Build a fully functional weather app from scratch",
      "Learn to work with real-time data and APIs",
      "Understand how apps get information from the internet",
      "Create beautiful, user-friendly interfaces",
      "Deploy your app so friends and family can use it",
    ],
    ageRange: "Ages 14+ (Teens & Adults)",
    maxStudents: 6,
    status: "active",
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z",
    category: "Web Development",
    tags: ["intermediate", "project", "api", "weather", "javascript"],
    displayOrder: 3,
    location: "Bit by Bit Learning Center",
  },
  {
    id: "personal-finance-tracker",
    slug: "personal-finance-tracker",
    title: "Project: Personal Finance Tracker",
    description:
      "Build a complete expense tracking app with charts, budgets, and insights! Learn database concepts while creating something that helps manage your money.",
    startDate: "2025-04-12",
    schedule: {
      daysOfWeek: ["Saturday"],
      time: "14:00",
      duration: 120,
    },
    price: "$229",
    outcomes: [
      "Create a full-featured personal finance application",
      "Learn database design and data storage",
      "Build interactive charts and visualizations",
      "Understand user authentication and security",
      "Deploy a real app you'll actually use",
    ],
    ageRange: "Ages 16+ (Teens & Adults)",
    maxStudents: 6,
    status: "active",
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z",
    category: "Web Development",
    tags: ["intermediate", "project", "database", "finance", "charts"],
    displayOrder: 4,
    location: "Bit by Bit Learning Center",
  },
];

export default courseData;
