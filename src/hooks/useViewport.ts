import { useState, useEffect } from 'react';

interface ViewportSize {
  width: number;
  height: number;
}

interface UseViewportReturn extends ViewportSize {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  isLandscape: boolean;
}

const BREAKPOINTS = {
  mobile: 768,
  tablet: 1024,
} as const;

export const useViewport = (): UseViewportReturn => {
  const [viewport, setViewport] = useState<ViewportSize>(() => ({
    width: typeof window !== 'undefined' ? window.innerWidth : 0,
    height: typeof window !== 'undefined' ? window.innerHeight : 0,
  }));

  useEffect(() => {
    const handleResize = () => {
      setViewport({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };

    // Debounce resize events for better performance
    let timeoutId: NodeJS.Timeout;
    const debouncedResize = () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(handleResize, 150);
    };

    window.addEventListener('resize', debouncedResize);
    return () => {
      window.removeEventListener('resize', debouncedResize);
      clearTimeout(timeoutId);
    };
  }, []);

  const isMobile = viewport.width < BREAKPOINTS.mobile;
  const isTablet = viewport.width >= BREAKPOINTS.mobile && viewport.width < BREAKPOINTS.tablet;
  const isDesktop = viewport.width >= BREAKPOINTS.tablet;
  const isLandscape = viewport.width > viewport.height;

  return {
    ...viewport,
    isMobile,
    isTablet,
    isDesktop,
    isLandscape,
  };
};
