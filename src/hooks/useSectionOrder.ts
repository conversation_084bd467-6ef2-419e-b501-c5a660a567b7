import { useState, useEffect, useCallback } from "react";

export interface SectionOrderItem {
  id: string;
  type: "watchlist" | "routine" | "role";
  name: string;
  visible: boolean;
}

interface UseSectionOrderOptions {
  locationId?: string;
  viewMode: "routine" | "role";
  defaultSections: SectionOrderItem[];
}

interface UseSectionOrderReturn {
  sectionOrder: SectionOrderItem[];
  updateSectionOrder: (newOrder: SectionOrderItem[]) => void;
  resetToDefault: () => void;
}

/**
 * Custom hook for managing drag-and-drop section ordering in kiosk interface
 * Persists order per location and view mode in localStorage
 */
export const useSectionOrder = ({
  locationId,
  viewMode,
  defaultSections,
}: UseSectionOrderOptions): UseSectionOrderReturn => {
  // Create storage key based on location and view mode
  const storageKey = locationId
    ? `adminbuddy_kiosk_section_order_${locationId}_${viewMode}`
    : `adminbuddy_kiosk_section_order_default_${viewMode}`;

  // Initialize state with stored order or default sections
  const [sectionOrder, setSectionOrder] = useState<SectionOrderItem[]>(() => {
    try {
      const stored = localStorage.getItem(storageKey);
      if (stored) {
        const parsedOrder = JSON.parse(stored) as SectionOrderItem[];

        // Merge with current sections to handle new sections or removed ones
        const mergedSections = mergeSectionOrders(parsedOrder, defaultSections);
        return mergedSections;
      }
      return defaultSections;
    } catch (error) {
      console.warn(`Error reading section order from localStorage:`, error);
      return defaultSections;
    }
  });

  // Update section order and persist to localStorage
  const updateSectionOrder = useCallback(
    (newOrder: SectionOrderItem[]) => {
      try {
        setSectionOrder(newOrder);
        localStorage.setItem(storageKey, JSON.stringify(newOrder));
      } catch (error) {
        console.warn(`Error saving section order to localStorage:`, error);
        // Still update state even if localStorage fails
        setSectionOrder(newOrder);
      }
    },
    [storageKey]
  );

  // Reset to default order
  const resetToDefault = useCallback(() => {
    try {
      setSectionOrder(defaultSections);
      localStorage.removeItem(storageKey);
    } catch (error) {
      console.warn(`Error resetting section order:`, error);
      setSectionOrder(defaultSections);
    }
  }, [defaultSections, storageKey]);

  // Update when default sections change (new routines added, etc.)
  useEffect(() => {
    try {
      const stored = localStorage.getItem(storageKey);
      if (stored) {
        const parsedOrder = JSON.parse(stored) as SectionOrderItem[];
        const mergedSections = mergeSectionOrders(parsedOrder, defaultSections);
        setSectionOrder(mergedSections);
      } else {
        setSectionOrder(defaultSections);
      }
    } catch (error) {
      console.warn(`Error updating section order:`, error);
      setSectionOrder(defaultSections);
    }
  }, [defaultSections, storageKey]);

  return {
    sectionOrder,
    updateSectionOrder,
    resetToDefault,
  };
};

/**
 * Merge stored section order with current sections
 * Handles cases where sections are added/removed
 */
function mergeSectionOrders(
  storedOrder: SectionOrderItem[],
  currentSections: SectionOrderItem[]
): SectionOrderItem[] {
  const result: SectionOrderItem[] = [];
  const currentSectionMap = new Map(currentSections.map((s) => [s.id, s]));
  const addedSections = new Set(currentSections.map((s) => s.id));

  // First, add sections from stored order that still exist
  for (const storedSection of storedOrder) {
    const currentSection = currentSectionMap.get(storedSection.id);
    if (currentSection) {
      result.push({
        ...currentSection,
        visible: storedSection.visible, // Preserve visibility preference
      });
      addedSections.delete(storedSection.id);
    }
  }

  // Then, add any new sections that weren't in stored order
  Array.from(addedSections).forEach((sectionId) => {
    const section = currentSectionMap.get(sectionId);
    if (section) {
      result.push(section);
    }
  });

  return result;
}
