import { useEffect, useRef } from "react";

interface PerformanceMetrics {
  renderTime: number;
  componentName: string;
}

interface UsePerformanceOptions {
  componentName?: string;
  logToConsole?: boolean;
  threshold?: number; // Warn if render time exceeds this (ms)
}

export const usePerformance = (options: UsePerformanceOptions = {}) => {
  // Early return in production to avoid any performance overhead
  if (process.env.NODE_ENV === "production") {
    return;
  }

  // Early return if performance mode is disabled
  if (process.env.REACT_APP_PERFORMANCE_MODE !== "true") {
    return;
  }

  const {
    componentName = "Component",
    logToConsole = true, // Always log in development
    threshold = 16, // 60fps = ~16ms per frame
  } = options;

  const renderStartTime = useRef<number>(performance.now());
  const mountTime = useRef<number>(performance.now());

  useEffect(() => {
    mountTime.current = performance.now();
  }, []);

  useEffect(() => {
    const renderEndTime = performance.now();
    const renderTime = renderEndTime - renderStartTime.current;

    if (logToConsole) {
      const metrics: PerformanceMetrics = {
        renderTime,
        componentName,
      };

      if (renderTime > threshold) {
        console.warn(
          `⚠️ ${componentName} render time: ${renderTime.toFixed(
            2
          )}ms (exceeds ${threshold}ms threshold)`,
          metrics
        );
      } else {
        console.log(
          `✅ ${componentName} render time: ${renderTime.toFixed(2)}ms`,
          metrics
        );
      }
    }

    // Reset for next render
    renderStartTime.current = performance.now();
  });

  // Web Vitals tracking
  useEffect(() => {
    if (typeof window !== "undefined" && "PerformanceObserver" in window) {
      // Track Largest Contentful Paint (LCP)
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        if (logToConsole) {
          console.log(`📊 LCP: ${lastEntry.startTime.toFixed(2)}ms`);
        }
      });

      // Track First Input Delay (FID) - simplified for compatibility
      const fidObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          if (logToConsole) {
            console.log(`📊 Input Delay: ${entry.duration || 0}ms`);
          }
        });
      });

      try {
        lcpObserver.observe({ entryTypes: ["largest-contentful-paint"] });
        fidObserver.observe({ entryTypes: ["first-input"] });
      } catch (e) {
        // Observers not supported
      }

      return () => {
        lcpObserver.disconnect();
        fidObserver.disconnect();
      };
    }
  }, [logToConsole]);
};
