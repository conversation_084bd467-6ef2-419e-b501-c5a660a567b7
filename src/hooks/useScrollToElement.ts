import { useEffect, useRef } from 'react';
import { useLocation } from 'react-router-dom';

interface UseScrollToElementOptions {
  behavior?: ScrollBehavior;
  block?: ScrollLogicalPosition;
  inline?: ScrollLogicalPosition;
}

export const useScrollToElement = <T extends HTMLElement>(
  items: any[],
  findIndex: (item: any, scrollTarget: string) => boolean,
  options: UseScrollToElementOptions = {}
) => {
  const location = useLocation();
  const refs = useRef<(T | null)[]>([]);

  useEffect(() => {
    if (location.state?.scrollTo) {
      const index = items.findIndex(item => 
        findIndex(item, location.state.scrollTo)
      );
      
      if (index !== -1 && refs.current[index]) {
        refs.current[index]?.scrollIntoView({
          behavior: options.behavior || 'smooth',
          block: options.block || 'start',
          inline: options.inline || 'nearest',
        });
      }
    }
  }, [location, items, findIndex, options]);

  const setRef = (index: number) => (el: T | null) => {
    refs.current[index] = el;
  };

  return { setRef };
};
