import { useState, useEffect, useMemo } from "react";
import { Course, UseCoursesReturn } from "../types";
import { getAllCourses } from "../services/firebaseCourseCapacityService";

export const useCourses = (): UseCoursesReturn => {
  const [courses, setCourses] = useState<Course[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCourses = async () => {
      try {
        setLoading(true);
        setError(null);

        console.log("🔍 Fetching courses for customer-facing site...");

        // Get courses from Firebase
        const firebaseCourses = await getAllCourses();

        // Filter to only show active courses to customers
        const activeCourses = firebaseCourses.filter(
          (course) => course.status === "active"
        );

        console.log(
          `📚 Found ${activeCourses.length} active courses for display`
        );
        setCourses(activeCourses);
      } catch (err) {
        console.error("Error fetching courses:", err);
        setError(err instanceof Error ? err.message : "Failed to load courses");
      } finally {
        setLoading(false);
      }
    };

    fetchCourses();
  }, []);

  const getCourseBySlug = useMemo(() => {
    return (slug: string): Course | undefined => {
      return courses.find((course) => course.slug === slug);
    };
  }, [courses]);

  return {
    courses,
    loading,
    error,
    getCourseBySlug,
  };
};
