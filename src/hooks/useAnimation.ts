import { useState, useEffect } from 'react';
import { prefersReducedMotion } from '../utils/accessibility';

interface UseAnimationReturn {
  shouldAnimate: boolean;
  prefersReducedMotion: boolean;
  animationClass: string;
  triggerAnimation: () => void;
}

export const useAnimation = (
  animationName: string = 'fade-in',
  delay: number = 0
): UseAnimationReturn => {
  const [shouldAnimate, setShouldAnimate] = useState(false);
  const [hasTriggered, setHasTriggered] = useState(false);
  const reducedMotion = prefersReducedMotion();

  const triggerAnimation = () => {
    if (!hasTriggered && !reducedMotion) {
      setTimeout(() => {
        setShouldAnimate(true);
        setHasTriggered(true);
      }, delay);
    }
  };

  useEffect(() => {
    // Auto-trigger if no delay and reduced motion is not preferred
    if (delay === 0 && !reducedMotion && !hasTriggered) {
      triggerAnimation();
    }
  }, [delay, reducedMotion, hasTriggered]);

  const animationClass = shouldAnimate && !reducedMotion ? `animate-${animationName}` : '';

  return {
    shouldAnimate: shouldAnimate && !reducedMotion,
    prefersReducedMotion: reducedMotion,
    animationClass,
    triggerAnimation,
  };
};
