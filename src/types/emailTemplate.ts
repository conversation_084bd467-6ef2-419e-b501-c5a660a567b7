/**
 * Email template types and interfaces
 */

export interface EmailTemplate {
  id: string;
  name: string;
  subject: string;
  body: string;
  category: EmailTemplateCategory;
  variables: string[]; // Available variables like {{studentName}}, {{courseName}}
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
}

export type EmailTemplateCategory = 
  | 'registration'
  | 'payment'
  | 'course'
  | 'waitlist'
  | 'general'
  | 'reminder';

export interface EmailTemplateVariable {
  key: string;
  description: string;
  example: string;
}

export interface CreateEmailTemplateData {
  name: string;
  subject: string;
  body: string;
  category: EmailTemplateCategory;
  variables: string[];
}

export interface UpdateEmailTemplateData extends Partial<CreateEmailTemplateData> {
  id: string;
  isActive?: boolean;
}

// Predefined template variables
export const EMAIL_TEMPLATE_VARIABLES: EmailTemplateVariable[] = [
  {
    key: '{{studentName}}',
    description: 'Student\'s full name',
    example: '<PERSON>'
  },
  {
    key: '{{firstName}}',
    description: 'Student\'s first name',
    example: '<PERSON>'
  },
  {
    key: '{{lastName}}',
    description: 'Student\'s last name',
    example: '<PERSON>'
  },
  {
    key: '{{courseName}}',
    description: 'Course title',
    example: 'Introduction to Programming with Scratch'
  },
  {
    key: '{{coursePrice}}',
    description: 'Course price',
    example: '$299'
  },
  {
    key: '{{startDate}}',
    description: 'Course start date',
    example: 'January 15, 2024'
  },
  {
    key: '{{endDate}}',
    description: 'Course end date',
    example: 'February 19, 2024'
  },
  {
    key: '{{paymentStatus}}',
    description: 'Current payment status',
    example: 'Pending'
  },
  {
    key: '{{enrollmentStatus}}',
    description: 'Current enrollment status',
    example: 'Enrolled'
  },
  {
    key: '{{waitlistPosition}}',
    description: 'Position on waitlist',
    example: '3'
  },
  {
    key: '{{parentEmail}}',
    description: 'Parent/guardian email',
    example: '<EMAIL>'
  },
  {
    key: '{{phone}}',
    description: 'Contact phone number',
    example: '(*************'
  },
  {
    key: '{{emergencyContact}}',
    description: 'Emergency contact information',
    example: 'Jane Smith - (*************'
  },
  {
    key: '{{adminName}}',
    description: 'Admin/instructor name',
    example: 'Derek MacDonald'
  },
  {
    key: '{{adminEmail}}',
    description: 'Admin contact email',
    example: '<EMAIL>'
  },
  {
    key: '{{companyName}}',
    description: 'Company name',
    example: 'Bit by Bit'
  },
  {
    key: '{{currentDate}}',
    description: 'Current date',
    example: 'December 15, 2024'
  }
];

// Default email templates
export const DEFAULT_EMAIL_TEMPLATES: Omit<EmailTemplate, 'id' | 'createdAt' | 'updatedAt' | 'createdBy'>[] = [
  {
    name: 'Registration Confirmation',
    subject: 'Welcome to {{courseName}} - Registration Confirmed!',
    body: `Hi {{firstName}},

Great news! Your registration for {{courseName}} has been confirmed.

Course Details:
- Course: {{courseName}}
- Price: {{coursePrice}}
- Start Date: {{startDate}}
- End Date: {{endDate}}
- Status: {{enrollmentStatus}}

Payment Status: {{paymentStatus}}

We're excited to have you join us! If you have any questions, please don't hesitate to reach out.

Best regards,
{{adminName}}
{{companyName}}
{{adminEmail}}`,
    category: 'registration',
    variables: ['{{firstName}}', '{{courseName}}', '{{coursePrice}}', '{{startDate}}', '{{endDate}}', '{{enrollmentStatus}}', '{{paymentStatus}}', '{{adminName}}', '{{companyName}}', '{{adminEmail}}'],
    isActive: true
  },
  {
    name: 'Payment Reminder',
    subject: 'Payment Reminder for {{courseName}}',
    body: `Hi {{firstName}},

This is a friendly reminder that payment for {{courseName}} is still pending.

Course Details:
- Course: {{courseName}}
- Amount Due: {{coursePrice}}
- Start Date: {{startDate}}

Please complete your payment to secure your spot in the course. If you have any questions about payment options, please contact us.

Thank you!

{{adminName}}
{{companyName}}
{{adminEmail}}`,
    category: 'payment',
    variables: ['{{firstName}}', '{{courseName}}', '{{coursePrice}}', '{{startDate}}', '{{adminName}}', '{{companyName}}', '{{adminEmail}}'],
    isActive: true
  },
  {
    name: 'Waitlist Notification',
    subject: 'You\'re on the waitlist for {{courseName}}',
    body: `Hi {{firstName}},

Thank you for your interest in {{courseName}}! The course is currently full, but we've added you to our waitlist.

Your current position: #{{waitlistPosition}}

We'll notify you immediately if a spot becomes available. In the meantime, feel free to explore our other available courses.

Thank you for your patience!

{{adminName}}
{{companyName}}
{{adminEmail}}`,
    category: 'waitlist',
    variables: ['{{firstName}}', '{{courseName}}', '{{waitlistPosition}}', '{{adminName}}', '{{companyName}}', '{{adminEmail}}'],
    isActive: true
  },
  {
    name: 'Spot Available - Waitlist',
    subject: 'Great news! A spot opened up in {{courseName}}',
    body: `Hi {{firstName}},

Excellent news! A spot has opened up in {{courseName}} and we'd love to have you join us.

Course Details:
- Course: {{courseName}}
- Price: {{coursePrice}}
- Start Date: {{startDate}}
- End Date: {{endDate}}

Please reply to this email within 24 hours to confirm your enrollment. If we don't hear from you, we'll offer the spot to the next person on the waitlist.

Looking forward to having you in class!

{{adminName}}
{{companyName}}
{{adminEmail}}`,
    category: 'waitlist',
    variables: ['{{firstName}}', '{{courseName}}', '{{coursePrice}}', '{{startDate}}', '{{endDate}}', '{{adminName}}', '{{companyName}}', '{{adminEmail}}'],
    isActive: true
  },
  {
    name: 'Course Starting Soon',
    subject: 'Your {{courseName}} course starts soon!',
    body: `Hi {{firstName}},

Just a friendly reminder that your {{courseName}} course starts on {{startDate}}.

What to bring:
- Just your curiosity and enthusiasm! We provide all computers and software.
- Parents are welcome to observe.

If you have any questions or need to make any changes, please contact us as soon as possible.

We're excited to see you in class!

{{adminName}}
{{companyName}}
{{adminEmail}}`,
    category: 'reminder',
    variables: ['{{firstName}}', '{{courseName}}', '{{startDate}}', '{{adminName}}', '{{companyName}}', '{{adminEmail}}'],
    isActive: true
  }
];
