// Firebase configuration for AdminBuddy
import { initializeApp } from "firebase/app";
import { getFirestore, connectFirestoreEmulator } from "firebase/firestore";
import { getAuth, connectAuthEmulator } from "firebase/auth";
import { getStorage, connectStorageEmulator } from "firebase/storage";
import { getFunctions, connectFunctionsEmulator } from "firebase/functions";

// Firebase configuration object
const firebaseConfig = {
  apiKey: "AIzaSyCDtTEuXAROJTlBmVe4GZggjDu_nWHsB_o",
  authDomain: "adminbuddy.firebaseapp.com",
  projectId: "adminbuddy",
  storageBucket: "adminbuddy.firebasestorage.app",
  messagingSenderId: "291736946612",
  appId: "1:291736946612:web:7fee3309d96244410814e1",
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firestore
export const db = getFirestore(app);

// Initialize Auth
export const auth = getAuth(app);

// Initialize Storage
export const storage = getStorage(app);

// Initialize Functions
export const functions = getFunctions(app);

// Configure for development (optional)
if (
  process.env.NODE_ENV === "development" &&
  process.env.REACT_APP_USE_FIREBASE_EMULATOR === "true"
) {
  // Connect to Firebase emulators if running locally
  try {
    connectFirestoreEmulator(db, "localhost", 8080);
    connectAuthEmulator(auth, "http://localhost:9099");
    connectStorageEmulator(storage, "localhost", 9199);
    connectFunctionsEmulator(functions, "localhost", 5001);
    console.log("🔥 Connected to Firebase emulators");
  } catch (error) {
    console.log("Firebase emulators already connected or not available");
  }
}

// Export the initialized app
export default app;

// Collection names (centralized for consistency)
export const COLLECTIONS = {
  // AdminBuddy Collections
  TENANTS: "tenants",
  USERS: "users",
  LOCATIONS: "locations",
  TASK_GROUPS: "taskGroups",
  TASKS: "tasks",
  TASK_COMPLETIONS: "taskCompletions",
  SHIFT_HANDOFFS: "shiftHandoffs",

  // Legacy Collections (for reference)
  REGISTRATIONS: "registrations",
  COURSE_CAPACITIES: "courseCapacities",
  SESSIONS: "sessions",
  ATTENDANCE: "attendance",
  ADMIN_SETTINGS: "adminSettings",
} as const;

// Admin configuration
export const ADMIN_CONFIG = {
  ADMIN_EMAIL:
    process.env.REACT_APP_ADMIN_EMAIL || "<EMAIL>",
} as const;

// Stripe configuration
export const STRIPE_CONFIG = {
  publishableKey:
    "pk_test_51RW6bV2Z0fYxSFF6oKpYqp8KZSZuBRR4T7SlbCWdNpovaUfI7VP2BhnBD2vJI6Rb4Oz0cr0lYRb6EuTkqeFEDT1a000fPUj6A1",
  // Secret key will be stored in Firebase Functions environment variables
  priceIds: {
    monthly: "price_1RW6mc2Z0fYxSFF6k5UZjqEy",
    yearly: "price_1RW6nz2Z0fYxSFF6XkbEBouZ",
  },
} as const;
