/**
 * Price formatting utilities
 * Handles automatic dollar sign formatting for prices
 */

/**
 * Format a numeric price value to display with dollar sign
 * @param price - The price value (can be string or number)
 * @returns Formatted price string with dollar sign (e.g., "$299")
 */
export const formatPrice = (price: string | number | undefined | null): string => {
  if (!price && price !== 0) {
    return "N/A";
  }

  // Convert to string and remove any existing dollar signs and non-numeric characters except decimal
  const cleanPrice = String(price).replace(/[^0-9.]/g, "");
  
  // Parse as number
  const numericPrice = parseFloat(cleanPrice);
  
  // Return N/A if not a valid number
  if (isNaN(numericPrice)) {
    return "N/A";
  }
  
  // Format with dollar sign
  return `$${numericPrice.toFixed(0)}`;
};

/**
 * Extract numeric value from a price string
 * @param price - The price string (e.g., "$299" or "299")
 * @returns Numeric price value
 */
export const parsePrice = (price: string | number | undefined | null): number => {
  if (!price && price !== 0) {
    return 0;
  }

  // Convert to string and remove any non-numeric characters except decimal
  const cleanPrice = String(price).replace(/[^0-9.]/g, "");
  
  // Parse as number
  const numericPrice = parseFloat(cleanPrice);
  
  // Return 0 if not a valid number
  return isNaN(numericPrice) ? 0 : numericPrice;
};

/**
 * Validate if a price input is valid
 * @param price - The price input value
 * @returns True if valid, false otherwise
 */
export const isValidPrice = (price: string | number): boolean => {
  if (!price && price !== 0) {
    return false;
  }

  const numericPrice = parsePrice(price);
  return numericPrice >= 0;
};

/**
 * Clean price input for storage (removes dollar signs, keeps numeric value)
 * @param price - The price input value
 * @returns Clean numeric string for storage
 */
export const cleanPriceForStorage = (price: string | number): string => {
  const numericPrice = parsePrice(price);
  return numericPrice.toString();
};

/**
 * Format price for display in tables and lists
 * @param price - The price value
 * @returns Formatted price with dollar sign or "N/A"
 */
export const displayPrice = (price: string | number | undefined | null): string => {
  return formatPrice(price);
};

/**
 * Calculate total revenue from an array of registrations
 * @param registrations - Array of registration objects with coursePrice field
 * @returns Total revenue as number
 */
export const calculateTotalRevenue = (registrations: any[]): number => {
  return registrations.reduce((sum, reg) => {
    const price = parsePrice(reg.coursePrice);
    return sum + price;
  }, 0);
};

/**
 * Format currency for analytics displays
 * @param amount - The numeric amount
 * @returns Formatted currency string (e.g., "$1,299")
 */
export const formatCurrency = (amount: number): string => {
  return `$${amount.toLocaleString()}`;
};
