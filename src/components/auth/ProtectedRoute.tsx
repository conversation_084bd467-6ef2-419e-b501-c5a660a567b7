import React from "react";
import { Navigate, useLocation } from "react-router-dom";
import LoadingSpinner from "../ui/LoadingSpinner";
import { useTenant } from "../../contexts/TenantContext";

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  allowedRoles?: string[];
  redirectTo?: string;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requireAuth = true,
  allowedRoles = [],
  redirectTo = "/login",
}) => {
  const location = useLocation();
  const { userProfile, loading: tenantLoading } = useTenant();

  // If we don't require auth, just render children
  if (!requireAuth) {
    return <>{children}</>;
  }

  // Show loading while TenantContext is still loading
  if (tenantLoading) {
    return (
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          minHeight: "100vh",
          background: "#f8fafc",
        }}
      >
        <LoadingSpinner size="large" />
      </div>
    );
  }

  // Check if user is authenticated
  if (!userProfile) {
    console.log("🔒 ProtectedRoute: No authenticated user found");
    // Store the attempted URL for redirect after login
    localStorage.setItem(
      "adminbuddy_redirect_after_login",
      location.pathname + location.search
    );
    return <Navigate to={redirectTo} replace />;
  }

  // Check role-based access
  if (
    allowedRoles.length > 0 &&
    userProfile.role &&
    !allowedRoles.includes(userProfile.role)
  ) {
    console.log(
      `🔒 ProtectedRoute: User role '${userProfile.role}' not in allowed roles:`,
      allowedRoles
    );

    // Special handling for kiosk users - redirect them to kiosk interface
    if (userProfile.role === "kiosk") {
      console.log("🏪 Redirecting kiosk user to kiosk interface");
      const kioskUrl = userProfile.kioskLocationId
        ? `/kiosk/${userProfile.kioskLocationId}`
        : "/kiosk";
      return <Navigate to={kioskUrl} replace />;
    }

    return (
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          minHeight: "100vh",
          flexDirection: "column",
          background: "#f8fafc",
          padding: "2rem",
        }}
      >
        <h2 style={{ color: "#dc2626", marginBottom: "1rem" }}>
          Access Denied
        </h2>
        <p style={{ color: "#6b7280", textAlign: "center", maxWidth: "400px" }}>
          You don't have permission to access this page. Your role is '
          {userProfile.role}' but this page requires one of:{" "}
          {allowedRoles.join(", ")}.
        </p>
        <button
          onClick={() => window.history.back()}
          style={{
            marginTop: "1rem",
            padding: "0.5rem 1rem",
            background: "#3b82f6",
            color: "white",
            border: "none",
            borderRadius: "6px",
            cursor: "pointer",
          }}
        >
          Go Back
        </button>
      </div>
    );
  }

  console.log(
    "✅ ProtectedRoute: Authentication successful for user:",
    userProfile.email
  );
  return <>{children}</>;
};

export default ProtectedRoute;
