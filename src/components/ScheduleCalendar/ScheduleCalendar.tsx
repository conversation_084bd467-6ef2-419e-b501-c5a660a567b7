import React, { useState, useMemo } from "react";
import Calendar from "react-calendar";
import {
  Location,
  Routine,
  LocationRoutineSchedule,
} from "../../services/adminBuddyFirebaseService";
import "react-calendar/dist/Calendar.css";
import "./ScheduleCalendar.css";

interface ScheduleCalendarProps {
  locations: Location[];
  routines: Routine[];
  schedules: LocationRoutineSchedule[];
  selectedLocationId?: string;
  onLocationChange: (locationId: string) => void;
}

type ValuePiece = Date | null;
type Value = ValuePiece | [ValuePiece, ValuePiece];

const ScheduleCalendar: React.FC<ScheduleCalendarProps> = ({
  locations,
  routines,
  schedules,
  selectedLocationId,
  onLocationChange,
}) => {
  const [value, setValue] = useState<Value>(new Date());

  // Auto-select first location if none selected and locations exist
  React.useEffect(() => {
    if (!selectedLocationId && locations.length > 0) {
      onLocationChange(locations[0].id);
    }
  }, [selectedLocationId, locations, onLocationChange]);

  // Helper function to check if a schedule should run on a given date
  const shouldScheduleRun = (
    schedule: LocationRoutineSchedule,
    date: Date
  ): boolean => {
    const dayOfWeek = date.getDay();

    switch (schedule.cadence) {
      case "daily":
        return true;

      case "weekly":
        return schedule.daysOfWeek.includes(dayOfWeek);

      case "biweekly": {
        const epochTime = date.getTime();
        const weeksSinceEpoch = Math.floor(
          epochTime / (1000 * 60 * 60 * 24 * 7)
        );
        const isBiweeklyWeek = weeksSinceEpoch % 2 === 0;
        return isBiweeklyWeek && schedule.daysOfWeek.includes(dayOfWeek);
      }

      case "monthly": {
        // Run on the first occurrence of specified days each month
        const firstOfMonth = new Date(date.getFullYear(), date.getMonth(), 1);
        const lastOfMonth = new Date(
          date.getFullYear(),
          date.getMonth() + 1,
          0
        );

        for (const targetDay of schedule.daysOfWeek) {
          const firstOccurrence = new Date(firstOfMonth);
          while (
            firstOccurrence.getDay() !== targetDay &&
            firstOccurrence <= lastOfMonth
          ) {
            firstOccurrence.setDate(firstOccurrence.getDate() + 1);
          }

          if (
            firstOccurrence.getDate() === date.getDate() &&
            firstOccurrence.getMonth() === date.getMonth()
          ) {
            return true;
          }
        }
        return false;
      }

      case "quarterly": {
        // Run on the first occurrence of specified days each quarter
        const quarter = Math.floor(date.getMonth() / 3);
        const firstOfQuarter = new Date(date.getFullYear(), quarter * 3, 1);
        const lastOfQuarter = new Date(date.getFullYear(), quarter * 3 + 3, 0);

        for (const targetDay of schedule.daysOfWeek) {
          const firstOccurrence = new Date(firstOfQuarter);
          while (
            firstOccurrence.getDay() !== targetDay &&
            firstOccurrence <= lastOfQuarter
          ) {
            firstOccurrence.setDate(firstOccurrence.getDate() + 1);
          }

          if (
            firstOccurrence.getDate() === date.getDate() &&
            firstOccurrence.getMonth() === date.getMonth()
          ) {
            return true;
          }
        }
        return false;
      }

      case "yearly": {
        // Run on the first occurrence of specified days each year
        const firstOfYear = new Date(date.getFullYear(), 0, 1);
        const lastOfYear = new Date(date.getFullYear(), 11, 31);

        for (const targetDay of schedule.daysOfWeek) {
          const firstOccurrence = new Date(firstOfYear);
          while (
            firstOccurrence.getDay() !== targetDay &&
            firstOccurrence <= lastOfYear
          ) {
            firstOccurrence.setDate(firstOccurrence.getDate() + 1);
          }

          if (
            firstOccurrence.getDate() === date.getDate() &&
            firstOccurrence.getMonth() === date.getMonth()
          ) {
            return true;
          }
        }
        return false;
      }

      default:
        return schedule.daysOfWeek.includes(dayOfWeek);
    }
  };

  // Get routines for a specific date
  const getRoutinesForDate = (date: Date) => {
    if (!selectedLocationId) return [];

    const locationSchedules = schedules.filter(
      (s) => s.locationId === selectedLocationId
    );

    const dayRoutines: Array<{
      routine: Routine;
      schedule: LocationRoutineSchedule;
    }> = [];

    for (const schedule of locationSchedules) {
      if (shouldScheduleRun(schedule, date)) {
        const routine = routines.find((r) => r.id === schedule.routineId);
        if (routine) {
          dayRoutines.push({ routine, schedule });
        }
      }
    }

    return dayRoutines;
  };

  // Custom tile content to show routines
  const tileContent = ({ date, view }: { date: Date; view: string }) => {
    if (view !== "month") return null;

    const routines = getRoutinesForDate(date);
    if (routines.length === 0) return null;

    return (
      <div className="calendar-tile-content">
        {routines.slice(0, 2).map((item, index) => (
          <div
            key={`${item.routine.id}-${index}`}
            className={`routine-indicator ${item.schedule.cadence}`}
            title={`${item.routine.name} (${item.schedule.cadence})`}
          >
            {item.routine.name}
          </div>
        ))}
        {routines.length > 2 && (
          <div className="routine-overflow">+{routines.length - 2} more</div>
        )}
      </div>
    );
  };

  // Custom tile class name to highlight days with routines
  const tileClassName = ({ date, view }: { date: Date; view: string }) => {
    if (view !== "month") return null;

    const routines = getRoutinesForDate(date);
    return routines.length > 0 ? "has-routines" : null;
  };

  return (
    <div className="schedule-calendar">
      <div className="calendar-header">
        <div className="calendar-controls">
          <div className="location-selector">
            <label htmlFor="calendar-location">Location:</label>
            <select
              id="calendar-location"
              value={selectedLocationId || ""}
              onChange={(e) => onLocationChange(e.target.value)}
            >
              <option value="">Select a location</option>
              {locations.map((location) => (
                <option key={location.id} value={location.id}>
                  {location.name}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {!selectedLocationId ? (
        <div className="calendar-empty-state">
          <p>Select a location to view scheduled routines</p>
        </div>
      ) : (
        <>
          <div className="react-calendar-wrapper">
            <Calendar
              value={value}
              onChange={setValue}
              tileContent={tileContent}
              tileClassName={tileClassName}
              showNeighboringMonth={false}
              next2Label={null}
              prev2Label={null}
            />
          </div>

          <div className="calendar-legend">
            <h4>Legend:</h4>
            <div className="legend-items">
              <div className="legend-item">
                <div className="legend-color daily"></div>
                <span>Daily</span>
              </div>
              <div className="legend-item">
                <div className="legend-color weekly"></div>
                <span>Weekly</span>
              </div>
              <div className="legend-item">
                <div className="legend-color biweekly"></div>
                <span>Bi-weekly</span>
              </div>
              <div className="legend-item">
                <div className="legend-color monthly"></div>
                <span>Monthly</span>
              </div>
              <div className="legend-item">
                <div className="legend-color quarterly"></div>
                <span>Quarterly</span>
              </div>
              <div className="legend-item">
                <div className="legend-color yearly"></div>
                <span>Yearly</span>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default ScheduleCalendar;
