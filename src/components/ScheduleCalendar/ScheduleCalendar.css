.schedule-calendar {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.calendar-header {
  margin-bottom: 1.5rem;
}

.calendar-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
  flex-wrap: wrap;
}

.location-selector {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.location-selector label {
  font-weight: 600;
  color: #374151;
}

.location-selector select {
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  font-size: 0.875rem;
  min-width: 200px;
}

.month-navigation {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.nav-button {
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 0.5rem 0.75rem;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.2s;
}

.nav-button:hover {
  background: #e5e7eb;
}

.current-month {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  min-width: 200px;
  text-align: center;
}

.today-button {
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s;
}

.today-button:hover {
  background: #2563eb;
}

.calendar-empty-state {
  text-align: center;
  padding: 3rem;
  color: #6b7280;
  font-size: 1.1rem;
}

/* React Calendar Wrapper */
.react-calendar-wrapper {
  margin: 1rem 0;
}

.react-calendar-wrapper .react-calendar {
  width: 100%;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  font-family: inherit;
  line-height: 1.125em;
}

.react-calendar-wrapper .react-calendar__tile {
  min-height: 120px;
  padding: 0.5rem;
  background: white;
  border: none;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.react-calendar-wrapper .react-calendar__tile:enabled:hover,
.react-calendar-wrapper .react-calendar__tile:enabled:focus {
  background: #f3f4f6;
}

.react-calendar-wrapper .react-calendar__tile--now {
  background: #eff6ff;
}

.react-calendar-wrapper .react-calendar__tile--now:enabled:hover,
.react-calendar-wrapper .react-calendar__tile--now:enabled:focus {
  background: #dbeafe;
}

.react-calendar-wrapper .react-calendar__tile.has-routines {
  background: #fefce8;
}

.react-calendar-wrapper .react-calendar__tile.has-routines:enabled:hover,
.react-calendar-wrapper .react-calendar__tile.has-routines:enabled:focus {
  background: #fef3c7;
}

.react-calendar-wrapper .react-calendar__tile--now.has-routines {
  background: #dbeafe;
}

.react-calendar-wrapper .react-calendar__month-view__weekdays {
  text-align: center;
  text-transform: uppercase;
  font-weight: bold;
  font-size: 0.75em;
  color: #6b7280;
}

.react-calendar-wrapper .react-calendar__month-view__weekdays__weekday {
  padding: 0.5em;
}

.react-calendar-wrapper .react-calendar__navigation {
  display: flex;
  height: 44px;
  margin-bottom: 1em;
}

.react-calendar-wrapper .react-calendar__navigation button {
  min-width: 44px;
  background: none;
  border: 1px solid #d1d5db;
  color: #374151;
  font-size: 16px;
  font-weight: 500;
}

.react-calendar-wrapper .react-calendar__navigation button:enabled:hover,
.react-calendar-wrapper .react-calendar__navigation button:enabled:focus {
  background-color: #f3f4f6;
}

.react-calendar-wrapper .react-calendar__navigation button[disabled] {
  background-color: #f9fafb;
  color: #9ca3af;
}

/* Calendar tile content */
.calendar-tile-content {
  margin-top: 0.25rem;
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
  width: 100%;
}

.routine-indicator {
  font-size: 0.75rem;
  padding: 0.125rem 0.25rem;
  border-radius: 3px;
  color: white;
  font-weight: 500;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  min-width: 0;
  width: 100%;
  box-sizing: border-box;
}

.routine-indicator.daily {
  background: #ef4444;
}

.routine-indicator.weekly {
  background: #3b82f6;
}

.routine-indicator.biweekly {
  background: #8b5cf6;
}

.routine-indicator.monthly {
  background: #10b981;
}

.routine-indicator.quarterly {
  background: #f59e0b;
}

.routine-indicator.yearly {
  background: #6b7280;
}

.routine-overflow {
  font-size: 0.625rem;
  color: #6b7280;
  font-weight: 500;
  margin-top: 0.125rem;
}

.calendar-legend {
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
}

.calendar-legend h4 {
  margin: 0 0 0.75rem 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
}

.legend-items {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 3px;
}

.legend-color.daily {
  background: #ef4444;
}

.legend-color.weekly {
  background: #3b82f6;
}

.legend-color.biweekly {
  background: #8b5cf6;
}

.legend-color.monthly {
  background: #10b981;
}

.legend-color.quarterly {
  background: #f59e0b;
}

.legend-color.yearly {
  background: #6b7280;
}

.legend-item span {
  font-size: 0.875rem;
  color: #374151;
}

/* Responsive Design */
@media (max-width: 768px) {
  .calendar-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .location-selector {
    justify-content: space-between;
  }

  .location-selector select {
    min-width: auto;
    flex: 1;
  }

  .month-navigation {
    justify-content: center;
  }

  .calendar-day {
    min-height: 80px;
    padding: 0.25rem;
  }

  .day-number {
    font-size: 0.75rem;
  }

  .routine-indicator {
    font-size: 0.625rem;
    padding: 0.125rem;
  }

  .legend-items {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .schedule-calendar {
    padding: 1rem;
  }

  .calendar-day {
    min-height: 60px;
  }

  .current-month {
    font-size: 1rem;
    min-width: auto;
  }
}
