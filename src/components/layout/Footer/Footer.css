.footer {
  background-color: #0a2d69;
  color: white;
  padding: 3rem 2rem 1rem;
  width: 100%;
  box-sizing: border-box;
}

.footer__content {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 3rem;
  margin-bottom: 2rem;
}

.footer__brand h3 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  color: white;
}

.footer__brand p {
  color: #e8f4f8;
  margin: 0;
  font-style: italic;
}

.footer__links {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
}

.footer__link-group h4 {
  font-size: 1rem;
  margin-bottom: 1rem;
  color: white;
  font-weight: 600;
}

.footer__link-group a {
  display: block;
  color: #e8f4f8;
  text-decoration: none;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  transition: color 0.2s ease;
}

.footer__link-group a:hover {
  color: white;
  text-decoration: underline;
}

.footer__bottom {
  max-width: 1200px;
  margin: 0 auto;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.footer__bottom p {
  margin: 0;
  color: #e8f4f8;
  font-size: 0.9rem;
}

.footer__legal {
  display: flex;
  gap: 1.5rem;
}

.footer__legal a {
  color: #e8f4f8;
  text-decoration: none;
  font-size: 0.9rem;
  transition: color 0.2s ease;
}

.footer__legal a:hover {
  color: white;
  text-decoration: underline;
}

@media (max-width: 768px) {
  .footer {
    padding: 2rem 1rem 1rem;
  }

  .footer__content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .footer__links {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .footer__bottom {
    flex-direction: column;
    text-align: center;
  }

  .footer__legal {
    justify-content: center;
  }
}
