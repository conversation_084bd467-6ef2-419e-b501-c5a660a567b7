.header {
  height: 64px;
  padding: 0 1rem;
  background-color: #0a2d69;
  color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1000;
}

@media (min-width: 768px) {
  .header {
    padding: 0 1.5rem;
  }
}

.header__brand-link {
  text-decoration: none;
  color: inherit;
  flex: 1;
  transition: opacity 0.2s ease;
}

.header__brand-link:hover {
  opacity: 0.9;
  text-decoration: none; /* Ensure no underline on hover */
}

.header__brand-link:focus {
  text-decoration: none; /* Ensure no underline on focus */
  outline: 2px solid #ffd46f;
  outline-offset: 2px;
}

.header__brand-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 0.25rem;
}

/* Header button overrides for dark background */
.header__cta .btn--secondary,
.header__mobile-cta .btn--secondary {
  background-color: transparent;
  color: white;
  border: 2px solid white;
}

.header__cta .btn--secondary:hover:not(:disabled),
.header__mobile-cta .btn--secondary:hover:not(:disabled) {
  background-color: white;
  color: #0a2d69;
  border-color: white;
}

.header__brand-name {
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1;
  color: white;
}

@media (min-width: 768px) {
  .header__brand-name {
    font-size: 1.8rem;
  }
}

.header__tagline {
  font-size: 0.75rem;
  font-style: italic;
  color: #e8f4f8;
  margin-top: -0.25rem;
  border-top: 1px solid rgba(255, 212, 111, 0.3);
  padding-top: 0.25rem;
}

@media (min-width: 768px) {
  .header__tagline {
    font-size: 0.85rem;
  }
}

/* Desktop Navigation */
.header__nav-container {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.header__nav {
  display: flex;
  gap: 1rem;
}

.header__cta {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.header__nav-link {
  color: white;
  text-decoration: none;
  position: relative;
  padding: 0.5rem 0;
  transition: color 0.3s ease;
  font-size: 0.95rem;
}

.header__nav-link:hover {
  color: #ffd46f;
}

.header__nav-link--active::after {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 3px;
  background-color: #ffd46f;
  border-radius: 2px;
}

/* Mobile Navigation */
.header__mobile-toggle {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  z-index: 1001;
}

.header__hamburger {
  display: flex;
  flex-direction: column;
  width: 24px;
  height: 18px;
  justify-content: space-between;
}

.header__hamburger span {
  display: block;
  height: 2px;
  width: 100%;
  background-color: white;
  transition: all 0.3s ease;
  transform-origin: center;
}

.header__hamburger.open span:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.header__hamburger.open span:nth-child(2) {
  opacity: 0;
}

.header__hamburger.open span:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

.header__nav--mobile {
  position: fixed;
  top: 64px;
  right: -100%;
  width: 250px;
  height: calc(100vh - 64px);
  background-color: #0a2d69;
  flex-direction: column;
  padding: 2rem 1.5rem;
  transition: right 0.3s ease;
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
  z-index: 999;
}

.header__nav--mobile.open {
  right: 0;
}

.header__nav--mobile .header__nav-link {
  padding: 1rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 1.1rem;
}

.header__nav--mobile .header__nav-link:last-child {
  border-bottom: none;
}

.header__nav--mobile .header__nav-link--active::after {
  display: none;
}

.header__nav--mobile .header__nav-link--active {
  color: #ffd46f;
  font-weight: 600;
}

.header__mobile-cta {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.header__overlay {
  position: fixed;
  top: 64px;
  left: 0;
  width: 100%;
  height: calc(100vh - 64px);
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 998;
}
