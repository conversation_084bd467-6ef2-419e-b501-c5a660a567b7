import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { checkFeatureAccess, FeatureAccess } from "../../services/featureEnforcementService";
import "./LocationLimitGuard.css";

interface LocationLimitGuardProps {
  userProfile: any;
  currentLocationCount: number;
  onCanCreate: () => void;
  children: React.ReactNode;
}

const LocationLimitGuard: React.FC<LocationLimitGuardProps> = ({
  userProfile,
  currentLocationCount,
  onCanCreate,
  children,
}) => {
  const [featureAccess, setFeatureAccess] = useState<FeatureAccess | null>(null);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    const checkAccess = async () => {
      try {
        const access = await checkFeatureAccess(userProfile, currentLocationCount);
        setFeatureAccess(access);
        
        if (access.canCreateLocations) {
          onCanCreate();
        }
      } catch (error) {
        console.error("Error checking feature access:", error);
      } finally {
        setLoading(false);
      }
    };

    if (userProfile) {
      checkAccess();
    } else {
      setLoading(false);
    }
  }, [userProfile, currentLocationCount, onCanCreate]);

  const handleUpgrade = () => {
    navigate("/billing");
  };

  if (loading) {
    return (
      <div className="location-limit-guard location-limit-guard--loading">
        <div className="location-limit-guard__spinner"></div>
        <p>Checking access permissions...</p>
      </div>
    );
  }

  if (!featureAccess || !featureAccess.canCreateLocations) {
    return (
      <div className="location-limit-guard location-limit-guard--blocked">
        <div className="location-limit-guard__content">
          <div className="location-limit-guard__icon">🚫</div>
          <h3>Location Limit Reached</h3>
          <p>{featureAccess?.reason || "You cannot create more locations."}</p>
          
          {featureAccess?.maxLocations && (
            <div className="location-limit-guard__stats">
              <div className="limit-stat">
                <span className="limit-stat__label">Current:</span>
                <span className="limit-stat__value">{currentLocationCount}</span>
              </div>
              <div className="limit-stat">
                <span className="limit-stat__label">Limit:</span>
                <span className="limit-stat__value">{featureAccess.maxLocations}</span>
              </div>
            </div>
          )}
          
          <div className="location-limit-guard__actions">
            <button 
              className="btn btn--primary"
              onClick={handleUpgrade}
            >
              Upgrade Plan
            </button>
          </div>
        </div>
      </div>
    );
  }

  // User can create locations - render children
  return <>{children}</>;
};

export default LocationLimitGuard;
