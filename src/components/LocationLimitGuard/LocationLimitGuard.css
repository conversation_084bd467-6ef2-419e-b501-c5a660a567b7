.location-limit-guard {
  padding: 2rem;
  border-radius: 12px;
  background: white;
  border: 1px solid #e1e5e9;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.location-limit-guard--loading {
  text-align: center;
  color: #6b7280;
}

.location-limit-guard__spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #0a2d69;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.location-limit-guard--blocked {
  border-color: #f87171;
  background: #fef2f2;
}

.location-limit-guard__content {
  text-align: center;
  max-width: 400px;
  margin: 0 auto;
}

.location-limit-guard__icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.location-limit-guard__content h3 {
  color: #dc2626;
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.location-limit-guard__content p {
  color: #7f1d1d;
  font-size: 1rem;
  line-height: 1.5;
  margin-bottom: 1.5rem;
}

.location-limit-guard__stats {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-bottom: 2rem;
  padding: 1rem;
  background: rgba(239, 68, 68, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.limit-stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
}

.limit-stat__label {
  font-size: 0.875rem;
  color: #7f1d1d;
  font-weight: 500;
}

.limit-stat__value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #dc2626;
}

.location-limit-guard__actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.btn {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn--primary {
  background: #0a2d69;
  color: white;
}

.btn--primary:hover {
  background: #1e40af;
  transform: translateY(-1px);
}

/* Responsive design */
@media (max-width: 768px) {
  .location-limit-guard {
    padding: 1.5rem;
  }

  .location-limit-guard__stats {
    flex-direction: column;
    gap: 1rem;
  }

  .location-limit-guard__content h3 {
    font-size: 1.25rem;
  }

  .location-limit-guard__content p {
    font-size: 0.875rem;
  }
}
