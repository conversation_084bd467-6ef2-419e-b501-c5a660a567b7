import React, { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { canAccessFeatures } from "../../services/trialService";
import { UserProfile } from "../../services/adminBuddyFirebaseService";
import "./TrialExpirationGuard.css";

interface TrialExpirationGuardProps {
  userProfile: UserProfile | null;
  children: React.ReactNode;
  redirectTo?: string;
  showBlockedMessage?: boolean;
}

const TrialExpirationGuard: React.FC<TrialExpirationGuardProps> = ({
  userProfile,
  children,
  redirectTo = "/billing",
  showBlockedMessage = false,
}) => {
  const navigate = useNavigate();

  useEffect(() => {
    if (userProfile) {
      const hasAccess = canAccessFeatures(userProfile);
      
      if (!hasAccess) {
        // Store current URL for redirect after subscription
        const currentUrl = window.location.pathname + window.location.search;
        localStorage.setItem("adminbuddy_redirect_after_subscription", currentUrl);
        
        if (showBlockedMessage) {
          // Don't redirect immediately, show blocked message
          return;
        }
        
        // Redirect to billing
        navigate(redirectTo);
      }
    }
  }, [userProfile, navigate, redirectTo, showBlockedMessage]);

  // If user profile is loading, show loading state
  if (!userProfile) {
    return (
      <div className="trial-guard trial-guard--loading">
        <div className="trial-guard__spinner"></div>
        <p>Loading...</p>
      </div>
    );
  }

  const hasAccess = canAccessFeatures(userProfile);

  // If trial expired and we should show blocked message
  if (!hasAccess && showBlockedMessage) {
    return (
      <div className="trial-guard trial-guard--blocked">
        <div className="trial-guard__content">
          <div className="trial-guard__icon">⏰</div>
          <h2>Trial Expired</h2>
          <p>
            Your 14-day free trial has ended. Subscribe now to continue using AdminBuddy 
            and keep your team organized.
          </p>
          
          <div className="trial-guard__features">
            <h3>What you'll keep with a subscription:</h3>
            <ul>
              <li>✅ Unlimited task coordination</li>
              <li>✅ Multi-location support</li>
              <li>✅ Real-time reporting</li>
              <li>✅ Kiosk interface</li>
              <li>✅ Priority support</li>
            </ul>
          </div>
          
          <div className="trial-guard__actions">
            <button 
              className="btn btn--primary btn--large"
              onClick={() => navigate("/billing")}
            >
              Subscribe Now
            </button>
          </div>
          
          <p className="trial-guard__note">
            Cancel anytime • Secure payment by Stripe
          </p>
        </div>
      </div>
    );
  }

  // If trial expired and we should redirect, return null (redirect happens in useEffect)
  if (!hasAccess) {
    return null;
  }

  // User has access - render children
  return <>{children}</>;
};

export default TrialExpirationGuard;
