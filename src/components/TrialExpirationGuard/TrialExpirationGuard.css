.trial-guard {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8fafc;
  padding: 2rem;
}

.trial-guard--loading {
  text-align: center;
  color: #6b7280;
}

.trial-guard__spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #0a2d69;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.trial-guard--blocked {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.trial-guard__content {
  background: white;
  border-radius: 16px;
  padding: 3rem;
  max-width: 500px;
  width: 100%;
  text-align: center;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.trial-guard__icon {
  font-size: 4rem;
  margin-bottom: 1.5rem;
}

.trial-guard__content h2 {
  color: #1f2937;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.trial-guard__content > p {
  color: #6b7280;
  font-size: 1.125rem;
  line-height: 1.6;
  margin-bottom: 2rem;
}

.trial-guard__features {
  text-align: left;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
}

.trial-guard__features h3 {
  color: #374151;
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 1rem;
  text-align: center;
}

.trial-guard__features ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.trial-guard__features li {
  padding: 0.5rem 0;
  color: #4b5563;
  font-size: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.trial-guard__actions {
  margin-bottom: 1.5rem;
}

.btn {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn--primary {
  background: linear-gradient(135deg, #0a2d69 0%, #1e40af 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(10, 45, 105, 0.3);
}

.btn--primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(10, 45, 105, 0.4);
}

.btn--large {
  padding: 1rem 2rem;
  font-size: 1.125rem;
}

.trial-guard__note {
  color: #9ca3af;
  font-size: 0.875rem;
  margin: 0;
}

/* Responsive design */
@media (max-width: 768px) {
  .trial-guard {
    padding: 1rem;
  }

  .trial-guard__content {
    padding: 2rem;
  }

  .trial-guard__content h2 {
    font-size: 1.5rem;
  }

  .trial-guard__content > p {
    font-size: 1rem;
  }

  .trial-guard__features {
    padding: 1rem;
  }

  .trial-guard__features h3 {
    font-size: 1rem;
  }

  .trial-guard__features li {
    font-size: 0.875rem;
  }
}
