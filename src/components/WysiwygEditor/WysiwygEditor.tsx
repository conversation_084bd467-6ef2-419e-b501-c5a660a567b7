import React, { useState, useEffect } from "react";
// @ts-ignore
import { Editor } from "react-draft-wysiwyg";
// @ts-ignore
import { EditorState, ContentState, convertToRaw } from "draft-js";
// @ts-ignore
import draftToHtml from "draftjs-to-html";
// @ts-ignore
import htmlToDraft from "html-to-draftjs";
import "react-draft-wysiwyg/dist/react-draft-wysiwyg.css";
import "./WysiwygEditor.css";

interface WysiwygEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
}

const WysiwygEditor: React.FC<WysiwygEditorProps> = ({
  value,
  onChange,
  placeholder = "Enter your announcement...",
  className = "",
}) => {
  const [editorState, setEditorState] = useState(() => {
    if (value) {
      const contentBlock = htmlToDraft(value);
      if (contentBlock) {
        const contentState = ContentState.createFromBlockArray(
          contentBlock.contentBlocks
        );
        return EditorState.createWithContent(contentState);
      }
    }
    return EditorState.createEmpty();
  });

  // Update editor state when value prop changes
  useEffect(() => {
    if (value) {
      const contentBlock = htmlToDraft(value);
      if (contentBlock) {
        const contentState = ContentState.createFromBlockArray(
          contentBlock.contentBlocks
        );
        const newEditorState = EditorState.createWithContent(contentState);
        setEditorState(newEditorState);
      }
    } else {
      setEditorState(EditorState.createEmpty());
    }
  }, [value]);

  const onEditorStateChange = (newEditorState: EditorState) => {
    setEditorState(newEditorState);

    // Convert to HTML and call onChange
    const rawContentState = convertToRaw(newEditorState.getCurrentContent());
    const html = draftToHtml(rawContentState);
    onChange(html);
  };

  const toolbarConfig = {
    options: ["inline", "blockType", "list", "textAlign", "remove"],
    inline: {
      options: ["bold", "italic", "underline"],
      bold: { className: "wysiwyg-toolbar-btn" },
      italic: { className: "wysiwyg-toolbar-btn" },
      underline: { className: "wysiwyg-toolbar-btn" },
    },
    blockType: {
      options: ["Normal", "H3"],
      className: "wysiwyg-toolbar-dropdown",
    },
    list: {
      options: ["unordered", "ordered"],
      unordered: { className: "wysiwyg-toolbar-btn" },
      ordered: { className: "wysiwyg-toolbar-btn" },
    },
    textAlign: {
      options: ["left", "center"],
      left: { className: "wysiwyg-toolbar-btn" },
      center: { className: "wysiwyg-toolbar-btn" },
    },
    remove: {
      className: "wysiwyg-toolbar-btn",
    },
  };

  return (
    <div className={`wysiwyg-editor-wrapper ${className}`}>
      <Editor
        editorState={editorState}
        onEditorStateChange={onEditorStateChange}
        toolbar={toolbarConfig}
        placeholder={placeholder}
        editorClassName="wysiwyg-editor-content"
        toolbarClassName="wysiwyg-editor-toolbar"
        wrapperClassName="wysiwyg-editor-main"
      />
    </div>
  );
};

export default WysiwygEditor;
