import React, { useState, useEffect, useRef } from "react";
// @ts-ignore
import { Editor } from "react-draft-wysiwyg";
// @ts-ignore
import { EditorState, ContentState, convertToRaw } from "draft-js";
// @ts-ignore
import draftToHtml from "draftjs-to-html";
// @ts-ignore
import htmlToDraft from "html-to-draftjs";
import "react-draft-wysiwyg/dist/react-draft-wysiwyg.css";
import "./WysiwygEditor.css";

interface WysiwygEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
}

const WysiwygEditor: React.FC<WysiwygEditorProps> = ({
  value,
  onChange,
  placeholder = "Enter your announcement...",
  className = "",
}) => {
  const [editorState, setEditorState] = useState(() => {
    if (value) {
      const contentBlock = htmlToDraft(value);
      if (contentBlock) {
        const contentState = ContentState.createFromBlockArray(
          contentBlock.contentBlocks
        );
        return EditorState.createWithContent(contentState);
      }
    }
    // Create empty state and move focus to end to ensure cursor visibility
    const emptyState = EditorState.createEmpty();
    return EditorState.moveFocusToEnd(emptyState);
  });

  const [lastValue, setLastValue] = useState(value);
  const editorRef = useRef<any>(null);

  // Only update editor state when value prop changes from external source (not from typing)
  useEffect(() => {
    // Only update if the value changed from an external source
    if (value !== lastValue) {
      setLastValue(value);

      if (value) {
        const contentBlock = htmlToDraft(value);
        if (contentBlock) {
          const contentState = ContentState.createFromBlockArray(
            contentBlock.contentBlocks
          );
          const newEditorState = EditorState.createWithContent(contentState);
          setEditorState(newEditorState);
        }
      } else {
        const emptyState = EditorState.createEmpty();
        setEditorState(EditorState.moveFocusToEnd(emptyState));
      }
    }
  }, [value, lastValue]);

  // Auto-focus the editor when it mounts
  useEffect(() => {
    const timer = setTimeout(() => {
      if (editorRef.current) {
        // Force focus on the editor
        editorRef.current.focusEditor();

        // If the editor is empty, ensure cursor is visible by moving selection to start
        if (!value || value.trim() === "" || value === "<p></p>") {
          const newEditorState = EditorState.moveFocusToEnd(editorState);
          setEditorState(newEditorState);
        }
      }
    }, 150); // Slightly longer delay to ensure everything is ready

    return () => clearTimeout(timer);
  }, [editorState, value]);

  const onEditorStateChange = (newEditorState: EditorState) => {
    setEditorState(newEditorState);

    // Convert to HTML and call onChange
    const rawContentState = convertToRaw(newEditorState.getCurrentContent());
    const html = draftToHtml(rawContentState);

    // Update our tracked value to prevent useEffect from triggering
    setLastValue(html);
    onChange(html);
  };

  const toolbarConfig = {
    options: ["inline", "blockType", "list", "textAlign", "remove"],
    inline: {
      options: ["bold", "italic", "underline"],
      bold: { className: "wysiwyg-toolbar-btn" },
      italic: { className: "wysiwyg-toolbar-btn" },
      underline: { className: "wysiwyg-toolbar-btn" },
    },
    blockType: {
      options: ["Normal", "H3"],
      className: "wysiwyg-toolbar-dropdown",
    },
    list: {
      options: ["unordered", "ordered"],
      unordered: { className: "wysiwyg-toolbar-btn" },
      ordered: { className: "wysiwyg-toolbar-btn" },
    },
    textAlign: {
      options: ["left", "center"],
      left: { className: "wysiwyg-toolbar-btn" },
      center: { className: "wysiwyg-toolbar-btn" },
    },
    remove: {
      className: "wysiwyg-toolbar-btn",
    },
  };

  return (
    <div className={`wysiwyg-editor-wrapper ${className}`}>
      <Editor
        ref={editorRef}
        editorState={editorState}
        onEditorStateChange={onEditorStateChange}
        toolbar={toolbarConfig}
        placeholder={placeholder}
        editorClassName="wysiwyg-editor-content"
        toolbarClassName="wysiwyg-editor-toolbar"
        wrapperClassName="wysiwyg-editor-main"
      />
    </div>
  );
};

export default WysiwygEditor;
