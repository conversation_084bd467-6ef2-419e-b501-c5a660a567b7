import React, { useMemo } from "react";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";
import "./WysiwygEditor.css";

interface WysiwygEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
}

const WysiwygEditor: React.FC<WysiwygEditorProps> = ({
  value,
  onChange,
  placeholder = "Enter your announcement...",
  className = "",
}) => {
  // Configure toolbar with essential formatting options
  const modules = useMemo(
    () => ({
      toolbar: [
        ["bold", "italic", "underline"],
        [{ list: "ordered" }, { list: "bullet" }],
        [{ header: [3, false] }],
        ["clean"],
      ],
    }),
    []
  );

  // Configure formats allowed in the editor
  const formats = ["bold", "italic", "underline", "list", "bullet", "header"];

  return (
    <div className={`wysiwyg-editor-wrapper ${className}`}>
      <ReactQuill
        theme="snow"
        value={value}
        onChange={onChange}
        modules={modules}
        formats={formats}
        placeholder={placeholder}
        className="wysiwyg-editor-quill"
      />
    </div>
  );
};

export default WysiwygEditor;
