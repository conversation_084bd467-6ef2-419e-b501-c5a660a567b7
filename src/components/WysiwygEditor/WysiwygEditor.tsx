import React, { useState, useRef, useEffect } from "react";
import "./WysiwygEditor.css";

interface WysiwygEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
}

const WysiwygEditor: React.FC<WysiwygEditorProps> = ({
  value,
  onChange,
  placeholder = "Enter your announcement...",
  className = "",
}) => {
  const editorRef = useRef<HTMLDivElement>(null);
  const [isInitialized, setIsInitialized] = useState(false);

  // Initialize editor content
  useEffect(() => {
    if (editorRef.current && !isInitialized) {
      editorRef.current.innerHTML = value || "";
      setIsInitialized(true);
    }
  }, [value, isInitialized]);

  // Handle content changes
  const handleInput = () => {
    if (editorRef.current) {
      const content = editorRef.current.innerHTML;
      onChange(content);
    }
  };

  // Format commands
  const execCommand = (command: string, value?: string) => {
    document.execCommand(command, false, value);
    editorRef.current?.focus();
    handleInput();
  };

  // Handle keyboard shortcuts
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.ctrlKey || e.metaKey) {
      switch (e.key) {
        case "b":
          e.preventDefault();
          execCommand("bold");
          break;
        case "i":
          e.preventDefault();
          execCommand("italic");
          break;
        case "u":
          e.preventDefault();
          execCommand("underline");
          break;
      }
    }
  };

  return (
    <div className={`wysiwyg-editor ${className}`}>
      {/* Toolbar */}
      <div className="wysiwyg-toolbar">
        <button
          type="button"
          className="wysiwyg-btn"
          onClick={() => execCommand("bold")}
          title="Bold (Ctrl+B)"
        >
          <strong>B</strong>
        </button>
        <button
          type="button"
          className="wysiwyg-btn"
          onClick={() => execCommand("italic")}
          title="Italic (Ctrl+I)"
        >
          <em>I</em>
        </button>
        <button
          type="button"
          className="wysiwyg-btn"
          onClick={() => execCommand("underline")}
          title="Underline (Ctrl+U)"
        >
          <u>U</u>
        </button>
        <div className="wysiwyg-separator" />
        <button
          type="button"
          className="wysiwyg-btn"
          onClick={() => execCommand("insertUnorderedList")}
          title="Bullet List"
        >
          • List
        </button>
        <button
          type="button"
          className="wysiwyg-btn"
          onClick={() => execCommand("insertOrderedList")}
          title="Numbered List"
        >
          1. List
        </button>
        <div className="wysiwyg-separator" />
        <button
          type="button"
          className="wysiwyg-btn"
          onClick={() => execCommand("formatBlock", "h3")}
          title="Heading"
        >
          H
        </button>
        <button
          type="button"
          className="wysiwyg-btn"
          onClick={() => execCommand("formatBlock", "p")}
          title="Paragraph"
        >
          P
        </button>
      </div>

      {/* Editor */}
      <div
        ref={editorRef}
        className="wysiwyg-content"
        contentEditable
        onInput={handleInput}
        onKeyDown={handleKeyDown}
        data-placeholder={placeholder}
        suppressContentEditableWarning={true}
      />
    </div>
  );
};

export default WysiwygEditor;
