.wysiwyg-editor {
  border: 1px solid #ddd;
  border-radius: 8px;
  background: white;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.wysiwyg-toolbar {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background: #f8f9fa;
  border-bottom: 1px solid #ddd;
  gap: 4px;
  flex-wrap: wrap;
}

.wysiwyg-btn {
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 6px 10px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  transition: all 0.2s ease;
  min-width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.wysiwyg-btn:hover {
  background: #e9ecef;
  border-color: #adb5bd;
}

.wysiwyg-btn:active {
  background: #dee2e6;
  transform: translateY(1px);
}

.wysiwyg-separator {
  width: 1px;
  height: 24px;
  background: #ddd;
  margin: 0 4px;
}

.wysiwyg-content {
  min-height: 120px;
  max-height: 300px;
  overflow-y: auto;
  padding: 16px;
  font-size: 16px;
  line-height: 1.5;
  color: #333;
  outline: none;
  word-wrap: break-word;
}

.wysiwyg-content:empty::before {
  content: attr(data-placeholder);
  color: #999;
  font-style: italic;
}

.wysiwyg-content h1,
.wysiwyg-content h2,
.wysiwyg-content h3,
.wysiwyg-content h4,
.wysiwyg-content h5,
.wysiwyg-content h6 {
  margin: 16px 0 8px 0;
  font-weight: 600;
  color: #2c3e50;
}

.wysiwyg-content h3 {
  font-size: 1.25em;
}

.wysiwyg-content p {
  margin: 8px 0;
}

.wysiwyg-content ul,
.wysiwyg-content ol {
  margin: 8px 0;
  padding-left: 24px;
}

.wysiwyg-content li {
  margin: 4px 0;
}

.wysiwyg-content strong {
  font-weight: 600;
}

.wysiwyg-content em {
  font-style: italic;
}

.wysiwyg-content u {
  text-decoration: underline;
}

/* Kiosk-specific styling */
.wysiwyg-editor.kiosk-editor {
  border: 2px solid #0a2d69;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.wysiwyg-editor.kiosk-editor .wysiwyg-toolbar {
  background: linear-gradient(135deg, #0a2d69 0%, #1e4a8c 100%);
  border-bottom: none;
}

.wysiwyg-editor.kiosk-editor .wysiwyg-btn {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: #0a2d69;
  font-weight: 600;
}

.wysiwyg-editor.kiosk-editor .wysiwyg-btn:hover {
  background: white;
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.wysiwyg-editor.kiosk-editor .wysiwyg-separator {
  background: rgba(255, 255, 255, 0.3);
}

.wysiwyg-editor.kiosk-editor .wysiwyg-content {
  font-size: 18px;
  min-height: 150px;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .wysiwyg-toolbar {
    padding: 6px 8px;
  }
  
  .wysiwyg-btn {
    padding: 4px 8px;
    font-size: 13px;
    min-width: 28px;
    height: 28px;
  }
  
  .wysiwyg-content {
    padding: 12px;
    font-size: 16px;
    min-height: 100px;
  }
  
  .wysiwyg-editor.kiosk-editor .wysiwyg-content {
    font-size: 17px;
    min-height: 120px;
  }
}
