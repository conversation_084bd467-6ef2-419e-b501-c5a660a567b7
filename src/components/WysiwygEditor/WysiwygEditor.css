/* React Quill Editor Wrapper */
.wysiwyg-editor-wrapper {
  border-radius: 8px;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

/* Override React Quill default styles for light theme */
.wysiwyg-editor-quill .ql-toolbar {
  background: #f8f9fa;
  border: 1px solid #e1e5e9;
  border-bottom: 2px solid #e1e5e9;
  border-radius: 8px 8px 0 0;
  padding: 12px;
}

.wysiwyg-editor-quill .ql-container {
  border: 1px solid #e1e5e9;
  border-top: none;
  border-radius: 0 0 8px 8px;
  background: white;
  font-size: 16px;
}

.wysiwyg-editor-quill .ql-editor {
  min-height: 120px;
  max-height: 300px;
  padding: 16px;
  line-height: 1.6;
  color: #2c3e50;
}

.wysiwyg-editor-quill .ql-editor.ql-blank::before {
  color: #999;
  font-style: italic;
  left: 16px;
}

/* Toolbar button styling */
.wysiwyg-editor-quill .ql-toolbar .ql-formats {
  margin-right: 12px;
}

.wysiwyg-editor-quill .ql-toolbar button {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  margin: 0 2px;
  padding: 6px 8px;
  color: #495057;
  transition: all 0.2s ease;
}

.wysiwyg-editor-quill .ql-toolbar button:hover {
  background: #e9ecef;
  border-color: #0a2d69;
  color: #0a2d69;
}

.wysiwyg-editor-quill .ql-toolbar button.ql-active {
  background: #0a2d69;
  border-color: #0a2d69;
  color: white;
}

.wysiwyg-editor-quill .ql-toolbar .ql-picker {
  color: #495057;
}

.wysiwyg-editor-quill .ql-toolbar .ql-picker-label {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 6px 8px;
  transition: all 0.2s ease;
}

.wysiwyg-editor-quill .ql-toolbar .ql-picker-label:hover {
  background: #e9ecef;
  border-color: #0a2d69;
  color: #0a2d69;
}

/* Content styling */
.wysiwyg-editor-quill .ql-editor h3 {
  font-size: 1.25em;
  font-weight: 600;
  color: #0a2d69;
  margin: 16px 0 8px 0;
}

.wysiwyg-editor-quill .ql-editor p {
  margin: 8px 0;
}

.wysiwyg-editor-quill .ql-editor ul,
.wysiwyg-editor-quill .ql-editor ol {
  margin: 8px 0;
  padding-left: 24px;
}

.wysiwyg-editor-quill .ql-editor li {
  margin: 4px 0;
}

.wysiwyg-editor-quill .ql-editor strong {
  font-weight: 600;
  color: #0a2d69;
}

/* Kiosk-specific styling for React Quill */
.wysiwyg-editor-wrapper.kiosk-editor {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.wysiwyg-editor-wrapper.kiosk-editor .wysiwyg-editor-quill .ql-toolbar {
  background: #f8f9fa;
  border: 2px solid #e1e5e9;
  border-radius: 12px 12px 0 0;
  padding: 16px;
}

.wysiwyg-editor-wrapper.kiosk-editor .wysiwyg-editor-quill .ql-container {
  border: 2px solid #e1e5e9;
  border-top: none;
  border-radius: 0 0 12px 12px;
}

.wysiwyg-editor-wrapper.kiosk-editor .wysiwyg-editor-quill .ql-editor {
  font-size: 16px;
  min-height: 120px;
  padding: 20px;
}

.wysiwyg-editor-wrapper.kiosk-editor .wysiwyg-editor-quill .ql-toolbar button {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  margin: 0 3px;
  padding: 8px 10px;
  font-weight: 500;
}

.wysiwyg-editor-wrapper.kiosk-editor
  .wysiwyg-editor-quill
  .ql-toolbar
  button:hover {
  background: #e9ecef;
  border-color: #0a2d69;
  color: #0a2d69;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.wysiwyg-editor-wrapper.kiosk-editor
  .wysiwyg-editor-quill
  .ql-toolbar
  .ql-picker-label {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 8px 10px;
  font-weight: 500;
}

.wysiwyg-editor-wrapper.kiosk-editor
  .wysiwyg-editor-quill
  .ql-toolbar
  .ql-picker-label:hover {
  background: #e9ecef;
  border-color: #0a2d69;
  color: #0a2d69;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Mobile responsive */
@media (max-width: 768px) {
  .wysiwyg-toolbar {
    padding: 6px 8px;
  }

  .wysiwyg-btn {
    padding: 4px 8px;
    font-size: 13px;
    min-width: 28px;
    height: 28px;
  }

  .wysiwyg-content {
    padding: 12px;
    font-size: 16px;
    min-height: 100px;
  }

  .wysiwyg-editor.kiosk-editor .wysiwyg-content {
    font-size: 17px;
    min-height: 120px;
  }
}
