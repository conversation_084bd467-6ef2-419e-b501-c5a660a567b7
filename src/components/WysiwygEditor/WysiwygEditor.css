/* React Draft WYSIWYG Editor Wrapper */
.wysiwyg-editor-wrapper {
  border-radius: 12px;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  border: 2px solid #e1e5e9;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

/* Main editor wrapper */
.wysiwyg-editor-main {
  border: none !important;
}

/* Toolbar styling */
.wysiwyg-editor-toolbar {
  background: #f8f9fa !important;
  border: none !important;
  border-bottom: 2px solid #e1e5e9 !important;
  padding: 16px !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  flex-wrap: wrap !important;
}

/* Toolbar button groups */
.wysiwyg-editor-toolbar .rdw-option-wrapper {
  background: white !important;
  border: 1px solid #dee2e6 !important;
  border-radius: 6px !important;
  margin: 0 2px !important;
  padding: 0 !important;
  min-width: 36px !important;
  height: 36px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: all 0.2s ease !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

.wysiwyg-editor-toolbar .rdw-option-wrapper:hover {
  background: #e9ecef !important;
  border-color: #0a2d69 !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15) !important;
}

.wysiwyg-editor-toolbar .rdw-option-wrapper.rdw-option-active {
  background: #0a2d69 !important;
  border-color: #0a2d69 !important;
  color: white !important;
}

/* Toolbar icons - Override default orange/red colors */
.wysiwyg-editor-toolbar .rdw-option-wrapper img {
  width: 16px !important;
  height: 16px !important;
  /* Convert to neutral gray */
  filter: brightness(0) saturate(100%) invert(40%) sepia(0%) saturate(0%)
    hue-rotate(0deg) brightness(95%) contrast(95%) !important;
}

.wysiwyg-editor-toolbar .rdw-option-wrapper:hover img {
  /* Convert to blue on hover */
  filter: brightness(0) saturate(100%) invert(12%) sepia(74%) saturate(1537%)
    hue-rotate(201deg) brightness(95%) contrast(102%) !important;
}

.wysiwyg-editor-toolbar .rdw-option-wrapper.rdw-option-active img {
  /* Convert to white when active */
  filter: brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(7500%)
    hue-rotate(109deg) brightness(105%) contrast(105%) !important;
}

/* Override any default React Draft WYSIWYG colors */
.wysiwyg-editor-toolbar .rdw-option-wrapper {
  color: #495057 !important;
}

.wysiwyg-editor-toolbar .rdw-option-wrapper:hover {
  color: #0a2d69 !important;
}

.wysiwyg-editor-toolbar .rdw-option-wrapper.rdw-option-active {
  color: white !important;
}

/* Fix any SVG or icon colors that might be showing through */
.wysiwyg-editor-toolbar .rdw-option-wrapper svg {
  fill: #495057 !important;
}

.wysiwyg-editor-toolbar .rdw-option-wrapper:hover svg {
  fill: #0a2d69 !important;
}

.wysiwyg-editor-toolbar .rdw-option-wrapper.rdw-option-active svg {
  fill: white !important;
}

/* Dropdown styling */
.wysiwyg-editor-toolbar .rdw-dropdown-wrapper {
  background: white !important;
  border: 1px solid #dee2e6 !important;
  border-radius: 6px !important;
  margin: 0 2px !important;
  height: 36px !important;
  min-width: 80px !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

.wysiwyg-editor-toolbar .rdw-dropdown-wrapper:hover {
  border-color: #0a2d69 !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15) !important;
}

.wysiwyg-editor-toolbar .rdw-dropdown-selectedtext {
  color: #495057 !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  padding: 8px 12px !important;
}

.wysiwyg-editor-toolbar .rdw-dropdown-carettoopen,
.wysiwyg-editor-toolbar .rdw-dropdown-carettoclose {
  right: 8px !important;
}

.wysiwyg-editor-toolbar .rdw-dropdown-optionwrapper {
  background: white !important;
  border: 1px solid #dee2e6 !important;
  border-radius: 6px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  margin-top: 4px !important;
}

.wysiwyg-editor-toolbar .rdw-dropdownoption-default {
  padding: 8px 12px !important;
  font-size: 14px !important;
  color: #495057 !important;
  border-bottom: 1px solid #f1f3f4 !important;
}

.wysiwyg-editor-toolbar .rdw-dropdownoption-default:hover {
  background: #f8f9fa !important;
  color: #0a2d69 !important;
}

.wysiwyg-editor-toolbar .rdw-dropdownoption-active {
  background: #0a2d69 !important;
  color: white !important;
}

/* Additional overrides to ensure consistent theming */
.wysiwyg-editor-wrapper .rdw-editor-toolbar {
  background: #f8f9fa !important;
  border-bottom: 2px solid #e1e5e9 !important;
}

/* Override any remaining default colors from React Draft WYSIWYG */
.wysiwyg-editor-wrapper .rdw-option-wrapper {
  background: white !important;
  border: 1px solid #dee2e6 !important;
  color: #495057 !important;
}

.wysiwyg-editor-wrapper .rdw-option-wrapper:hover {
  background: #e9ecef !important;
  border-color: #0a2d69 !important;
  color: #0a2d69 !important;
}

.wysiwyg-editor-wrapper .rdw-option-wrapper.rdw-option-active {
  background: #0a2d69 !important;
  border-color: #0a2d69 !important;
  color: white !important;
}

/* Ensure dropdown arrows and carets match theme */
.wysiwyg-editor-wrapper .rdw-dropdown-carettoopen,
.wysiwyg-editor-wrapper .rdw-dropdown-carettoclose {
  border-top-color: #495057 !important;
}

.wysiwyg-editor-wrapper .rdw-dropdown-wrapper:hover .rdw-dropdown-carettoopen,
.wysiwyg-editor-wrapper .rdw-dropdown-wrapper:hover .rdw-dropdown-carettoclose {
  border-top-color: #0a2d69 !important;
}

/* Content area styling */
.wysiwyg-editor-content {
  min-height: 120px !important;
  max-height: 300px !important;
  padding: 20px !important;
  font-size: 16px !important;
  line-height: 1.6 !important;
  color: #2c3e50 !important;
  background: white !important;
  border: none !important;
  overflow-y: auto !important;
}

.wysiwyg-editor-content:focus {
  outline: none !important;
}

/* Content styling */
.wysiwyg-editor-content h3 {
  font-size: 1.25em !important;
  font-weight: 600 !important;
  color: #0a2d69 !important;
  margin: 16px 0 8px 0 !important;
}

.wysiwyg-editor-content p {
  margin: 8px 0 !important;
}

.wysiwyg-editor-content ul,
.wysiwyg-editor-content ol {
  margin: 8px 0 !important;
  padding-left: 24px !important;
}

.wysiwyg-editor-content li {
  margin: 4px 0 !important;
}

.wysiwyg-editor-content strong {
  font-weight: 600 !important;
  color: #0a2d69 !important;
}

/* Placeholder styling */
.wysiwyg-editor-content .public-DraftEditorPlaceholder-root {
  color: #999 !important;
  font-style: italic !important;
  position: absolute !important;
  pointer-events: none !important;
}

/* Kiosk-specific enhancements */
.wysiwyg-editor-wrapper.kiosk-editor {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

.wysiwyg-editor-wrapper.kiosk-editor .wysiwyg-editor-toolbar {
  padding: 20px !important;
}

.wysiwyg-editor-wrapper.kiosk-editor
  .wysiwyg-editor-toolbar
  .rdw-option-wrapper {
  min-width: 40px !important;
  height: 40px !important;
  margin: 0 3px !important;
}

.wysiwyg-editor-wrapper.kiosk-editor
  .wysiwyg-editor-toolbar
  .rdw-dropdown-wrapper {
  height: 40px !important;
  min-width: 100px !important;
}

.wysiwyg-editor-wrapper.kiosk-editor .wysiwyg-editor-content {
  padding: 24px !important;
  font-size: 17px !important;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .wysiwyg-editor-toolbar {
    padding: 12px !important;
    gap: 4px !important;
  }

  .wysiwyg-editor-toolbar .rdw-option-wrapper {
    min-width: 32px !important;
    height: 32px !important;
    margin: 0 1px !important;
  }

  .wysiwyg-editor-toolbar .rdw-dropdown-wrapper {
    height: 32px !important;
    min-width: 70px !important;
  }

  .wysiwyg-editor-content {
    padding: 16px !important;
    font-size: 15px !important;
    min-height: 100px !important;
  }

  .wysiwyg-editor-wrapper.kiosk-editor .wysiwyg-editor-content {
    padding: 18px !important;
    font-size: 16px !important;
  }
}
