.setup-wizard {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.setup-wizard__overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.setup-wizard__container {
  position: relative;
  background: white;
  border-radius: 16px;
  width: 100%;
  max-width: 900px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
}

.setup-wizard__header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 2rem 2rem 1rem;
  border-bottom: 1px solid #e2e8f0;
}

.setup-wizard__title h2 {
  margin: 0 0 0.5rem 0;
  color: #0a2d69;
  font-size: 1.5rem;
}

.setup-wizard__title p {
  margin: 0;
  color: #666;
  font-size: 1rem;
}

.setup-wizard__close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #666;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.setup-wizard__close:hover {
  background: #f1f5f9;
}

.setup-wizard__progress {
  padding: 1rem 2rem;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #e2e8f0;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #0a2d69 0%, #1e4a8c 100%);
  transition: width 0.3s ease;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-text {
  font-size: 0.9rem;
  color: #666;
  font-weight: 500;
}

.refresh-button {
  background: none;
  border: none;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.refresh-button:hover {
  background: #f1f5f9;
}

.refresh-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.refresh-button.refreshing {
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.setup-wizard__content {
  flex: 1;
  overflow-y: auto;
  padding: 2rem;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.setup-wizard__steps {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.setup-step {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  border-radius: 8px;
  border: 2px solid transparent;
  transition: all 0.2s;
}

.setup-step--current {
  border-color: #0a2d69;
  background: #f0f4ff;
}

.setup-step--completed {
  background: #f0fdf4;
  border-color: #10b981;
}

.setup-step__icon {
  font-size: 1.5rem;
  flex-shrink: 0;
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: white;
  border: 2px solid #e2e8f0;
}

.setup-step--current .setup-step__icon {
  border-color: #0a2d69;
  background: #0a2d69;
  color: white;
}

.setup-step--completed .setup-step__icon {
  border-color: #10b981;
  background: #10b981;
  color: white;
}

.setup-step__content h3 {
  margin: 0 0 0.25rem 0;
  color: #1a1a1a;
  font-size: 1rem;
}

.setup-step__content p {
  margin: 0 0 0.5rem 0;
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
}

.setup-step__required {
  background: #fef3c7;
  color: #92400e;
  padding: 0.125rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.setup-step__actions {
  margin-top: 0.5rem;
}

.completed-badge {
  color: #10b981;
  font-weight: 600;
  font-size: 0.875rem;
}

.setup-wizard__current-step {
  background: #f8fafc;
  border-radius: 12px;
  padding: 1.5rem;
  height: fit-content;
}

.current-step__header {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.current-step__icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.current-step__header h3 {
  margin: 0 0 0.5rem 0;
  color: #0a2d69;
  font-size: 1.25rem;
}

.current-step__header p {
  margin: 0;
  color: #666;
  line-height: 1.5;
}

.current-step__tips h4 {
  margin: 0 0 1rem 0;
  color: #0a2d69;
  font-size: 1rem;
}

.current-step__tips ul {
  margin: 0;
  padding-left: 1.5rem;
  color: #666;
}

.current-step__tips li {
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

.setup-wizard__actions {
  padding: 1.5rem 2rem;
  border-top: 1px solid #e2e8f0;
  background: #f8fafc;
}

.setup-wizard__quick-start {
  background: white;
  border: 2px solid #0a2d69;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  text-align: center;
}

.setup-wizard__quick-start h4 {
  margin: 0 0 0.5rem 0;
  color: #0a2d69;
  font-size: 1rem;
}

.setup-wizard__quick-start p {
  margin: 0 0 1rem 0;
  color: #666;
  font-size: 0.9rem;
}

.setup-wizard__navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.setup-wizard__nav-buttons {
  display: flex;
  gap: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .setup-wizard {
    padding: 1rem;
  }

  .setup-wizard__container {
    max-height: 95vh;
  }

  .setup-wizard__content {
    grid-template-columns: 1fr;
    padding: 1rem;
  }

  .setup-wizard__header {
    padding: 1.5rem 1rem 1rem;
  }

  .setup-wizard__actions {
    padding: 1rem;
  }

  .setup-wizard__navigation {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .setup-wizard__nav-buttons {
    justify-content: space-between;
  }

  .setup-wizard__quick-start {
    margin-bottom: 1rem;
  }
}

@media (max-width: 480px) {
  .setup-wizard__nav-buttons {
    flex-direction: column;
  }

  .setup-step {
    flex-direction: column;
    text-align: center;
  }

  .setup-step__icon {
    align-self: center;
  }
}
