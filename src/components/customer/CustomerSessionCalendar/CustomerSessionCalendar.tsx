import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { CourseSession } from "../../../types";
import { getAllSessionsWithCourseInfo } from "../../../services/sessionService";
import LoadingSpinner from "../../ui/LoadingSpinner";
import "./CustomerSessionCalendar.css";

interface CustomerSessionCalendarProps {
  viewMode?: "calendar" | "list";
}

const CustomerSessionCalendar: React.FC<CustomerSessionCalendarProps> = ({
  viewMode = "calendar",
}) => {
  const [sessions, setSessions] = useState<CourseSession[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentDate, setCurrentDate] = useState(new Date());
  const [filters, setFilters] = useState({
    categories: [] as string[],
    courses: [] as string[],
  });

  useEffect(() => {
    loadSessions();
  }, []);

  const loadSessions = async () => {
    try {
      setLoading(true);
      console.log("🔍 CustomerSessionCalendar: Loading sessions...");
      const sessionsData = await getAllSessionsWithCourseInfo();
      console.log("📊 CustomerSessionCalendar: Loaded sessions:", sessionsData);
      setSessions(sessionsData);
    } catch (error) {
      console.error(
        "❌ CustomerSessionCalendar: Error loading sessions:",
        error
      );
    } finally {
      setLoading(false);
    }
  };

  // Get unique values for filters
  const getUniqueCategories = (): string[] => {
    const categories = new Set<string>();

    sessions.forEach((session) => {
      // Handle legacy single category
      if (session.courseCategory) {
        categories.add(session.courseCategory);
      }

      // Handle new structured categories array
      if (session.courseCategories && Array.isArray(session.courseCategories)) {
        session.courseCategories.forEach((cat: any) => {
          if (typeof cat === "string") {
            categories.add(cat);
          } else if (cat && cat.name) {
            categories.add(cat.name);
          }
        });
      }
    });

    return Array.from(categories).sort();
  };

  const getUniqueCourses = (): string[] => {
    const courses = sessions
      .map((session) => session.courseTitle)
      .filter((course): course is string => Boolean(course));
    return Array.from(new Set(courses));
  };

  // Multi-select helper functions
  const toggleCategoryFilter = (category: string) => {
    setFilters((prev) => ({
      ...prev,
      categories: prev.categories.includes(category)
        ? prev.categories.filter((c) => c !== category)
        : [...prev.categories, category],
    }));
  };

  const toggleCourseFilter = (course: string) => {
    setFilters((prev) => ({
      ...prev,
      courses: prev.courses.includes(course)
        ? prev.courses.filter((c) => c !== course)
        : [...prev.courses, course],
    }));
  };

  const clearAllFilters = () => {
    setFilters({
      categories: [],
      courses: [],
    });
  };

  // Apply filters
  const getFilteredSessions = () => {
    let filtered = sessions;

    // Apply category filter
    if (filters.categories.length > 0) {
      filtered = filtered.filter((session) => {
        // Check legacy single category
        if (
          session.courseCategory &&
          filters.categories.includes(session.courseCategory)
        ) {
          return true;
        }

        // Check new structured categories array
        if (
          session.courseCategories &&
          Array.isArray(session.courseCategories)
        ) {
          return session.courseCategories.some((cat: any) => {
            const categoryName = typeof cat === "string" ? cat : cat?.name;
            return categoryName && filters.categories.includes(categoryName);
          });
        }

        return false;
      });
    }

    // Apply course filter
    if (filters.courses.length > 0) {
      filtered = filtered.filter(
        (session) =>
          session.courseTitle && filters.courses.includes(session.courseTitle)
      );
    }

    return filtered;
  };

  const filteredSessions = getFilteredSessions();

  // Calendar view helpers
  const getCalendarDays = () => {
    const firstDayOfMonth = new Date(
      currentDate.getFullYear(),
      currentDate.getMonth(),
      1
    );
    const lastDayOfMonth = new Date(
      currentDate.getFullYear(),
      currentDate.getMonth() + 1,
      0
    );

    const startDate = new Date(firstDayOfMonth);
    startDate.setDate(startDate.getDate() - startDate.getDay());

    const endDate = new Date(lastDayOfMonth);
    endDate.setDate(endDate.getDate() + (6 - endDate.getDay()));

    const days = [];
    const currentDay = new Date(startDate);

    while (currentDay <= endDate) {
      days.push(new Date(currentDay));
      currentDay.setDate(currentDay.getDate() + 1);
    }

    return days;
  };

  const getSessionsForDate = (date: Date) => {
    const dateString = date.toISOString().split("T")[0];
    return filteredSessions.filter((session) => session.date === dateString);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      weekday: "short",
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  };

  const formatTime = (timeString: string) => {
    const [hours, minutes] = timeString.split(":");
    const date = new Date();
    date.setHours(parseInt(hours), parseInt(minutes));
    return date.toLocaleTimeString("en-US", {
      hour: "numeric",
      minute: "2-digit",
      hour12: true,
    });
  };

  const navigateMonth = (direction: "prev" | "next") => {
    const newDate = new Date(currentDate);
    if (direction === "prev") {
      newDate.setMonth(newDate.getMonth() - 1);
    } else {
      newDate.setMonth(newDate.getMonth() + 1);
    }
    setCurrentDate(newDate);
  };

  const isToday = (date: Date) => {
    const today = new Date();
    return date.toDateString() === today.toDateString();
  };

  const isCurrentMonth = (date: Date) => {
    return date.getMonth() === currentDate.getMonth();
  };

  if (loading) {
    return (
      <div className="customer-session-calendar__loading">
        <LoadingSpinner size="large" />
        <p>Loading session calendar...</p>
      </div>
    );
  }

  return (
    <div className="customer-session-calendar">
      <div className="customer-session-calendar__header">
        <div className="customer-session-calendar__title">
          <p className="customer-session-calendar__subtitle">
            {filteredSessions.length} session
            {filteredSessions.length !== 1 ? "s" : ""} showing
          </p>
          {sessions.length === 0 && (
            <p style={{ color: "#6b7280", fontStyle: "italic" }}>
              No sessions have been scheduled yet. Check back soon!
            </p>
          )}
        </div>

        <div className="customer-session-calendar__filters">
          <div className="customer-session-calendar__filter-group">
            <h4>Categories</h4>
            <div className="customer-session-calendar__filter-options">
              {getUniqueCategories().map((category) => (
                <label
                  key={category}
                  className="customer-session-calendar__filter-option"
                >
                  <input
                    type="checkbox"
                    checked={filters.categories.includes(category)}
                    onChange={() => toggleCategoryFilter(category)}
                  />
                  <span>{category}</span>
                </label>
              ))}
            </div>
          </div>

          <div className="customer-session-calendar__filter-group">
            <h4>Courses</h4>
            <div className="customer-session-calendar__filter-options">
              {getUniqueCourses().map((course) => (
                <label
                  key={course}
                  className="customer-session-calendar__filter-option"
                >
                  <input
                    type="checkbox"
                    checked={filters.courses.includes(course)}
                    onChange={() => toggleCourseFilter(course)}
                  />
                  <span>{course}</span>
                </label>
              ))}
            </div>
          </div>

          <button
            onClick={clearAllFilters}
            disabled={
              filters.categories.length === 0 && filters.courses.length === 0
            }
            className="customer-session-calendar__clear-filters"
          >
            Clear All Filters
          </button>
        </div>
      </div>

      {viewMode === "calendar" ? (
        <div className="customer-session-calendar__calendar">
          <div className="customer-session-calendar__calendar-header">
            <button
              onClick={() => navigateMonth("prev")}
              className="customer-session-calendar__nav-button"
            >
              ←
            </button>
            <h3>
              {currentDate.toLocaleDateString("en-US", {
                month: "long",
                year: "numeric",
              })}
            </h3>
            <button
              onClick={() => navigateMonth("next")}
              className="customer-session-calendar__nav-button"
            >
              →
            </button>
          </div>

          <div className="customer-session-calendar__weekdays">
            {["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"].map((day) => (
              <div key={day} className="customer-session-calendar__weekday">
                {day}
              </div>
            ))}
          </div>

          <div className="customer-session-calendar__days">
            {getCalendarDays().map((date, index) => {
              const daySessions = getSessionsForDate(date);
              return (
                <div
                  key={index}
                  className={`customer-session-calendar__day ${
                    isCurrentMonth(date) ? "current-month" : "other-month"
                  } ${isToday(date) ? "today" : ""}`}
                >
                  <div className="customer-session-calendar__day-number">
                    {date.getDate()}
                  </div>
                  <div className="customer-session-calendar__day-sessions">
                    {daySessions.slice(0, 3).map((session) => (
                      <Link
                        key={session.id}
                        to={`/course/${session.courseSlug}`}
                        className="customer-session-calendar__session"
                      >
                        <div className="customer-session-calendar__session-title">
                          {session.courseTitle}
                        </div>
                        <div className="customer-session-calendar__session-time">
                          {formatTime(session.startTime)}
                        </div>
                      </Link>
                    ))}
                    {daySessions.length > 3 && (
                      <div className="customer-session-calendar__more-sessions">
                        +{daySessions.length - 3} more
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      ) : (
        <div className="customer-session-calendar__list">
          {filteredSessions.length === 0 ? (
            <div className="customer-session-calendar__empty">
              <p>No sessions found matching your filters.</p>
            </div>
          ) : (
            filteredSessions.map((session) => (
              <Link
                key={session.id}
                to={`/course/${session.courseSlug}`}
                className="customer-session-calendar__session-card"
              >
                <div className="customer-session-calendar__session-info">
                  <h4>{session.courseTitle}</h4>
                  {session.topic && (
                    <p className="customer-session-calendar__session-topic">
                      {session.topic}
                    </p>
                  )}
                  <div className="customer-session-calendar__session-details">
                    <span>{formatDate(session.date)}</span>
                    <span>
                      {formatTime(session.startTime)} -{" "}
                      {formatTime(session.endTime)}
                    </span>
                    {session.courseCategory && (
                      <span className="customer-session-calendar__category">
                        {session.courseCategory}
                      </span>
                    )}
                  </div>
                </div>
              </Link>
            ))
          )}
        </div>
      )}
    </div>
  );
};

export default CustomerSessionCalendar;
