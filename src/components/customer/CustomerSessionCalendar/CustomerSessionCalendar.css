.customer-session-calendar {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem 2rem 2rem;
}

.customer-session-calendar__loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem;
  color: #6b7280;
}

.customer-session-calendar__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  gap: 2rem;
  padding: 1.5rem 2rem;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.customer-session-calendar__subtitle {
  margin: 0;
  color: #374151;
  font-size: 0.875rem;
  font-weight: 500;
}

.customer-session-calendar__filters {
  display: flex;
  gap: 1.5rem;
  flex-wrap: wrap;
  align-items: flex-start;
}

.customer-session-calendar__filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.customer-session-calendar__filter-group h4 {
  margin: 0;
  font-size: 0.75rem;
  font-weight: 600;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.customer-session-calendar__filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.customer-session-calendar__filter-option {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 0.375rem 0.875rem;
  font-size: 0.8rem;
  color: #6b7280;
  background: #f3f4f6;
  border: 1px solid transparent;
  border-radius: 25px;
  transition: all 0.2s ease;
  white-space: nowrap;
  font-weight: 500;
}

.customer-session-calendar__filter-option input[type="checkbox"] {
  display: none;
}

.customer-session-calendar__filter-option:hover {
  background: #e5e7eb;
  color: #374151;
}

.customer-session-calendar__filter-option:has(input:checked) {
  background: #0a2d69;
  color: white;
  border-color: #0a2d69;
  box-shadow: 0 2px 4px rgba(10, 45, 105, 0.2);
}

.customer-session-calendar__filter-select {
  padding: 0.5rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  background: white;
  color: #374151;
  font-size: 0.875rem;
  min-width: 140px;
}

.customer-session-calendar__filter-select:focus {
  outline: none;
  border-color: #0a2d69;
  box-shadow: 0 0 0 3px rgba(10, 45, 105, 0.1);
}

.customer-session-calendar__clear-filters {
  background: #ef4444;
  color: white;
  border: none;
  padding: 0.375rem 0.875rem;
  border-radius: 25px;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  align-self: flex-end;
  white-space: nowrap;
  box-shadow: 0 2px 4px rgba(239, 68, 68, 0.2);
}

.customer-session-calendar__clear-filters:hover:not(:disabled) {
  background: #dc2626;
  box-shadow: 0 2px 6px rgba(220, 38, 38, 0.3);
}

.customer-session-calendar__clear-filters:disabled {
  background: #d1d5db;
  color: #9ca3af;
  cursor: not-allowed;
  box-shadow: none;
}

/* Calendar View */
.customer-session-calendar__calendar {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.customer-session-calendar__calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  background: #0a2d69;
  color: white;
}

.customer-session-calendar__calendar-header h3 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.customer-session-calendar__nav-button {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1.2rem;
  transition: background-color 0.2s ease;
}

.customer-session-calendar__nav-button:hover {
  background: rgba(255, 255, 255, 0.3);
}

.customer-session-calendar__weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  background: #f8f9fa;
  border-bottom: 1px solid #e5e7eb;
}

.customer-session-calendar__weekday {
  padding: 1rem;
  text-align: center;
  font-weight: 600;
  color: #6b7280;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.customer-session-calendar__days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
}

.customer-session-calendar__day {
  min-height: 120px;
  border-right: 1px solid #e5e7eb;
  border-bottom: 1px solid #e5e7eb;
  padding: 0.5rem;
  position: relative;
}

.customer-session-calendar__day:nth-child(7n) {
  border-right: none;
}

.customer-session-calendar__day.other-month {
  background: #f9fafb;
  color: #9ca3af;
}

.customer-session-calendar__day.today {
  background: #eff6ff;
}

.customer-session-calendar__day.today .customer-session-calendar__day-number {
  background: #0a2d69;
  color: white;
}

.customer-session-calendar__day-number {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  font-weight: 600;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

.customer-session-calendar__day-sessions {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.customer-session-calendar__session {
  background: #0a2d69;
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.75rem;
  text-decoration: none;
  transition: background-color 0.2s ease;
  overflow: hidden;
}

.customer-session-calendar__session:hover {
  background: #1e40af;
}

.customer-session-calendar__session-title {
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.customer-session-calendar__session-time {
  font-size: 0.7rem;
  opacity: 0.9;
}

.customer-session-calendar__more-sessions {
  font-size: 0.7rem;
  color: #6b7280;
  padding: 2px 6px;
  text-align: center;
}

/* List View */
.customer-session-calendar__list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.customer-session-calendar__empty {
  text-align: center;
  padding: 4rem;
  color: #6b7280;
}

.customer-session-calendar__session-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 1.5rem;
  text-decoration: none;
  color: inherit;
  transition: all 0.2s ease;
  display: block;
}

.customer-session-calendar__session-card:hover {
  border-color: #0a2d69;
  box-shadow: 0 8px 24px rgba(10, 45, 105, 0.1);
  transform: translateY(-2px);
}

.customer-session-calendar__session-info h4 {
  margin: 0 0 0.5rem 0;
  color: #0a2d69;
  font-size: 1.25rem;
  font-weight: 600;
}

.customer-session-calendar__session-topic {
  margin: 0 0 1rem 0;
  color: #6b7280;
  font-style: italic;
}

.customer-session-calendar__session-details {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  font-size: 0.875rem;
  color: #6b7280;
}

.customer-session-calendar__session-details span {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.customer-session-calendar__category {
  background: #eff6ff;
  color: #0a2d69;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-weight: 500;
}

/* Responsive design */
@media (max-width: 768px) {
  .customer-session-calendar {
    padding: 1rem;
  }

  .customer-session-calendar__header {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
    padding: 1rem;
  }

  .customer-session-calendar__filters {
    gap: 1rem;
  }

  .customer-session-calendar__filter-group {
    min-width: auto;
  }

  .customer-session-calendar__filter-options {
    gap: 0.375rem;
  }

  .customer-session-calendar__filter-option {
    font-size: 0.7rem;
    padding: 0.2rem 0.6rem;
  }

  .customer-session-calendar__clear-filters {
    align-self: stretch;
    text-align: center;
  }

  .customer-session-calendar__calendar-header {
    padding: 1rem;
  }

  .customer-session-calendar__calendar-header h3 {
    font-size: 1.25rem;
  }

  .customer-session-calendar__day {
    min-height: 80px;
    padding: 0.25rem;
  }

  .customer-session-calendar__weekday {
    padding: 0.5rem;
    font-size: 0.75rem;
  }

  .customer-session-calendar__session {
    font-size: 0.7rem;
    padding: 1px 4px;
  }

  .customer-session-calendar__session-time {
    font-size: 0.65rem;
  }

  .customer-session-calendar__session-details {
    flex-direction: column;
    gap: 0.5rem;
  }
}

@media (max-width: 480px) {
  .customer-session-calendar__day {
    min-height: 60px;
  }

  .customer-session-calendar__session-title {
    display: none;
  }

  .customer-session-calendar__session {
    text-align: center;
  }
}
