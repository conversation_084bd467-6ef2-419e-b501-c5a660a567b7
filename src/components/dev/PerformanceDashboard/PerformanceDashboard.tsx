import React, { useState, useEffect } from "react";
import { useViewport } from "../../../hooks";
import "./PerformanceDashboard.css";

interface PerformanceMetric {
  name: string;
  value: number;
  timestamp: number;
  status: "good" | "warning" | "poor";
}

const PerformanceDashboard: React.FC = () => {
  // Completely hide in production - return null immediately
  if (process.env.NODE_ENV === "production") {
    return null;
  }

  const [metrics, setMetrics] = useState<PerformanceMetric[]>([]);
  const [isVisible, setIsVisible] = useState(false);
  const { isMobile } = useViewport();

  useEffect(() => {
    // Listen for performance entries
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();

      entries.forEach((entry) => {
        let status: "good" | "warning" | "poor" = "good";
        let value = entry.duration || entry.startTime;

        // Determine status based on metric type and value
        if (entry.entryType === "navigation") {
          if (value > 3000) status = "poor";
          else if (value > 1000) status = "warning";
        } else if (entry.entryType === "largest-contentful-paint") {
          if (value > 4000) status = "poor";
          else if (value > 2500) status = "warning";
        }

        const metric: PerformanceMetric = {
          name: entry.name || entry.entryType,
          value: Math.round(value * 100) / 100,
          timestamp: Date.now(),
          status,
        };

        setMetrics((prev) => [...prev.slice(-9), metric]); // Keep last 10 metrics
      });
    });

    try {
      observer.observe({
        entryTypes: ["navigation", "largest-contentful-paint", "first-input"],
      });
    } catch (e) {
      console.log("Performance Observer not supported");
    }

    return () => observer.disconnect();
  }, []);

  // Get current page performance
  const getPagePerformance = () => {
    const navigation = performance.getEntriesByType(
      "navigation"
    )[0] as PerformanceNavigationTiming;
    if (!navigation) return null;

    // Use fetchStart as the baseline instead of navigationStart
    const baseline = navigation.fetchStart;

    return {
      domContentLoaded: Math.round(
        navigation.domContentLoadedEventEnd - baseline
      ),
      loadComplete: Math.round(navigation.loadEventEnd - baseline),
      firstByte: Math.round(navigation.responseStart - baseline),
    };
  };

  const pagePerf = getPagePerformance();

  return (
    <div
      className={`perf-dashboard ${isVisible ? "visible" : ""} ${
        isMobile ? "mobile" : ""
      }`}
    >
      <button
        className="perf-dashboard__toggle"
        onClick={() => setIsVisible(!isVisible)}
        title="Toggle Performance Dashboard"
      >
        📊
      </button>

      {isVisible && (
        <div className="perf-dashboard__content">
          <h3>Performance Metrics</h3>

          {pagePerf && (
            <div className="perf-dashboard__section">
              <h4>Page Load</h4>
              <div className="perf-dashboard__metrics">
                <div className="perf-metric">
                  <span>First Byte:</span>
                  <span
                    className={
                      pagePerf.firstByte > 800
                        ? "poor"
                        : pagePerf.firstByte > 400
                        ? "warning"
                        : "good"
                    }
                  >
                    {pagePerf.firstByte}ms
                  </span>
                </div>
                <div className="perf-metric">
                  <span>DOM Ready:</span>
                  <span
                    className={
                      pagePerf.domContentLoaded > 3000
                        ? "poor"
                        : pagePerf.domContentLoaded > 1500
                        ? "warning"
                        : "good"
                    }
                  >
                    {pagePerf.domContentLoaded}ms
                  </span>
                </div>
                <div className="perf-metric">
                  <span>Load Complete:</span>
                  <span
                    className={
                      pagePerf.loadComplete > 5000
                        ? "poor"
                        : pagePerf.loadComplete > 3000
                        ? "warning"
                        : "good"
                    }
                  >
                    {pagePerf.loadComplete}ms
                  </span>
                </div>
              </div>
            </div>
          )}

          <div className="perf-dashboard__section">
            <h4>Recent Metrics</h4>
            <div className="perf-dashboard__recent">
              {metrics.slice(-5).map((metric, index) => (
                <div key={index} className={`perf-metric ${metric.status}`}>
                  <span>{metric.name}:</span>
                  <span>{metric.value}ms</span>
                </div>
              ))}
            </div>
          </div>

          <div className="perf-dashboard__section">
            <h4>Memory Usage</h4>
            <div className="perf-metric">
              <span>Used:</span>
              <span>
                {(() => {
                  const memory = (performance as any).memory;
                  if (memory && typeof memory.usedJSHeapSize === "number") {
                    return `${Math.round(
                      memory.usedJSHeapSize / 1024 / 1024
                    )}MB`;
                  }
                  return "N/A";
                })()}
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PerformanceDashboard;
