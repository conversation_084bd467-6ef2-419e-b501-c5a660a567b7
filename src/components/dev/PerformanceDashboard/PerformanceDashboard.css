.perf-dashboard {
  position: fixed;
  top: 80px;
  right: 20px;
  z-index: 9999;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.perf-dashboard.mobile {
  top: 70px;
  right: 10px;
  left: 10px;
}

.perf-dashboard__toggle {
  background: #0a2d69;
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  cursor: pointer;
  font-size: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  transition: transform 0.2s ease;
}

.perf-dashboard__toggle:hover {
  transform: scale(1.1);
}

.perf-dashboard__content {
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 12px;
  margin-top: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  min-width: 250px;
  max-width: 300px;
}

.perf-dashboard.mobile .perf-dashboard__content {
  max-width: none;
}

.perf-dashboard__content h3 {
  margin: 0 0 12px 0;
  color: #0a2d69;
  font-size: 14px;
  border-bottom: 1px solid #eee;
  padding-bottom: 4px;
}

.perf-dashboard__content h4 {
  margin: 12px 0 6px 0;
  color: #666;
  font-size: 12px;
}

.perf-dashboard__section {
  margin-bottom: 12px;
}

.perf-dashboard__metrics,
.perf-dashboard__recent {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.perf-metric {
  display: flex;
  justify-content: space-between;
  padding: 2px 4px;
  border-radius: 3px;
  background: #f8f9fa;
}

.perf-metric span:first-child {
  color: #666;
}

.perf-metric span:last-child {
  font-weight: bold;
}

.perf-metric.good span:last-child {
  color: #28a745;
}

.perf-metric.warning span:last-child {
  color: #ffc107;
}

.perf-metric.poor span:last-child {
  color: #dc3545;
}

.perf-metric.good {
  background: #d4edda;
}

.perf-metric.warning {
  background: #fff3cd;
}

.perf-metric.poor {
  background: #f8d7da;
}
