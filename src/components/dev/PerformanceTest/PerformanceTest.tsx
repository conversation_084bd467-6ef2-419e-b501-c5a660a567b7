import React, { useState } from 'react';
import { usePerformance } from '../../../hooks';

const PerformanceTest: React.FC = () => {
  const [count, setCount] = useState(0);
  
  // Add performance monitoring with a low threshold to trigger warnings
  usePerformance({
    componentName: 'PerformanceTest',
    threshold: 1, // Very low threshold to trigger warnings
    logToConsole: true,
  });

  // Simulate heavy computation to trigger performance warnings
  const heavyComputation = () => {
    let result = 0;
    for (let i = 0; i < 100000; i++) {
      result += Math.random() * Math.sin(i) * Math.cos(i);
    }
    return result;
  };

  const handleClick = () => {
    setCount(count + 1);
    // Force a heavy computation on each click
    heavyComputation();
  };

  return (
    <div style={{ 
      padding: '1rem', 
      margin: '1rem', 
      border: '2px solid #ffa300', 
      borderRadius: '8px',
      backgroundColor: '#fff4d1'
    }}>
      <h3>Performance Test Component</h3>
      <p>This component intentionally does heavy computation to test performance monitoring.</p>
      <p>Count: {count}</p>
      <p>Heavy computation result: {heavyComputation().toFixed(2)}</p>
      <button 
        onClick={handleClick}
        style={{
          padding: '0.5rem 1rem',
          backgroundColor: '#ffa300',
          border: 'none',
          borderRadius: '4px',
          cursor: 'pointer'
        }}
      >
        Trigger Heavy Computation
      </button>
      <p style={{ fontSize: '0.8rem', color: '#666' }}>
        Check the browser console for performance logs!
      </p>
    </div>
  );
};

export default PerformanceTest;
