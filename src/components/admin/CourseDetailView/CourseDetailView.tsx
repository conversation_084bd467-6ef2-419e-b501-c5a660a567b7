import React, { useState, useEffect } from "react";
import Button from "../../ui/Button";
import Spinner from "../../ui/Spinner";
import {
  getCourseRegistrations,
  updatePaymentStatus,
  deleteRegistration,
  addRegistrationToCapacity,
  updateCourseCapacity,
  updateEnrollmentStatus,
  updateInstructorNotes,
  CourseRegistration,
  getEffectivePaymentStatus,
} from "../../../services/firebaseCourseCapacityService";
import { RegistrationData } from "../../course/RegistrationForm";
import "./CourseDetailView.css";

interface CourseDetailViewProps {
  courseId: string;
  courseTitle: string;
  maxStudents: number;
  onBack: () => void;
  onRefresh: () => void;
  onManageSessions?: () => void;
}

interface ManualRegistration extends RegistrationData {
  paymentStatus: "pending" | "paid" | "refunded" | "late" | "cancelled";
}

const CourseDetailView: React.FC<CourseDetailViewProps> = ({
  courseId,
  courseTitle,
  maxStudents,
  onBack,
  onRefresh,
  onManageSessions,
}) => {
  const [registrations, setRegistrations] = useState<CourseRegistration[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [loadingActions, setLoadingActions] = useState<{
    [key: string]: boolean;
  }>({});
  const [addingStudent, setAddingStudent] = useState(false);
  const [editingNotes, setEditingNotes] = useState<string | null>(null);
  const [notesText, setNotesText] = useState("");
  const [courseCompleted, setCourseCompleted] = useState(false);
  const [newStudent, setNewStudent] = useState<ManualRegistration>({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    experienceLevel: "",
    specialRequirements: "",
    emergencyContactName: "",
    emergencyContactPhone: "",
    emergencyContactRelationship: "",
    paymentStatus: "pending",
  });

  useEffect(() => {
    loadRegistrations();
  }, [courseId]);

  const setActionLoading = (actionId: string, isLoading: boolean) => {
    setLoadingActions((prev) => ({
      ...prev,
      [actionId]: isLoading,
    }));
  };

  const isActionLoading = (actionId: string) => {
    return loadingActions[actionId] || false;
  };

  const loadRegistrations = async () => {
    setLoading(true);
    try {
      const courseRegistrations = await getCourseRegistrations(courseId);
      setRegistrations(courseRegistrations);
    } catch (error) {
      console.error("Error loading course registrations:", error);
    } finally {
      setLoading(false);
    }
  };

  const handlePaymentStatusUpdate = async (
    documentId: string,
    newStatus: "pending" | "paid" | "refunded" | "late" | "cancelled"
  ) => {
    const actionId = `payment-${documentId}`;
    setActionLoading(actionId, true);

    // Small delay to ensure loading state is visible
    await new Promise((resolve) => setTimeout(resolve, 200));

    // Optimistic update - update UI immediately
    const originalRegistrations = [...registrations];
    const updatedRegistrations = registrations.map((reg) =>
      reg.id === documentId ? { ...reg, paymentStatus: newStatus } : reg
    );
    setRegistrations(updatedRegistrations);

    try {
      await updatePaymentStatus(documentId, newStatus);
      onRefresh();
    } catch (error) {
      console.error("Error updating payment status:", error);
      // Revert optimistic update on error
      setRegistrations(originalRegistrations);
      alert("Failed to update payment status. Please try again.");
    } finally {
      setActionLoading(actionId, false);
    }
  };

  const handleDeleteRegistration = async (documentId: string) => {
    const actionId = `delete-${documentId}`;

    // Set loading state first, then show confirm
    setActionLoading(actionId, true);

    // Small delay to show loading state before confirm dialog
    await new Promise((resolve) => setTimeout(resolve, 100));

    const confirmed = window.confirm(
      "Are you sure you want to remove this student from the course?"
    );

    if (!confirmed) {
      setActionLoading(actionId, false);
      return;
    }

    // Optimistic update - remove from UI immediately
    const originalRegistrations = [...registrations];
    const updatedRegistrations = registrations.filter(
      (reg) => reg.id !== documentId
    );
    setRegistrations(updatedRegistrations);

    try {
      await deleteRegistration(documentId);
      onRefresh();
    } catch (error) {
      console.error("Error deleting registration:", error);
      // Revert optimistic update on error
      setRegistrations(originalRegistrations);
      alert("Failed to remove student. Please try again.");
    } finally {
      setActionLoading(actionId, false);
    }
  };

  const handlePromoteFromWaitlist = async (registrationId: string) => {
    const currentEnrolled = enrolledStudents.length;
    const isAtCapacity = currentEnrolled >= maxStudents;

    const confirmMessage = isAtCapacity
      ? "Promote this student from waitlist? This will increase the course capacity by 1."
      : "Promote this student from waitlist?";

    if (window.confirm(confirmMessage)) {
      const actionId = `promote-${registrationId}`;
      setActionLoading(actionId, true);
      try {
        // Update enrollment status to enrolled
        await updateEnrollmentStatus(registrationId, "enrolled");

        // Only increase course capacity if the course is currently at capacity
        if (isAtCapacity) {
          await updateCourseCapacity(courseId, maxStudents + 1);
        }

        // Refresh data
        loadRegistrations();
        onRefresh();

        const successMessage = isAtCapacity
          ? "Student promoted from waitlist! Course capacity increased by 1."
          : "Student promoted from waitlist!";

        alert(successMessage);
      } catch (error) {
        console.error("Error promoting student:", error);
        alert("Failed to promote student. Please try again.");
      } finally {
        setActionLoading(actionId, false);
      }
    }
  };

  const handleAddStudent = async () => {
    if (!newStudent.firstName || !newStudent.lastName || !newStudent.email) {
      alert("Please fill in at least the name and email fields.");
      return;
    }

    setAddingStudent(true);
    try {
      const registrationId = `ADMIN-${Date.now().toString(36).toUpperCase()}`;
      const registrationDate = new Date().toISOString();

      // Get the actual course price from the course data
      const courseDetails = await import(
        "../../../services/firebaseCourseCapacityService"
      ).then((module) => module.getAllCourses());
      const course = courseDetails.find((c) => c.id === courseId);
      const coursePrice = course?.price || "N/A";

      const registration = {
        ...newStudent,
        courseId,
        courseTitle,
        coursePrice: coursePrice,
        registrationDate,
        registrationId,
        enrollmentStatus: (enrolledStudents.length < maxStudents
          ? "enrolled"
          : "waitlist") as "enrolled" | "waitlist",
      };

      await addRegistrationToCapacity(registration);

      // Reset form
      setNewStudent({
        firstName: "",
        lastName: "",
        email: "",
        phone: "",
        experienceLevel: "",
        specialRequirements: "",
        emergencyContactName: "",
        emergencyContactPhone: "",
        emergencyContactRelationship: "",
        paymentStatus: "pending",
      });

      setShowAddForm(false);
      loadRegistrations();
      onRefresh();
    } catch (error) {
      console.error("Error adding student:", error);
      alert("Failed to add student. Please try again.");
    } finally {
      setAddingStudent(false);
    }
  };

  const handleMoveToWaitlist = async (documentId: string) => {
    const actionId = `waitlist-${documentId}`;
    setActionLoading(actionId, true);

    // Small delay to show loading state before confirm dialog
    await new Promise((resolve) => setTimeout(resolve, 100));

    const confirmed = window.confirm(
      "Are you sure you want to move this student to the waitlist?"
    );

    if (!confirmed) {
      setActionLoading(actionId, false);
      return;
    }

    // Optimistic update - move to waitlist immediately
    const originalRegistrations = [...registrations];
    const updatedRegistrations = registrations.map((reg) =>
      reg.id === documentId
        ? { ...reg, enrollmentStatus: "waitlist" as "enrolled" | "waitlist" }
        : reg
    );
    setRegistrations(updatedRegistrations);

    try {
      await updateEnrollmentStatus(documentId, "waitlist");
      onRefresh();
    } catch (error) {
      console.error("Error moving to waitlist:", error);
      // Revert optimistic update on error
      setRegistrations(originalRegistrations);
      alert("Failed to move student to waitlist. Please try again.");
    } finally {
      setActionLoading(actionId, false);
    }
  };

  const handleEditNotes = (
    registrationId: string,
    currentNotes: string,
    completed?: boolean
  ) => {
    setEditingNotes(registrationId);
    setNotesText(currentNotes || "");
    setCourseCompleted(completed || false);
  };

  const handleSaveNotes = async (registrationId: string) => {
    const actionId = `notes-${registrationId}`;
    setActionLoading(actionId, true);

    try {
      await updateInstructorNotes(registrationId, notesText, courseCompleted);

      // Update local state optimistically
      const updatedRegistrations = registrations.map((reg) =>
        reg.id === registrationId
          ? {
              ...reg,
              instructorNotes: notesText,
              courseCompleted: courseCompleted,
              completionDate: courseCompleted
                ? new Date().toISOString()
                : reg.completionDate,
            }
          : reg
      );
      setRegistrations(updatedRegistrations);

      setEditingNotes(null);
      setNotesText("");
      setCourseCompleted(false);
      onRefresh();
    } catch (error) {
      console.error("Error updating instructor notes:", error);
      alert("Failed to update notes. Please try again.");
    } finally {
      setActionLoading(actionId, false);
    }
  };

  const handleCancelNotes = () => {
    setEditingNotes(null);
    setNotesText("");
    setCourseCompleted(false);
  };

  // Filter by enrollment status and exclude cancelled registrations
  const enrolledStudents = registrations.filter(
    (reg) =>
      reg.enrollmentStatus === "enrolled" && reg.paymentStatus !== "cancelled"
  );
  const waitlistStudents = registrations.filter(
    (reg) =>
      reg.enrollmentStatus === "waitlist" && reg.paymentStatus !== "cancelled"
  );

  if (loading) {
    return (
      <div className="course-detail-view__loading">
        Loading course details...
      </div>
    );
  }

  return (
    <div className="course-detail-view">
      <header className="course-detail-view__header">
        <div className="course-detail-view__header-content">
          <div>
            <h1>{courseTitle}</h1>
            <div className="course-detail-view__stats">
              <span className="course-detail-view__stat">
                {enrolledStudents.length}/{maxStudents} enrolled
              </span>
              {waitlistStudents.length > 0 && (
                <span className="course-detail-view__stat course-detail-view__stat--waitlist">
                  {waitlistStudents.length} on waitlist
                </span>
              )}
            </div>
          </div>
          <div className="course-detail-view__header-actions">
            {onManageSessions && (
              <Button variant="secondary" onClick={onManageSessions}>
                Manage Sessions
              </Button>
            )}
            <Button onClick={() => setShowAddForm(true)}>Add Student</Button>
          </div>
        </div>
      </header>

      <nav className="course-detail-view__breadcrumb">
        <button
          onClick={onBack}
          className="course-detail-view__breadcrumb-link"
        >
          Admin Dashboard
        </button>
        <span className="course-detail-view__breadcrumb-separator">›</span>
        <span className="course-detail-view__breadcrumb-current">
          Course Details
        </span>
      </nav>

      {showAddForm && (
        <div className="course-detail-view__add-form">
          <h3>Add Student Manually</h3>
          <div className="course-detail-view__form-grid">
            <input
              type="text"
              placeholder="First Name *"
              value={newStudent.firstName}
              onChange={(e) =>
                setNewStudent((prev) => ({
                  ...prev,
                  firstName: e.target.value,
                }))
              }
            />
            <input
              type="text"
              placeholder="Last Name *"
              value={newStudent.lastName}
              onChange={(e) =>
                setNewStudent((prev) => ({ ...prev, lastName: e.target.value }))
              }
            />
            <input
              type="email"
              placeholder="Email *"
              value={newStudent.email}
              onChange={(e) =>
                setNewStudent((prev) => ({ ...prev, email: e.target.value }))
              }
            />
            <input
              type="tel"
              placeholder="Phone"
              value={newStudent.phone}
              onChange={(e) =>
                setNewStudent((prev) => ({ ...prev, phone: e.target.value }))
              }
            />
            <select
              value={newStudent.experienceLevel}
              onChange={(e) =>
                setNewStudent((prev) => ({
                  ...prev,
                  experienceLevel: e.target.value,
                }))
              }
            >
              <option value="">Experience Level</option>
              <option value="Complete Beginner">Complete Beginner</option>
              <option value="Some Experience">Some Experience</option>
              <option value="Intermediate">Intermediate</option>
              <option value="Advanced">Advanced</option>
            </select>
            <select
              value={newStudent.paymentStatus}
              onChange={(e) =>
                setNewStudent((prev) => ({
                  ...prev,
                  paymentStatus: e.target.value as
                    | "pending"
                    | "paid"
                    | "refunded"
                    | "late"
                    | "cancelled",
                }))
              }
            >
              <option value="pending">Payment Pending</option>
              <option value="paid">Already Paid</option>
              <option value="refunded">Refunded</option>
              <option value="late">Late</option>
              <option value="cancelled">Cancelled</option>
            </select>
          </div>
          <textarea
            placeholder="Special Requirements or Notes"
            value={newStudent.specialRequirements}
            onChange={(e) =>
              setNewStudent((prev) => ({
                ...prev,
                specialRequirements: e.target.value,
              }))
            }
            rows={3}
          />
          <div className="course-detail-view__form-actions">
            <Button loading={addingStudent} onClick={handleAddStudent}>
              Add Student
            </Button>
            <Button
              variant="secondary"
              disabled={addingStudent}
              onClick={() => setShowAddForm(false)}
            >
              Cancel
            </Button>
          </div>
        </div>
      )}

      <div className="course-detail-view__sections">
        <section className="course-detail-view__section">
          <h2>
            Enrolled Students ({enrolledStudents.length}/{maxStudents})
          </h2>
          {enrolledStudents.length === 0 ? (
            <p className="course-detail-view__empty">
              No students enrolled yet.
            </p>
          ) : (
            <div className="course-detail-view__student-list">
              {enrolledStudents.map((registration, index) => (
                <div
                  key={registration.id}
                  className="course-detail-view__student-card"
                >
                  <div className="course-detail-view__student-info">
                    <div className="course-detail-view__student-name">
                      {registration.firstName} {registration.lastName}
                      <span className="course-detail-view__student-position">
                        #{index + 1}
                      </span>
                    </div>
                    <div className="course-detail-view__student-details">
                      <span>{registration.email}</span>
                      <span>{registration.phone}</span>
                      <span>Experience: {registration.experienceLevel}</span>
                    </div>
                    {registration.specialRequirements && (
                      <div className="course-detail-view__student-notes">
                        Notes: {registration.specialRequirements}
                      </div>
                    )}

                    {/* Instructor Notes Section */}
                    <div className="course-detail-view__instructor-notes">
                      <div className="course-detail-view__instructor-notes-header">
                        <span className="course-detail-view__instructor-notes-label">
                          Instructor Notes:
                        </span>
                        {editingNotes === registration.id ? (
                          <div className="course-detail-view__instructor-notes-actions">
                            <Button
                              size="small"
                              loading={isActionLoading(
                                `notes-${registration.id}`
                              )}
                              onClick={() =>
                                registration.id &&
                                handleSaveNotes(registration.id)
                              }
                            >
                              Save
                            </Button>
                            <Button
                              variant="secondary"
                              size="small"
                              disabled={isActionLoading(
                                `notes-${registration.id}`
                              )}
                              onClick={handleCancelNotes}
                            >
                              Cancel
                            </Button>
                          </div>
                        ) : (
                          <Button
                            variant="secondary"
                            size="small"
                            onClick={() =>
                              registration.id &&
                              handleEditNotes(
                                registration.id,
                                registration.instructorNotes || "",
                                registration.courseCompleted
                              )
                            }
                          >
                            {registration.instructorNotes
                              ? "Edit Notes"
                              : "Add Notes"}
                          </Button>
                        )}
                      </div>

                      {editingNotes === registration.id ? (
                        <div className="course-detail-view__instructor-notes-form">
                          <textarea
                            value={notesText}
                            onChange={(e) => setNotesText(e.target.value)}
                            placeholder="Add instructor notes about this student's progress, performance, or any observations..."
                            className="course-detail-view__instructor-notes-textarea"
                            rows={3}
                          />
                          <div className="course-detail-view__completion-controls">
                            <label className="course-detail-view__completion-label">
                              <input
                                type="checkbox"
                                checked={courseCompleted}
                                onChange={(e) =>
                                  setCourseCompleted(e.target.checked)
                                }
                                className="course-detail-view__completion-checkbox"
                              />
                              <span className="course-detail-view__completion-text">
                                Mark course as finished
                              </span>
                            </label>
                            {courseCompleted && (
                              <span className="course-detail-view__completion-note">
                                This will mark the course as completed for this
                                student
                              </span>
                            )}
                          </div>
                        </div>
                      ) : (
                        <div className="course-detail-view__instructor-notes-display">
                          {registration.instructorNotes || (
                            <span className="course-detail-view__instructor-notes-empty">
                              No instructor notes yet
                            </span>
                          )}
                          {registration.courseCompleted && (
                            <div className="course-detail-view__completion-status">
                              <span className="course-detail-view__completion-badge">
                                ✓ Course Finished
                              </span>
                              {registration.completionDate && (
                                <span className="course-detail-view__completion-date">
                                  Completed:{" "}
                                  {new Date(
                                    registration.completionDate
                                  ).toLocaleDateString()}
                                </span>
                              )}
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="course-detail-view__student-actions">
                    <div
                      className={`course-detail-view__payment-controls ${
                        isActionLoading(`payment-${registration.id}`)
                          ? "course-detail-view__payment-controls--loading"
                          : ""
                      }`}
                    >
                      {isActionLoading(`payment-${registration.id}`) && (
                        <div className="course-detail-view__payment-loading">
                          <Spinner size="small" color="primary" />
                        </div>
                      )}
                      <span
                        className={`course-detail-view__payment-badge course-detail-view__payment-badge--${getEffectivePaymentStatus(
                          registration
                        )}`}
                      >
                        {getEffectivePaymentStatus(registration).toUpperCase()}
                      </span>
                      <select
                        value={registration.paymentStatus || "pending"}
                        onChange={(e) =>
                          registration.id &&
                          handlePaymentStatusUpdate(
                            registration.id,
                            e.target.value as
                              | "pending"
                              | "paid"
                              | "refunded"
                              | "late"
                              | "cancelled"
                          )
                        }
                        className="course-detail-view__payment-select"
                        disabled={isActionLoading(`payment-${registration.id}`)}
                      >
                        <option value="pending">Pending</option>
                        <option value="paid">Paid</option>
                        <option value="refunded">Refunded</option>
                        <option value="late">Late</option>
                        <option value="cancelled">Cancelled</option>
                      </select>
                    </div>
                    <div className="course-detail-view__enrolled-actions">
                      <Button
                        variant="secondary"
                        size="small"
                        loading={isActionLoading(`waitlist-${registration.id}`)}
                        onClick={() =>
                          registration.id &&
                          handleMoveToWaitlist(registration.id)
                        }
                      >
                        Move to Waitlist
                      </Button>
                      <Button
                        variant="secondary"
                        size="small"
                        loading={isActionLoading(`delete-${registration.id}`)}
                        onClick={() =>
                          registration.id &&
                          handleDeleteRegistration(registration.id)
                        }
                      >
                        Remove
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </section>

        {waitlistStudents.length > 0 && (
          <section className="course-detail-view__section">
            <h2>Waitlist ({waitlistStudents.length})</h2>
            <div className="course-detail-view__student-list">
              {waitlistStudents.map((registration, index) => (
                <div
                  key={registration.id}
                  className="course-detail-view__student-card course-detail-view__student-card--waitlist"
                >
                  <div className="course-detail-view__student-info">
                    <div className="course-detail-view__student-name">
                      {registration.firstName} {registration.lastName}
                      <span className="course-detail-view__student-position">
                        Waitlist #{index + 1}
                      </span>
                    </div>
                    <div className="course-detail-view__student-details">
                      <span>{registration.email}</span>
                      <span>{registration.phone}</span>
                      <span>Experience: {registration.experienceLevel}</span>
                    </div>
                    {registration.specialRequirements && (
                      <div className="course-detail-view__student-notes">
                        Notes: {registration.specialRequirements}
                      </div>
                    )}
                  </div>
                  <div className="course-detail-view__student-actions">
                    <div
                      className={`course-detail-view__payment-controls ${
                        isActionLoading(`payment-${registration.id}`)
                          ? "course-detail-view__payment-controls--loading"
                          : ""
                      }`}
                    >
                      {isActionLoading(`payment-${registration.id}`) && (
                        <div className="course-detail-view__payment-loading">
                          <Spinner size="small" color="primary" />
                        </div>
                      )}
                      <span
                        className={`course-detail-view__payment-badge course-detail-view__payment-badge--${getEffectivePaymentStatus(
                          registration
                        )}`}
                      >
                        {getEffectivePaymentStatus(registration).toUpperCase()}
                      </span>
                      <select
                        value={registration.paymentStatus || "pending"}
                        onChange={(e) =>
                          registration.id &&
                          handlePaymentStatusUpdate(
                            registration.id,
                            e.target.value as
                              | "pending"
                              | "paid"
                              | "refunded"
                              | "late"
                              | "cancelled"
                          )
                        }
                        className="course-detail-view__payment-select"
                        disabled={isActionLoading(`payment-${registration.id}`)}
                      >
                        <option value="pending">Pending</option>
                        <option value="paid">Paid</option>
                        <option value="refunded">Refunded</option>
                        <option value="late">Late</option>
                        <option value="cancelled">Cancelled</option>
                      </select>
                    </div>
                    <div className="course-detail-view__waitlist-actions">
                      <Button
                        variant="primary"
                        size="small"
                        loading={isActionLoading(`promote-${registration.id}`)}
                        onClick={() =>
                          registration.id &&
                          handlePromoteFromWaitlist(registration.id)
                        }
                      >
                        Promote
                      </Button>
                      <Button
                        variant="secondary"
                        size="small"
                        loading={isActionLoading(`delete-${registration.id}`)}
                        onClick={() =>
                          registration.id &&
                          handleDeleteRegistration(registration.id)
                        }
                      >
                        Remove
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </section>
        )}
      </div>
    </div>
  );
};

export default CourseDetailView;
