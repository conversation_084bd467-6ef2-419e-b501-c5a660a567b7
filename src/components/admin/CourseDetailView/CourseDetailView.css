.course-detail-view {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.course-detail-view__breadcrumb {
  background: white;
  padding: 1rem 2rem;
  border-bottom: 1px solid #e8f4f8;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 2rem;
}

.course-detail-view__breadcrumb-link {
  background: none;
  border: none;
  color: #0a2d69;
  text-decoration: none;
  cursor: pointer;
  font-size: 0.9rem;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.course-detail-view__breadcrumb-link:hover {
  background-color: #f8f9fa;
  text-decoration: underline;
}

.course-detail-view__breadcrumb-separator {
  color: #666;
  font-size: 0.9rem;
}

.course-detail-view__breadcrumb-current {
  color: #666;
  font-size: 0.9rem;
}

.course-detail-view__loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50vh;
  font-size: 1.1rem;
  color: #666;
}

.course-detail-view__header {
  background: linear-gradient(135deg, #0a2d69 0%, #1e4a8c 100%);
  color: white;
  padding: 2rem;
  margin-bottom: 0;
}

.course-detail-view__header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.course-detail-view__header-actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  flex-shrink: 0;
}

/* Target the Manage Sessions button specifically */
.course-detail-view__header-actions button[variant="secondary"],
.course-detail-view__header-actions .button--secondary,
.course-detail-view__header-actions button:first-child {
  background-color: #ffd46f !important;
  border: 2px solid #ffd46f !important;
  color: #0a2d69 !important;
  font-weight: 700 !important;
  transition: all 0.2s ease !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
}

.course-detail-view__header-actions button[variant="secondary"]:hover,
.course-detail-view__header-actions .button--secondary:hover,
.course-detail-view__header-actions button:first-child:hover {
  background-color: #ffcc33 !important;
  border-color: #ffcc33 !important;
  color: #0a2d69 !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3) !important;
}

/* Make sure the Add Student button stays as primary */
.course-detail-view__header-actions button:last-child {
  background-color: #0a2d69 !important;
  border: 2px solid #0a2d69 !important;
  color: white !important;
}

.course-detail-view__back-btn {
  flex-shrink: 0;
}

.course-detail-view__header h1 {
  margin: 0 0 0.5rem 0;
  color: white;
  font-size: 2rem;
}

.course-detail-view__stats {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.course-detail-view__stat {
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  color: white;
}

.course-detail-view__stat--waitlist {
  background: #ffa300;
  color: white;
}

.course-detail-view__add-form {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  margin: 0 2rem 2rem 2rem;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}

.course-detail-view__add-form h3 {
  margin: 0 0 1.5rem 0;
  color: #0a2d69;
}

.course-detail-view__form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.course-detail-view__form-grid input,
.course-detail-view__form-grid select {
  padding: 0.75rem;
  border: 1px solid #e8f4f8;
  border-radius: 8px;
  font-size: 1rem;
}

.course-detail-view__add-form textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #e8f4f8;
  border-radius: 8px;
  font-size: 1rem;
  margin-bottom: 1rem;
  resize: vertical;
}

.course-detail-view__form-actions {
  display: flex;
  gap: 1rem;
}

.course-detail-view__sections {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.course-detail-view__section {
  margin-bottom: 3rem;
}

.course-detail-view__section h2 {
  color: #0a2d69;
  margin: 0 0 1.5rem 0;
  font-size: 1.5rem;
}

.course-detail-view__empty {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  text-align: center;
  color: #666;
  font-style: italic;
}

.course-detail-view__student-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.course-detail-view__student-card {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2rem;
}

.course-detail-view__student-card--waitlist {
  background: #fff9e6;
  border-left: 4px solid #ffa300;
}

.course-detail-view__student-info {
  flex: 1;
}

.course-detail-view__student-name {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
  font-weight: 600;
  color: #0a2d69;
}

.course-detail-view__student-position {
  background: #e8f4f8;
  color: #0a2d69;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.course-detail-view__student-card--waitlist
  .course-detail-view__student-position {
  background: #ffa300;
  color: white;
}

.course-detail-view__student-details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  color: #666;
  font-size: 0.9rem;
}

.course-detail-view__student-notes {
  margin-top: 0.5rem;
  padding: 0.5rem;
  background: #f8f9fa;
  border-radius: 6px;
  font-size: 0.9rem;
  color: #555;
}

.course-detail-view__student-actions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: flex-end;
}

.course-detail-view__payment-controls {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  align-items: flex-end;
  position: relative;
}

.course-detail-view__payment-controls--loading {
  opacity: 0.6;
  pointer-events: none;
}

.course-detail-view__payment-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
}

.course-detail-view__payment-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.course-detail-view__payment-badge--pending {
  background-color: #fef3c7;
  color: #92400e;
}

.course-detail-view__payment-badge--paid {
  background-color: #d1fae5;
  color: #065f46;
}

.course-detail-view__payment-badge--refunded {
  background-color: #fee2e2;
  color: #991b1b;
}

.course-detail-view__payment-badge--late {
  background-color: #fef2f2;
  color: #dc2626;
  animation: pulse-late 2s infinite;
}

.course-detail-view__payment-badge--cancelled {
  background-color: #f3f4f6;
  color: #6b7280;
  text-decoration: line-through;
}

@keyframes pulse-late {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.course-detail-view__payment-select {
  padding: 0.25rem 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  background-color: white;
  cursor: pointer;
}

.course-detail-view__payment-select:hover {
  border-color: #9ca3af;
}

.course-detail-view__payment-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 1px #3b82f6;
}

.course-detail-view__waitlist-actions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.course-detail-view__enrolled-actions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.course-detail-view__instructor-notes {
  margin-top: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #0a2d69;
}

.course-detail-view__instructor-notes-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.course-detail-view__instructor-notes-label {
  font-weight: 600;
  color: #0a2d69;
  font-size: 0.9rem;
}

.course-detail-view__instructor-notes-actions {
  display: flex;
  gap: 0.5rem;
}

.course-detail-view__instructor-notes-textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.9rem;
  font-family: inherit;
  resize: vertical;
  min-height: 80px;
}

.course-detail-view__instructor-notes-textarea:focus {
  outline: none;
  border-color: #0a2d69;
  box-shadow: 0 0 0 2px rgba(10, 45, 105, 0.1);
}

.course-detail-view__instructor-notes-display {
  font-size: 0.9rem;
  line-height: 1.5;
  color: #374151;
  white-space: pre-wrap;
}

.course-detail-view__instructor-notes-empty {
  color: #9ca3af;
  font-style: italic;
}

.course-detail-view__instructor-notes-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.course-detail-view__completion-controls {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.course-detail-view__completion-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  font-size: 0.9rem;
}

.course-detail-view__completion-checkbox {
  width: 16px;
  height: 16px;
  cursor: pointer;
}

.course-detail-view__completion-text {
  color: #374151;
  font-weight: 500;
}

.course-detail-view__completion-note {
  font-size: 0.8rem;
  color: #6b7280;
  font-style: italic;
  margin-left: 1.5rem;
}

.course-detail-view__completion-status {
  margin-top: 1rem;
  padding: 0.75rem;
  background: #f0f9ff;
  border-radius: 6px;
  border-left: 4px solid #0ea5e9;
}

.course-detail-view__completion-badge {
  display: block;
  color: #0369a1;
  font-weight: 600;
  font-size: 0.9rem;
  margin-bottom: 0.25rem;
}

.course-detail-view__completion-date {
  color: #64748b;
  font-size: 0.8rem;
}

@media (max-width: 768px) {
  .course-detail-view__header {
    padding: 1rem;
  }

  .course-detail-view__header-content {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .course-detail-view__sections {
    padding: 0 1rem;
  }

  .course-detail-view__add-form {
    margin: 0 1rem 2rem 1rem;
    padding: 1.5rem;
  }

  .course-detail-view__form-grid {
    grid-template-columns: 1fr;
  }

  .course-detail-view__student-card {
    flex-direction: column;
    gap: 1rem;
  }

  .course-detail-view__student-actions {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }

  .course-detail-view__payment-controls {
    flex-direction: row;
    align-items: center;
  }
}
