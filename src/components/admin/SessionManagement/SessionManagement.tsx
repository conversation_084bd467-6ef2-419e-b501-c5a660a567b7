import React, { useState, useEffect } from "react";
import Button from "../../ui/Button";
import Spinner from "../../ui/Spinner";
import {
  getCourseSessionsService,
  createSession,
  updateSession,
  deleteSession,
  generateDefaultSessions,
  autoGenerateSessions,
  formatSessionTime,
  formatSessionDate,
} from "../../../services/sessionService";
import { CourseSession } from "../../../types";
import "./SessionManagement.css";

interface SessionManagementProps {
  courseId: string;
  courseTitle: string;
  onBack: () => void;
}

const SessionManagement: React.FC<SessionManagementProps> = ({
  courseId,
  courseTitle,
  onBack,
}) => {
  const [sessions, setSessions] = useState<CourseSession[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [editingSession, setEditingSession] = useState<CourseSession | null>(
    null
  );
  const [showAddForm, setShowAddForm] = useState(false);

  const [newSession, setNewSession] = useState({
    sessionNumber: 1,
    date: "",
    startTime: "10:00",
    endTime: "11:30",
    topic: "",
    description: "",
  });

  useEffect(() => {
    loadSessions();
  }, [courseId]);

  const loadSessions = async () => {
    setLoading(true);
    try {
      const courseSessions = await getCourseSessionsService(courseId);
      setSessions(courseSessions);
    } catch (error) {
      console.error("Error loading sessions:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddSession = async () => {
    setSaving(true);
    try {
      const sessionData = {
        ...newSession,
        courseId: courseId,
        status: "scheduled" as const,
      };

      await createSession(courseId, sessionData);
      await loadSessions();
      setShowAddForm(false);
      setNewSession({
        sessionNumber: sessions.length + 1,
        date: "",
        startTime: "10:00",
        endTime: "11:30",
        topic: "",
        description: "",
      });
    } catch (error) {
      console.error("Error adding session:", error);
      alert("Failed to add session. Please try again.");
    } finally {
      setSaving(false);
    }
  };

  const handleUpdateSession = async () => {
    if (!editingSession) return;

    setSaving(true);
    try {
      const { id, ...updates } = editingSession;
      await updateSession(id, updates);
      await loadSessions();
      setEditingSession(null);
    } catch (error) {
      console.error("Error updating session:", error);
      alert("Failed to update session. Please try again.");
    } finally {
      setSaving(false);
    }
  };

  const handleDeleteSession = async (sessionId: string) => {
    if (!window.confirm("Are you sure you want to delete this session?")) {
      return;
    }

    setSaving(true);
    try {
      await deleteSession(sessionId);
      await loadSessions();
    } catch (error) {
      console.error("Error deleting session:", error);
      alert("Failed to delete session. Please try again.");
    } finally {
      setSaving(false);
    }
  };

  const handleGenerateDefaultSessions = async () => {
    if (sessions.length > 0) {
      if (
        !window.confirm("This will replace existing sessions. Are you sure?")
      ) {
        return;
      }
    }

    setSaving(true);
    try {
      // Delete existing sessions
      for (const session of sessions) {
        await deleteSession(session.id);
      }

      // Generate default sessions (you might want to get these from course data)
      const defaultSessions = generateDefaultSessions(
        "2025-03-15", // You'll want to get this from course data
        "Saturdays at 10:00 AM", // You'll want to get this from course data
        6,
        90
      );

      // Create new sessions
      for (const sessionData of defaultSessions) {
        const sessionWithCourseId = {
          ...sessionData,
          courseId: courseId,
        };
        await createSession(courseId, sessionWithCourseId);
      }

      await loadSessions();
    } catch (error) {
      console.error("Error generating default sessions:", error);
      alert("Failed to generate default sessions. Please try again.");
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="session-management__loading">
        <Spinner size="large" />
        <p>Loading sessions...</p>
      </div>
    );
  }

  return (
    <div className="session-management">
      <div className="session-management__header">
        <div className="session-management__title">
          <h2>{courseTitle} - Session Management</h2>
          <Button variant="secondary" onClick={onBack}>
            ← Back to Course
          </Button>
        </div>
        <div className="session-management__actions">
          <Button
            variant="secondary"
            onClick={() => setShowAddForm(true)}
            disabled={saving}
          >
            Add Session
          </Button>
          <Button
            variant="secondary"
            onClick={handleGenerateDefaultSessions}
            disabled={saving}
          >
            Generate Default Sessions
          </Button>
        </div>
      </div>

      {showAddForm && (
        <div className="session-management__add-form">
          <h3>Add New Session</h3>
          <div className="session-management__form-grid">
            <div className="session-management__field">
              <label>Session Number</label>
              <input
                type="number"
                value={newSession.sessionNumber}
                onChange={(e) =>
                  setNewSession({
                    ...newSession,
                    sessionNumber: parseInt(e.target.value),
                  })
                }
                min="1"
              />
            </div>
            <div className="session-management__field">
              <label>Date</label>
              <input
                type="date"
                value={newSession.date}
                onChange={(e) =>
                  setNewSession({ ...newSession, date: e.target.value })
                }
                required
              />
            </div>
            <div className="session-management__field">
              <label>Start Time</label>
              <input
                type="time"
                value={newSession.startTime}
                onChange={(e) =>
                  setNewSession({ ...newSession, startTime: e.target.value })
                }
                required
              />
            </div>
            <div className="session-management__field">
              <label>End Time</label>
              <input
                type="time"
                value={newSession.endTime}
                onChange={(e) =>
                  setNewSession({ ...newSession, endTime: e.target.value })
                }
                required
              />
            </div>
            <div className="session-management__field">
              <label>Topic</label>
              <input
                type="text"
                value={newSession.topic}
                onChange={(e) =>
                  setNewSession({ ...newSession, topic: e.target.value })
                }
                placeholder="Session topic"
              />
            </div>
          </div>
          <div className="session-management__field">
            <label>Description</label>
            <textarea
              value={newSession.description}
              onChange={(e) =>
                setNewSession({ ...newSession, description: e.target.value })
              }
              placeholder="Session description"
              rows={3}
            />
          </div>
          <div className="session-management__form-actions">
            <Button
              onClick={handleAddSession}
              loading={saving}
              disabled={saving}
            >
              {saving ? "Adding..." : "Add Session"}
            </Button>
            <Button
              variant="secondary"
              onClick={() => setShowAddForm(false)}
              disabled={saving}
            >
              Cancel
            </Button>
          </div>
        </div>
      )}

      {editingSession && (
        <div className="session-management__edit-form">
          <h3>Edit Session {editingSession.sessionNumber}</h3>
          <div className="session-management__form-grid">
            <div className="session-management__field">
              <label>Session Number</label>
              <input
                type="number"
                value={editingSession.sessionNumber}
                onChange={(e) =>
                  setEditingSession({
                    ...editingSession,
                    sessionNumber: parseInt(e.target.value),
                  })
                }
                min="1"
              />
            </div>
            <div className="session-management__field">
              <label>Date</label>
              <input
                type="date"
                value={editingSession.date}
                onChange={(e) =>
                  setEditingSession({ ...editingSession, date: e.target.value })
                }
                required
              />
            </div>
            <div className="session-management__field">
              <label>Start Time</label>
              <input
                type="time"
                value={editingSession.startTime}
                onChange={(e) =>
                  setEditingSession({
                    ...editingSession,
                    startTime: e.target.value,
                  })
                }
                required
              />
            </div>
            <div className="session-management__field">
              <label>End Time</label>
              <input
                type="time"
                value={editingSession.endTime}
                onChange={(e) =>
                  setEditingSession({
                    ...editingSession,
                    endTime: e.target.value,
                  })
                }
                required
              />
            </div>
            <div className="session-management__field">
              <label>Topic</label>
              <input
                type="text"
                value={editingSession.topic || ""}
                onChange={(e) =>
                  setEditingSession({
                    ...editingSession,
                    topic: e.target.value,
                  })
                }
                placeholder="Session topic"
              />
            </div>
            <div className="session-management__field">
              <label>Location</label>
              <input
                type="text"
                value={editingSession.location || ""}
                onChange={(e) =>
                  setEditingSession({
                    ...editingSession,
                    location: e.target.value,
                  })
                }
                placeholder="Session location"
              />
            </div>
            <div className="session-management__field">
              <label>Status</label>
              <select
                value={editingSession.status}
                onChange={(e) =>
                  setEditingSession({
                    ...editingSession,
                    status: e.target.value as any,
                  })
                }
              >
                <option value="scheduled">Scheduled</option>
                <option value="completed">Completed</option>
                <option value="cancelled">Cancelled</option>
                <option value="rescheduled">Rescheduled</option>
              </select>
            </div>
          </div>
          <div className="session-management__field">
            <label>Description</label>
            <textarea
              value={editingSession.description || ""}
              onChange={(e) =>
                setEditingSession({
                  ...editingSession,
                  description: e.target.value,
                })
              }
              placeholder="Session description"
              rows={3}
            />
          </div>
          <div className="session-management__form-actions">
            <Button
              onClick={handleUpdateSession}
              loading={saving}
              disabled={saving}
            >
              {saving ? "Updating..." : "Update Session"}
            </Button>
            <Button
              variant="secondary"
              onClick={() => setEditingSession(null)}
              disabled={saving}
            >
              Cancel
            </Button>
          </div>
        </div>
      )}

      <div className="session-management__list">
        <h3>Course Sessions ({sessions.length})</h3>
        {sessions.length === 0 ? (
          <div className="session-management__empty">
            <p>No sessions scheduled yet.</p>
            <Button onClick={() => setShowAddForm(true)}>
              Add First Session
            </Button>
          </div>
        ) : (
          <div className="session-management__sessions">
            {sessions.map((session) => (
              <div
                key={session.id}
                className="session-management__session-card"
              >
                <div className="session-management__session-header">
                  <h4>Session {session.sessionNumber}</h4>
                  <div className="session-management__session-actions">
                    <Button
                      variant="secondary"
                      onClick={() => setEditingSession(session)}
                      disabled={saving}
                    >
                      Edit
                    </Button>
                    <Button
                      variant="secondary"
                      onClick={() => handleDeleteSession(session.id)}
                      disabled={saving}
                    >
                      Delete
                    </Button>
                  </div>
                </div>
                <div className="session-management__session-details">
                  <p>
                    <strong>Date:</strong> {formatSessionDate(session.date)}
                  </p>
                  <p>
                    <strong>Time:</strong>{" "}
                    {formatSessionTime(session.startTime, session.endTime)}
                  </p>
                  {session.topic && (
                    <p>
                      <strong>Topic:</strong> {session.topic}
                    </p>
                  )}
                  {session.description && (
                    <p>
                      <strong>Description:</strong> {session.description}
                    </p>
                  )}
                  <p>
                    <strong>Status:</strong>{" "}
                    <span
                      className={`session-status session-status--${session.status}`}
                    >
                      {session.status}
                    </span>
                  </p>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default SessionManagement;
