.session-management {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.session-management__loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

.session-management__header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  gap: 1rem;
}

.session-management__title h2 {
  margin: 0 0 0.5rem 0;
  color: #0a2d69;
  font-size: 1.5rem;
  font-weight: 600;
}

.session-management__actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

/* Add Form */
.session-management__add-form {
  background: #f8f9fa;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
}

/* Edit Form */
.session-management__edit-form {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.session-management__add-form h3,
.session-management__edit-form h3 {
  margin: 0 0 1rem 0;
  color: #0a2d69;
  font-size: 1.2rem;
  font-weight: 600;
}

.session-management__form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.session-management__field {
  display: flex;
  flex-direction: column;
}

.session-management__field label {
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

.session-management__field input,
.session-management__field textarea,
.session-management__field select {
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.session-management__field input:focus,
.session-management__field textarea:focus,
.session-management__field select:focus {
  outline: none;
  border-color: #0a2d69;
  box-shadow: 0 0 0 2px rgba(10, 45, 105, 0.1);
}

.session-management__form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-start;
  margin-top: 1rem;
}

/* Session List */
.session-management__list h3 {
  margin: 0 0 1rem 0;
  color: #0a2d69;
  font-size: 1.2rem;
  font-weight: 600;
}

.session-management__empty {
  text-align: center;
  padding: 3rem 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.session-management__empty p {
  margin: 0 0 1rem 0;
  color: #6b7280;
  font-size: 1rem;
}

.session-management__sessions {
  display: grid;
  gap: 1rem;
}

.session-management__session-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1.5rem;
  transition: box-shadow 0.2s ease;
}

.session-management__session-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.session-management__session-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid #e5e7eb;
}

.session-management__session-header h4 {
  margin: 0;
  color: #0a2d69;
  font-size: 1.1rem;
  font-weight: 600;
}

.session-management__session-actions {
  display: flex;
  gap: 0.5rem;
}

.session-management__session-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.75rem;
}

.session-management__session-details p {
  margin: 0;
  font-size: 0.875rem;
  color: #374151;
}

.session-management__session-details strong {
  color: #111827;
  font-weight: 500;
}

/* Session Status */
.session-status {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: capitalize;
}

.session-status--scheduled {
  background: #dbeafe;
  color: #1e40af;
}

.session-status--completed {
  background: #d1fae5;
  color: #065f46;
}

.session-status--cancelled {
  background: #fee2e2;
  color: #dc2626;
}

.session-status--rescheduled {
  background: #fef3c7;
  color: #d97706;
}

/* Responsive Design */
@media (max-width: 768px) {
  .session-management {
    padding: 1rem;
  }

  .session-management__header {
    flex-direction: column;
    align-items: stretch;
  }

  .session-management__actions {
    justify-content: stretch;
  }

  .session-management__actions button {
    flex: 1;
  }

  .session-management__form-grid {
    grid-template-columns: 1fr;
  }

  .session-management__session-header {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .session-management__session-actions {
    justify-content: stretch;
  }

  .session-management__session-actions button {
    flex: 1;
  }

  .session-management__session-details {
    grid-template-columns: 1fr;
  }

  .session-management__form-actions {
    flex-direction: column;
  }

  .session-management__form-actions button {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .session-management__session-actions {
    flex-direction: column;
  }
}
