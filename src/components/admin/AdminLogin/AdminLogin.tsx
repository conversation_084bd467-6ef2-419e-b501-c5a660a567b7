import React, { useState } from "react";
import Button from "../../ui/Button";
import { adminLogin, LoginCredentials } from "../../../services/adminService";
import { sendPasswordReset } from "../../../services/userService";
import "./AdminLogin.css";

interface AdminLoginProps {
  onLoginSuccess: () => void;
}

const AdminLogin: React.FC<AdminLoginProps> = ({ onLoginSuccess }) => {
  const [credentials, setCredentials] = useState<LoginCredentials>({
    email: "",
    password: "",
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [resetLoading, setResetLoading] = useState(false);
  const [resetMessage, setResetMessage] = useState("");

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setCredentials((prev) => ({
      ...prev,
      [name]: value,
    }));

    // Clear error when user starts typing
    if (error) {
      setError(null);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!credentials.email || !credentials.password) {
      setError("Please enter both email and password");
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const result = await adminLogin(credentials);

      if (result.success) {
        onLoginSuccess();
      } else {
        setError(result.error || "Login failed");
      }
    } catch (error) {
      setError("An unexpected error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  const handleForgotPassword = async () => {
    if (!credentials.email.trim()) {
      setError("Please enter your email address first");
      return;
    }

    // Check if this is a username (no @) - can't reset password for kiosk users
    if (!credentials.email.includes("@")) {
      setError(
        "Password reset is only available for email accounts. Contact your manager to reset kiosk passwords."
      );
      return;
    }

    setResetLoading(true);
    setError(null);
    setResetMessage("");

    try {
      await sendPasswordReset(credentials.email.trim());
      setResetMessage(
        "Password reset email sent! Check your inbox and spam folder."
      );
    } catch (error: any) {
      setError(error.message || "Failed to send password reset email");
    } finally {
      setResetLoading(false);
    }
  };

  return (
    <div className="admin-login">
      <div className="admin-login__container">
        <div className="admin-login__header">
          <h1>Admin Dashboard</h1>
          <p>Sign in to manage course registrations and capacity</p>
        </div>

        <form onSubmit={handleSubmit} className="admin-login__form">
          {error && <div className="admin-login__error">{error}</div>}
          {resetMessage && (
            <div className="admin-login__success">{resetMessage}</div>
          )}

          <div className="admin-login__field">
            <label htmlFor="email">Email / Username</label>
            <input
              type="text"
              id="email"
              name="email"
              value={credentials.email}
              onChange={handleInputChange}
              disabled={isLoading}
              placeholder="Email or kiosk username"
              autoComplete="email"
            />
          </div>

          <div className="admin-login__field">
            <label htmlFor="password">Password</label>
            <input
              type="password"
              id="password"
              name="password"
              value={credentials.password}
              onChange={handleInputChange}
              disabled={isLoading}
              placeholder="Enter your password"
              autoComplete="current-password"
            />
          </div>

          <Button
            type="submit"
            variant="primary"
            size="large"
            disabled={isLoading}
            className="admin-login__submit"
          >
            {isLoading ? "Signing In..." : "Sign In"}
          </Button>

          <div className="admin-login__forgot-password">
            <button
              type="button"
              onClick={handleForgotPassword}
              disabled={resetLoading}
              className="admin-login__forgot-link"
            >
              {resetLoading ? "Sending..." : "Forgot your password?"}
            </button>
          </div>
        </form>

        <div className="admin-login__info">
          <p>
            <strong>Default Credentials:</strong>
            <br />
            Email: <EMAIL>
            <br />
            Password: BitByBit2024!
          </p>
          <p className="admin-login__note">
            You can change these credentials in the admin service configuration.
          </p>
        </div>
      </div>
    </div>
  );
};

export default AdminLogin;
