import React, { useState, useEffect, useMemo } from "react";
import { useNavigate, useParams } from "react-router-dom";
import Button from "../../ui/Button";
import Spinner from "../../ui/Spinner";
import { generateSlug, generateUniqueSlug } from "../../../utils/slugUtils";
import {
  adminLogout,
  getAdminSession,
  onAdminAuthStateChanged,
} from "../../../services/adminService";
import {
  getRegistrationStats,
  getAllCourseCapacities,
  updateCourseCapacity,
  deleteRegistration,
  updatePaymentStatus,
  addRegistrationToCapacity,
  getAllUserHistories,
  UserHistory,
  getEffectivePaymentStatus,
  updateRegistrationDetails,
  updateEnrollmentStatus,
  getAllCourses,
  createCourse,
  updateCourse,
  archiveCourse,
  duplicateCourse,
  reorderCourses,
  migrateCourseDisplayOrder,
  Course,
} from "../../../services/firebaseCourseCapacityService";
import { getAllCategories, Category } from "../../../services/categoryService";
import CategoryManager from "../CategoryManager/CategoryManager";

import { formatPrice } from "../../../utils/priceUtils";

import EmailTemplates from "../EmailTemplates/EmailTemplates";
import CourseDetailView from "../CourseDetailView";
import Analytics from "../Analytics/Analytics";
import InstructorManagement from "../InstructorManagement";
import SessionManagement from "../SessionManagement/SessionManagement";
import CourseAssignment from "../CourseAssignment/CourseAssignment";

import "./AdminDashboard.css";

interface AdminDashboardProps {
  onLogout: () => void;
}

const AdminDashboard: React.FC<AdminDashboardProps> = ({ onLogout }) => {
  const navigate = useNavigate();
  const { tab } = useParams<{ tab?: string }>();

  const [stats, setStats] = useState<any>(null);
  const [capacities, setCapacities] = useState<any[]>([]);

  // Get active tab from URL, default to overview
  const activeTab =
    (tab as
      | "overview"
      | "registrations"
      | "capacity"
      | "users"
      | "analytics"
      | "courses"
      | "emails"
      | "instructors"
      | "assignments") || "overview";
  const [editingCapacity, setEditingCapacity] = useState<string | null>(null);
  const [newCapacity, setNewCapacity] = useState<number>(0);
  const [selectedCourse, setSelectedCourse] = useState<{
    courseId: string;
    courseTitle: string;
    maxStudents: number;
  } | null>(null);
  const [managingSessions, setManagingSessions] = useState<{
    courseId: string;
    courseTitle: string;
  } | null>(null);
  const [filters, setFilters] = useState({
    course: "",
    paymentStatus: "",
    searchTerm: "",
    enrollmentStatus: "",
  });
  const [loadingActions, setLoadingActions] = useState<{
    [key: string]: boolean;
  }>({});
  const [showAddRegistrationForm, setShowAddRegistrationForm] = useState(false);
  const [newRegistration, setNewRegistration] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    experienceLevel: "",
    specialRequirements: "",
    emergencyContactName: "",
    emergencyContactPhone: "",
    emergencyContactRelationship: "",
    paymentStatus: "pending" as
      | "pending"
      | "paid"
      | "refunded"
      | "late"
      | "cancelled",
    courseId: "",
  });
  const [addingRegistration, setAddingRegistration] = useState(false);
  const [editingRegistrationId, setEditingRegistrationId] = useState<
    string | null
  >(null);
  const [editingRegistrationData, setEditingRegistrationData] = useState<any>(
    {}
  );
  const [updatingRegistration, setUpdatingRegistration] = useState(false);
  const [userHistories, setUserHistories] = useState<UserHistory[]>([]);
  const [loadingUsers, setLoadingUsers] = useState(false);
  const [selectedUser, setSelectedUser] = useState<UserHistory | null>(null);
  const [userSearchTerm, setUserSearchTerm] = useState("");
  const [userSortBy, setUserSortBy] = useState<"name" | "courses" | "activity">(
    "activity"
  );
  const [courses, setCourses] = useState<Course[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loadingCourses, setLoadingCourses] = useState(false);
  const [showAddCourseForm, setShowAddCourseForm] = useState(false);
  const [editingCourseId, setEditingCourseId] = useState<string | null>(null);
  const [editingCourseData, setEditingCourseData] = useState<Partial<Course>>(
    {}
  );
  const [updatingCourse, setUpdatingCourse] = useState(false);
  const [newCourse, setNewCourse] = useState({
    slug: "",
    title: "",
    description: "",
    startDate: "",
    schedule: {
      daysOfWeek: [] as string[],
      time: "",
      duration: 90, // Default 90 minutes
    },
    price: "",
    outcomes: [] as string[],
    ageRange: "",
    maxStudents: 8,
    status: "draft" as "active" | "archived" | "draft",
    category: "",
    categories: [] as string[],
    tags: [] as string[],
    displayOrder: 0,
    location: "",
  });

  const adminUser = getAdminSession();

  // Helper function to navigate to different tabs
  const navigateToTab = (tabName: string) => {
    navigate(`/admin/${tabName}`);
  };

  // Helper functions for loading states
  const setActionLoading = (actionId: string, isLoading: boolean) => {
    setLoadingActions((prev) => ({ ...prev, [actionId]: isLoading }));
  };

  const isActionLoading = (actionId: string) => {
    return loadingActions[actionId] || false;
  };

  useEffect(() => {
    loadData();
  }, []);

  // Redirect to overview if no tab is specified
  useEffect(() => {
    if (!tab) {
      navigate("/admin/overview", { replace: true });
    }
  }, [tab, navigate]);

  // Load data based on active tab
  useEffect(() => {
    if (activeTab === "users" && userHistories.length === 0) {
      loadUserHistories();
    }
    if (activeTab === "courses") {
      if (courses.length === 0) {
        loadCourses();
      }
      if (categories.length === 0) {
        loadCategories();
      }
    }
  }, [activeTab, userHistories.length, courses.length, categories.length]);

  // Listen for auth state changes and logout if user becomes unauthenticated
  useEffect(() => {
    const unsubscribe = onAdminAuthStateChanged((user) => {
      if (!user) {
        // User is no longer authenticated, trigger logout
        onLogout();
      }
    });

    return () => unsubscribe();
  }, [onLogout]);

  const loadData = async () => {
    try {
      const registrationStats = await getRegistrationStats();
      const courseCapacities = await getAllCourseCapacities();
      setStats(registrationStats);
      setCapacities(courseCapacities);
    } catch (error) {
      console.error("Error loading dashboard data:", error);
    }
  };

  const loadUserHistories = async () => {
    setLoadingUsers(true);
    try {
      const histories = await getAllUserHistories();
      setUserHistories(histories);
    } catch (error) {
      console.error("Error loading user histories:", error);
    } finally {
      setLoadingUsers(false);
    }
  };

  const loadCourses = async () => {
    setLoadingCourses(true);
    try {
      const allCourses = await getAllCourses();
      setCourses(allCourses);
    } catch (error) {
      console.error("Error loading courses:", error);
    } finally {
      setLoadingCourses(false);
    }
  };

  const loadCategories = async () => {
    try {
      const fetchedCategories = await getAllCategories();
      setCategories(fetchedCategories);
    } catch (error) {
      console.error("Error loading categories:", error);
    }
  };

  // Filter and sort users
  const filteredAndSortedUsers = useMemo(() => {
    let filtered = userHistories;

    // Apply search filter
    if (userSearchTerm.trim()) {
      const searchLower = userSearchTerm.toLowerCase();
      filtered = filtered.filter(
        (user) =>
          user.firstName.toLowerCase().includes(searchLower) ||
          user.lastName.toLowerCase().includes(searchLower) ||
          user.email.toLowerCase().includes(searchLower)
      );
    }

    // Apply sorting
    const sorted = [...filtered].sort((a, b) => {
      switch (userSortBy) {
        case "name":
          const nameA = `${a.firstName} ${a.lastName}`.toLowerCase();
          const nameB = `${b.firstName} ${b.lastName}`.toLowerCase();
          return nameA.localeCompare(nameB);
        case "courses":
          return b.totalCourses - a.totalCourses;
        case "activity":
        default:
          return (
            new Date(b.lastActivity).getTime() -
            new Date(a.lastActivity).getTime()
          );
      }
    });

    return sorted;
  }, [userHistories, userSearchTerm, userSortBy]);

  const handleLogout = async () => {
    await adminLogout();
    onLogout();
  };

  const handleCapacityEdit = (courseId: string, currentCapacity: number) => {
    setEditingCapacity(courseId);
    setNewCapacity(currentCapacity);
  };

  const handleCapacitySave = async (courseId: string) => {
    if (newCapacity > 0) {
      await updateCourseCapacity(courseId, newCapacity);
      setEditingCapacity(null);
      loadData();
    }
  };

  const handleCapacityCancel = () => {
    setEditingCapacity(null);
    setNewCapacity(0);
  };

  const handleDeleteRegistration = async (registrationId: string) => {
    const actionId = `delete-${registrationId}`;

    // Set loading state first
    setActionLoading(actionId, true);

    // Small delay to show loading state before confirm dialog
    await new Promise((resolve) => setTimeout(resolve, 100));

    const confirmed = window.confirm(
      "Are you sure you want to delete this registration?"
    );

    if (!confirmed) {
      setActionLoading(actionId, false);
      return;
    }

    // Optimistic update - remove from UI immediately
    const originalStats = { ...stats };
    const updatedRegistrations = stats.recentRegistrations.filter(
      (reg: any) => reg.id !== registrationId
    );
    setStats({
      ...stats,
      recentRegistrations: updatedRegistrations,
      totalRegistrations: stats.totalRegistrations - 1,
    });

    try {
      await deleteRegistration(registrationId);
      loadData(); // Refresh to get accurate data
    } catch (error) {
      console.error("Error deleting registration:", error);
      // Revert optimistic update on error
      setStats(originalStats);
      alert("Failed to delete registration. Please try again.");
    } finally {
      setActionLoading(actionId, false);
    }
  };

  const handlePaymentStatusUpdate = async (
    documentId: string,
    newStatus: "pending" | "paid" | "refunded" | "late" | "cancelled",
    notes?: string
  ) => {
    const actionId = `payment-${documentId}`;
    setActionLoading(actionId, true);

    // Small delay to ensure loading state is visible
    await new Promise((resolve) => setTimeout(resolve, 200));

    // Optimistic update - update UI immediately
    const originalStats = { ...stats };
    const updatedRegistrations = stats.recentRegistrations.map((reg: any) =>
      reg.id === documentId ? { ...reg, paymentStatus: newStatus } : reg
    );
    setStats({
      ...stats,
      recentRegistrations: updatedRegistrations,
    });

    try {
      await updatePaymentStatus(documentId, newStatus, notes);
      loadData(); // Refresh to get accurate data
    } catch (error) {
      console.error("Error updating payment status:", error);
      // Revert optimistic update on error
      setStats(originalStats);
      alert("Failed to update payment status. Please try again.");
    } finally {
      setActionLoading(actionId, false);
    }
  };

  const handleCourseClick = (course: any) => {
    setSelectedCourse({
      courseId: course.courseId,
      courseTitle: course.courseTitle,
      maxStudents: course.capacity,
    });
  };

  const handleBackToDashboard = () => {
    setSelectedCourse(null);
    setManagingSessions(null);
    loadData(); // Refresh data when returning to dashboard
  };

  const handleManageSessions = (courseId: string, courseTitle: string) => {
    setManagingSessions({ courseId, courseTitle });
    setSelectedCourse(null);
  };

  const handleBackFromSessions = () => {
    setManagingSessions(null);
  };

  const getFilteredRegistrations = () => {
    if (!stats?.recentRegistrations) return [];

    return stats.recentRegistrations.filter((registration: any) => {
      const matchesCourse =
        !filters.course ||
        registration.courseTitle
          .toLowerCase()
          .includes(filters.course.toLowerCase());
      const matchesPayment =
        !filters.paymentStatus ||
        getEffectivePaymentStatus(registration) === filters.paymentStatus;
      const matchesEnrollment =
        !filters.enrollmentStatus ||
        registration.enrollmentStatus === filters.enrollmentStatus;
      const matchesSearch =
        !filters.searchTerm ||
        registration.firstName
          .toLowerCase()
          .includes(filters.searchTerm.toLowerCase()) ||
        registration.lastName
          .toLowerCase()
          .includes(filters.searchTerm.toLowerCase()) ||
        registration.email
          .toLowerCase()
          .includes(filters.searchTerm.toLowerCase());

      return (
        matchesCourse && matchesPayment && matchesEnrollment && matchesSearch
      );
    });
  };

  const handleFilterChange = (filterType: string, value: string) => {
    setFilters((prev) => ({
      ...prev,
      [filterType]: value,
    }));
  };

  const clearFilters = () => {
    setFilters({
      course: "",
      paymentStatus: "",
      searchTerm: "",
      enrollmentStatus: "",
    });
  };

  const handleStartEditRegistration = (registration: any) => {
    setEditingRegistrationId(registration.id);
    setEditingRegistrationData({ ...registration });
  };

  const handleCancelEditRegistration = () => {
    setEditingRegistrationId(null);
    setEditingRegistrationData({});
  };

  const handleSaveEditRegistration = async () => {
    if (!editingRegistrationId || !editingRegistrationData) return;

    if (
      !editingRegistrationData.firstName ||
      !editingRegistrationData.lastName ||
      !editingRegistrationData.email ||
      !editingRegistrationData.courseId
    ) {
      alert("Please fill in at least the name, email, and course fields.");
      return;
    }

    setUpdatingRegistration(true);
    try {
      const { id, originalCourseId, ...updates } = editingRegistrationData;

      // Update in Firebase
      await updateRegistrationDetails(editingRegistrationId, updates);

      setEditingRegistrationId(null);
      setEditingRegistrationData({});
      loadData(); // Refresh data
    } catch (error) {
      console.error("Error updating registration:", error);
      alert("Failed to update registration. Please try again.");
    } finally {
      setUpdatingRegistration(false);
    }
  };

  const handleEditRegistrationFieldChange = (field: string, value: any) => {
    setEditingRegistrationData((prev: any) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Enrollment Status Management
  const handlePromoteFromWaitlist = async (registrationId: string) => {
    const registration = stats.recentRegistrations.find(
      (reg: any) => reg.id === registrationId
    );
    if (!registration) return;

    const course = stats.courseBreakdown.find(
      (c: any) => c.courseId === registration.courseId
    );
    if (!course) return;

    const isAtCapacity = course.enrolled >= course.capacity;
    const confirmMessage = isAtCapacity
      ? "Promote this student from waitlist? This will increase the course capacity by 1."
      : "Promote this student from waitlist?";

    if (window.confirm(confirmMessage)) {
      const actionId = `promote-${registrationId}`;
      setActionLoading(actionId, true);

      // Optimistic update
      const originalStats = { ...stats };
      const updatedRegistrations = stats.recentRegistrations.map((reg: any) =>
        reg.id === registrationId
          ? { ...reg, enrollmentStatus: "enrolled" }
          : reg
      );
      setStats({
        ...stats,
        recentRegistrations: updatedRegistrations,
      });

      try {
        await updateEnrollmentStatus(registrationId, "enrolled");

        // Only increase course capacity if the course is currently at capacity
        if (isAtCapacity) {
          await updateCourseCapacity(
            registration.courseId,
            course.capacity + 1
          );
        }

        loadData(); // Refresh data

        const successMessage = isAtCapacity
          ? "Student promoted from waitlist! Course capacity increased by 1."
          : "Student promoted from waitlist!";
        alert(successMessage);
      } catch (error) {
        console.error("Error promoting student:", error);
        setStats(originalStats); // Revert optimistic update
        alert("Failed to promote student. Please try again.");
      } finally {
        setActionLoading(actionId, false);
      }
    }
  };

  const handleMoveToWaitlist = async (registrationId: string) => {
    const actionId = `waitlist-${registrationId}`;
    setActionLoading(actionId, true);

    // Small delay to show loading state before confirm dialog
    await new Promise((resolve) => setTimeout(resolve, 100));

    const confirmed = window.confirm(
      "Are you sure you want to move this student to the waitlist?"
    );

    if (!confirmed) {
      setActionLoading(actionId, false);
      return;
    }

    // Optimistic update
    const originalStats = { ...stats };
    const updatedRegistrations = stats.recentRegistrations.map((reg: any) =>
      reg.id === registrationId ? { ...reg, enrollmentStatus: "waitlist" } : reg
    );
    setStats({
      ...stats,
      recentRegistrations: updatedRegistrations,
    });

    try {
      await updateEnrollmentStatus(registrationId, "waitlist");
      loadData(); // Refresh data
    } catch (error) {
      console.error("Error moving to waitlist:", error);
      setStats(originalStats); // Revert optimistic update
      alert("Failed to move student to waitlist. Please try again.");
    } finally {
      setActionLoading(actionId, false);
    }
  };

  // Course Management Handlers
  const handleCreateCourse = async () => {
    // Auto-generate slug from title if not provided
    const slug =
      newCourse.slug ||
      generateUniqueSlug(
        generateSlug(newCourse.title),
        courses.map((c) => c.slug)
      );

    if (
      !newCourse.title ||
      !newCourse.description ||
      !newCourse.price ||
      !newCourse.startDate ||
      !newCourse.schedule.time ||
      newCourse.schedule.daysOfWeek.length === 0 ||
      !newCourse.ageRange
    ) {
      alert("Please fill in all required fields marked with *");
      return;
    }

    try {
      await createCourse({
        ...newCourse,
        slug,
      });
      setNewCourse({
        slug: "",
        title: "",
        description: "",
        startDate: "",
        schedule: {
          daysOfWeek: [],
          time: "",
          duration: 90,
        },
        price: "",
        outcomes: [],
        ageRange: "",
        maxStudents: 8,
        status: "draft",
        category: "",
        categories: [],
        tags: [],
        displayOrder: 0,
        location: "",
      });
      setShowAddCourseForm(false);
      loadCourses();
      loadData(); // Refresh dashboard data
    } catch (error) {
      console.error("Error creating course:", error);
      alert("Failed to create course. Please try again.");
    }
  };

  const handleStartEditCourse = (course: Course) => {
    setEditingCourseId(course.id);
    setEditingCourseData({ ...course });
    setShowAddCourseForm(false);
  };

  const handleSaveEditCourse = async () => {
    if (!editingCourseId || !editingCourseData) return;

    if (
      !editingCourseData.title?.trim() ||
      !editingCourseData.description?.trim() ||
      !editingCourseData.price
    ) {
      alert("Please fill in all required fields (title, description, price)");
      return;
    }

    setUpdatingCourse(true);
    try {
      const { id, createdAt, updatedAt, ...updates } = editingCourseData;
      await updateCourse(editingCourseId, updates);
      setEditingCourseId(null);
      setEditingCourseData({});
      loadCourses();
      loadData(); // Refresh dashboard data
      alert("Course updated successfully!");
    } catch (error) {
      console.error("Error updating course:", error);
      alert("Failed to update course. Please try again.");
    } finally {
      setUpdatingCourse(false);
    }
  };

  const handleCancelEditCourse = () => {
    setEditingCourseId(null);
    setEditingCourseData({});
  };

  const handleEditCourseFieldChange = (field: keyof Course, value: any) => {
    setEditingCourseData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleArchiveCourse = async (courseId: string) => {
    const confirmed = window.confirm(
      "Are you sure you want to archive this course? It will no longer be available for new registrations."
    );

    if (!confirmed) return;

    try {
      await archiveCourse(courseId);
      loadCourses();
      loadData(); // Refresh dashboard data
    } catch (error) {
      console.error("Error archiving course:", error);
      alert("Failed to archive course. Please try again.");
    }
  };

  const handleDuplicateCourse = async (courseId: string, title: string) => {
    const newTitle = prompt(
      "Enter title for the duplicated course:",
      `${title} (Copy)`
    );
    if (!newTitle) return;

    try {
      await duplicateCourse(courseId, newTitle);
      loadCourses();
    } catch (error) {
      console.error("Error duplicating course:", error);
      alert("Failed to duplicate course. Please try again.");
    }
  };

  const handleMoveCourseUp = async (courseIndex: number) => {
    if (courseIndex === 0) return; // Already at top

    const courseToMove = courses[courseIndex];
    const courseAbove = courses[courseIndex - 1];

    // Check if courses have display orders
    if (
      courseToMove.displayOrder === undefined ||
      courseAbove.displayOrder === undefined
    ) {
      const shouldMigrate = window.confirm(
        "Some courses don't have display order numbers yet. Would you like to run the migration first?"
      );
      if (shouldMigrate) {
        await handleMigrateCourseOrder();
        return;
      } else {
        alert(
          "Please run the 'Migrate Display Order' first to enable reordering."
        );
        return;
      }
    }

    try {
      await reorderCourses([
        { id: courseToMove.id, displayOrder: courseAbove.displayOrder },
        { id: courseAbove.id, displayOrder: courseToMove.displayOrder },
      ]);
      loadCourses();
    } catch (error) {
      console.error("Error moving course up:", error);
      alert("Failed to reorder course. Please try again.");
    }
  };

  const handleMoveCourseDown = async (courseIndex: number) => {
    if (courseIndex === courses.length - 1) return; // Already at bottom

    const courseToMove = courses[courseIndex];
    const courseBelow = courses[courseIndex + 1];

    // Check if courses have display orders
    if (
      courseToMove.displayOrder === undefined ||
      courseBelow.displayOrder === undefined
    ) {
      const shouldMigrate = window.confirm(
        "Some courses don't have display order numbers yet. Would you like to run the migration first?"
      );
      if (shouldMigrate) {
        await handleMigrateCourseOrder();
        return;
      } else {
        alert(
          "Please run the 'Migrate Display Order' first to enable reordering."
        );
        return;
      }
    }

    try {
      await reorderCourses([
        { id: courseToMove.id, displayOrder: courseBelow.displayOrder },
        { id: courseBelow.id, displayOrder: courseToMove.displayOrder },
      ]);
      loadCourses();
    } catch (error) {
      console.error("Error moving course down:", error);
      alert("Failed to reorder course. Please try again.");
    }
  };

  const handleMigrateCourseOrder = async () => {
    const confirmed = window.confirm(
      "This will add display order numbers to existing courses. This is safe to run multiple times. Continue?"
    );
    if (!confirmed) return;

    try {
      await migrateCourseDisplayOrder();
      await loadCourses(); // Reload courses to get updated display orders
      alert(
        "Course display order migration completed successfully! You can now reorder courses using the up/down arrows."
      );
    } catch (error) {
      console.error("Error migrating course order:", error);
      alert("Failed to migrate course order. Please try again.");
    }
  };

  // Migration function to add categories to existing courses
  const handleMigrateCourseCategories = async () => {
    const confirmed = window.confirm(
      "This will add structured categories to existing courses based on their titles and content. This is safe to run multiple times. Continue?"
    );
    if (!confirmed) return;

    try {
      // Define category mappings based on course content
      const categoryMappings: Record<string, string[]> = {
        "coding-foundations-kids": ["Programming Languages", "Introductory"],
        "web-foundations-teens-adults": ["Web Development", "Foundational"],
        "weather-app-project": ["Web Development", "Intermediate"],
        "personal-finance-tracker": [
          "Web Development",
          "Database Management",
          "Intermediate",
        ],
        "social-media-dashboard": [
          "Web Development",
          "Data Science",
          "Advanced",
        ],
        "mobile-app-project": ["Mobile Development", "Advanced"],
        "ai-chatbot-project": ["Artificial Intelligence", "Advanced"],
        "game-development-project": ["Game Development", "Intermediate"],
      };

      let migratedCount = 0;
      for (const course of courses) {
        const suggestedCategories = categoryMappings[course.slug];
        if (
          suggestedCategories &&
          (!course.categories || course.categories.length === 0)
        ) {
          await updateCourse(course.id, { categories: suggestedCategories });
          migratedCount++;
        }
      }

      await loadCourses(); // Reload courses to see updated categories
      alert(
        `Course categories migration completed successfully! ${migratedCount} courses updated with structured categories.`
      );
    } catch (error) {
      console.error("Error migrating course categories:", error);
      alert("Failed to migrate course categories. Please try again.");
    }
  };

  // Helper functions for array fields
  const addOutcome = () => {
    setNewCourse((prev) => ({
      ...prev,
      outcomes: [...prev.outcomes, ""],
    }));
  };

  const updateOutcome = (index: number, value: string) => {
    setNewCourse((prev) => ({
      ...prev,
      outcomes: prev.outcomes.map((outcome, i) =>
        i === index ? value : outcome
      ),
    }));
  };

  const removeOutcome = (index: number) => {
    setNewCourse((prev) => ({
      ...prev,
      outcomes: prev.outcomes.filter((_, i) => i !== index),
    }));
  };

  // Get available categories from database
  const availableCategories: string[] = categories.map((cat) => cat.name);

  // Helper functions for managing categories
  const toggleCategory = (category: string) => {
    setNewCourse((prev) => ({
      ...prev,
      categories: prev.categories.includes(category)
        ? prev.categories.filter((c) => c !== category)
        : [...prev.categories, category],
    }));
  };

  const handleAddRegistration = async () => {
    if (
      !newRegistration.firstName ||
      !newRegistration.lastName ||
      !newRegistration.email ||
      !newRegistration.courseId
    ) {
      alert("Please fill in at least the name, email, and course fields.");
      return;
    }

    setAddingRegistration(true);
    try {
      const registrationId = `ADMIN-${Date.now().toString(36).toUpperCase()}`;
      const registrationDate = new Date().toISOString();

      // Find the selected course to get course details
      const selectedCourse = stats.courseBreakdown.find(
        (course: any) => course.courseId === newRegistration.courseId
      );
      if (!selectedCourse) {
        alert("Selected course not found.");
        return;
      }

      // Get the actual course price from the courses collection
      // Try to find course in the courses array first
      let courseDetails = courses.find(
        (course) =>
          course.id === newRegistration.courseId ||
          course.slug === newRegistration.courseId
      );

      // If not found in courses array, try to get it directly from Firebase
      // This handles cases where the courses array hasn't been loaded yet
      if (!courseDetails && newRegistration.courseId) {
        try {
          const allCourses = await getAllCourses();
          courseDetails = allCourses.find(
            (course) =>
              course.id === newRegistration.courseId ||
              course.slug === newRegistration.courseId
          );
        } catch (error) {
          console.error("Error fetching course details:", error);
        }
      }

      const coursePrice = courseDetails?.price || "N/A";

      const registration = {
        ...newRegistration,
        courseTitle: selectedCourse.courseTitle,
        coursePrice: coursePrice,
        registrationDate,
        registrationId,
        enrollmentStatus: (selectedCourse.enrolled < selectedCourse.capacity
          ? "enrolled"
          : "waitlist") as "enrolled" | "waitlist",
      };

      await addRegistrationToCapacity(registration);

      // Reset form
      setNewRegistration({
        firstName: "",
        lastName: "",
        email: "",
        phone: "",
        experienceLevel: "",
        specialRequirements: "",
        emergencyContactName: "",
        emergencyContactPhone: "",
        emergencyContactRelationship: "",
        paymentStatus: "pending",
        courseId: "",
      });

      setShowAddRegistrationForm(false);
      loadData(); // Refresh data
    } catch (error) {
      console.error("Error adding registration:", error);
      alert("Failed to add registration. Please try again.");
    } finally {
      setAddingRegistration(false);
    }
  };

  if (!stats) {
    return <div className="admin-dashboard__loading">Loading dashboard...</div>;
  }

  // Show session management if managing sessions
  if (managingSessions) {
    return (
      <SessionManagement
        courseId={managingSessions.courseId}
        courseTitle={managingSessions.courseTitle}
        onBack={handleBackFromSessions}
      />
    );
  }

  // Show course detail view if a course is selected
  if (selectedCourse) {
    return (
      <CourseDetailView
        courseId={selectedCourse.courseId}
        courseTitle={selectedCourse.courseTitle}
        maxStudents={selectedCourse.maxStudents}
        onBack={handleBackToDashboard}
        onRefresh={loadData}
        onManageSessions={() =>
          handleManageSessions(
            selectedCourse.courseId,
            selectedCourse.courseTitle
          )
        }
      />
    );
  }

  return (
    <div className="admin-dashboard">
      <header className="admin-dashboard__header">
        <div className="admin-dashboard__header-content">
          <div>
            <h1>Admin Dashboard</h1>
            <p>Welcome back, {adminUser?.name}</p>
          </div>
          <Button
            variant="secondary"
            onClick={handleLogout}
            className="admin-dashboard__logout-btn"
          >
            Sign Out
          </Button>
        </div>
      </header>

      <nav className="admin-dashboard__nav">
        <button
          className={`admin-dashboard__nav-item ${
            activeTab === "overview" ? "active" : ""
          }`}
          onClick={() => navigateToTab("overview")}
        >
          Overview
        </button>
        <button
          className={`admin-dashboard__nav-item ${
            activeTab === "registrations" ? "active" : ""
          }`}
          onClick={() => navigateToTab("registrations")}
        >
          Registrations
        </button>
        <button
          className={`admin-dashboard__nav-item ${
            activeTab === "capacity" ? "active" : ""
          }`}
          onClick={() => navigateToTab("capacity")}
        >
          Course Capacity
        </button>
        <button
          className={`admin-dashboard__nav-item ${
            activeTab === "users" ? "active" : ""
          }`}
          onClick={() => navigateToTab("users")}
        >
          Users
        </button>
        <button
          className={`admin-dashboard__nav-item ${
            activeTab === "analytics" ? "active" : ""
          }`}
          onClick={() => navigateToTab("analytics")}
        >
          Analytics
        </button>
        <button
          className={`admin-dashboard__nav-item ${
            activeTab === "courses" ? "active" : ""
          }`}
          onClick={() => navigateToTab("courses")}
        >
          Courses
        </button>
        <button
          className={`admin-dashboard__nav-item ${
            activeTab === "emails" ? "active" : ""
          }`}
          onClick={() => navigateToTab("emails")}
        >
          Email Templates
        </button>
        <button
          className={`admin-dashboard__nav-item ${
            activeTab === "instructors" ? "active" : ""
          }`}
          onClick={() => navigateToTab("instructors")}
        >
          Instructors
        </button>
        <button
          className={`admin-dashboard__nav-item ${
            activeTab === "assignments" ? "active" : ""
          }`}
          onClick={() => navigateToTab("assignments")}
        >
          Course Assignments
        </button>
      </nav>

      <main className="admin-dashboard__content">
        {activeTab === "overview" && (
          <div className="admin-dashboard__overview">
            <div className="admin-dashboard__stats">
              <div className="admin-dashboard__stat-card">
                <h3>Total Registrations</h3>
                <div className="admin-dashboard__stat-number">
                  {stats.totalRegistrations}
                </div>
              </div>
              <div className="admin-dashboard__stat-card">
                <h3>Paid Students</h3>
                <div className="admin-dashboard__stat-number">
                  {stats.paymentStats?.paid || 0}
                </div>
                <div className="admin-dashboard__stat-subtitle">
                  {stats.paymentStats?.pending || 0} pending
                </div>
              </div>
              <div className="admin-dashboard__stat-card">
                <h3>Current Enrollment</h3>
                <div className="admin-dashboard__stat-number">
                  {stats.totalEnrolled}
                </div>
                <div className="admin-dashboard__stat-subtitle">
                  of {stats.totalCapacity} capacity
                </div>
              </div>
              <div className="admin-dashboard__stat-card">
                <h3>Utilization Rate</h3>
                <div className="admin-dashboard__stat-number">
                  {stats.utilizationRate.toFixed(1)}%
                </div>
              </div>
              <div className="admin-dashboard__stat-card">
                <h3>Waitlist Students</h3>
                <div className="admin-dashboard__stat-number">
                  {stats.courseBreakdown.reduce(
                    (sum: number, course: any) => sum + course.waitlist,
                    0
                  )}
                </div>
                <div className="admin-dashboard__stat-subtitle">
                  across all courses
                </div>
              </div>
              <div className="admin-dashboard__stat-card">
                <h3>Total Revenue</h3>
                <div className="admin-dashboard__stat-number">
                  ${stats.totalRevenue?.toLocaleString() || "0"}
                </div>
                <div className="admin-dashboard__stat-subtitle">
                  from paid registrations
                </div>
              </div>
            </div>

            <div className="admin-dashboard__section">
              <h2>Course Breakdown</h2>
              <div className="admin-dashboard__course-grid">
                {stats.courseBreakdown.map((course: any) => (
                  <div
                    key={course.courseId}
                    className="admin-dashboard__course-card admin-dashboard__course-card--clickable"
                    onClick={() => handleCourseClick(course)}
                  >
                    <h4>{course.courseTitle}</h4>
                    <div className="admin-dashboard__course-stats">
                      <span>
                        {course.enrolled} / {course.capacity} students
                      </span>
                      {course.waitlist > 0 && (
                        <span className="admin-dashboard__waitlist-count">
                          {course.waitlist} on waitlist
                        </span>
                      )}
                      <span
                        className={`admin-dashboard__status admin-dashboard__status--${course.status}`}
                      >
                        {course.status}
                      </span>
                    </div>
                    <div className="admin-dashboard__progress-bar">
                      <div
                        className="admin-dashboard__progress-fill"
                        style={{ width: `${course.utilizationRate}%` }}
                      />
                    </div>
                    <div className="admin-dashboard__course-card-hint">
                      Click to view students →
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="admin-dashboard__section">
              <h2>Recent Registrations</h2>
              <div className="admin-dashboard__recent-list">
                {stats.recentRegistrations
                  .slice(0, 5)
                  .map((registration: any) => (
                    <div
                      key={registration.id}
                      className="admin-dashboard__recent-item"
                    >
                      <div className="admin-dashboard__recent-item-info">
                        <strong>
                          {registration.firstName} {registration.lastName}
                        </strong>
                        <span className="admin-dashboard__recent-course">
                          {registration.courseTitle}
                        </span>
                        <div className="admin-dashboard__recent-status">
                          <span
                            className={`admin-dashboard__payment-badge admin-dashboard__payment-badge--${registration.paymentStatus}`}
                          >
                            {registration.paymentStatus?.toUpperCase() ||
                              "PENDING"}
                          </span>
                          <span
                            className={`admin-dashboard__enrollment-badge admin-dashboard__enrollment-badge--${registration.enrollmentStatus}`}
                          >
                            {registration.enrollmentStatus?.toUpperCase() ||
                              "ENROLLED"}
                          </span>
                        </div>
                      </div>
                      <div className="admin-dashboard__recent-date">
                        {new Date(
                          registration.registrationDate
                        ).toLocaleDateString()}
                      </div>
                    </div>
                  ))}
              </div>
            </div>
          </div>
        )}

        {activeTab === "registrations" && (
          <div className="admin-dashboard__registrations">
            <div className="admin-dashboard__registrations-header">
              <div className="admin-dashboard__registrations-title">
                <h2>
                  All Registrations ({getFilteredRegistrations().length} of{" "}
                  {stats.totalRegistrations})
                </h2>
                <Button onClick={() => setShowAddRegistrationForm(true)}>
                  Add Registration
                </Button>
              </div>

              <div className="admin-dashboard__filters">
                <div className="admin-dashboard__filter-group">
                  <input
                    type="text"
                    placeholder="Search by name or email..."
                    value={filters.searchTerm}
                    onChange={(e) =>
                      handleFilterChange("searchTerm", e.target.value)
                    }
                    className="admin-dashboard__filter-input"
                  />

                  <select
                    value={filters.course}
                    onChange={(e) =>
                      handleFilterChange("course", e.target.value)
                    }
                    className="admin-dashboard__filter-select"
                  >
                    <option value="">All Courses</option>
                    {Array.from(
                      new Set(
                        stats.recentRegistrations.map((r: any) => r.courseTitle)
                      )
                    ).map((course: any) => (
                      <option key={course} value={course}>
                        {course}
                      </option>
                    ))}
                  </select>

                  <select
                    value={filters.paymentStatus}
                    onChange={(e) =>
                      handleFilterChange("paymentStatus", e.target.value)
                    }
                    className="admin-dashboard__filter-select"
                  >
                    <option value="">All Payment Status</option>
                    <option value="pending">Pending</option>
                    <option value="paid">Paid</option>
                    <option value="refunded">Refunded</option>
                    <option value="late">Late</option>
                    <option value="cancelled">Cancelled</option>
                  </select>

                  <select
                    value={filters.enrollmentStatus}
                    onChange={(e) =>
                      handleFilterChange("enrollmentStatus", e.target.value)
                    }
                    className="admin-dashboard__filter-select"
                  >
                    <option value="">All Enrollment Status</option>
                    <option value="enrolled">Enrolled</option>
                    <option value="waitlist">Waitlist</option>
                  </select>

                  <Button
                    variant="secondary"
                    size="small"
                    onClick={clearFilters}
                  >
                    Clear Filters
                  </Button>
                </div>
              </div>
            </div>

            {showAddRegistrationForm && (
              <div className="admin-dashboard__add-form">
                <h3>Add New Registration</h3>
                <div className="admin-dashboard__form-grid">
                  <input
                    type="text"
                    placeholder="First Name *"
                    value={newRegistration.firstName}
                    onChange={(e) =>
                      setNewRegistration((prev) => ({
                        ...prev,
                        firstName: e.target.value,
                      }))
                    }
                  />
                  <input
                    type="text"
                    placeholder="Last Name *"
                    value={newRegistration.lastName}
                    onChange={(e) =>
                      setNewRegistration((prev) => ({
                        ...prev,
                        lastName: e.target.value,
                      }))
                    }
                  />
                  <input
                    type="email"
                    placeholder="Email *"
                    value={newRegistration.email}
                    onChange={(e) =>
                      setNewRegistration((prev) => ({
                        ...prev,
                        email: e.target.value,
                      }))
                    }
                  />
                  <input
                    type="tel"
                    placeholder="Phone"
                    value={newRegistration.phone}
                    onChange={(e) =>
                      setNewRegistration((prev) => ({
                        ...prev,
                        phone: e.target.value,
                      }))
                    }
                  />
                  <input
                    type="text"
                    placeholder="Emergency Contact Name"
                    value={newRegistration.emergencyContactName}
                    onChange={(e) =>
                      setNewRegistration((prev) => ({
                        ...prev,
                        emergencyContactName: e.target.value,
                      }))
                    }
                  />
                  <input
                    type="tel"
                    placeholder="Emergency Contact Phone"
                    value={newRegistration.emergencyContactPhone}
                    onChange={(e) =>
                      setNewRegistration((prev) => ({
                        ...prev,
                        emergencyContactPhone: e.target.value,
                      }))
                    }
                  />
                  <select
                    value={newRegistration.emergencyContactRelationship}
                    onChange={(e) =>
                      setNewRegistration((prev) => ({
                        ...prev,
                        emergencyContactRelationship: e.target.value,
                      }))
                    }
                  >
                    <option value="">Emergency Contact Relationship</option>
                    <option value="parent">Parent</option>
                    <option value="guardian">Guardian</option>
                    <option value="grandparent">Grandparent</option>
                    <option value="sibling">Sibling</option>
                    <option value="aunt-uncle">Aunt/Uncle</option>
                    <option value="family-friend">Family Friend</option>
                    <option value="other">Other</option>
                  </select>
                  <select
                    value={newRegistration.courseId}
                    onChange={(e) =>
                      setNewRegistration((prev) => ({
                        ...prev,
                        courseId: e.target.value,
                      }))
                    }
                  >
                    <option value="">Select Course *</option>
                    {stats.courseBreakdown.map((course: any) => (
                      <option key={course.courseId} value={course.courseId}>
                        {course.courseTitle} ({course.enrolled}/
                        {course.capacity}
                        {course.waitlist > 0
                          ? `, ${course.waitlist} waitlist`
                          : ""}
                        )
                      </option>
                    ))}
                  </select>
                  <select
                    value={newRegistration.experienceLevel}
                    onChange={(e) =>
                      setNewRegistration((prev) => ({
                        ...prev,
                        experienceLevel: e.target.value,
                      }))
                    }
                  >
                    <option value="">Experience Level</option>
                    <option value="Complete Beginner">Complete Beginner</option>
                    <option value="Some Experience">Some Experience</option>
                    <option value="Intermediate">Intermediate</option>
                    <option value="Advanced">Advanced</option>
                  </select>
                  <select
                    value={newRegistration.paymentStatus}
                    onChange={(e) =>
                      setNewRegistration((prev) => ({
                        ...prev,
                        paymentStatus: e.target.value as
                          | "pending"
                          | "paid"
                          | "refunded"
                          | "late"
                          | "cancelled",
                      }))
                    }
                  >
                    <option value="pending">Payment Pending</option>
                    <option value="paid">Already Paid</option>
                    <option value="refunded">Refunded</option>
                    <option value="late">Late</option>
                    <option value="cancelled">Cancelled</option>
                  </select>
                </div>
                <textarea
                  placeholder="Special Requirements or Notes"
                  value={newRegistration.specialRequirements}
                  onChange={(e) =>
                    setNewRegistration((prev) => ({
                      ...prev,
                      specialRequirements: e.target.value,
                    }))
                  }
                  rows={3}
                />
                <div className="admin-dashboard__form-actions">
                  <Button
                    loading={addingRegistration}
                    onClick={handleAddRegistration}
                  >
                    Add Registration
                  </Button>
                  <Button
                    variant="secondary"
                    disabled={addingRegistration}
                    onClick={() => setShowAddRegistrationForm(false)}
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            )}

            <div className="admin-dashboard__table-container">
              <table className="admin-dashboard__table">
                <thead>
                  <tr>
                    <th>Student</th>
                    <th>Course</th>
                    <th>Price</th>
                    <th>Email</th>
                    <th>Emergency Contact</th>
                    <th>Enrollment</th>
                    <th>Payment Status</th>
                    <th>Date</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {getFilteredRegistrations().map((registration: any) => {
                    const isEditing = editingRegistrationId === registration.id;
                    const editData = isEditing
                      ? editingRegistrationData
                      : registration;

                    return (
                      <tr
                        key={registration.id}
                        className={isEditing ? "editing" : ""}
                      >
                        <td>
                          {isEditing ? (
                            <div className="admin-dashboard__inline-edit-names">
                              <input
                                type="text"
                                value={editData.firstName || ""}
                                onChange={(e) =>
                                  handleEditRegistrationFieldChange(
                                    "firstName",
                                    e.target.value
                                  )
                                }
                                placeholder="First Name"
                                className="admin-dashboard__inline-input"
                              />
                              <input
                                type="text"
                                value={editData.lastName || ""}
                                onChange={(e) =>
                                  handleEditRegistrationFieldChange(
                                    "lastName",
                                    e.target.value
                                  )
                                }
                                placeholder="Last Name"
                                className="admin-dashboard__inline-input"
                              />
                            </div>
                          ) : (
                            `${registration.firstName} ${registration.lastName}`
                          )}
                        </td>
                        <td>{registration.courseTitle}</td>
                        <td>
                          <span className="admin-dashboard__price">
                            {formatPrice(registration.coursePrice)}
                          </span>
                        </td>
                        <td>
                          {isEditing ? (
                            <input
                              type="email"
                              value={editData.email || ""}
                              onChange={(e) =>
                                handleEditRegistrationFieldChange(
                                  "email",
                                  e.target.value
                                )
                              }
                              placeholder="Email"
                              className="admin-dashboard__inline-input"
                            />
                          ) : (
                            registration.email
                          )}
                        </td>
                        <td>
                          {registration.emergencyContactName ? (
                            <div className="admin-dashboard__emergency-contact">
                              <div className="admin-dashboard__emergency-contact-name">
                                {registration.emergencyContactName}
                              </div>
                              <div className="admin-dashboard__emergency-contact-details">
                                {registration.emergencyContactPhone} (
                                {registration.emergencyContactRelationship})
                              </div>
                            </div>
                          ) : (
                            <span className="admin-dashboard__missing-data">
                              No emergency contact
                            </span>
                          )}
                        </td>
                        <td>
                          <div
                            className={`admin-dashboard__enrollment-status ${
                              isActionLoading(`promote-${registration.id}`) ||
                              isActionLoading(`waitlist-${registration.id}`)
                                ? "admin-dashboard__enrollment-status--loading"
                                : ""
                            }`}
                          >
                            {(isActionLoading(`promote-${registration.id}`) ||
                              isActionLoading(
                                `waitlist-${registration.id}`
                              )) && (
                              <div className="admin-dashboard__enrollment-loading">
                                <Spinner size="small" color="primary" />
                              </div>
                            )}
                            <span
                              className={`admin-dashboard__enrollment-badge admin-dashboard__enrollment-badge--${
                                registration.enrollmentStatus || "enrolled"
                              }`}
                            >
                              {(
                                registration.enrollmentStatus || "enrolled"
                              ).toUpperCase()}
                            </span>
                            {registration.enrollmentStatus === "waitlist" ? (
                              <Button
                                variant="primary"
                                size="small"
                                loading={isActionLoading(
                                  `promote-${registration.id}`
                                )}
                                onClick={() =>
                                  handlePromoteFromWaitlist(registration.id)
                                }
                                disabled={
                                  isActionLoading(
                                    `promote-${registration.id}`
                                  ) ||
                                  isActionLoading(`waitlist-${registration.id}`)
                                }
                              >
                                Promote
                              </Button>
                            ) : (
                              <Button
                                variant="secondary"
                                size="small"
                                loading={isActionLoading(
                                  `waitlist-${registration.id}`
                                )}
                                onClick={() =>
                                  handleMoveToWaitlist(registration.id)
                                }
                                disabled={
                                  isActionLoading(
                                    `promote-${registration.id}`
                                  ) ||
                                  isActionLoading(`waitlist-${registration.id}`)
                                }
                              >
                                Move to Waitlist
                              </Button>
                            )}
                          </div>
                        </td>
                        <td>
                          <div
                            className={`admin-dashboard__payment-status ${
                              isActionLoading(`payment-${registration.id}`)
                                ? "admin-dashboard__payment-status--loading"
                                : ""
                            }`}
                          >
                            {isActionLoading(`payment-${registration.id}`) && (
                              <div className="admin-dashboard__payment-loading">
                                <Spinner size="small" color="primary" />
                              </div>
                            )}
                            <span
                              className={`admin-dashboard__payment-badge admin-dashboard__payment-badge--${getEffectivePaymentStatus(
                                registration
                              )}`}
                            >
                              {getEffectivePaymentStatus(
                                registration
                              ).toUpperCase()}
                            </span>
                            <select
                              value={registration.paymentStatus || "pending"}
                              onChange={(e) =>
                                handlePaymentStatusUpdate(
                                  registration.id,
                                  e.target.value as
                                    | "pending"
                                    | "paid"
                                    | "refunded"
                                    | "late"
                                    | "cancelled"
                                )
                              }
                              className="admin-dashboard__payment-select"
                              disabled={isActionLoading(
                                `payment-${registration.id}`
                              )}
                            >
                              <option value="pending">Pending</option>
                              <option value="paid">Paid</option>
                              <option value="refunded">Refunded</option>
                              <option value="late">Late</option>
                              <option value="cancelled">Cancelled</option>
                            </select>
                          </div>
                        </td>
                        <td>
                          {new Date(
                            registration.registrationDate
                          ).toLocaleDateString()}
                        </td>
                        <td>
                          <div className="admin-dashboard__action-buttons">
                            {isEditing ? (
                              <>
                                <Button
                                  variant="primary"
                                  size="small"
                                  onClick={handleSaveEditRegistration}
                                  loading={updatingRegistration}
                                  disabled={updatingRegistration}
                                >
                                  {updatingRegistration ? "Saving..." : "Save"}
                                </Button>
                                <Button
                                  variant="secondary"
                                  size="small"
                                  onClick={handleCancelEditRegistration}
                                  disabled={updatingRegistration}
                                >
                                  Cancel
                                </Button>
                              </>
                            ) : (
                              <>
                                <Button
                                  variant="secondary"
                                  size="small"
                                  onClick={() =>
                                    handleStartEditRegistration(registration)
                                  }
                                >
                                  Edit
                                </Button>
                                <Button
                                  variant="secondary"
                                  size="small"
                                  loading={isActionLoading(
                                    `delete-${registration.id}`
                                  )}
                                  onClick={() =>
                                    handleDeleteRegistration(registration.id)
                                  }
                                >
                                  Delete
                                </Button>
                              </>
                            )}
                          </div>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {activeTab === "capacity" && (
          <div className="admin-dashboard__capacity">
            <h2>Course Capacity Management</h2>
            <div className="admin-dashboard__capacity-grid">
              {capacities.map((course) => (
                <div
                  key={course.courseId}
                  className="admin-dashboard__capacity-card"
                >
                  <h4>{course.courseTitle}</h4>
                  <div className="admin-dashboard__capacity-info">
                    <span>Current: {course.currentEnrollment} students</span>
                    <div className="admin-dashboard__capacity-edit">
                      {editingCapacity === course.courseId ? (
                        <div className="admin-dashboard__capacity-form">
                          <input
                            type="number"
                            value={newCapacity}
                            onChange={(e) =>
                              setNewCapacity(parseInt(e.target.value) || 0)
                            }
                            min="1"
                            max="20"
                          />
                          <Button
                            size="small"
                            onClick={() => handleCapacitySave(course.courseId)}
                          >
                            Save
                          </Button>
                          <Button
                            variant="secondary"
                            size="small"
                            onClick={handleCapacityCancel}
                          >
                            Cancel
                          </Button>
                        </div>
                      ) : (
                        <div className="admin-dashboard__capacity-display">
                          <span>Max: {course.maxStudents}</span>
                          <Button
                            variant="secondary"
                            size="small"
                            onClick={() =>
                              handleCapacityEdit(
                                course.courseId,
                                course.maxStudents
                              )
                            }
                          >
                            Edit
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>
                  <div
                    className={`admin-dashboard__status admin-dashboard__status--${course.status}`}
                  >
                    {course.status}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {activeTab === "users" && (
          <div className="admin-dashboard__users">
            <div className="admin-dashboard__users-header">
              <h2>
                User History ({filteredAndSortedUsers.length} of{" "}
                {userHistories.length} users)
              </h2>
              <Button onClick={loadUserHistories} disabled={loadingUsers}>
                {loadingUsers ? "Refreshing..." : "Refresh"}
              </Button>
            </div>

            {/* User Filters */}
            <div className="admin-dashboard__users-filters">
              <div className="admin-dashboard__filter-group">
                <input
                  type="text"
                  placeholder="Search by name or email..."
                  value={userSearchTerm}
                  onChange={(e) => setUserSearchTerm(e.target.value)}
                  className="admin-dashboard__filter-input"
                />
                <select
                  value={userSortBy}
                  onChange={(e) =>
                    setUserSortBy(
                      e.target.value as "name" | "courses" | "activity"
                    )
                  }
                  className="admin-dashboard__filter-select"
                >
                  <option value="activity">Sort by Recent Activity</option>
                  <option value="name">Sort by Name</option>
                  <option value="courses">Sort by Course Count</option>
                </select>
                {userSearchTerm && (
                  <Button
                    variant="secondary"
                    size="small"
                    onClick={() => setUserSearchTerm("")}
                  >
                    Clear Search
                  </Button>
                )}
              </div>
            </div>

            {loadingUsers ? (
              <div className="admin-dashboard__loading">
                <Spinner size="large" color="primary" />
                <p>Loading user histories...</p>
              </div>
            ) : selectedUser ? (
              <div className="admin-dashboard__user-detail">
                <div className="admin-dashboard__user-detail-header">
                  <div>
                    <h3>
                      {selectedUser.firstName} {selectedUser.lastName}
                    </h3>
                    <p className="admin-dashboard__user-email">
                      {selectedUser.email}
                    </p>
                    <div className="admin-dashboard__user-stats">
                      <span>
                        {selectedUser.totalCourses} course
                        {selectedUser.totalCourses !== 1 ? "s" : ""} registered
                      </span>
                      <span>
                        {selectedUser.completedCourses} course
                        {selectedUser.completedCourses !== 1 ? "s" : ""}{" "}
                        finished
                      </span>
                      <span>
                        Last activity:{" "}
                        {new Date(
                          selectedUser.lastActivity
                        ).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                  <Button
                    variant="secondary"
                    onClick={() => setSelectedUser(null)}
                  >
                    Back to Users
                  </Button>
                </div>

                <div className="admin-dashboard__user-courses">
                  <h4>Course History</h4>
                  <div className="admin-dashboard__user-course-list">
                    {selectedUser.registrations.map((registration) => (
                      <div
                        key={registration.id}
                        className="admin-dashboard__user-course-card"
                      >
                        <div className="admin-dashboard__user-course-info">
                          <h5>{registration.courseTitle}</h5>
                          <div className="admin-dashboard__user-course-details">
                            <span>
                              Registered:{" "}
                              {new Date(
                                registration.registrationDate
                              ).toLocaleDateString()}
                            </span>
                            {registration.emergencyContactName && (
                              <div className="admin-dashboard__emergency-contact-info">
                                <span className="admin-dashboard__status-label">
                                  Emergency Contact:
                                </span>
                                <span>
                                  {registration.emergencyContactName} (
                                  {registration.emergencyContactRelationship}) -{" "}
                                  {registration.emergencyContactPhone}
                                </span>
                              </div>
                            )}
                            <div className="admin-dashboard__status-group">
                              <span className="admin-dashboard__status-label">
                                Payment:
                              </span>
                              <span
                                className={`admin-dashboard__payment-badge admin-dashboard__payment-badge--${getEffectivePaymentStatus(
                                  registration
                                )}`}
                              >
                                {getEffectivePaymentStatus(
                                  registration
                                ).toUpperCase()}
                              </span>
                            </div>
                            <div className="admin-dashboard__status-group">
                              <span className="admin-dashboard__status-label">
                                Status:
                              </span>
                              <span
                                className={`admin-dashboard__enrollment-badge admin-dashboard__enrollment-badge--${registration.enrollmentStatus}`}
                              >
                                {registration.enrollmentStatus.toUpperCase()}
                              </span>
                            </div>
                            <div className="admin-dashboard__status-group">
                              <span className="admin-dashboard__status-label">
                                Progress:
                              </span>
                              <span
                                className={`admin-dashboard__completion-badge ${
                                  registration.courseCompleted
                                    ? "admin-dashboard__completion-badge--completed"
                                    : "admin-dashboard__completion-badge--incomplete"
                                }`}
                              >
                                {registration.courseCompleted
                                  ? "COMPLETED"
                                  : "IN PROGRESS"}
                              </span>
                            </div>
                          </div>
                          {registration.instructorNotes && (
                            <div className="admin-dashboard__user-course-notes">
                              <strong>Instructor Notes:</strong>
                              <p>{registration.instructorNotes}</p>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            ) : (
              <div className="admin-dashboard__users-list">
                {filteredAndSortedUsers.length === 0 ? (
                  <p className="admin-dashboard__empty">
                    {userHistories.length === 0
                      ? "No users found."
                      : "No users match your search criteria."}
                  </p>
                ) : (
                  <div className="admin-dashboard__users-grid">
                    {filteredAndSortedUsers.map((user) => (
                      <div
                        key={user.email}
                        className="admin-dashboard__user-card"
                        onClick={() => setSelectedUser(user)}
                      >
                        <div className="admin-dashboard__user-card-header">
                          <h4>
                            {user.firstName} {user.lastName}
                          </h4>
                          <span className="admin-dashboard__user-email">
                            {user.email}
                          </span>
                        </div>
                        <div className="admin-dashboard__user-card-stats">
                          <div className="admin-dashboard__user-stat">
                            <span className="admin-dashboard__user-stat-number">
                              {user.totalCourses}
                            </span>
                            <span className="admin-dashboard__user-stat-label">
                              Course{user.totalCourses !== 1 ? "s" : ""}
                            </span>
                          </div>
                          <div className="admin-dashboard__user-stat">
                            <span className="admin-dashboard__user-stat-number">
                              {user.completedCourses}
                            </span>
                            <span className="admin-dashboard__user-stat-label">
                              Finished
                            </span>
                          </div>
                        </div>
                        <div className="admin-dashboard__user-card-footer">
                          <span>
                            Last activity:{" "}
                            {new Date(user.lastActivity).toLocaleDateString()}
                          </span>
                          <span className="admin-dashboard__user-card-hint">
                            Click to view details →
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}
          </div>
        )}

        {activeTab === "analytics" && <Analytics stats={stats} />}

        {activeTab === "courses" && (
          <div className="admin-dashboard__courses">
            <div className="admin-dashboard__courses-header">
              <h2>Course Management</h2>
              <div className="admin-dashboard__courses-header-actions">
                <Button
                  variant="secondary"
                  onClick={handleMigrateCourseOrder}
                  size="small"
                >
                  Migrate Display Order
                </Button>
                <Button
                  variant="secondary"
                  onClick={handleMigrateCourseCategories}
                  size="small"
                >
                  Migrate Categories
                </Button>
                <Button onClick={() => setShowAddCourseForm(true)}>
                  Add New Course
                </Button>
              </div>
            </div>

            {/* Category Management Section */}
            <CategoryManager onCategoriesChange={loadCategories} />

            {showAddCourseForm && (
              <div className="admin-dashboard__course-form">
                <h3>Create New Course</h3>

                {/* Basic Information */}
                <div className="admin-dashboard__form-section">
                  <h4>Basic Information</h4>
                  <div className="admin-dashboard__form-grid">
                    <div className="admin-dashboard__form-field">
                      <label htmlFor="course-title">Course Title *</label>
                      <input
                        id="course-title"
                        type="text"
                        value={newCourse.title}
                        onChange={(e) =>
                          setNewCourse((prev) => ({
                            ...prev,
                            title: e.target.value,
                          }))
                        }
                      />
                    </div>
                    <div className="admin-dashboard__form-field">
                      <label htmlFor="course-slug">URL Slug</label>
                      <input
                        id="course-slug"
                        type="text"
                        placeholder="Auto-generated from title"
                        value={newCourse.slug || generateSlug(newCourse.title)}
                        onChange={(e) =>
                          setNewCourse((prev) => ({
                            ...prev,
                            slug: e.target.value
                              .toLowerCase()
                              .replace(/[^a-z0-9-]/g, "-"),
                          }))
                        }
                        disabled
                        style={{ backgroundColor: "#f5f5f5", color: "#666" }}
                      />
                      <small style={{ color: "#666", fontSize: "0.8rem" }}>
                        Automatically generated from course title
                      </small>
                    </div>
                    <div className="admin-dashboard__form-field">
                      <label htmlFor="course-price">Price (CAD) *</label>
                      <input
                        id="course-price"
                        type="number"
                        placeholder="299"
                        value={newCourse.price}
                        onChange={(e) =>
                          setNewCourse((prev) => ({
                            ...prev,
                            price: e.target.value,
                          }))
                        }
                        min="0"
                        step="0.01"
                      />
                    </div>
                    <div className="admin-dashboard__form-field">
                      <label htmlFor="course-age-range">Age Range *</label>
                      <input
                        id="course-age-range"
                        type="text"
                        placeholder="e.g., Ages 14+"
                        value={newCourse.ageRange}
                        onChange={(e) =>
                          setNewCourse((prev) => ({
                            ...prev,
                            ageRange: e.target.value,
                          }))
                        }
                      />
                    </div>
                    <div className="admin-dashboard__form-field">
                      <label htmlFor="course-start-date">Start Date *</label>
                      <input
                        id="course-start-date"
                        type="date"
                        value={newCourse.startDate}
                        onChange={(e) =>
                          setNewCourse((prev) => ({
                            ...prev,
                            startDate: e.target.value,
                          }))
                        }
                      />
                    </div>
                    <div className="admin-dashboard__form-field">
                      <label htmlFor="course-schedule-time">Class Time *</label>
                      <input
                        id="course-schedule-time"
                        type="time"
                        value={newCourse.schedule.time}
                        onChange={(e) =>
                          setNewCourse((prev) => ({
                            ...prev,
                            schedule: {
                              ...prev.schedule,
                              time: e.target.value,
                            },
                          }))
                        }
                      />
                    </div>
                    <div className="admin-dashboard__form-field">
                      <label htmlFor="course-duration">
                        Duration (minutes) *
                      </label>
                      <input
                        id="course-duration"
                        type="number"
                        min="30"
                        max="240"
                        step="15"
                        value={newCourse.schedule.duration}
                        onChange={(e) =>
                          setNewCourse((prev) => ({
                            ...prev,
                            schedule: {
                              ...prev.schedule,
                              duration: parseInt(e.target.value) || 90,
                            },
                          }))
                        }
                      />
                    </div>
                    <div className="admin-dashboard__form-field admin-dashboard__form-field--full">
                      <label>Days of Week *</label>
                      <div className="admin-dashboard__checkbox-group">
                        {[
                          "Monday",
                          "Tuesday",
                          "Wednesday",
                          "Thursday",
                          "Friday",
                          "Saturday",
                          "Sunday",
                        ].map((day) => (
                          <label
                            key={day}
                            className="admin-dashboard__checkbox-label"
                          >
                            <input
                              type="checkbox"
                              checked={newCourse.schedule.daysOfWeek.includes(
                                day
                              )}
                              onChange={(e) => {
                                const isChecked = e.target.checked;
                                setNewCourse((prev) => ({
                                  ...prev,
                                  schedule: {
                                    ...prev.schedule,
                                    daysOfWeek: isChecked
                                      ? [...prev.schedule.daysOfWeek, day]
                                      : prev.schedule.daysOfWeek.filter(
                                          (d) => d !== day
                                        ),
                                  },
                                }));
                              }}
                            />
                            {day}
                          </label>
                        ))}
                      </div>
                    </div>
                    <div className="admin-dashboard__form-field">
                      <label htmlFor="course-max-students">Max Students</label>
                      <input
                        id="course-max-students"
                        type="number"
                        value={newCourse.maxStudents}
                        onChange={(e) =>
                          setNewCourse((prev) => ({
                            ...prev,
                            maxStudents: parseInt(e.target.value) || 8,
                          }))
                        }
                        min="1"
                        max="20"
                      />
                    </div>
                    <div className="admin-dashboard__form-field">
                      <label htmlFor="course-status">Status</label>
                      <select
                        id="course-status"
                        value={newCourse.status}
                        onChange={(e) =>
                          setNewCourse((prev) => ({
                            ...prev,
                            status: e.target.value as
                              | "active"
                              | "archived"
                              | "draft",
                          }))
                        }
                      >
                        <option value="draft">Draft</option>
                        <option value="active">Active</option>
                        <option value="archived">Archived</option>
                      </select>
                    </div>
                    <div className="admin-dashboard__form-field admin-dashboard__form-field--full">
                      <label>Categories</label>
                      <p className="admin-dashboard__form-help">
                        Select multiple categories that apply to this course
                      </p>
                      <div className="admin-dashboard__category-grid">
                        {availableCategories.map((category) => (
                          <label
                            key={category}
                            className="admin-dashboard__category-checkbox"
                          >
                            <input
                              type="checkbox"
                              checked={newCourse.categories.includes(category)}
                              onChange={() => toggleCategory(category)}
                            />
                            <span className="admin-dashboard__category-label">
                              {category}
                            </span>
                          </label>
                        ))}
                      </div>
                      {newCourse.categories.length > 0 && (
                        <div className="admin-dashboard__selected-categories">
                          <strong>Selected:</strong>
                          {newCourse.categories.map((cat, index) => (
                            <span
                              key={index}
                              className="admin-dashboard__category-pill"
                            >
                              {cat}
                            </span>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Description */}
                <div className="admin-dashboard__form-section">
                  <h4>Course Description</h4>
                  <div className="admin-dashboard__form-field">
                    <label htmlFor="course-description">Description *</label>
                    <textarea
                      id="course-description"
                      placeholder="Detailed course description that will be shown to students..."
                      value={newCourse.description}
                      onChange={(e) =>
                        setNewCourse((prev) => ({
                          ...prev,
                          description: e.target.value,
                        }))
                      }
                      rows={6}
                    />
                  </div>
                </div>

                {/* Learning Outcomes */}
                <div className="admin-dashboard__form-section">
                  <h4>Learning Outcomes</h4>
                  <p className="admin-dashboard__form-help">
                    What will students learn and achieve?
                  </p>
                  {newCourse.outcomes.map((outcome, index) => (
                    <div key={index} className="admin-dashboard__array-field">
                      <input
                        type="text"
                        placeholder={`Outcome ${index + 1}`}
                        value={outcome}
                        onChange={(e) => updateOutcome(index, e.target.value)}
                      />
                      <button
                        type="button"
                        onClick={() => removeOutcome(index)}
                        className="admin-dashboard__remove-btn"
                      >
                        Remove
                      </button>
                    </div>
                  ))}
                  <button
                    type="button"
                    onClick={addOutcome}
                    className="admin-dashboard__add-btn"
                  >
                    Add Outcome
                  </button>
                </div>

                <div className="admin-dashboard__form-actions">
                  <Button onClick={handleCreateCourse}>Create Course</Button>
                  <Button
                    variant="secondary"
                    onClick={() => setShowAddCourseForm(false)}
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            )}

            {loadingCourses ? (
              <div className="admin-dashboard__loading">
                <Spinner size="large" color="primary" />
                <p>Loading courses...</p>
              </div>
            ) : (
              <div className="admin-dashboard__courses-grid">
                {courses.map((course, index) => {
                  const isEditing = editingCourseId === course.id;
                  const editData = isEditing ? editingCourseData : course;

                  return (
                    <div
                      key={course.id}
                      className={`admin-dashboard__course-card ${
                        isEditing ? "editing" : ""
                      }`}
                    >
                      <div className="admin-dashboard__course-card-header">
                        <div className="admin-dashboard__course-title-section">
                          {isEditing ? (
                            <div className="admin-dashboard__inline-field">
                              <label className="admin-dashboard__inline-label">
                                Course Title
                              </label>
                              <input
                                type="text"
                                value={editData.title || ""}
                                onChange={(e) =>
                                  handleEditCourseFieldChange(
                                    "title",
                                    e.target.value
                                  )
                                }
                                placeholder="Course Title"
                                className="admin-dashboard__inline-input admin-dashboard__inline-input--title"
                              />
                            </div>
                          ) : (
                            <h4>{course.title}</h4>
                          )}
                          <div className="admin-dashboard__course-order-controls">
                            <span className="admin-dashboard__display-order">
                              #{course.displayOrder || index + 1}
                            </span>
                            {!isEditing && (
                              <div className="admin-dashboard__order-buttons">
                                <button
                                  className="admin-dashboard__order-btn"
                                  onClick={() => handleMoveCourseUp(index)}
                                  disabled={index === 0}
                                  title="Move up"
                                >
                                  ↑
                                </button>
                                <button
                                  className="admin-dashboard__order-btn"
                                  onClick={() => handleMoveCourseDown(index)}
                                  disabled={index === courses.length - 1}
                                  title="Move down"
                                >
                                  ↓
                                </button>
                              </div>
                            )}
                          </div>
                        </div>
                        {isEditing ? (
                          <div className="admin-dashboard__inline-field admin-dashboard__inline-field--status">
                            <label className="admin-dashboard__inline-label">
                              Status
                            </label>
                            <select
                              value={editData.status || "draft"}
                              onChange={(e) =>
                                handleEditCourseFieldChange(
                                  "status",
                                  e.target.value
                                )
                              }
                              className="admin-dashboard__inline-select admin-dashboard__inline-select--status"
                            >
                              <option value="draft">Draft</option>
                              <option value="active">Active</option>
                              <option value="archived">Archived</option>
                            </select>
                          </div>
                        ) : (
                          <span
                            className={`admin-dashboard__status admin-dashboard__status--${course.status}`}
                          >
                            {course.status}
                          </span>
                        )}
                      </div>
                      <div className="admin-dashboard__course-card-content">
                        {isEditing ? (
                          <div className="admin-dashboard__inline-edit-content">
                            <div className="admin-dashboard__inline-field">
                              <label className="admin-dashboard__inline-label">
                                Description
                              </label>
                              <textarea
                                value={editData.description || ""}
                                onChange={(e) =>
                                  handleEditCourseFieldChange(
                                    "description",
                                    e.target.value
                                  )
                                }
                                placeholder="Course Description"
                                rows={3}
                                className="admin-dashboard__inline-textarea"
                              />
                            </div>
                            <div className="admin-dashboard__inline-edit-row">
                              <div className="admin-dashboard__inline-field">
                                <label className="admin-dashboard__inline-label">
                                  Price ($)
                                </label>
                                <input
                                  type="number"
                                  value={editData.price || ""}
                                  onChange={(e) =>
                                    handleEditCourseFieldChange(
                                      "price",
                                      e.target.value
                                    )
                                  }
                                  placeholder="299"
                                  className="admin-dashboard__inline-input"
                                  min="0"
                                  step="0.01"
                                />
                              </div>
                              <div className="admin-dashboard__inline-field">
                                <label className="admin-dashboard__inline-label">
                                  Max Students
                                </label>
                                <input
                                  type="number"
                                  value={editData.maxStudents || 8}
                                  onChange={(e) =>
                                    handleEditCourseFieldChange(
                                      "maxStudents",
                                      parseInt(e.target.value) || 8
                                    )
                                  }
                                  placeholder="8"
                                  className="admin-dashboard__inline-input"
                                  min="1"
                                  max="20"
                                />
                              </div>
                            </div>
                            <div className="admin-dashboard__inline-edit-row">
                              <div className="admin-dashboard__inline-field">
                                <label className="admin-dashboard__inline-label">
                                  Start Date
                                </label>
                                <input
                                  type="date"
                                  value={editData.startDate || ""}
                                  onChange={(e) =>
                                    handleEditCourseFieldChange(
                                      "startDate",
                                      e.target.value
                                    )
                                  }
                                  className="admin-dashboard__inline-input"
                                />
                              </div>
                              <div className="admin-dashboard__inline-field">
                                <label className="admin-dashboard__inline-label">
                                  Age Range
                                </label>
                                <input
                                  type="text"
                                  value={editData.ageRange || ""}
                                  onChange={(e) =>
                                    handleEditCourseFieldChange(
                                      "ageRange",
                                      e.target.value
                                    )
                                  }
                                  placeholder="Ages 14+"
                                  className="admin-dashboard__inline-input"
                                />
                              </div>
                            </div>
                          </div>
                        ) : (
                          <>
                            <p className="admin-dashboard__course-description">
                              {course.description}
                            </p>
                            <div className="admin-dashboard__course-details">
                              <div className="admin-dashboard__course-detail">
                                <span className="admin-dashboard__course-label">
                                  Price:
                                </span>
                                <span className="admin-dashboard__course-value">
                                  {formatPrice(course.price)}
                                </span>
                              </div>
                              <div className="admin-dashboard__course-detail">
                                <span className="admin-dashboard__course-label">
                                  Age Range:
                                </span>
                                <span className="admin-dashboard__course-value">
                                  {course.ageRange}
                                </span>
                              </div>
                              <div className="admin-dashboard__course-detail">
                                <span className="admin-dashboard__course-label">
                                  Start Date:
                                </span>
                                <span className="admin-dashboard__course-value">
                                  {course.startDate}
                                </span>
                              </div>
                              <div className="admin-dashboard__course-detail">
                                <span className="admin-dashboard__course-label">
                                  Schedule:
                                </span>
                                <span className="admin-dashboard__course-value">
                                  {course.schedule
                                    ? `${course.schedule.daysOfWeek.join(
                                        ", "
                                      )} at ${course.schedule.time} (${
                                        course.schedule.duration
                                      }min)`
                                    : "Not scheduled"}
                                </span>
                              </div>
                              <div className="admin-dashboard__course-detail">
                                <span className="admin-dashboard__course-label">
                                  Max Students:
                                </span>
                                <span className="admin-dashboard__course-value">
                                  {course.maxStudents}
                                </span>
                              </div>
                              {course.category && (
                                <div className="admin-dashboard__course-detail">
                                  <span className="admin-dashboard__course-label">
                                    Category:
                                  </span>
                                  <span className="admin-dashboard__course-value">
                                    {course.category}
                                  </span>
                                </div>
                              )}
                            </div>
                          </>
                        )}
                      </div>
                      <div className="admin-dashboard__course-card-actions">
                        {isEditing ? (
                          <>
                            <Button
                              variant="primary"
                              size="small"
                              onClick={handleSaveEditCourse}
                              loading={updatingCourse}
                              disabled={updatingCourse}
                            >
                              {updatingCourse ? "Saving..." : "Save"}
                            </Button>
                            <Button
                              variant="secondary"
                              size="small"
                              onClick={handleCancelEditCourse}
                              disabled={updatingCourse}
                            >
                              Cancel
                            </Button>
                          </>
                        ) : (
                          <>
                            <Button
                              variant="secondary"
                              size="small"
                              onClick={() => handleStartEditCourse(course)}
                            >
                              Edit
                            </Button>
                            <Button
                              variant="secondary"
                              size="small"
                              onClick={() =>
                                handleDuplicateCourse(course.id, course.title)
                              }
                            >
                              Duplicate
                            </Button>
                            {course.status !== "archived" && (
                              <Button
                                variant="secondary"
                                size="small"
                                onClick={() => handleArchiveCourse(course.id)}
                              >
                                Archive
                              </Button>
                            )}
                          </>
                        )}
                      </div>
                      {!isEditing && (
                        <div className="admin-dashboard__course-card-footer">
                          <span>
                            Created:{" "}
                            {new Date(course.createdAt).toLocaleDateString()}
                          </span>
                          <span>
                            Updated:{" "}
                            {new Date(course.updatedAt).toLocaleDateString()}
                          </span>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        )}

        {activeTab === "emails" && <EmailTemplates />}

        {activeTab === "instructors" && <InstructorManagement />}

        {activeTab === "assignments" && (
          <CourseAssignment onBack={() => navigateToTab("overview")} />
        )}
      </main>
    </div>
  );
};

export default AdminDashboard;
