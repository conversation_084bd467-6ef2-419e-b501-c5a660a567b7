.admin-dashboard {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.admin-dashboard__loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  font-size: 1.2rem;
  color: #666;
}

.admin-dashboard__header {
  background: linear-gradient(135deg, #0a2d69 0%, #1e4a8c 100%);
  color: white;
  padding: 2rem;
}

.admin-dashboard__header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.admin-dashboard__header h1 {
  margin: 0 0 0.5rem 0;
  font-size: 2rem;
}

.admin-dashboard__header p {
  margin: 0;
  color: #e8f4f8;
}

.admin-dashboard__logout-btn {
  background: rgba(255, 255, 255, 0.2) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  color: white !important;
}

.admin-dashboard__logout-btn:hover {
  background: rgba(255, 255, 255, 0.3) !important;
  border-color: rgba(255, 255, 255, 0.5) !important;
}

.admin-dashboard__nav {
  background: white;
  border-bottom: 1px solid #e8f4f8;
  padding: 0 2rem;
  display: flex;
  max-width: 1200px;
  margin: 0 auto;
}

.admin-dashboard__nav-item {
  background: none;
  border: none;
  padding: 1rem 2rem;
  cursor: pointer;
  font-size: 1rem;
  color: #666;
  border-bottom: 3px solid transparent;
  transition: all 0.2s ease;
}

.admin-dashboard__nav-item:hover {
  color: #0a2d69;
}

.admin-dashboard__nav-item.active {
  color: #0a2d69;
  border-bottom-color: #ffa300;
}

.admin-dashboard__content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.admin-dashboard__stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.admin-dashboard__stat-card {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  text-align: center;
}

.admin-dashboard__stat-card h3 {
  margin: 0 0 1rem 0;
  color: #666;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.admin-dashboard__stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: #0a2d69;
}

.admin-dashboard__stat-subtitle {
  font-size: 0.875rem;
  color: #666;
  margin-top: 0.25rem;
}

.admin-dashboard__section {
  margin-bottom: 3rem;
}

.admin-dashboard__section h2 {
  color: #0a2d69;
  margin: 0 0 1.5rem 0;
  font-size: 1.5rem;
}

.admin-dashboard__course-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.admin-dashboard__course-card {
  background: white;
  padding: 1.5rem 1.5rem 3rem 1.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  position: relative;
}

.admin-dashboard__course-card--clickable {
  cursor: pointer;
  transition: all 0.2s ease;
}

.admin-dashboard__course-card--clickable:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.admin-dashboard__course-card-hint {
  position: absolute;
  bottom: 0.5rem;
  left: 50%;
  transform: translateX(-50%);
  font-size: 0.85rem;
  color: #0a2d69;
  font-weight: 500;
  opacity: 0;
  transition: opacity 0.2s ease;
  text-align: center;
  white-space: nowrap;
  background: rgba(255, 255, 255, 0.95);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.admin-dashboard__course-card--clickable:hover
  .admin-dashboard__course-card-hint {
  opacity: 1;
}

.admin-dashboard__course-card h4 {
  margin: 0 0 1rem 0;
  color: #0a2d69;
}

.admin-dashboard__course-stats {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.admin-dashboard__course-stats > span:first-child {
  font-weight: 600;
  color: #0a2d69;
}

.admin-dashboard__waitlist-count {
  color: #f59e0b;
  font-size: 0.875rem;
  font-weight: 500;
}

.admin-dashboard__status {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.admin-dashboard__status--open {
  background: #d4edda;
  color: #155724;
}

.admin-dashboard__status--full {
  background: #f8d7da;
  color: #721c24;
}

.admin-dashboard__status--waitlist {
  background: #fff3cd;
  color: #856404;
}

.admin-dashboard__progress-bar {
  width: 100%;
  height: 8px;
  background: #e8f4f8;
  border-radius: 4px;
  overflow: hidden;
}

.admin-dashboard__progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #ffa300 0%, #ff8c00 100%);
  transition: width 0.3s ease;
}

.admin-dashboard__recent-list {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.admin-dashboard__recent-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e8f4f8;
}

.admin-dashboard__recent-item:last-child {
  border-bottom: none;
}

.admin-dashboard__recent-item-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  flex: 1;
}

.admin-dashboard__recent-item strong {
  display: block;
  color: #0a2d69;
  margin-bottom: 0.25rem;
}

.admin-dashboard__recent-course {
  color: #666;
  font-size: 0.9rem;
  font-weight: 500;
}

.admin-dashboard__recent-status {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.admin-dashboard__recent-date {
  color: #999;
  font-size: 0.9rem;
  white-space: nowrap;
  margin-left: 1rem;
}

.admin-dashboard__table-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  overflow-x: auto;
}

.admin-dashboard__table {
  width: 100%;
  border-collapse: collapse;
}

.admin-dashboard__table th,
.admin-dashboard__table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid #e8f4f8;
}

.admin-dashboard__table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #0a2d69;
}

.admin-dashboard__table tbody tr:hover {
  background: #f8f9fa;
}

.admin-dashboard__payment-status {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  align-items: flex-start;
}

.admin-dashboard__payment-status--loading {
  opacity: 0.6;
  pointer-events: none;
}

.admin-dashboard__payment-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
}

.admin-dashboard__enrollment-status {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  align-items: flex-start;
  min-width: 120px;
}

.admin-dashboard__enrollment-status--loading {
  opacity: 0.6;
  pointer-events: none;
}

.admin-dashboard__enrollment-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
}

.admin-dashboard__registrations-header {
  margin-bottom: 1.5rem;
}

.admin-dashboard__registrations-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.admin-dashboard__add-form {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.admin-dashboard__add-form h3 {
  margin: 0 0 1.5rem 0;
  color: #0a2d69;
}

.admin-dashboard__form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.admin-dashboard__form-field--full {
  grid-column: 1 / -1;
}

.admin-dashboard__checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-top: 0.5rem;
}

.admin-dashboard__checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  font-size: 0.9rem;
  color: #333;
}

.admin-dashboard__checkbox-label input[type="checkbox"] {
  margin: 0;
  cursor: pointer;
}

.admin-dashboard__form-field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.admin-dashboard__form-field label {
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
}

.admin-dashboard__form-field input,
.admin-dashboard__form-field textarea,
.admin-dashboard__form-field select {
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.admin-dashboard__form-field input:focus,
.admin-dashboard__form-field textarea:focus,
.admin-dashboard__form-field select:focus {
  outline: none;
  border-color: #0a2d69;
  box-shadow: 0 0 0 3px rgba(10, 45, 105, 0.1);
}

.admin-dashboard__form-field textarea {
  resize: vertical;
  min-height: 80px;
  font-family: inherit;
}

.admin-dashboard__form-grid input,
.admin-dashboard__form-grid select {
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 1rem;
}

.admin-dashboard__form-grid input:focus,
.admin-dashboard__form-grid select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 1px #3b82f6;
}

.admin-dashboard__add-form textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 1rem;
  margin-bottom: 1rem;
  resize: vertical;
}

.admin-dashboard__add-form textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 1px #3b82f6;
}

.admin-dashboard__form-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.admin-dashboard__filters {
  margin-top: 1rem;
}

.admin-dashboard__filter-group {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
}

.admin-dashboard__filter-input,
.admin-dashboard__filter-select {
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.9rem;
  background: white;
}

.admin-dashboard__filter-input {
  min-width: 200px;
}

.admin-dashboard__filter-select {
  min-width: 150px;
}

.admin-dashboard__filter-input:focus,
.admin-dashboard__filter-select:focus {
  outline: none;
  border-color: #0a2d69;
  box-shadow: 0 0 0 2px rgba(10, 45, 105, 0.1);
}

.admin-dashboard__capacity-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.admin-dashboard__capacity-card {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.admin-dashboard__capacity-card h4 {
  margin: 0 0 1rem 0;
  color: #0a2d69;
}

.admin-dashboard__capacity-info {
  margin-bottom: 1rem;
}

.admin-dashboard__capacity-edit {
  margin-top: 0.5rem;
}

.admin-dashboard__capacity-form {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.admin-dashboard__capacity-form input {
  width: 80px;
  padding: 0.5rem;
  border: 1px solid #e8f4f8;
  border-radius: 4px;
}

.admin-dashboard__capacity-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

@media (max-width: 768px) {
  .admin-dashboard__header {
    padding: 1rem;
  }

  .admin-dashboard__header-content {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .admin-dashboard__nav {
    padding: 0 1rem;
    overflow-x: auto;
  }

  .admin-dashboard__content {
    padding: 1rem;
  }

  .admin-dashboard__stats {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }

  .admin-dashboard__course-grid,
  .admin-dashboard__capacity-grid {
    grid-template-columns: 1fr;
  }
}

/* Payment status */
.admin-dashboard__payment-status {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  align-items: flex-start;
}

.admin-dashboard__payment-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.admin-dashboard__payment-badge--pending {
  background-color: #fef3c7;
  color: #92400e;
}

.admin-dashboard__payment-badge--paid {
  background-color: #d1fae5;
  color: #065f46;
}

.admin-dashboard__payment-badge--refunded {
  background-color: #fee2e2;
  color: #991b1b;
}

.admin-dashboard__payment-badge--late {
  background-color: #fef2f2;
  color: #dc2626;
  animation: pulse-late 2s infinite;
}

.admin-dashboard__payment-badge--cancelled {
  background-color: #f3f4f6;
  color: #6b7280;
  text-decoration: line-through;
}

@keyframes pulse-late {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.admin-dashboard__payment-select {
  padding: 0.25rem 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  background-color: white;
  cursor: pointer;
}

.admin-dashboard__payment-select:hover {
  border-color: #9ca3af;
}

.admin-dashboard__payment-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 1px #3b82f6;
}

/* Users Tab Styles */
.admin-dashboard__users {
  padding: 2rem;
}

.admin-dashboard__users-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.admin-dashboard__users-header h2 {
  margin: 0;
  color: #0a2d69;
}

.admin-dashboard__users-filters {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.admin-dashboard__users-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

.admin-dashboard__user-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  cursor: pointer;
  transition: all 0.2s ease;
  border: 2px solid transparent;
}

.admin-dashboard__user-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  border-color: #0a2d69;
}

.admin-dashboard__user-card-header h4 {
  margin: 0 0 0.5rem 0;
  color: #0a2d69;
  font-size: 1.1rem;
}

.admin-dashboard__user-email {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 1rem;
  display: block;
}

.admin-dashboard__user-card-stats {
  display: flex;
  gap: 2rem;
  margin: 1rem 0;
}

.admin-dashboard__user-stat {
  text-align: center;
}

.admin-dashboard__user-stat-number {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: #0a2d69;
}

.admin-dashboard__user-stat-label {
  font-size: 0.8rem;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.admin-dashboard__user-card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e8f4f8;
  font-size: 0.85rem;
  color: #666;
}

.admin-dashboard__user-card-hint {
  color: #0a2d69;
  font-weight: 500;
}

/* User Detail View */
.admin-dashboard__user-detail {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.admin-dashboard__user-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #e8f4f8;
}

.admin-dashboard__user-detail-header h3 {
  margin: 0 0 0.5rem 0;
  color: #0a2d69;
  font-size: 1.5rem;
}

.admin-dashboard__user-stats {
  display: flex;
  gap: 2rem;
  margin-top: 1rem;
}

.admin-dashboard__user-stats span {
  color: #666;
  font-size: 0.9rem;
}

.admin-dashboard__user-courses h4 {
  margin: 0 0 1.5rem 0;
  color: #0a2d69;
  font-size: 1.2rem;
}

.admin-dashboard__user-course-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.admin-dashboard__user-course-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  border-left: 4px solid #0a2d69;
}

.admin-dashboard__user-course-info h5 {
  margin: 0 0 1rem 0;
  color: #0a2d69;
  font-size: 1.1rem;
}

.admin-dashboard__user-course-details {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  align-items: center;
  margin-bottom: 1rem;
}

.admin-dashboard__status-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.admin-dashboard__status-label {
  font-size: 0.8rem;
  color: #666;
  font-weight: 500;
}

.admin-dashboard__user-course-details span {
  font-size: 0.85rem;
  color: #666;
}

.admin-dashboard__enrollment-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.admin-dashboard__enrollment-badge--enrolled {
  background-color: #d1fae5;
  color: #065f46;
}

.admin-dashboard__enrollment-badge--waitlist {
  background-color: #fef3c7;
  color: #92400e;
}

.admin-dashboard__completion-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.admin-dashboard__completion-badge--completed {
  background-color: #d1fae5;
  color: #065f46;
}

.admin-dashboard__completion-badge--incomplete {
  background-color: #fef3c7;
  color: #92400e;
}

.admin-dashboard__enrollment-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.admin-dashboard__enrollment-badge--enrolled {
  background-color: #d1fae5;
  color: #065f46;
}

.admin-dashboard__enrollment-badge--waitlist {
  background-color: #fef3c7;
  color: #92400e;
}

.admin-dashboard__action-buttons {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

/* Inline Editing */
.admin-dashboard__table tr.editing {
  background-color: #f8fafc;
  border: 2px solid #0ea5e9;
}

.admin-dashboard__inline-input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 0.875rem;
  background: white;
}

.admin-dashboard__inline-input:focus {
  outline: none;
  border-color: #0ea5e9;
  box-shadow: 0 0 0 2px rgba(14, 165, 233, 0.1);
}

.admin-dashboard__inline-edit-names {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.admin-dashboard__inline-edit-names .admin-dashboard__inline-input {
  min-width: 100px;
}

.admin-dashboard__inline-select {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 0.875rem;
  background: white;
}

.admin-dashboard__inline-select:focus {
  outline: none;
  border-color: #0ea5e9;
  box-shadow: 0 0 0 2px rgba(14, 165, 233, 0.1);
}

.admin-dashboard__inline-edit-emergency {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.admin-dashboard__inline-input--price {
  max-width: 100px;
}

.admin-dashboard__edit-form {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: #f8fafc;
  border-radius: 12px;
  border: 2px solid #e2e8f0;
}

.admin-dashboard__edit-form h3 {
  margin: 0 0 1rem 0;
  color: #0a2d69;
}

.admin-dashboard__price {
  font-weight: 600;
  color: #059669;
}

.admin-dashboard__emergency-contact {
  font-size: 0.9rem;
}

.admin-dashboard__emergency-contact-name {
  font-weight: 600;
  color: #0a2d69;
  margin-bottom: 0.25rem;
}

.admin-dashboard__emergency-contact-details {
  color: #6b7280;
  font-size: 0.8rem;
}

.admin-dashboard__missing-data {
  color: #9ca3af;
  font-style: italic;
  font-size: 0.9rem;
}

.admin-dashboard__emergency-contact-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  margin: 0.5rem 0;
  padding: 0.5rem;
  background: #f8fafc;
  border-radius: 4px;
  border-left: 3px solid #ffa300;
}

.admin-dashboard__emergency-contact-info .admin-dashboard__status-label {
  font-weight: 600;
  color: #0a2d69;
  margin-bottom: 0.25rem;
}

/* Analytics Tab Styles */
.admin-dashboard__analytics {
  padding: 2rem;
}

.admin-dashboard__analytics h2 {
  margin: 0 0 2rem 0;
  color: #0a2d69;
}

.admin-dashboard__analytics-section {
  margin-bottom: 3rem;
}

.admin-dashboard__analytics-section h3 {
  margin: 0 0 1.5rem 0;
  color: #1e40af;
  font-size: 1.25rem;
}

.admin-dashboard__analytics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.admin-dashboard__analytics-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.admin-dashboard__analytics-card h4 {
  margin: 0 0 1rem 0;
  color: #374151;
  font-size: 1rem;
  font-weight: 600;
}

.admin-dashboard__analytics-value {
  font-size: 2rem;
  font-weight: 700;
  color: #0a2d69;
  margin-bottom: 0.5rem;
}

.admin-dashboard__analytics-subtitle {
  color: #6b7280;
  font-size: 0.875rem;
}

.admin-dashboard__course-performance {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
}

.admin-dashboard__course-performance-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.admin-dashboard__course-performance-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.admin-dashboard__course-performance-header h4 {
  margin: 0;
  color: #0a2d69;
  font-size: 1.125rem;
}

.admin-dashboard__course-performance-stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1rem;
}

.admin-dashboard__performance-stat {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.admin-dashboard__performance-label {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
}

.admin-dashboard__performance-value {
  font-size: 1.25rem;
  font-weight: 600;
  color: #0a2d69;
}

.admin-dashboard__recent-activity {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.admin-dashboard__activity-summary {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.admin-dashboard__activity-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid #f3f4f6;
}

.admin-dashboard__activity-item:last-child {
  border-bottom: none;
}

.admin-dashboard__activity-label {
  font-weight: 500;
  color: #374151;
}

.admin-dashboard__activity-value {
  font-weight: 600;
  color: #0a2d69;
}

/* Courses Tab Styles */
.admin-dashboard__courses {
  padding: 2rem;
}

.admin-dashboard__courses-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.admin-dashboard__courses-header h2 {
  margin: 0;
  color: #0a2d69;
}

.admin-dashboard__courses-header-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.admin-dashboard__course-form {
  background: #f8fafc;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  border: 2px solid #e2e8f0;
}

.admin-dashboard__course-form h3 {
  margin: 0 0 1rem 0;
  color: #0a2d69;
}

.admin-dashboard__courses-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 1.5rem;
}

.admin-dashboard__course-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  display: flex;
  flex-direction: column;
}

.admin-dashboard__course-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.admin-dashboard__course-title-section {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  flex: 1;
  margin-right: 1rem;
}

.admin-dashboard__course-card-header h4 {
  margin: 0;
  color: #0a2d69;
  font-size: 1.125rem;
}

.admin-dashboard__course-order-controls {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.admin-dashboard__display-order {
  font-size: 0.85rem;
  color: #6b7280;
  font-weight: 500;
  background: #f3f4f6;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.admin-dashboard__order-buttons {
  display: flex;
  gap: 0.25rem;
}

.admin-dashboard__order-btn {
  background: #0a2d69;
  color: white;
  border: none;
  border-radius: 4px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 0.8rem;
  font-weight: bold;
  transition: all 0.2s ease;
}

.admin-dashboard__order-btn:hover:not(:disabled) {
  background: #1e4a8c;
  transform: translateY(-1px);
}

.admin-dashboard__order-btn:disabled {
  background: #d1d5db;
  color: #9ca3af;
  cursor: not-allowed;
  transform: none;
}

.admin-dashboard__course-card-content {
  flex: 1;
  margin-bottom: 1rem;
}

.admin-dashboard__course-description {
  color: #374151;
  margin-bottom: 1rem;
  line-height: 1.5;
}

.admin-dashboard__course-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.admin-dashboard__course-detail {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.admin-dashboard__course-label {
  font-weight: 500;
  color: #6b7280;
  font-size: 0.875rem;
}

.admin-dashboard__course-value {
  font-weight: 600;
  color: #0a2d69;
  font-size: 0.875rem;
}

.admin-dashboard__course-prerequisites {
  background: #f3f4f6;
  padding: 0.75rem;
  border-radius: 8px;
  font-size: 0.875rem;
  color: #374151;
  margin-top: 0.5rem;
}

.admin-dashboard__course-card-actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  margin-bottom: 1rem;
}

.admin-dashboard__course-card-footer {
  display: flex;
  justify-content: space-between;
  font-size: 0.75rem;
  color: #6b7280;
  border-top: 1px solid #f3f4f6;
  padding-top: 0.75rem;
}

.admin-dashboard__form-section {
  margin-bottom: 2rem;
}

.admin-dashboard__form-section h4 {
  margin: 0 0 0.5rem 0;
  color: #1e40af;
  font-size: 1rem;
  font-weight: 600;
}

.admin-dashboard__form-help {
  margin: 0 0 1rem 0;
  color: #6b7280;
  font-size: 0.875rem;
  font-style: italic;
}

.admin-dashboard__array-field {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  align-items: center;
}

.admin-dashboard__array-field input {
  flex: 1;
}

.admin-dashboard__add-btn,
.admin-dashboard__remove-btn {
  padding: 0.5rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  color: #374151;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s;
}

.admin-dashboard__add-btn:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.admin-dashboard__remove-btn {
  background: #fef2f2;
  border-color: #fecaca;
  color: #dc2626;
}

.admin-dashboard__remove-btn:hover {
  background: #fee2e2;
  border-color: #fca5a5;
}

.admin-dashboard__image-uploads {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.admin-dashboard__image-upload-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.admin-dashboard__image-label {
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
}

/* Category selection styles */
.admin-dashboard__category-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.5rem;
  margin: 1rem 0;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e8f4f8;
}

.admin-dashboard__category-checkbox {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.admin-dashboard__category-checkbox:hover {
  background-color: rgba(10, 45, 105, 0.05);
}

.admin-dashboard__category-checkbox input[type="checkbox"] {
  margin: 0;
  cursor: pointer;
}

.admin-dashboard__category-label {
  font-size: 0.9rem;
  color: #333;
  cursor: pointer;
  user-select: none;
}

.admin-dashboard__selected-categories {
  margin-top: 1rem;
  padding: 1rem;
  background: #e8f4f8;
  border-radius: 6px;
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  align-items: center;
}

.admin-dashboard__category-pill {
  background: rgba(255, 163, 0, 0.9);
  color: #0a2d69;
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.admin-dashboard__edit-categories {
  grid-column: 1 / -1;
  margin-top: 1rem;
}

.admin-dashboard__edit-categories label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #0a2d69;
}

.admin-dashboard__image-help {
  margin: 0;
  color: #6b7280;
  font-size: 0.75rem;
  font-style: italic;
}

@media (max-width: 768px) {
  .admin-dashboard__image-uploads {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

.admin-dashboard__user-course-notes {
  margin-top: 1rem;
  padding: 1rem;
  background: white;
  border-radius: 6px;
  border: 1px solid #e8f4f8;
}

.admin-dashboard__user-course-notes strong {
  color: #0a2d69;
  display: block;
  margin-bottom: 0.5rem;
}

.admin-dashboard__user-course-notes p {
  margin: 0;
  color: #374151;
  line-height: 1.5;
  white-space: pre-wrap;
}

/* Course Inline Editing */
.admin-dashboard__course-card.editing {
  background-color: #f8fafc;
  border: 2px solid #0ea5e9;
  transform: scale(1.02);
}

.admin-dashboard__course-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 1rem;
}

.admin-dashboard__course-title-section {
  flex: 1;
  min-width: 0;
}

.admin-dashboard__inline-field {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.admin-dashboard__inline-field--status {
  flex-shrink: 0;
  min-width: 120px;
  max-width: 150px;
}

.admin-dashboard__inline-label {
  font-size: 0.75rem;
  font-weight: 600;
  color: #374151;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.admin-dashboard__inline-input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 0.875rem;
  background: white;
}

.admin-dashboard__inline-input--title {
  font-size: 1rem;
  font-weight: 600;
}

.admin-dashboard__inline-input:focus {
  outline: none;
  border-color: #0ea5e9;
  box-shadow: 0 0 0 2px rgba(14, 165, 233, 0.1);
}

.admin-dashboard__inline-select {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 0.875rem;
  background: white;
}

.admin-dashboard__inline-select--status {
  min-width: 100px;
}

.admin-dashboard__inline-select:focus {
  outline: none;
  border-color: #0ea5e9;
  box-shadow: 0 0 0 2px rgba(14, 165, 233, 0.1);
}

.admin-dashboard__inline-textarea {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 0.875rem;
  background: white;
  resize: vertical;
  min-height: 80px;
}

.admin-dashboard__inline-textarea:focus {
  outline: none;
  border-color: #0ea5e9;
  box-shadow: 0 0 0 2px rgba(14, 165, 233, 0.1);
}

.admin-dashboard__inline-edit-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.admin-dashboard__inline-edit-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

@media (max-width: 768px) {
  .admin-dashboard__course-card-header {
    flex-direction: column;
    gap: 0.75rem;
  }

  .admin-dashboard__inline-field--status {
    max-width: none;
  }

  .admin-dashboard__inline-edit-row {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }
}
