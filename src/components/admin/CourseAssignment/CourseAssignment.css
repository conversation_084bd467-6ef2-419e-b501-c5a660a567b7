.course-assignment {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.course-assignment__header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.course-assignment__title h2 {
  margin: 0 0 0.5rem 0;
  color: #0a2d69;
  font-size: 1.75rem;
  font-weight: 700;
}

.course-assignment__title p {
  margin: 0;
  color: #6b7280;
  font-size: 1rem;
}

.course-assignment__loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem;
  color: #6b7280;
}

.course-assignment__summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.course-assignment__stat {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1.5rem;
  text-align: center;
}

.course-assignment__stat-number {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  color: #0a2d69;
  margin-bottom: 0.5rem;
}

.course-assignment__stat-label {
  display: block;
  font-size: 0.875rem;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.course-assignment__courses {
  display: grid;
  gap: 1rem;
}

.course-assignment__course-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2rem;
  transition: all 0.2s ease;
}

.course-assignment__course-card:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-color: #d1d5db;
}

.course-assignment__course-info {
  flex: 1;
}

.course-assignment__course-info h3 {
  margin: 0 0 0.5rem 0;
  color: #0a2d69;
  font-size: 1.25rem;
  font-weight: 600;
}

.course-assignment__course-details {
  margin: 0 0 0.75rem 0;
  color: #6b7280;
  font-size: 0.9rem;
}

.course-assignment__course-meta {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.course-assignment__course-category {
  background: #f3f4f6;
  color: #374151;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.course-assignment__course-price {
  color: #059669;
  font-weight: 600;
  font-size: 0.9rem;
}

.course-assignment__assignment {
  display: flex;
  align-items: center;
  gap: 1rem;
  min-width: 300px;
}

.course-assignment__assigned {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  width: 100%;
}

.course-assignment__instructor-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.course-assignment__instructor-label {
  font-size: 0.8rem;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.course-assignment__instructor-name {
  font-weight: 600;
  color: #0a2d69;
}

.course-assignment__unassigned {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  width: 100%;
}

.course-assignment__unassigned-label {
  font-size: 0.8rem;
  color: #ef4444;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.course-assignment__instructor-select {
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  font-size: 0.9rem;
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.course-assignment__instructor-select:focus {
  outline: none;
  border-color: #0a2d69;
  box-shadow: 0 0 0 3px rgba(10, 45, 105, 0.1);
}

.course-assignment__instructor-select:disabled {
  background: #f9fafb;
  cursor: not-allowed;
}

.course-assignment__saving {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .course-assignment {
    padding: 1rem;
  }

  .course-assignment__header {
    flex-direction: column;
    align-items: stretch;
  }

  .course-assignment__course-card {
    flex-direction: column;
    gap: 1rem;
  }

  .course-assignment__assignment {
    min-width: auto;
  }

  .course-assignment__summary {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  }
}
