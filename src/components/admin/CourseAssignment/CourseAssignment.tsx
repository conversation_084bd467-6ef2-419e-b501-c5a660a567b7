import React, { useState, useEffect } from "react";
import Button from "../../ui/Button";
import Spinner from "../../ui/Spinner";
import { getAllCourses } from "../../../services/firebaseCourseCapacityService";
import {
  getAllInstructors,
  updateInstructorAccount,
} from "../../../services/instructorManagementService";
import "./CourseAssignment.css";

interface CourseAssignmentProps {
  onBack: () => void;
}

const CourseAssignment: React.FC<CourseAssignmentProps> = ({ onBack }) => {
  const [courses, setCourses] = useState<any[]>([]);
  const [instructors, setInstructors] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState<string | null>(null);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    try {
      const [coursesData, instructorsData] = await Promise.all([
        getAllCourses(),
        getAllInstructors(),
      ]);
      setCourses(coursesData);
      setInstructors(instructorsData);
    } catch (error) {
      console.error("Error loading data:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleAssignCourse = async (courseId: string, instructorId: string) => {
    setSaving(courseId);
    try {
      console.log(
        `🔄 Assigning course ${courseId} to instructor ${instructorId}`
      );

      // Update the course with instructor assignment
      const { updateCourse } = await import(
        "../../../services/firebaseCourseCapacityService"
      );
      await updateCourse(courseId, { instructorId });

      // Update local state
      setCourses((prev) =>
        prev.map((c) => (c.id === courseId ? { ...c, instructorId } : c))
      );

      console.log(`✅ Successfully assigned course to instructor`);
    } catch (error) {
      console.error("❌ Error assigning course:", error);
      alert("Failed to assign course. Please try again.");
    } finally {
      setSaving(null);
    }
  };

  const handleUnassignCourse = async (courseId: string) => {
    setSaving(courseId);
    try {
      console.log(`🔄 Unassigning course ${courseId}`);

      // Update the course to remove instructor assignment
      const { updateCourse } = await import(
        "../../../services/firebaseCourseCapacityService"
      );
      await updateCourse(courseId, { instructorId: undefined });

      // Update local state
      setCourses((prev) =>
        prev.map((c) => (c.id === courseId ? { ...c, instructorId: null } : c))
      );

      console.log(`✅ Successfully unassigned course`);
    } catch (error) {
      console.error("❌ Error unassigning course:", error);
      alert("Failed to unassign course. Please try again.");
    } finally {
      setSaving(null);
    }
  };

  const getInstructorName = (instructorId: string) => {
    const instructor = instructors.find((i) => i.id === instructorId);
    if (!instructor) {
      console.log(`⚠️ Instructor not found for ID: ${instructorId}`);
      console.log(
        `Available instructors:`,
        instructors.map((i) => ({ id: i.id, name: i.name }))
      );
      return "Unknown Instructor";
    }

    // Handle both possible data structures
    if (instructor.firstName && instructor.lastName) {
      return `${instructor.firstName} ${instructor.lastName}`;
    } else if (instructor.name) {
      return instructor.name;
    } else {
      return instructor.email || "Unknown Instructor";
    }
  };

  if (loading) {
    return (
      <div className="course-assignment__loading">
        <Spinner size="large" />
        <p>Loading courses and instructors...</p>
      </div>
    );
  }

  return (
    <div className="course-assignment">
      <div className="course-assignment__header">
        <div className="course-assignment__title">
          <h2>Course Assignment</h2>
          <p>Assign instructors to courses</p>
        </div>
        <Button variant="secondary" onClick={onBack}>
          ← Back to Dashboard
        </Button>
      </div>

      <div className="course-assignment__content">
        <div className="course-assignment__summary">
          <div className="course-assignment__stat">
            <span className="course-assignment__stat-number">
              {courses.length}
            </span>
            <span className="course-assignment__stat-label">Total Courses</span>
          </div>
          <div className="course-assignment__stat">
            <span className="course-assignment__stat-number">
              {courses.filter((c) => c.instructorId).length}
            </span>
            <span className="course-assignment__stat-label">Assigned</span>
          </div>
          <div className="course-assignment__stat">
            <span className="course-assignment__stat-number">
              {courses.filter((c) => !c.instructorId).length}
            </span>
            <span className="course-assignment__stat-label">Unassigned</span>
          </div>
        </div>

        <div className="course-assignment__courses">
          {courses.map((course) => (
            <div key={course.id} className="course-assignment__course-card">
              <div className="course-assignment__course-info">
                <h3>{course.title}</h3>
                <p className="course-assignment__course-details">
                  {course.startDate} • {course.schedule?.daysOfWeek?.join(", ")}{" "}
                  at {course.schedule?.time}
                </p>
                <div className="course-assignment__course-meta">
                  <span className="course-assignment__course-category">
                    {course.category}
                  </span>
                  <span className="course-assignment__course-price">
                    {course.price}
                  </span>
                </div>
              </div>

              <div className="course-assignment__assignment">
                {course.instructorId ? (
                  <div className="course-assignment__assigned">
                    <div className="course-assignment__instructor-info">
                      <span className="course-assignment__instructor-label">
                        Assigned to:
                      </span>
                      <span className="course-assignment__instructor-name">
                        {getInstructorName(course.instructorId)}
                      </span>
                    </div>
                    <div className="course-assignment__actions">
                      <select
                        value={course.instructorId}
                        onChange={(e) => {
                          if (e.target.value === "") {
                            handleUnassignCourse(course.id);
                          } else {
                            handleAssignCourse(course.id, e.target.value);
                          }
                        }}
                        disabled={saving === course.id}
                        className="course-assignment__instructor-select"
                      >
                        <option value={course.instructorId}>
                          {getInstructorName(course.instructorId)}
                        </option>
                        <option value="">Unassign</option>
                        {instructors
                          .filter((i) => i.id !== course.instructorId)
                          .map((instructor) => (
                            <option key={instructor.id} value={instructor.id}>
                              {getInstructorName(instructor.id)}
                            </option>
                          ))}
                      </select>
                    </div>
                  </div>
                ) : (
                  <div className="course-assignment__unassigned">
                    <span className="course-assignment__unassigned-label">
                      No instructor assigned
                    </span>
                    <select
                      value=""
                      onChange={(e) => {
                        if (e.target.value) {
                          handleAssignCourse(course.id, e.target.value);
                        }
                      }}
                      disabled={saving === course.id}
                      className="course-assignment__instructor-select"
                    >
                      <option value="">Select instructor...</option>
                      {instructors.map((instructor) => (
                        <option key={instructor.id} value={instructor.id}>
                          {getInstructorName(instructor.id)}
                        </option>
                      ))}
                    </select>
                  </div>
                )}

                {saving === course.id && (
                  <div className="course-assignment__saving">
                    <Spinner size="small" />
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default CourseAssignment;
