.analytics {
  padding: 2rem;
  background: #f8f9fa;
  min-height: 100vh;
}

.analytics h2 {
  margin: 0 0 2rem 0;
  color: #0a2d69;
  font-size: 1.8rem;
  font-weight: 700;
}

/* Metrics Cards */
.analytics__metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.analytics__metric-card {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  border-left: 4px solid #0a2d69;
}

.analytics__metric-card h3 {
  margin: 0 0 0.5rem 0;
  color: #6b7280;
  font-size: 0.9rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.analytics__metric-value {
  font-size: 2rem;
  font-weight: 700;
  color: #0a2d69;
  margin-bottom: 0.5rem;
}

.analytics__metric-change {
  font-size: 0.85rem;
  color: #10b981;
  font-weight: 500;
}

/* Charts Grid */
.analytics__charts {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.analytics__chart-card {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.analytics__chart-card--large {
  grid-column: span 2;
}

.analytics__chart-card h3 {
  margin: 0 0 1rem 0;
  color: #0a2d69;
  font-size: 1.1rem;
  font-weight: 600;
}

.analytics__chart-container {
  height: 300px;
  position: relative;
}

/* Insights Section */
.analytics__insights {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
}

.analytics__insight-card {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.analytics__insight-card h3 {
  margin: 0 0 1rem 0;
  color: #0a2d69;
  font-size: 1.1rem;
  font-weight: 600;
  border-bottom: 2px solid #ffa300;
  padding-bottom: 0.5rem;
}

.analytics__insight-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.analytics__insight-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 3px solid #e5e7eb;
}

.analytics__insight-label {
  color: #6b7280;
  font-weight: 500;
  font-size: 0.9rem;
}

.analytics__insight-value {
  color: #0a2d69;
  font-weight: 700;
  font-size: 1rem;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .analytics__chart-card--large {
    grid-column: span 1;
  }
}

@media (max-width: 768px) {
  .analytics {
    padding: 1rem;
  }

  .analytics__metrics {
    grid-template-columns: 1fr;
  }

  .analytics__charts {
    grid-template-columns: 1fr;
  }

  .analytics__chart-container {
    height: 250px;
  }

  .analytics__metric-value {
    font-size: 1.5rem;
  }

  .analytics__insights {
    grid-template-columns: 1fr;
  }

  .analytics__insight-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}

/* Chart Specific Styling */
.analytics__chart-card canvas {
  max-height: 300px !important;
}

/* Loading States */
.analytics__loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #6b7280;
  font-size: 1.1rem;
}

/* Hover Effects */
.analytics__metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  transition: all 0.2s ease;
}

.analytics__chart-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.analytics__insight-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

/* Special styling for revenue insights */
.analytics__insight-item:first-child {
  border-left-color: #ffa300;
}

.analytics__insight-item:nth-child(2) {
  border-left-color: #ef4444;
}

.analytics__insight-item:nth-child(3) {
  border-left-color: #8b5cf6;
}
