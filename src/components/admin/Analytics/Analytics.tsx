import React, { useMemo } from "react";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from "chart.js";
import { Line, Bar, Doughnut } from "react-chartjs-2";
import { formatCurrency } from "../../../utils/priceUtils";
import "./Analytics.css";

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

interface AnalyticsProps {
  stats: any;
}

const Analytics: React.FC<AnalyticsProps> = ({ stats }) => {
  // Revenue trend data (based on actual registration dates)
  const revenueChartData = useMemo(() => {
    if (!stats.recentRegistrations || stats.recentRegistrations.length === 0) {
      return {
        labels: ["No Data"],
        datasets: [
          {
            label: "Monthly Revenue",
            data: [0],
            borderColor: "#0a2d69",
            backgroundColor: "rgba(10, 45, 105, 0.1)",
            borderWidth: 3,
            fill: true,
            tension: 0.4,
          },
        ],
      };
    }

    // Group registrations by month and calculate revenue
    const monthlyRevenue: { [key: string]: number } = {};

    stats.recentRegistrations
      .filter((reg: any) => reg.paymentStatus === "paid")
      .forEach((reg: any) => {
        const date = new Date(reg.registrationDate);
        const monthKey = `${date.getFullYear()}-${String(
          date.getMonth() + 1
        ).padStart(2, "0")}`;
        const monthLabel = date.toLocaleDateString("en-US", {
          month: "short",
          year: "numeric",
        });

        const price = parseFloat(
          reg.coursePrice?.replace(/[^0-9.]/g, "") || "0"
        );

        if (!monthlyRevenue[monthLabel]) {
          monthlyRevenue[monthLabel] = 0;
        }
        monthlyRevenue[monthLabel] += price;
      });

    // Sort months chronologically and get last 6 months
    const sortedMonths = Object.keys(monthlyRevenue).sort((a, b) => {
      const dateA = new Date(a);
      const dateB = new Date(b);
      return dateA.getTime() - dateB.getTime();
    });

    const last6Months = sortedMonths.slice(-6);
    const revenueData = last6Months.map((month) => monthlyRevenue[month] || 0);

    return {
      labels: last6Months.length > 0 ? last6Months : ["No Data"],
      datasets: [
        {
          label: "Monthly Revenue",
          data: revenueData.length > 0 ? revenueData : [0],
          borderColor: "#0a2d69",
          backgroundColor: "rgba(10, 45, 105, 0.1)",
          borderWidth: 3,
          fill: true,
          tension: 0.4,
        },
      ],
    };
  }, [stats.recentRegistrations]);

  // Payment status breakdown
  const paymentStatusData = useMemo(() => {
    const paymentStats = stats.paymentStats || {};
    return {
      labels: ["Paid", "Pending", "Late", "Refunded", "Cancelled"],
      datasets: [
        {
          data: [
            paymentStats.paid || 0,
            paymentStats.pending || 0,
            paymentStats.late || 0,
            paymentStats.refunded || 0,
            paymentStats.cancelled || 0,
          ],
          backgroundColor: [
            "#10b981", // Green for paid
            "#f59e0b", // Yellow for pending
            "#ef4444", // Red for late
            "#8b5cf6", // Purple for refunded
            "#6b7280", // Gray for cancelled
          ],
          borderWidth: 0,
        },
      ],
    };
  }, [stats.paymentStats]);

  // Course enrollment data
  const enrollmentChartData = useMemo(() => {
    const courseBreakdown = stats.courseBreakdown || [];
    return {
      labels: courseBreakdown.map(
        (course: any) => course.courseTitle.substring(0, 20) + "..."
      ),
      datasets: [
        {
          label: "Enrolled",
          data: courseBreakdown.map((course: any) => course.enrolled),
          backgroundColor: "#0a2d69",
        },
        {
          label: "Capacity",
          data: courseBreakdown.map((course: any) => course.capacity),
          backgroundColor: "#e5e7eb",
        },
        {
          label: "Waitlist",
          data: courseBreakdown.map((course: any) => course.waitlist),
          backgroundColor: "#ffa300",
        },
      ],
    };
  }, [stats.courseBreakdown]);

  // Conversion funnel data
  const conversionData = useMemo(() => {
    const totalRegistrations = stats.totalRegistrations || 0;
    const paidRegistrations = stats.paymentStats?.paid || 0;
    const completedCourses =
      stats.recentRegistrations?.filter((reg: any) => reg.courseCompleted)
        .length || 0;

    return {
      labels: ["Registered", "Paid", "Completed"],
      datasets: [
        {
          label: "Conversion Funnel",
          data: [totalRegistrations, paidRegistrations, completedCourses],
          backgroundColor: ["#3b82f6", "#10b981", "#8b5cf6"],
          borderWidth: 0,
        },
      ],
    };
  }, [stats]);

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: "top" as const,
      },
    },
    scales: {
      y: {
        beginAtZero: true,
      },
    },
  };

  const doughnutOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: "bottom" as const,
      },
    },
  };

  return (
    <div className="analytics">
      <h2>Enhanced Analytics & Insights</h2>

      {/* Key Metrics Cards */}
      <div className="analytics__metrics">
        <div className="analytics__metric-card">
          <h3>Total Revenue</h3>
          <div className="analytics__metric-value">
            {formatCurrency(stats.totalRevenue || 0)}
          </div>
          <div className="analytics__metric-change">Current total revenue</div>
        </div>

        <div className="analytics__metric-card">
          <h3>Conversion Rate</h3>
          <div className="analytics__metric-value">
            {stats.totalRegistrations > 0
              ? Math.round(
                  (stats.paymentStats?.paid / stats.totalRegistrations) * 100
                )
              : 0}
            %
          </div>
          <div className="analytics__metric-change">
            {stats.paymentStats?.paid || 0} of {stats.totalRegistrations || 0}{" "}
            paid
          </div>
        </div>

        <div className="analytics__metric-card">
          <h3>Average Revenue per Student</h3>
          <div className="analytics__metric-value">
            {formatCurrency(
              stats.paymentStats?.paid > 0
                ? Math.round(stats.totalRevenue / stats.paymentStats.paid)
                : 0
            )}
          </div>
          <div className="analytics__metric-change">Per paid registration</div>
        </div>

        <div className="analytics__metric-card">
          <h3>Course Utilization</h3>
          <div className="analytics__metric-value">
            {stats.utilizationRate?.toFixed(1) || 0}%
          </div>
          <div className="analytics__metric-change">
            {stats.totalEnrolled || 0} of {stats.totalCapacity || 0} spots
            filled
          </div>
        </div>
      </div>

      {/* Charts Grid */}
      <div className="analytics__charts">
        {/* Revenue Trend */}
        <div className="analytics__chart-card analytics__chart-card--large">
          <h3>Revenue Trend</h3>
          <div className="analytics__chart-container">
            <Line data={revenueChartData} options={chartOptions} />
          </div>
        </div>

        {/* Payment Status Breakdown */}
        <div className="analytics__chart-card">
          <h3>Payment Status</h3>
          <div className="analytics__chart-container">
            <Doughnut data={paymentStatusData} options={doughnutOptions} />
          </div>
        </div>

        {/* Course Enrollment */}
        <div className="analytics__chart-card analytics__chart-card--large">
          <h3>Course Enrollment vs Capacity</h3>
          <div className="analytics__chart-container">
            <Bar data={enrollmentChartData} options={chartOptions} />
          </div>
        </div>

        {/* Conversion Funnel */}
        <div className="analytics__chart-card">
          <h3>Conversion Funnel</h3>
          <div className="analytics__chart-container">
            <Bar data={conversionData} options={chartOptions} />
          </div>
        </div>
      </div>

      {/* Revenue Insights */}
      <div className="analytics__insights">
        <div className="analytics__insight-card">
          <h3>Revenue Insights</h3>
          <div className="analytics__insight-list">
            <div className="analytics__insight-item">
              <span className="analytics__insight-label">Pending Revenue:</span>
              <span className="analytics__insight-value">
                {formatCurrency(
                  stats.recentRegistrations
                    ?.filter((reg: any) => reg.paymentStatus === "pending")
                    .reduce((sum: number, reg: any) => {
                      const price = parseFloat(
                        reg.coursePrice?.replace(/[^0-9.]/g, "") || "0"
                      );
                      return sum + price;
                    }, 0) || 0
                )}
              </span>
            </div>
            <div className="analytics__insight-item">
              <span className="analytics__insight-label">
                Lost Revenue (Cancelled):
              </span>
              <span className="analytics__insight-value">
                {formatCurrency(
                  stats.recentRegistrations
                    ?.filter((reg: any) => reg.paymentStatus === "cancelled")
                    .reduce((sum: number, reg: any) => {
                      const price = parseFloat(
                        reg.coursePrice?.replace(/[^0-9.]/g, "") || "0"
                      );
                      return sum + price;
                    }, 0) || 0
                )}
              </span>
            </div>
            <div className="analytics__insight-item">
              <span className="analytics__insight-label">Refunded Amount:</span>
              <span className="analytics__insight-value">
                {formatCurrency(
                  stats.recentRegistrations
                    ?.filter((reg: any) => reg.paymentStatus === "refunded")
                    .reduce((sum: number, reg: any) => {
                      const price = parseFloat(
                        reg.coursePrice?.replace(/[^0-9.]/g, "") || "0"
                      );
                      return sum + price;
                    }, 0) || 0
                )}
              </span>
            </div>
          </div>
        </div>

        <div className="analytics__insight-card">
          <h3>Performance Metrics</h3>
          <div className="analytics__insight-list">
            <div className="analytics__insight-item">
              <span className="analytics__insight-label">
                Total Students Served:
              </span>
              <span className="analytics__insight-value">
                {stats.totalRegistrations || 0}
              </span>
            </div>
            <div className="analytics__insight-item">
              <span className="analytics__insight-label">
                Active Enrollments:
              </span>
              <span className="analytics__insight-value">
                {stats.totalEnrolled || 0}
              </span>
            </div>
            <div className="analytics__insight-item">
              <span className="analytics__insight-label">Waitlist Total:</span>
              <span className="analytics__insight-value">
                {stats.courseBreakdown?.reduce(
                  (sum: number, course: any) => sum + course.waitlist,
                  0
                ) || 0}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Analytics;
