import React, { useState, useEffect } from "react";
import Button from "../../ui/Button";
import Spinner from "../../ui/Spinner";
import {
  getAllInstructors,
  createInstructorAccount,
  updateInstructorAccount,
  deleteInstructorAccount,
  resendInstructorInvitation,
  InstructorAccount,
  CreateInstructorData,
} from "../../../services/instructorManagementService";
import { getAllCourses } from "../../../services/firebaseCourseCapacityService";
import { getAdminSession } from "../../../services/adminService";
import "./InstructorManagement.css";

interface Course {
  slug: string;
  title: string;
  status: string;
}

const InstructorManagement: React.FC = () => {
  const [instructors, setInstructors] = useState<InstructorAccount[]>([]);
  const [courses, setCourses] = useState<Course[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingInstructorId, setEditingInstructorId] = useState<string | null>(
    null
  );
  const [editingInstructorData, setEditingInstructorData] = useState<
    Partial<InstructorAccount>
  >({});
  const [newInstructor, setNewInstructor] = useState<CreateInstructorData>({
    email: "",
    name: "",
    phone: "",
    assignedCourses: [], // Keep for compatibility but won't be used in UI
  });
  const [saving, setSaving] = useState(false);

  const adminUser = getAdminSession();

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    try {
      console.log("🔄 Loading instructor management data...");
      const [instructorData, courseData] = await Promise.all([
        getAllInstructors(),
        getAllCourses(),
      ]);
      console.log("📊 Loaded instructors:", instructorData);
      console.log("📚 Loaded courses:", courseData);
      setInstructors(instructorData);
      setCourses(courseData.filter((course) => course.status === "active"));
    } catch (error) {
      console.error("❌ Error loading data:", error);
      alert(
        `Failed to load data: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    } finally {
      setLoading(false);
    }
  };

  const handleCreateInstructor = async () => {
    if (!newInstructor.email || !newInstructor.name) {
      alert("Please fill in all required fields (email, name)");
      return;
    }

    console.log("🚀 Sending instructor invitation with data:", newInstructor);

    setSaving(true);
    try {
      const result = await createInstructorAccount(
        newInstructor,
        adminUser?.email || ""
      );
      console.log("📝 Create instructor result:", result);

      if (result.success) {
        console.log(
          "✅ Instructor invitation sent successfully, reloading data..."
        );
        const setupLink = `${window.location.origin}/instructor/setup?instructorId=${result.instructorId}`;
        alert(
          `Instructor invitation created successfully!\n\nFor testing, use this setup link:\n${setupLink}\n\n(In production, this would be sent via email)`
        );
        setNewInstructor({
          email: "",
          name: "",
          phone: "",
          assignedCourses: [],
        });
        setShowAddForm(false);
        await loadData(); // Reload data to show the invited instructor
      } else {
        console.error("❌ Failed to create instructor:", result.error);
        alert(result.error || "Failed to create instructor account");
      }
    } catch (error) {
      console.error("❌ Error creating instructor:", error);
      alert(
        `Failed to create instructor account: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    } finally {
      setSaving(false);
    }
  };

  const handleStartEditInstructor = (instructor: InstructorAccount) => {
    setEditingInstructorId(instructor.id);
    setEditingInstructorData({ ...instructor });
    setShowAddForm(false);
  };

  const handleSaveEditInstructor = async () => {
    if (!editingInstructorId || !editingInstructorData) return;

    if (!editingInstructorData.name?.trim()) {
      alert("Instructor name is required");
      return;
    }

    setSaving(true);
    try {
      const { id, email, createdAt, updatedAt, ...updates } =
        editingInstructorData;
      const success = await updateInstructorAccount(editingInstructorId, {
        name: updates.name!.trim(),
        phone: updates.phone || "",
        status: updates.status!,
        // Note: assignedCourses are now managed via the Course Assignment interface
      });

      if (success) {
        alert("Instructor updated successfully!");
        setEditingInstructorId(null);
        setEditingInstructorData({});
        loadData();
      } else {
        alert("Failed to update instructor");
      }
    } catch (error) {
      console.error("Error updating instructor:", error);
      alert("Failed to update instructor");
    } finally {
      setSaving(false);
    }
  };

  const handleCancelEditInstructor = () => {
    setEditingInstructorId(null);
    setEditingInstructorData({});
  };

  const handleEditInstructorFieldChange = (
    field: keyof InstructorAccount,
    value: any
  ) => {
    setEditingInstructorData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleDeleteInstructor = async (
    instructorId: string,
    instructorName: string
  ) => {
    const confirmed = window.confirm(
      `Are you sure you want to delete instructor "${instructorName}"? This action cannot be undone.`
    );

    if (!confirmed) return;

    try {
      const success = await deleteInstructorAccount(instructorId);
      if (success) {
        alert("Instructor deleted successfully!");
        loadData();
      } else {
        alert("Failed to delete instructor");
      }
    } catch (error) {
      console.error("Error deleting instructor:", error);
      alert("Failed to delete instructor");
    }
  };

  const handleResendInvitation = async (instructorEmail: string) => {
    setSaving(true);
    try {
      const result = await resendInstructorInvitation(instructorEmail);
      if (result.success) {
        alert("Invitation resent successfully!");
        loadData();
      } else {
        alert(result.error || "Failed to resend invitation");
      }
    } catch (error) {
      console.error("Error resending invitation:", error);
      alert("Failed to resend invitation");
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="instructor-management__loading">
        <Spinner size="large" />
        <p>Loading instructors...</p>
      </div>
    );
  }

  return (
    <div className="instructor-management">
      <div className="instructor-management__header">
        <h2>Instructor Management</h2>
        <Button onClick={() => setShowAddForm(true)} disabled={showAddForm}>
          Invite New Instructor
        </Button>
      </div>

      <div className="instructor-management__info">
        <p>
          <strong>💡 Tip:</strong> Use the "Course Assignments" tab to assign
          courses to instructors.
        </p>
      </div>

      {showAddForm && (
        <div className="instructor-management__form">
          <h3>Invite New Instructor</h3>
          <div className="instructor-management__form-grid">
            <div className="instructor-management__field">
              <label>Email *</label>
              <input
                type="email"
                value={newInstructor.email}
                onChange={(e) =>
                  setNewInstructor({ ...newInstructor, email: e.target.value })
                }
                placeholder="<EMAIL>"
              />
            </div>

            <div className="instructor-management__field">
              <label>Full Name *</label>
              <input
                type="text"
                value={newInstructor.name}
                onChange={(e) =>
                  setNewInstructor({ ...newInstructor, name: e.target.value })
                }
                placeholder="John Doe"
              />
            </div>
            <div className="instructor-management__field">
              <label>Phone</label>
              <input
                type="tel"
                value={newInstructor.phone}
                onChange={(e) =>
                  setNewInstructor({ ...newInstructor, phone: e.target.value })
                }
                placeholder="(*************"
              />
            </div>
          </div>

          <div className="instructor-management__note">
            <p>
              <strong>Note:</strong> Course assignments can be managed after
              creating the instructor account using the "Course Assignments" tab
              in the admin dashboard.
            </p>
          </div>

          <div className="instructor-management__form-actions">
            <Button
              onClick={handleCreateInstructor}
              loading={saving}
              disabled={saving}
            >
              {saving ? "Sending Invitation..." : "Send Invitation"}
            </Button>
            <Button
              variant="secondary"
              onClick={() => {
                setShowAddForm(false);
                setNewInstructor({
                  email: "",
                  name: "",
                  phone: "",
                  assignedCourses: [],
                });
              }}
              disabled={saving}
            >
              Cancel
            </Button>
          </div>
        </div>
      )}

      <div className="instructor-management__list">
        {instructors.length === 0 ? (
          <div className="instructor-management__empty">
            <p>
              No instructors found. Create your first instructor account above.
            </p>
          </div>
        ) : (
          <div className="instructor-management__table-container">
            <table className="instructor-management__table">
              <thead>
                <tr>
                  <th>Name</th>
                  <th>Email</th>
                  <th>Phone</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {instructors.map((instructor) => {
                  const isEditing = editingInstructorId === instructor.id;
                  const editData = isEditing
                    ? editingInstructorData
                    : instructor;

                  return (
                    <tr
                      key={instructor.id}
                      className={isEditing ? "editing" : ""}
                    >
                      <td>
                        {isEditing ? (
                          <input
                            type="text"
                            value={editData.name || ""}
                            onChange={(e) =>
                              handleEditInstructorFieldChange(
                                "name",
                                e.target.value
                              )
                            }
                            placeholder="Full Name"
                            className="instructor-management__inline-input"
                          />
                        ) : (
                          instructor.name
                        )}
                      </td>
                      <td>
                        {instructor.email}
                        {isEditing && (
                          <small className="instructor-management__email-note">
                            (Email cannot be changed)
                          </small>
                        )}
                      </td>
                      <td>
                        {isEditing ? (
                          <input
                            type="tel"
                            value={editData.phone || ""}
                            onChange={(e) =>
                              handleEditInstructorFieldChange(
                                "phone",
                                e.target.value
                              )
                            }
                            placeholder="Phone"
                            className="instructor-management__inline-input"
                          />
                        ) : (
                          instructor.phone || "—"
                        )}
                      </td>
                      <td>
                        {isEditing ? (
                          <select
                            value={editData.status || "active"}
                            onChange={(e) =>
                              handleEditInstructorFieldChange(
                                "status",
                                e.target.value
                              )
                            }
                            className="instructor-management__inline-select"
                          >
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                          </select>
                        ) : (
                          <span
                            className={`instructor-management__status instructor-management__status--${instructor.status}`}
                          >
                            {instructor.status}
                          </span>
                        )}
                      </td>
                      <td>
                        <div className="instructor-management__actions">
                          {isEditing ? (
                            <>
                              <Button
                                variant="primary"
                                size="small"
                                onClick={handleSaveEditInstructor}
                                loading={saving}
                                disabled={saving}
                              >
                                {saving ? "Saving..." : "Save"}
                              </Button>
                              <Button
                                variant="secondary"
                                size="small"
                                onClick={handleCancelEditInstructor}
                                disabled={saving}
                              >
                                Cancel
                              </Button>
                            </>
                          ) : instructor.status === "invited" ? (
                            <>
                              <Button
                                variant="secondary"
                                size="small"
                                onClick={() =>
                                  handleResendInvitation(instructor.email)
                                }
                                disabled={saving}
                              >
                                Resend Invitation
                              </Button>
                              <Button
                                variant="secondary"
                                size="small"
                                onClick={() =>
                                  handleDeleteInstructor(
                                    instructor.id,
                                    instructor.name
                                  )
                                }
                                className="instructor-management__delete-btn"
                              >
                                Cancel Invitation
                              </Button>
                            </>
                          ) : (
                            <>
                              <Button
                                variant="secondary"
                                size="small"
                                onClick={() =>
                                  handleStartEditInstructor(instructor)
                                }
                              >
                                Edit
                              </Button>
                              <Button
                                variant="secondary"
                                size="small"
                                onClick={() =>
                                  handleDeleteInstructor(
                                    instructor.id,
                                    instructor.name
                                  )
                                }
                                className="instructor-management__delete-btn"
                              >
                                Delete
                              </Button>
                            </>
                          )}
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

export default InstructorManagement;
