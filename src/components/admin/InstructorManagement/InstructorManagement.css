.instructor-management {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.instructor-management__loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  gap: 1rem;
}

.instructor-management__loading p {
  color: #6b7280;
  font-size: 1.1rem;
}

/* Header */
.instructor-management__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.instructor-management__header h2 {
  margin: 0;
  color: #0a2d69;
  font-size: 1.75rem;
  font-weight: 600;
}

.instructor-management__info {
  background: #f0f9ff;
  border: 1px solid #0ea5e9;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 2rem;
}

.instructor-management__info p {
  margin: 0;
  color: #0c4a6e;
  font-size: 0.9rem;
}

.instructor-management__note {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 1rem;
  margin-bottom: 1.5rem;
}

.instructor-management__note p {
  margin: 0;
  color: #475569;
  font-size: 0.875rem;
}

/* Form */
.instructor-management__form {
  background: #f8f9fa;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
}

.instructor-management__form h3 {
  margin: 0 0 1.5rem 0;
  color: #0a2d69;
  font-size: 1.25rem;
  font-weight: 600;
}

.instructor-management__form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.instructor-management__field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.instructor-management__field label {
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
}

.instructor-management__field input,
.instructor-management__field select {
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
}

.instructor-management__field input:focus,
.instructor-management__field select:focus {
  outline: none;
  border-color: #0a2d69;
  box-shadow: 0 0 0 3px rgba(10, 45, 105, 0.1);
}

.instructor-management__disabled-field {
  background: #f3f4f6 !important;
  color: #6b7280 !important;
  cursor: not-allowed !important;
}

.instructor-management__field small {
  color: #6b7280;
  font-size: 0.75rem;
}

/* Courses */
.instructor-management__courses {
  margin-bottom: 1.5rem;
}

.instructor-management__courses label {
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
  margin-bottom: 1rem;
  display: block;
}

.instructor-management__course-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 0.75rem;
  padding: 1rem;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  max-height: 200px;
  overflow-y: auto;
}

.instructor-management__course-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.instructor-management__course-item:hover {
  background: #f8f9fa;
}

.instructor-management__course-item input[type="checkbox"] {
  margin: 0;
}

/* Form Actions */
.instructor-management__form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

/* List */
.instructor-management__list {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  overflow: hidden;
}

.instructor-management__empty {
  padding: 3rem;
  text-align: center;
  color: #6b7280;
}

.instructor-management__table-container {
  overflow-x: auto;
}

.instructor-management__table {
  width: 100%;
  border-collapse: collapse;
}

.instructor-management__table th {
  background: #f8f9fa;
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  color: #374151;
  border-bottom: 1px solid #e5e7eb;
  font-size: 0.875rem;
}

.instructor-management__table td {
  padding: 1rem;
  border-bottom: 1px solid #f3f4f6;
  vertical-align: middle;
}

.instructor-management__table tbody tr:hover {
  background: #f8f9fa;
}

/* Course Tags */
.instructor-management__course-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.instructor-management__course-tag {
  background: #e0f2fe;
  color: #0277bd;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

/* Status */
.instructor-management__status {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.instructor-management__status--active {
  background: #dcfce7;
  color: #166534;
}

.instructor-management__status--inactive {
  background: #fef2f2;
  color: #dc2626;
}

.instructor-management__status--invited {
  background: #fef3c7;
  color: #d97706;
}

.instructor-management__status--pending {
  background: #f3f4f6;
  color: #6b7280;
}

/* Actions */
.instructor-management__actions {
  display: flex;
  gap: 0.5rem;
}

.instructor-management__delete-btn {
  color: #dc2626 !important;
  border-color: #dc2626 !important;
}

.instructor-management__delete-btn:hover {
  background-color: #dc2626 !important;
  color: white !important;
}

/* Inline Editing */
.instructor-management__table tr.editing {
  background-color: #f8fafc;
  border: 2px solid #0ea5e9;
}

.instructor-management__inline-input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 0.875rem;
  background: white;
}

.instructor-management__inline-input:focus {
  outline: none;
  border-color: #0ea5e9;
  box-shadow: 0 0 0 2px rgba(14, 165, 233, 0.1);
}

.instructor-management__inline-select {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 0.875rem;
  background: white;
}

.instructor-management__inline-select:focus {
  outline: none;
  border-color: #0ea5e9;
  box-shadow: 0 0 0 2px rgba(14, 165, 233, 0.1);
}

.instructor-management__email-note {
  display: block;
  color: #6b7280;
  font-size: 0.75rem;
  margin-top: 0.25rem;
  font-style: italic;
}

/* Modal */
.instructor-management__modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.instructor-management__modal {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
}

.instructor-management__modal h3 {
  margin: 0 0 1.5rem 0;
  color: #0a2d69;
  font-size: 1.25rem;
  font-weight: 600;
}

/* Responsive design */
@media (max-width: 768px) {
  .instructor-management {
    padding: 1rem;
  }

  .instructor-management__header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .instructor-management__form-grid {
    grid-template-columns: 1fr;
  }

  .instructor-management__course-list {
    grid-template-columns: 1fr;
  }

  .instructor-management__form-actions {
    flex-direction: column;
  }

  .instructor-management__actions {
    flex-direction: column;
  }

  .instructor-management__modal {
    margin: 1rem;
    padding: 1.5rem;
  }
}
