.email-templates {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.email-templates__loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem;
  gap: 1rem;
}

.email-templates__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #e8f4f8;
}

.email-templates__header h2 {
  color: #0a2d69;
  margin: 0;
}

.email-templates__header-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.email-templates__filter {
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.9rem;
}

.email-templates__form {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  margin-bottom: 2rem;
  border: 2px solid #e8f4f8;
}

.email-templates__form h3 {
  color: #0a2d69;
  margin: 0 0 1.5rem 0;
}

.email-templates__form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1rem;
}

.email-templates__subject-input,
.email-templates__body-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 1rem;
  margin-bottom: 1rem;
}

.email-templates__body-input {
  font-family: "Courier New", monospace;
  resize: vertical;
}

.email-templates__variables {
  background: #f8fafc;
  padding: 1.5rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
}

.email-templates__variables h4 {
  margin: 0 0 1rem 0;
  color: #374151;
}

.email-templates__variables-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 0.75rem;
}

.email-templates__variable {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.email-templates__variable code {
  background: #e5e7eb;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 600;
  color: #1f2937;
}

.email-templates__variable span {
  font-size: 0.875rem;
  color: #6b7280;
}

.email-templates__form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.email-templates__preview {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  margin-bottom: 2rem;
  border: 2px solid #e8f4f8;
}

.email-templates__preview h3 {
  color: #0a2d69;
  margin: 0 0 1.5rem 0;
}

.email-templates__preview-content {
  background: #f8fafc;
  padding: 1.5rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
}

.email-templates__preview-subject {
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e5e7eb;
}

.email-templates__preview-body pre {
  white-space: pre-wrap;
  font-family: inherit;
  margin: 0;
  line-height: 1.6;
}

/* Enhanced Preview Styles */
.email-templates__preview-controls {
  background: #f8fafc;
  padding: 1.5rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
  border: 1px solid #e5e7eb;
}

.email-templates__preview-control-group {
  margin-bottom: 1rem;
}

.email-templates__preview-control-group:last-child {
  margin-bottom: 0;
}

.email-templates__preview-control-group label {
  display: block;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.email-templates__preview-select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.9rem;
  background: white;
}

.email-templates__preview-custom {
  background: #fef3c7;
  padding: 1.5rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
  border: 1px solid #f59e0b;
}

.email-templates__preview-custom h4 {
  margin: 0 0 1rem 0;
  color: #92400e;
  font-size: 1rem;
}

.email-templates__preview-custom-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.email-templates__preview-custom-field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.email-templates__preview-custom-label {
  font-weight: 600;
  color: #92400e;
  font-size: 0.9rem;
  font-family: "Courier New", monospace;
  background: #fef3c7;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  border: 1px solid #f59e0b;
  display: inline-block;
  width: fit-content;
}

.email-templates__preview-custom-input {
  padding: 0.75rem;
  border: 1px solid #f59e0b;
  border-radius: 6px;
  font-size: 0.9rem;
  background: white;
  transition: border-color 0.2s ease;
}

.email-templates__preview-custom-input:focus {
  outline: none;
  border-color: #d97706;
  box-shadow: 0 0 0 3px rgba(251, 191, 36, 0.1);
}

.email-templates__preview-text {
  background: white;
  padding: 1rem;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
  margin-top: 0.5rem;
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
}

.email-templates__preview-text pre {
  margin: 0;
  white-space: pre-wrap;
  font-family: inherit;
}

.email-templates__preview-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e5e7eb;
}

.email-templates__preview-filter-note {
  font-size: 0.8rem;
  color: #6b7280;
  font-weight: normal;
  font-style: italic;
}

.email-templates__preview-no-registrations {
  margin-top: 0.5rem;
  padding: 0.75rem;
  background: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 6px;
  font-size: 0.9rem;
  color: #92400e;
  text-align: center;
}

.email-templates__list {
  margin-top: 2rem;
}

.email-templates__empty {
  text-align: center;
  color: #6b7280;
  font-style: italic;
  padding: 3rem;
  background: #f8fafc;
  border-radius: 8px;
}

.email-templates__grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 1.5rem;
}

.email-templates__card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  border: 1px solid #e8f4f8;
  transition: all 0.2s ease;
}

.email-templates__card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.email-templates__card-header {
  padding: 1.5rem 1.5rem 1rem 1.5rem;
  border-bottom: 1px solid #e8f4f8;
}

.email-templates__card-header h4 {
  margin: 0 0 0.75rem 0;
  color: #0a2d69;
  font-size: 1.125rem;
}

.email-templates__card-badges {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.email-templates__category-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.email-templates__category-badge--registration {
  background: #dbeafe;
  color: #1e40af;
}

.email-templates__category-badge--payment {
  background: #d1fae5;
  color: #065f46;
}

.email-templates__category-badge--course {
  background: #fef3c7;
  color: #92400e;
}

.email-templates__category-badge--waitlist {
  background: #fce7f3;
  color: #be185d;
}

.email-templates__category-badge--general {
  background: #e5e7eb;
  color: #374151;
}

.email-templates__category-badge--reminder {
  background: #fed7d7;
  color: #c53030;
}

.email-templates__status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.email-templates__status-badge--active {
  background: #d1fae5;
  color: #065f46;
}

.email-templates__status-badge--inactive {
  background: #fee2e2;
  color: #dc2626;
}

.email-templates__card-content {
  padding: 1rem 1.5rem;
}

.email-templates__card-content p {
  margin: 0 0 0.75rem 0;
  font-size: 0.9rem;
}

.email-templates__card-body {
  color: #6b7280;
  line-height: 1.5;
}

.email-templates__card-actions {
  padding: 1rem 1.5rem;
  background: #f8fafc;
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

/* Responsive design */
@media (max-width: 768px) {
  .email-templates {
    padding: 1rem;
  }

  .email-templates__header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .email-templates__header-actions {
    justify-content: space-between;
  }

  .email-templates__form-grid {
    grid-template-columns: 1fr;
  }

  .email-templates__variables-grid {
    grid-template-columns: 1fr;
  }

  .email-templates__grid {
    grid-template-columns: 1fr;
  }

  .email-templates__card-actions {
    flex-direction: column;
  }

  .email-templates__preview-custom-grid {
    grid-template-columns: 1fr;
  }

  .email-templates__preview-custom-label {
    font-size: 0.8rem;
  }

  .email-templates__preview-actions {
    flex-direction: column;
  }
}

/* Inline Editing */
.email-templates__card.editing {
  background-color: #f8fafc;
  border: 2px solid #0ea5e9;
  transform: scale(1.02);
}

.email-templates__inline-edit-header {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  width: 100%;
}

.email-templates__inline-edit-content {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  width: 100%;
}

.email-templates__inline-input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 0.875rem;
  background: white;
}

.email-templates__inline-input:focus {
  outline: none;
  border-color: #0ea5e9;
  box-shadow: 0 0 0 2px rgba(14, 165, 233, 0.1);
}

.email-templates__inline-select {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 0.875rem;
  background: white;
}

.email-templates__inline-select:focus {
  outline: none;
  border-color: #0ea5e9;
  box-shadow: 0 0 0 2px rgba(14, 165, 233, 0.1);
}

.email-templates__inline-textarea {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 0.875rem;
  background: white;
  resize: vertical;
  min-height: 120px;
}

.email-templates__inline-textarea:focus {
  outline: none;
  border-color: #0ea5e9;
  box-shadow: 0 0 0 2px rgba(14, 165, 233, 0.1);
}
