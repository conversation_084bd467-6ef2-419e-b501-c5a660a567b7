import React, { useState, useEffect } from "react";
import Button from "../../ui/Button";
import Spinner from "../../ui/Spinner";
import {
  getAllEmailTemplates,
  createEmailTemplate,
  updateEmailTemplate,
  deleteEmailTemplate,
  toggleEmailTemplateStatus,
  previewEmailTemplate,
} from "../../../services/emailTemplateService";
import {
  EmailTemplate,
  CreateEmailTemplateData,
  EmailTemplateCategory,
  EMAIL_TEMPLATE_VARIABLES,
} from "../../../types/emailTemplate";
import {
  getRegistrationStats,
  getAllCourses,
} from "../../../services/firebaseCourseCapacityService";
import "./EmailTemplates.css";

interface EmailTemplatesProps {
  onClose?: () => void;
}

const EmailTemplates: React.FC<EmailTemplatesProps> = ({ onClose }) => {
  const [templates, setTemplates] = useState<EmailTemplate[]>([]);
  const [loading, setLoading] = useState(false);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingTemplateId, setEditingTemplateId] = useState<string | null>(
    null
  );
  const [editingTemplateData, setEditingTemplateData] = useState<
    Partial<EmailTemplate>
  >({});
  const [updating, setUpdating] = useState(false);
  const [previewingTemplate, setPreviewingTemplate] =
    useState<EmailTemplate | null>(null);
  const [newTemplate, setNewTemplate] = useState<CreateEmailTemplateData>({
    name: "",
    subject: "",
    body: "",
    category: "general",
    variables: [],
  });
  const [filterCategory, setFilterCategory] = useState<string>("");

  // Enhanced preview state
  const [courses, setCourses] = useState<any[]>([]);
  const [registrations, setRegistrations] = useState<any[]>([]);
  const [selectedCourse, setSelectedCourse] = useState<string>("");
  const [selectedRegistration, setSelectedRegistration] = useState<string>("");
  const [customVariables, setCustomVariables] = useState<
    Record<string, string>
  >({});

  useEffect(() => {
    loadTemplates();
    loadCoursesAndRegistrations();
  }, []);

  const loadTemplates = async () => {
    setLoading(true);
    try {
      const allTemplates = await getAllEmailTemplates();
      setTemplates(allTemplates);
    } catch (error) {
      console.error("Error loading email templates:", error);
      alert("Failed to load email templates");
    } finally {
      setLoading(false);
    }
  };

  const loadCoursesAndRegistrations = async () => {
    try {
      // Load courses
      const allCourses = await getAllCourses();
      setCourses(allCourses);

      // Load registrations
      const stats = await getRegistrationStats();
      console.log(
        "📧 Email Templates - Registration data:",
        stats.recentRegistrations
      );
      setRegistrations(stats.recentRegistrations || []);
    } catch (error) {
      console.error("Error loading courses and registrations:", error);
    }
  };

  const handleCreateTemplate = async () => {
    if (!newTemplate.name || !newTemplate.subject || !newTemplate.body) {
      alert("Please fill in all required fields");
      return;
    }

    try {
      await createEmailTemplate(newTemplate);
      setNewTemplate({
        name: "",
        subject: "",
        body: "",
        category: "general",
        variables: [],
      });
      setShowAddForm(false);
      loadTemplates();
      alert("Email template created successfully!");
    } catch (error) {
      console.error("Error creating email template:", error);
      alert("Failed to create email template");
    }
  };

  const handleStartEditTemplate = (template: EmailTemplate) => {
    setEditingTemplateId(template.id);
    setEditingTemplateData({ ...template });
    setShowAddForm(false);
  };

  const handleSaveEditTemplate = async () => {
    if (!editingTemplateId || !editingTemplateData) return;

    if (
      !editingTemplateData.name?.trim() ||
      !editingTemplateData.subject?.trim() ||
      !editingTemplateData.body?.trim()
    ) {
      alert("Please fill in all required fields (name, subject, body)");
      return;
    }

    setUpdating(true);
    try {
      const { id, createdAt, updatedAt, ...updates } = editingTemplateData;
      await updateEmailTemplate({
        id: editingTemplateId,
        name: updates.name!.trim(),
        subject: updates.subject!.trim(),
        body: updates.body!.trim(),
        category: updates.category!,
        variables: updates.variables || [],
        isActive: updates.isActive!,
      });
      setEditingTemplateId(null);
      setEditingTemplateData({});
      loadTemplates();
      alert("Email template updated successfully!");
    } catch (error) {
      console.error("Error updating email template:", error);
      alert("Failed to update email template");
    } finally {
      setUpdating(false);
    }
  };

  const handleCancelEditTemplate = () => {
    setEditingTemplateId(null);
    setEditingTemplateData({});
  };

  const handleEditTemplateFieldChange = (
    field: keyof EmailTemplate,
    value: any
  ) => {
    setEditingTemplateData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleDeleteTemplate = async (id: string) => {
    if (
      !window.confirm("Are you sure you want to delete this email template?")
    ) {
      return;
    }

    try {
      await deleteEmailTemplate(id);
      loadTemplates();
      alert("Email template deleted successfully!");
    } catch (error) {
      console.error("Error deleting email template:", error);
      alert("Failed to delete email template");
    }
  };

  const handleToggleStatus = async (id: string, isActive: boolean) => {
    try {
      await toggleEmailTemplateStatus(id, !isActive);
      loadTemplates();
    } catch (error) {
      console.error("Error toggling template status:", error);
      alert("Failed to update template status");
    }
  };

  const generatePreviewVariables = (): Record<string, string> => {
    const selectedCourseData = courses.find((c) => c.id === selectedCourse);
    const selectedRegistrationData = registrations.find(
      (r) => r.id === selectedRegistration
    );

    const variables: Record<string, string> = {
      "{{currentDate}}": new Date().toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
      }),
      "{{adminName}}": "Derek MacDonald",
      "{{adminEmail}}": "<EMAIL>",
      "{{companyName}}": "Bit by Bit",
    };

    // Add course data if selected
    if (selectedCourseData) {
      variables["{{courseName}}"] = selectedCourseData.title || "Sample Course";
      variables["{{coursePrice}}"] = selectedCourseData.price
        ? `$${selectedCourseData.price}`
        : "$299";
      variables["{{startDate}}"] =
        selectedCourseData.startDate || "January 15, 2024";
      variables["{{endDate}}"] =
        selectedCourseData.endDate || "February 19, 2024";
    }

    // Add registration data if selected
    if (selectedRegistrationData) {
      variables[
        "{{studentName}}"
      ] = `${selectedRegistrationData.firstName} ${selectedRegistrationData.lastName}`;
      variables["{{firstName}}"] = selectedRegistrationData.firstName || "John";
      variables["{{lastName}}"] = selectedRegistrationData.lastName || "Smith";
      variables["{{parentEmail}}"] =
        selectedRegistrationData.email || "<EMAIL>";
      variables["{{phone}}"] =
        selectedRegistrationData.phone || "(*************";
      variables["{{paymentStatus}}"] =
        selectedRegistrationData.paymentStatus || "Pending";
      variables["{{enrollmentStatus}}"] =
        selectedRegistrationData.enrollmentStatus || "Enrolled";
      variables["{{emergencyContact}}"] =
        selectedRegistrationData.emergencyContactName &&
        selectedRegistrationData.emergencyContactPhone
          ? `${selectedRegistrationData.emergencyContactName} (${selectedRegistrationData.emergencyContactRelationship}) - ${selectedRegistrationData.emergencyContactPhone}`
          : "Jane Smith (Parent) - (*************";

      // Override course data with registration course data if available
      if (selectedRegistrationData.courseTitle) {
        variables["{{courseName}}"] = selectedRegistrationData.courseTitle;
      }
      if (selectedRegistrationData.coursePrice) {
        variables["{{coursePrice}}"] = selectedRegistrationData.coursePrice;
      }
    }

    // Add custom variables
    Object.entries(customVariables).forEach(([key, value]) => {
      if (key && value) {
        variables[key] = value;
      }
    });

    // Add default values for any missing variables
    const defaultValues: Record<string, string> = {
      "{{studentName}}": "John Smith",
      "{{firstName}}": "John",
      "{{lastName}}": "Smith",
      "{{courseName}}": "Introduction to Programming with Scratch",
      "{{coursePrice}}": "$299",
      "{{startDate}}": "January 15, 2024",
      "{{endDate}}": "February 19, 2024",
      "{{paymentStatus}}": "Pending",
      "{{enrollmentStatus}}": "Enrolled",
      "{{waitlistPosition}}": "3",
      "{{parentEmail}}": "<EMAIL>",
      "{{phone}}": "(*************",
      "{{emergencyContact}}": "Jane Smith (Parent) - (*************",
    };

    // Fill in any missing variables with defaults
    Object.entries(defaultValues).forEach(([key, value]) => {
      if (!variables[key]) {
        variables[key] = value;
      }
    });

    return variables;
  };

  const getTemplateVariables = (template: EmailTemplate): string[] => {
    const templateText = `${template.subject} ${template.body}`;
    const variableRegex = /\{\{[^}]+\}\}/g;
    const matches = templateText.match(variableRegex) || [];
    return Array.from(new Set(matches)); // Remove duplicates
  };

  const getCustomizableVariables = (template: EmailTemplate): string[] => {
    const allVariables = getTemplateVariables(template);
    const autoPopulatedVariables = [
      "{{currentDate}}",
      "{{adminName}}",
      "{{adminEmail}}",
      "{{companyName}}",
      "{{studentName}}",
      "{{firstName}}",
      "{{lastName}}",
      "{{parentEmail}}",
      "{{phone}}",
      "{{paymentStatus}}",
      "{{enrollmentStatus}}",
      "{{emergencyContact}}",
      "{{courseName}}",
      "{{coursePrice}}",
      "{{startDate}}",
      "{{endDate}}",
    ];

    // Return variables that aren't auto-populated from course/registration data
    return allVariables.filter(
      (variable) => !autoPopulatedVariables.includes(variable)
    );
  };

  const getEnhancedPreview = (template: EmailTemplate) => {
    const variables = generatePreviewVariables();

    let subject = template.subject;
    let body = template.body;

    Object.entries(variables).forEach(([key, value]) => {
      const regex = new RegExp(key.replace(/[{}]/g, "\\$&"), "g");
      subject = subject.replace(regex, value);
      body = body.replace(regex, value);
    });

    return { subject, body };
  };

  // Filter registrations based on selected course
  const getFilteredRegistrations = () => {
    if (!selectedCourse) {
      return registrations; // Show all registrations if no course selected
    }

    const selectedCourseData = courses.find((c) => c.id === selectedCourse);
    if (!selectedCourseData) {
      return registrations;
    }

    // Filter registrations that match the selected course
    return registrations.filter(
      (registration) =>
        registration.courseId === selectedCourse ||
        registration.courseTitle === selectedCourseData.title
    );
  };

  const filteredTemplates = templates.filter(
    (template) => !filterCategory || template.category === filterCategory
  );

  const categories: EmailTemplateCategory[] = [
    "registration",
    "payment",
    "course",
    "waitlist",
    "general",
    "reminder",
  ];

  if (loading) {
    return (
      <div className="email-templates__loading">
        <Spinner size="large" color="primary" />
        <p>Loading email templates...</p>
      </div>
    );
  }

  return (
    <div className="email-templates">
      <div className="email-templates__header">
        <h2>Email Templates</h2>
        <div className="email-templates__header-actions">
          <select
            value={filterCategory}
            onChange={(e) => setFilterCategory(e.target.value)}
            className="email-templates__filter"
          >
            <option value="">All Categories</option>
            {categories.map((category) => (
              <option key={category} value={category}>
                {category.charAt(0).toUpperCase() + category.slice(1)}
              </option>
            ))}
          </select>
          <Button onClick={() => setShowAddForm(true)}>Add Template</Button>
        </div>
      </div>

      {showAddForm && (
        <div className="email-templates__form">
          <h3>Create New Email Template</h3>
          <div className="email-templates__form-grid">
            <input
              type="text"
              placeholder="Template Name *"
              value={newTemplate.name}
              onChange={(e) =>
                setNewTemplate((prev) => ({ ...prev, name: e.target.value }))
              }
            />
            <select
              value={newTemplate.category}
              onChange={(e) =>
                setNewTemplate((prev) => ({
                  ...prev,
                  category: e.target.value as EmailTemplateCategory,
                }))
              }
            >
              {categories.map((category) => (
                <option key={category} value={category}>
                  {category.charAt(0).toUpperCase() + category.slice(1)}
                </option>
              ))}
            </select>
          </div>
          <input
            type="text"
            placeholder="Email Subject *"
            value={newTemplate.subject}
            onChange={(e) =>
              setNewTemplate((prev) => ({ ...prev, subject: e.target.value }))
            }
            className="email-templates__subject-input"
          />
          <textarea
            placeholder="Email Body *"
            value={newTemplate.body}
            onChange={(e) =>
              setNewTemplate((prev) => ({ ...prev, body: e.target.value }))
            }
            rows={10}
            className="email-templates__body-input"
          />
          <div className="email-templates__variables">
            <h4>Available Variables:</h4>
            <div className="email-templates__variables-grid">
              {EMAIL_TEMPLATE_VARIABLES.map((variable) => (
                <div key={variable.key} className="email-templates__variable">
                  <code>{variable.key}</code>
                  <span>{variable.description}</span>
                </div>
              ))}
            </div>
          </div>
          <div className="email-templates__form-actions">
            <Button onClick={handleCreateTemplate}>Create Template</Button>
            <Button variant="secondary" onClick={() => setShowAddForm(false)}>
              Cancel
            </Button>
          </div>
        </div>
      )}

      {previewingTemplate && (
        <div className="email-templates__preview">
          <h3>Enhanced Email Preview</h3>

          {/* Data Selection Controls */}
          <div className="email-templates__preview-controls">
            <div className="email-templates__preview-control-group">
              <label>Select Course (for course variables):</label>
              <select
                value={selectedCourse}
                onChange={(e) => {
                  setSelectedCourse(e.target.value);
                  // Clear selected registration when course changes
                  setSelectedRegistration("");
                }}
                className="email-templates__preview-select"
              >
                <option value="">Use default course data</option>
                {courses.map((course) => (
                  <option key={course.id} value={course.id}>
                    {course.title} - ${course.price}
                  </option>
                ))}
              </select>
            </div>

            <div className="email-templates__preview-control-group">
              <label>
                Select Registration (for student variables):
                {selectedCourse && (
                  <span className="email-templates__preview-filter-note">
                    {" "}
                    - Filtered by selected course
                  </span>
                )}
              </label>
              <select
                value={selectedRegistration}
                onChange={(e) => setSelectedRegistration(e.target.value)}
                className="email-templates__preview-select"
              >
                <option value="">Use default student data</option>
                {getFilteredRegistrations().map((registration) => (
                  <option key={registration.id} value={registration.id}>
                    {registration.firstName} {registration.lastName} -{" "}
                    {registration.courseTitle} ({registration.paymentStatus})
                  </option>
                ))}
              </select>
              {selectedCourse && getFilteredRegistrations().length === 0 && (
                <div className="email-templates__preview-no-registrations">
                  No registrations found for the selected course.
                </div>
              )}
            </div>
          </div>

          {/* Custom Variables - Only show if template has customizable variables */}
          {getCustomizableVariables(previewingTemplate).length > 0 && (
            <div className="email-templates__preview-custom">
              <h4>Custom Variable Values:</h4>
              <div className="email-templates__preview-custom-grid">
                {getCustomizableVariables(previewingTemplate).map(
                  (variable) => (
                    <div
                      key={variable}
                      className="email-templates__preview-custom-field"
                    >
                      <label className="email-templates__preview-custom-label">
                        {variable}
                      </label>
                      <input
                        type="text"
                        placeholder={`Enter value for ${variable}`}
                        value={customVariables[variable] || ""}
                        onChange={(e) =>
                          setCustomVariables((prev) => ({
                            ...prev,
                            [variable]: e.target.value,
                          }))
                        }
                        className="email-templates__preview-custom-input"
                      />
                    </div>
                  )
                )}
              </div>
            </div>
          )}

          {/* Preview Content */}
          <div className="email-templates__preview-content">
            <div className="email-templates__preview-subject">
              <strong>Subject:</strong>
              <div className="email-templates__preview-text">
                {getEnhancedPreview(previewingTemplate).subject}
              </div>
            </div>
            <div className="email-templates__preview-body">
              <strong>Body:</strong>
              <div className="email-templates__preview-text">
                <pre>{getEnhancedPreview(previewingTemplate).body}</pre>
              </div>
            </div>
          </div>

          {/* Copy to Clipboard */}
          <div className="email-templates__preview-actions">
            <Button
              onClick={() => {
                const preview = getEnhancedPreview(previewingTemplate);
                navigator.clipboard.writeText(
                  `Subject: ${preview.subject}\n\n${preview.body}`
                );
                alert("Email content copied to clipboard!");
              }}
            >
              Copy to Clipboard
            </Button>
            <Button
              variant="secondary"
              onClick={() => {
                setPreviewingTemplate(null);
                setSelectedCourse("");
                setSelectedRegistration("");
                setCustomVariables({});
              }}
            >
              Close Preview
            </Button>
          </div>
        </div>
      )}

      <div className="email-templates__list">
        {filteredTemplates.length === 0 ? (
          <p className="email-templates__empty">
            {templates.length === 0
              ? "No email templates found."
              : "No templates match the selected category."}
          </p>
        ) : (
          <div className="email-templates__grid">
            {filteredTemplates.map((template) => {
              const isEditing = editingTemplateId === template.id;
              const editData = isEditing ? editingTemplateData : template;

              return (
                <div
                  key={template.id}
                  className={`email-templates__card ${
                    isEditing ? "editing" : ""
                  }`}
                >
                  <div className="email-templates__card-header">
                    {isEditing ? (
                      <div className="email-templates__inline-edit-header">
                        <input
                          type="text"
                          value={editData.name || ""}
                          onChange={(e) =>
                            handleEditTemplateFieldChange(
                              "name",
                              e.target.value
                            )
                          }
                          placeholder="Template Name"
                          className="email-templates__inline-input"
                        />
                        <select
                          value={editData.category || "general"}
                          onChange={(e) =>
                            handleEditTemplateFieldChange(
                              "category",
                              e.target.value
                            )
                          }
                          className="email-templates__inline-select"
                        >
                          {categories.map((category) => (
                            <option key={category} value={category}>
                              {category.charAt(0).toUpperCase() +
                                category.slice(1)}
                            </option>
                          ))}
                        </select>
                      </div>
                    ) : (
                      <>
                        <h4>{template.name}</h4>
                        <div className="email-templates__card-badges">
                          <span
                            className={`email-templates__category-badge email-templates__category-badge--${template.category}`}
                          >
                            {template.category}
                          </span>
                          <span
                            className={`email-templates__status-badge email-templates__status-badge--${
                              template.isActive ? "active" : "inactive"
                            }`}
                          >
                            {template.isActive ? "Active" : "Inactive"}
                          </span>
                        </div>
                      </>
                    )}
                  </div>
                  <div className="email-templates__card-content">
                    {isEditing ? (
                      <div className="email-templates__inline-edit-content">
                        <input
                          type="text"
                          value={editData.subject || ""}
                          onChange={(e) =>
                            handleEditTemplateFieldChange(
                              "subject",
                              e.target.value
                            )
                          }
                          placeholder="Email Subject"
                          className="email-templates__inline-input"
                        />
                        <textarea
                          value={editData.body || ""}
                          onChange={(e) =>
                            handleEditTemplateFieldChange(
                              "body",
                              e.target.value
                            )
                          }
                          placeholder="Email Body"
                          rows={6}
                          className="email-templates__inline-textarea"
                        />
                      </div>
                    ) : (
                      <>
                        <p>
                          <strong>Subject:</strong> {template.subject}
                        </p>
                        <p className="email-templates__card-body">
                          {template.body.substring(0, 100)}...
                        </p>
                      </>
                    )}
                  </div>
                  <div className="email-templates__card-actions">
                    {isEditing ? (
                      <>
                        <Button
                          size="small"
                          variant="primary"
                          onClick={handleSaveEditTemplate}
                          loading={updating}
                          disabled={updating}
                        >
                          {updating ? "Saving..." : "Save"}
                        </Button>
                        <Button
                          size="small"
                          variant="secondary"
                          onClick={handleCancelEditTemplate}
                          disabled={updating}
                        >
                          Cancel
                        </Button>
                      </>
                    ) : (
                      <>
                        <Button
                          size="small"
                          onClick={() => setPreviewingTemplate(template)}
                        >
                          Preview
                        </Button>
                        <Button
                          size="small"
                          variant="secondary"
                          onClick={() => handleStartEditTemplate(template)}
                        >
                          Edit
                        </Button>
                        <Button
                          size="small"
                          variant={template.isActive ? "secondary" : "primary"}
                          onClick={() =>
                            handleToggleStatus(template.id, template.isActive)
                          }
                        >
                          {template.isActive ? "Deactivate" : "Activate"}
                        </Button>
                        <Button
                          size="small"
                          variant="secondary"
                          onClick={() => handleDeleteTemplate(template.id)}
                        >
                          Delete
                        </Button>
                      </>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
};

export default EmailTemplates;
