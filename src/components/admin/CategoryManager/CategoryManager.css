.category-manager {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  border: 1px solid #e8f4f8;
}

.category-manager__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e8f4f8;
}

.category-manager__header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.category-manager__header h3 {
  margin: 0;
  color: #0a2d69;
  font-size: 1.5rem;
  font-weight: 600;
}

.category-manager__collapse-btn {
  background: none;
  border: none;
  color: #0a2d69;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.category-manager__collapse-btn:hover {
  background: rgba(10, 45, 105, 0.1);
}

.category-manager__header-actions {
  display: flex;
  gap: 0.5rem;
}

.category-manager__loading {
  text-align: center;
  padding: 2rem;
  color: #666;
  font-size: 1rem;
}

/* Add Form Styles */
.category-manager__add-form,
.category-manager__edit-form {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  border: 1px solid #e8f4f8;
}

.category-manager__edit-form {
  background: #fff3cd;
  border-color: #ffeaa7;
}

.category-manager__add-form h4 {
  margin: 0 0 1rem 0;
  color: #0a2d69;
  font-size: 1.1rem;
  font-weight: 600;
}

.category-manager__form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.category-manager__form-field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.category-manager__form-field--full {
  grid-column: 1 / -1;
}

.category-manager__form-field label {
  font-weight: 600;
  color: #0a2d69;
  font-size: 0.9rem;
}

.category-manager__form-field input,
.category-manager__form-field select {
  padding: 0.75rem;
  border: 1px solid #e8f4f8;
  border-radius: 6px;
  font-size: 0.9rem;
  background: white;
}

.category-manager__form-field input:focus,
.category-manager__form-field select:focus {
  outline: none;
  border-color: #0a2d69;
  box-shadow: 0 0 0 2px rgba(10, 45, 105, 0.1);
}

.category-manager__form-actions {
  display: flex;
  justify-content: flex-end;
}

/* Category Sections */
.category-manager__sections {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.category-manager__section h4 {
  margin: 0 0 1rem 0;
  color: #0a2d69;
  font-size: 1.1rem;
  font-weight: 600;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e8f4f8;
}

.category-manager__category-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.category-manager__category-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e8f4f8;
  transition: all 0.2s ease;
}

.category-manager__category-item:hover {
  background: #e8f4f8;
  border-color: #0a2d69;
}

.category-manager__category-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  flex: 1;
}

.category-manager__category-name {
  font-weight: 600;
  color: #0a2d69;
  font-size: 0.95rem;
}

.category-manager__category-description {
  font-size: 0.85rem;
  color: #666;
  line-height: 1.4;
}

.category-manager__category-badge {
  background: rgba(255, 163, 0, 0.9);
  color: #0a2d69;
  padding: 0.2rem 0.6rem;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  align-self: flex-start;
  margin-top: 0.25rem;
}

.category-manager__category-actions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
  flex-shrink: 0;
}

.category-manager__edit-btn {
  background: #28a745;
  color: white;
  border: none;
  border-radius: 50%;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.category-manager__edit-btn:hover {
  background: #218838;
  transform: scale(1.1);
}

.category-manager__delete-btn {
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 50%;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.category-manager__delete-btn:hover {
  background: #c82333;
  transform: scale(1.1);
}

/* Inline Editing */
.category-manager__category-item.editing {
  background-color: #f8fafc;
  border: 2px solid #0ea5e9;
  border-radius: 8px;
  padding: 1rem;
}

.category-manager__inline-edit {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  width: 100%;
}

.category-manager__inline-input {
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 0.875rem;
  background: white;
}

.category-manager__inline-input:focus {
  outline: none;
  border-color: #0ea5e9;
  box-shadow: 0 0 0 2px rgba(14, 165, 233, 0.1);
}

.category-manager__inline-select {
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 0.875rem;
  background: white;
}

.category-manager__inline-select:focus {
  outline: none;
  border-color: #0ea5e9;
  box-shadow: 0 0 0 2px rgba(14, 165, 233, 0.1);
}

.category-manager__save-btn {
  background: #10b981;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem;
  cursor: pointer;
  font-size: 1rem;
  min-width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.category-manager__save-btn:hover:not(:disabled) {
  background: #059669;
}

.category-manager__save-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.category-manager__cancel-btn {
  background: #6b7280;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem;
  cursor: pointer;
  font-size: 1rem;
  min-width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.category-manager__cancel-btn:hover:not(:disabled) {
  background: #4b5563;
}

.category-manager__cancel-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.category-manager__protected-label {
  color: #6c757d;
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  cursor: help;
}

/* Responsive Design */
@media (max-width: 768px) {
  .category-manager {
    padding: 1rem;
  }

  .category-manager__header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .category-manager__header-left {
    width: 100%;
    justify-content: space-between;
  }

  .category-manager__header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .category-manager__sections {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .category-manager__form-grid {
    grid-template-columns: 1fr;
  }

  .category-manager__category-item {
    padding: 0.75rem;
  }

  .category-manager__category-actions {
    gap: 0.25rem;
  }

  .category-manager__edit-btn,
  .category-manager__delete-btn {
    width: 24px;
    height: 24px;
    font-size: 12px;
  }
}
