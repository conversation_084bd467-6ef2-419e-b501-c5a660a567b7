import React, { useState, useEffect } from "react";
import {
  getAllCategories,
  createCategory,
  updateCategory,
  deleteCategory,
  Category,
} from "../../../services/categoryService";
import Button from "../../ui/Button";
import "./CategoryManager.css";

interface CategoryManagerProps {
  onCategoriesChange?: () => void;
}

const CategoryManager: React.FC<CategoryManagerProps> = ({
  onCategoriesChange,
}) => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingCategoryId, setEditingCategoryId] = useState<string | null>(
    null
  );
  const [editingCategoryData, setEditingCategoryData] = useState<
    Partial<Category>
  >({});
  const [updating, setUpdating] = useState(false);
  const [newCategory, setNewCategory] = useState({
    name: "",
    type: "subject" as "subject" | "difficulty",
    description: "",
  });

  useEffect(() => {
    loadCategories();
  }, []);

  const loadCategories = async () => {
    try {
      setLoading(true);
      const fetchedCategories = await getAllCategories();
      setCategories(fetchedCategories);
    } catch (error) {
      console.error("Error loading categories:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateCategory = async () => {
    if (!newCategory.name.trim()) {
      alert("Category name is required");
      return;
    }

    try {
      await createCategory({
        ...newCategory,
        name: newCategory.name.trim(),
      });

      setNewCategory({ name: "", type: "subject", description: "" });
      setShowAddForm(false);
      await loadCategories();
      onCategoriesChange?.();

      alert("Category created successfully!");
    } catch (error) {
      console.error("Error creating category:", error);
      alert(
        error instanceof Error ? error.message : "Failed to create category"
      );
    }
  };

  const handleStartEditCategory = (category: Category) => {
    setEditingCategoryId(category.id);
    setEditingCategoryData({ ...category });
    setShowAddForm(false);
  };

  const handleSaveEditCategory = async () => {
    if (!editingCategoryId || !editingCategoryData) return;

    if (!editingCategoryData.name?.trim()) {
      alert("Category name is required");
      return;
    }

    setUpdating(true);
    try {
      const { id, createdAt, updatedAt, ...updates } = editingCategoryData;
      await updateCategory(editingCategoryId, {
        name: updates.name!.trim(),
        type: updates.type!,
        description: updates.description || "",
      });

      setEditingCategoryId(null);
      setEditingCategoryData({});
      await loadCategories();
      onCategoriesChange?.();

      alert("Category updated successfully!");
    } catch (error) {
      console.error("Error updating category:", error);
      alert(
        error instanceof Error ? error.message : "Failed to update category"
      );
    } finally {
      setUpdating(false);
    }
  };

  const handleCancelEditCategory = () => {
    setEditingCategoryId(null);
    setEditingCategoryData({});
  };

  const handleEditCategoryFieldChange = (field: keyof Category, value: any) => {
    setEditingCategoryData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleDeleteCategory = async (
    categoryId: string,
    categoryName: string
  ) => {
    const confirmed = window.confirm(
      `Are you sure you want to delete the category "${categoryName}"? This action cannot be undone.`
    );

    if (!confirmed) return;

    try {
      await deleteCategory(categoryId);
      await loadCategories();
      onCategoriesChange?.();
      alert("Category deleted successfully!");
    } catch (error) {
      console.error("Error deleting category:", error);
      alert(
        error instanceof Error ? error.message : "Failed to delete category"
      );
    }
  };

  const subjectCategories = categories.filter((cat) => cat.type === "subject");
  const difficultyCategories = categories.filter(
    (cat) => cat.type === "difficulty"
  );

  if (loading) {
    return (
      <div className="category-manager">
        <div className="category-manager__loading">Loading categories...</div>
      </div>
    );
  }

  return (
    <div className="category-manager">
      <div className="category-manager__header">
        <div className="category-manager__header-left">
          <h3>Category Management</h3>
          <button
            className="category-manager__collapse-btn"
            onClick={() => setIsCollapsed(!isCollapsed)}
            title={isCollapsed ? "Expand" : "Collapse"}
          >
            {isCollapsed ? "▶" : "▼"}
          </button>
        </div>
        <div className="category-manager__header-actions">
          <Button
            onClick={() => {
              setShowAddForm(!showAddForm);
              setEditingCategoryId(null);
              setEditingCategoryData({});
            }}
            size="small"
            variant={showAddForm ? "secondary" : "primary"}
          >
            {showAddForm ? "Cancel" : "Add Category"}
          </Button>
        </div>
      </div>

      {!isCollapsed && (
        <>
          {showAddForm && (
            <div className="category-manager__add-form">
              <h4>Add New Category</h4>
              <div className="category-manager__form-grid">
                <div className="category-manager__form-field">
                  <label>Category Name</label>
                  <input
                    type="text"
                    placeholder="e.g., Blockchain Development"
                    value={newCategory.name}
                    onChange={(e) =>
                      setNewCategory((prev) => ({
                        ...prev,
                        name: e.target.value,
                      }))
                    }
                  />
                </div>

                <div className="category-manager__form-field">
                  <label>Category Type</label>
                  <select
                    value={newCategory.type}
                    onChange={(e) =>
                      setNewCategory((prev) => ({
                        ...prev,
                        type: e.target.value as "subject" | "difficulty",
                      }))
                    }
                  >
                    <option value="subject">Subject Area</option>
                    <option value="difficulty">Difficulty Level</option>
                  </select>
                </div>

                <div className="category-manager__form-field category-manager__form-field--full">
                  <label>Description (Optional)</label>
                  <input
                    type="text"
                    placeholder="Brief description of this category"
                    value={newCategory.description}
                    onChange={(e) =>
                      setNewCategory((prev) => ({
                        ...prev,
                        description: e.target.value,
                      }))
                    }
                  />
                </div>
              </div>

              <div className="category-manager__form-actions">
                <Button onClick={handleCreateCategory} size="small">
                  Create Category
                </Button>
              </div>
            </div>
          )}

          <div className="category-manager__sections">
            <div className="category-manager__section">
              <h4>Subject Areas ({subjectCategories.length})</h4>
              <div className="category-manager__category-list">
                {subjectCategories.map((category) => {
                  const isEditing = editingCategoryId === category.id;
                  const editData = isEditing ? editingCategoryData : category;

                  return (
                    <div
                      key={category.id}
                      className={`category-manager__category-item ${
                        isEditing ? "editing" : ""
                      }`}
                    >
                      <div className="category-manager__category-info">
                        {isEditing ? (
                          <div className="category-manager__inline-edit">
                            <input
                              type="text"
                              value={editData.name || ""}
                              onChange={(e) =>
                                handleEditCategoryFieldChange(
                                  "name",
                                  e.target.value
                                )
                              }
                              placeholder="Category name"
                              className="category-manager__inline-input"
                            />
                            <select
                              value={editData.type || "subject"}
                              onChange={(e) =>
                                handleEditCategoryFieldChange(
                                  "type",
                                  e.target.value as "subject" | "difficulty"
                                )
                              }
                              className="category-manager__inline-select"
                            >
                              <option value="subject">Subject Area</option>
                              <option value="difficulty">
                                Difficulty Level
                              </option>
                            </select>
                            <input
                              type="text"
                              value={editData.description || ""}
                              onChange={(e) =>
                                handleEditCategoryFieldChange(
                                  "description",
                                  e.target.value
                                )
                              }
                              placeholder="Description (optional)"
                              className="category-manager__inline-input"
                            />
                          </div>
                        ) : (
                          <>
                            <span className="category-manager__category-name">
                              {category.name}
                            </span>
                            {category.description && (
                              <span className="category-manager__category-description">
                                {category.description}
                              </span>
                            )}
                          </>
                        )}
                      </div>
                      <div className="category-manager__category-actions">
                        {isEditing ? (
                          <>
                            <button
                              className="category-manager__save-btn"
                              onClick={handleSaveEditCategory}
                              disabled={updating}
                              title="Save changes"
                            >
                              {updating ? "..." : "✓"}
                            </button>
                            <button
                              className="category-manager__cancel-btn"
                              onClick={handleCancelEditCategory}
                              disabled={updating}
                              title="Cancel editing"
                            >
                              ×
                            </button>
                          </>
                        ) : (
                          <>
                            <button
                              className="category-manager__edit-btn"
                              onClick={() => handleStartEditCategory(category)}
                              title="Edit category"
                            >
                              ✏️
                            </button>
                            <button
                              className="category-manager__delete-btn"
                              onClick={() =>
                                handleDeleteCategory(category.id, category.name)
                              }
                              title="Delete category"
                            >
                              ×
                            </button>
                          </>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>

            <div className="category-manager__section">
              <h4>Difficulty Levels ({difficultyCategories.length})</h4>
              <div className="category-manager__category-list">
                {difficultyCategories.map((category) => {
                  const isEditing = editingCategoryId === category.id;
                  const editData = isEditing ? editingCategoryData : category;

                  return (
                    <div
                      key={category.id}
                      className={`category-manager__category-item ${
                        isEditing ? "editing" : ""
                      }`}
                    >
                      <div className="category-manager__category-info">
                        {isEditing ? (
                          <div className="category-manager__inline-edit">
                            <input
                              type="text"
                              value={editData.name || ""}
                              onChange={(e) =>
                                handleEditCategoryFieldChange(
                                  "name",
                                  e.target.value
                                )
                              }
                              placeholder="Category name"
                              className="category-manager__inline-input"
                            />
                            <select
                              value={editData.type || "difficulty"}
                              onChange={(e) =>
                                handleEditCategoryFieldChange(
                                  "type",
                                  e.target.value as "subject" | "difficulty"
                                )
                              }
                              className="category-manager__inline-select"
                            >
                              <option value="subject">Subject Area</option>
                              <option value="difficulty">
                                Difficulty Level
                              </option>
                            </select>
                            <input
                              type="text"
                              value={editData.description || ""}
                              onChange={(e) =>
                                handleEditCategoryFieldChange(
                                  "description",
                                  e.target.value
                                )
                              }
                              placeholder="Description (optional)"
                              className="category-manager__inline-input"
                            />
                          </div>
                        ) : (
                          <>
                            <span className="category-manager__category-name">
                              {category.name}
                            </span>
                            {category.description && (
                              <span className="category-manager__category-description">
                                {category.description}
                              </span>
                            )}
                          </>
                        )}
                      </div>
                      <div className="category-manager__category-actions">
                        {isEditing ? (
                          <>
                            <button
                              className="category-manager__save-btn"
                              onClick={handleSaveEditCategory}
                              disabled={updating}
                              title="Save changes"
                            >
                              {updating ? "..." : "✓"}
                            </button>
                            <button
                              className="category-manager__cancel-btn"
                              onClick={handleCancelEditCategory}
                              disabled={updating}
                              title="Cancel editing"
                            >
                              ×
                            </button>
                          </>
                        ) : (
                          <>
                            <button
                              className="category-manager__edit-btn"
                              onClick={() => handleStartEditCategory(category)}
                              title="Edit category"
                            >
                              ✏️
                            </button>
                            <button
                              className="category-manager__delete-btn"
                              onClick={() =>
                                handleDeleteCategory(category.id, category.name)
                              }
                              title="Delete category"
                            >
                              ×
                            </button>
                          </>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default CategoryManager;
