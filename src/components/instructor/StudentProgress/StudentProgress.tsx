import React, { useState, useEffect } from "react";
import But<PERSON> from "../../ui/Button";
import Spinner from "../../ui/Spinner";
import {
  getStudentAttendance,
  getStudentProgress,
  updateStudentProgress,
  getCourseAttendanceSummary,
  StudentProgress as StudentProgressType,
} from "../../../services/attendanceService";
import { getInstructorSession } from "../../../services/instructorService";
import "./StudentProgress.css";

interface StudentProgressProps {
  courses: any[];
  selectedCourse: any;
  onCourseSelect: (course: any) => void;
  onRefresh: () => void;
}

const StudentProgress: React.FC<StudentProgressProps> = ({
  courses,
  selectedCourse,
  onCourseSelect,
  onRefresh,
}) => {
  const [selectedStudent, setSelectedStudent] = useState<any>(null);
  const [studentProgress, setStudentProgress] =
    useState<StudentProgressType | null>(null);
  const [attendanceRecords, setAttendanceRecords] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [attendanceSummary, setAttendanceSummary] = useState<any>(null);

  const instructorUser = getInstructorSession();

  // Load course attendance summary when course changes
  useEffect(() => {
    if (selectedCourse) {
      loadAttendanceSummary();
    }
  }, [selectedCourse]);

  // Load student data when student changes
  useEffect(() => {
    if (selectedStudent && selectedCourse) {
      loadStudentData();
    }
  }, [selectedStudent, selectedCourse]);

  const loadAttendanceSummary = async () => {
    if (!selectedCourse) return;

    try {
      const summary = await getCourseAttendanceSummary(selectedCourse.courseId);
      setAttendanceSummary(summary);
    } catch (error) {
      console.error("Error loading attendance summary:", error);
    }
  };

  const loadStudentData = async () => {
    if (!selectedStudent || !selectedCourse) return;

    setLoading(true);
    try {
      // Debug: Check instructor authentication
      console.log("🔍 DEBUG: Instructor user for progress:", instructorUser);
      console.log("🔍 DEBUG: Instructor ID from session:", instructorUser?.id);

      // Check Firebase Auth UID
      const { auth } = await import("../../../config/firebase");
      const firebaseUser = auth.currentUser;
      console.log("🔍 DEBUG: Firebase Auth UID:", firebaseUser?.uid);
      console.log("🔍 DEBUG: Firebase Auth Email:", firebaseUser?.email);

      console.log("🔍 DEBUG: Selected course for progress:", selectedCourse);
      console.log("🔍 DEBUG: Selected student for progress:", selectedStudent);

      // Load student attendance records
      const attendance = await getStudentAttendance(
        selectedCourse.courseId,
        selectedStudent.id
      );
      setAttendanceRecords(attendance);

      // Load or create student progress
      let progress = await getStudentProgress(
        selectedCourse.courseId,
        selectedStudent.id
      );

      if (!progress) {
        // Create initial progress record
        const attendedSessions = attendance.filter(
          (a) => a.status === "present" || a.status === "late"
        ).length;
        const totalSessions = attendance.length;

        progress = {
          studentId: selectedStudent.id,
          studentName: `${selectedStudent.firstName} ${selectedStudent.lastName}`,
          studentEmail: selectedStudent.email,
          courseId: selectedCourse.courseId,
          courseTitle: selectedCourse.title,
          totalSessions,
          attendedSessions,
          attendanceRate:
            totalSessions > 0 ? (attendedSessions / totalSessions) * 100 : 0,
          progressNotes: "",
          skillsAssessment: {
            technical: 3,
            collaboration: 3,
            problemSolving: 3,
            creativity: 3,
          },
          lastUpdated: new Date().toISOString(),
        };
      }

      setStudentProgress(progress);
    } catch (error) {
      console.error("Error loading student data:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleProgressUpdate = (field: string, value: any) => {
    if (!studentProgress) return;

    setStudentProgress((prev) => ({
      ...prev!,
      [field]: value,
    }));
  };

  const handleSkillsUpdate = (skill: string, value: number) => {
    if (!studentProgress) return;

    setStudentProgress((prev) => ({
      ...prev!,
      skillsAssessment: {
        ...prev!.skillsAssessment,
        [skill]: value,
      },
    }));
  };

  const saveProgress = async () => {
    if (!studentProgress) return;

    setSaving(true);
    try {
      await updateStudentProgress(studentProgress);
      onRefresh();
      alert("Student progress saved successfully!");
    } catch (error) {
      console.error("Error saving progress:", error);
      alert("Failed to save progress. Please try again.");
    } finally {
      setSaving(false);
    }
  };

  if (!selectedCourse) {
    return (
      <div className="student-progress">
        <h2>Student Progress</h2>
        <div className="student-progress__course-selection">
          <p>Select a course to view student progress:</p>
          <div className="student-progress__courses-grid">
            {courses.map((course) => (
              <div
                key={course.courseId}
                className="student-progress__course-card"
                onClick={() => onCourseSelect(course)}
              >
                <h3>{course.title}</h3>
                <p>{course.enrolled} students enrolled</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (!selectedStudent) {
    return (
      <div className="student-progress">
        <div className="student-progress__header">
          <div className="student-progress__course-info">
            <h2>{selectedCourse.title} - Student Progress</h2>
            <Button variant="secondary" onClick={() => onCourseSelect(null)}>
              ← Back to Courses
            </Button>
          </div>
        </div>

        <div className="student-progress__student-selection">
          <h3>Select a Student</h3>
          <div className="student-progress__students-grid">
            {selectedCourse.registrations
              ?.filter((reg: any) => reg.enrollmentStatus === "enrolled")
              .map((student: any) => {
                const studentSummary = attendanceSummary?.students.find(
                  (s: any) => s.studentId === student.id
                );

                return (
                  <div
                    key={student.id}
                    className="student-progress__student-card"
                    onClick={() => setSelectedStudent(student)}
                  >
                    <h4>
                      {student.firstName} {student.lastName}
                    </h4>
                    <p>{student.email}</p>
                    {studentSummary && (
                      <div className="student-progress__student-stats">
                        <span>
                          Attendance:{" "}
                          {Math.round(studentSummary.attendanceRate)}%
                        </span>
                      </div>
                    )}
                  </div>
                );
              })}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="student-progress">
      <div className="student-progress__header">
        <div className="student-progress__breadcrumb">
          <Button variant="secondary" onClick={() => setSelectedStudent(null)}>
            ← Back to Students
          </Button>
          <h2>
            {selectedStudent.firstName} {selectedStudent.lastName}
          </h2>
        </div>
      </div>

      {loading ? (
        <div className="student-progress__loading">
          <Spinner size="large" />
          <p>Loading student progress...</p>
        </div>
      ) : studentProgress ? (
        <div className="student-progress__content">
          {/* Attendance Overview */}
          <div className="student-progress__section">
            <h3>Attendance Overview</h3>
            <div className="student-progress__attendance-stats">
              <div className="student-progress__stat">
                <span className="student-progress__stat-number">
                  {studentProgress.attendedSessions}
                </span>
                <span className="student-progress__stat-label">
                  Sessions Attended
                </span>
              </div>
              <div className="student-progress__stat">
                <span className="student-progress__stat-number">
                  {studentProgress.totalSessions}
                </span>
                <span className="student-progress__stat-label">
                  Total Sessions
                </span>
              </div>
              <div className="student-progress__stat">
                <span className="student-progress__stat-number">
                  {Math.round(studentProgress.attendanceRate)}%
                </span>
                <span className="student-progress__stat-label">
                  Attendance Rate
                </span>
              </div>
            </div>
          </div>

          {/* Skills Assessment */}
          <div className="student-progress__section">
            <h3>Skills Assessment</h3>
            <div className="student-progress__skills-grid">
              {Object.entries(studentProgress.skillsAssessment).map(
                ([skill, value]) => (
                  <div key={skill} className="student-progress__skill">
                    <label className="student-progress__skill-label">
                      {skill.charAt(0).toUpperCase() + skill.slice(1)}
                    </label>
                    <div className="student-progress__skill-rating">
                      {[1, 2, 3, 4, 5].map((rating) => (
                        <button
                          key={rating}
                          type="button"
                          className={`student-progress__rating-btn ${
                            rating <= value ? "active" : ""
                          }`}
                          onClick={() => handleSkillsUpdate(skill, rating)}
                        >
                          ★
                        </button>
                      ))}
                    </div>
                    <span className="student-progress__skill-value">
                      {value}/5
                    </span>
                  </div>
                )
              )}
            </div>
          </div>

          {/* Progress Notes */}
          <div className="student-progress__section">
            <h3>Progress Notes</h3>
            <textarea
              className="student-progress__notes"
              placeholder="Add notes about the student's progress, strengths, areas for improvement..."
              value={studentProgress.progressNotes}
              onChange={(e) =>
                handleProgressUpdate("progressNotes", e.target.value)
              }
              rows={6}
            />
          </div>

          {/* Actions */}
          <div className="student-progress__actions">
            <Button onClick={saveProgress} loading={saving} disabled={saving}>
              {saving ? "Saving..." : "Save Progress"}
            </Button>
          </div>
        </div>
      ) : (
        <div className="student-progress__empty">
          <p>No progress data available for this student.</p>
        </div>
      )}
    </div>
  );
};

export default StudentProgress;
