.student-progress {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.student-progress h2 {
  margin: 0 0 2rem 0;
  color: #0a2d69;
  font-size: 1.5rem;
  font-weight: 600;
}

/* Course Selection */
.student-progress__course-selection {
  text-align: center;
  padding: 2rem 0;
}

.student-progress__course-selection p {
  margin: 0 0 2rem 0;
  color: #6b7280;
  font-size: 1.1rem;
}

.student-progress__courses-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
  max-width: 800px;
  margin: 0 auto;
}

.student-progress__course-card {
  background: #f8f9fa;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.student-progress__course-card:hover {
  border-color: #0a2d69;
  box-shadow: 0 8px 24px rgba(10, 45, 105, 0.1);
  transform: translateY(-2px);
}

.student-progress__course-card h3 {
  margin: 0 0 0.5rem 0;
  color: #0a2d69;
  font-size: 1.1rem;
  font-weight: 600;
}

.student-progress__course-card p {
  margin: 0;
  color: #6b7280;
  font-size: 0.9rem;
}

/* Header */
.student-progress__header {
  margin-bottom: 2rem;
}

.student-progress__course-info h2,
.student-progress__breadcrumb h2 {
  margin: 1rem 0 0 0;
}

.student-progress__breadcrumb {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* Student Selection */
.student-progress__student-selection h3 {
  margin: 0 0 1.5rem 0;
  color: #0a2d69;
  font-size: 1.25rem;
  font-weight: 600;
}

.student-progress__students-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
}

.student-progress__student-card {
  background: #f8f9fa;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.student-progress__student-card:hover {
  border-color: #0a2d69;
  box-shadow: 0 8px 24px rgba(10, 45, 105, 0.1);
  transform: translateY(-2px);
}

.student-progress__student-card h4 {
  margin: 0 0 0.5rem 0;
  color: #0a2d69;
  font-size: 1.1rem;
  font-weight: 600;
}

.student-progress__student-card p {
  margin: 0 0 1rem 0;
  color: #6b7280;
  font-size: 0.875rem;
}

.student-progress__student-stats {
  font-size: 0.875rem;
  color: #10b981;
  font-weight: 500;
}

/* Loading */
.student-progress__loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  gap: 1rem;
}

.student-progress__loading p {
  color: #6b7280;
  font-size: 1.1rem;
}

/* Content */
.student-progress__content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.student-progress__section {
  padding: 1.5rem;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: #f8f9fa;
}

.student-progress__section h3 {
  margin: 0 0 1.5rem 0;
  color: #0a2d69;
  font-size: 1.1rem;
  font-weight: 600;
}

/* Attendance Stats */
.student-progress__attendance-stats {
  display: flex;
  gap: 2rem;
  justify-content: center;
}

.student-progress__stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.student-progress__stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: #0a2d69;
  line-height: 1;
}

.student-progress__stat-label {
  font-size: 0.75rem;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-top: 0.5rem;
}

/* Skills Assessment */
.student-progress__skills-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.student-progress__skill {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  padding: 1rem;
  background: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.student-progress__skill-label {
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
}

.student-progress__skill-rating {
  display: flex;
  gap: 0.25rem;
}

.student-progress__rating-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #d1d5db;
  cursor: pointer;
  transition: color 0.2s ease;
  padding: 0;
  line-height: 1;
}

.student-progress__rating-btn:hover {
  color: #fbbf24;
}

.student-progress__rating-btn.active {
  color: #f59e0b;
}

.student-progress__skill-value {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
}

/* Progress Notes */
.student-progress__notes {
  width: 100%;
  padding: 1rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 0.875rem;
  font-family: inherit;
  resize: vertical;
  min-height: 120px;
}

.student-progress__notes:focus {
  outline: none;
  border-color: #0a2d69;
  box-shadow: 0 0 0 3px rgba(10, 45, 105, 0.1);
}

/* Actions */
.student-progress__actions {
  display: flex;
  justify-content: center;
  padding: 1rem 0;
  border-top: 1px solid #e5e7eb;
}

/* Empty State */
.student-progress__empty {
  text-align: center;
  padding: 3rem;
  color: #6b7280;
}

/* Responsive design */
@media (max-width: 768px) {
  .student-progress__attendance-stats {
    flex-direction: column;
    gap: 1rem;
  }
  
  .student-progress__skills-grid {
    grid-template-columns: 1fr;
  }
  
  .student-progress__students-grid {
    grid-template-columns: 1fr;
  }
  
  .student-progress__courses-grid {
    grid-template-columns: 1fr;
  }
}
