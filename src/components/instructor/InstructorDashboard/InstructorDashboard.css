.instructor-dashboard {
  min-height: 100vh;
  background: #f8f9fa;
}

.instructor-dashboard__loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  gap: 1rem;
}

.instructor-dashboard__loading p {
  color: #6b7280;
  font-size: 1.1rem;
}

/* Header */
.instructor-dashboard__header {
  background: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 1.5rem 2rem;
}

.instructor-dashboard__header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
}

.instructor-dashboard__brand h1 {
  margin: 0 0 0.25rem 0;
  color: #0a2d69;
  font-size: 1.75rem;
  font-weight: 700;
}

.instructor-dashboard__brand p {
  margin: 0;
  color: #6b7280;
  font-size: 1rem;
}

/* Navigation */
.instructor-dashboard__nav {
  background: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 0 2rem;
  display: flex;
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.instructor-dashboard__nav-item {
  background: none;
  border: none;
  padding: 1rem 0;
  color: #6b7280;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
}

.instructor-dashboard__nav-item:hover {
  color: #0a2d69;
}

.instructor-dashboard__nav-item.active {
  color: #0a2d69;
  border-bottom-color: #0a2d69;
}

/* Content */
.instructor-dashboard__content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.instructor-dashboard__content h2 {
  margin: 0 0 2rem 0;
  color: #0a2d69;
  font-size: 1.5rem;
  font-weight: 600;
}

/* Overview */
.instructor-dashboard__overview {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.instructor-dashboard__empty {
  text-align: center;
  padding: 3rem;
  color: #6b7280;
}

.instructor-dashboard__courses-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 1.5rem;
}

.instructor-dashboard__course-card {
  background: #f8f9fa;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.instructor-dashboard__course-card:hover {
  border-color: #0a2d69;
  box-shadow: 0 8px 24px rgba(10, 45, 105, 0.1);
  transform: translateY(-2px);
}

.instructor-dashboard__course-card::after {
  content: "Click to manage sessions →";
  position: absolute;
  bottom: 0.5rem;
  right: 1rem;
  font-size: 0.75rem;
  color: #6b7280;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.instructor-dashboard__course-card:hover::after {
  opacity: 1;
}

.instructor-dashboard__course-card h3 {
  margin: 0 0 1rem 0;
  color: #0a2d69;
  font-size: 1.1rem;
  font-weight: 600;
  line-height: 1.4;
}

.instructor-dashboard__course-stats {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 1rem;
}

.instructor-dashboard__stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.instructor-dashboard__stat-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: #0a2d69;
  line-height: 1;
}

.instructor-dashboard__stat-label {
  font-size: 0.75rem;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-top: 0.25rem;
}

.instructor-dashboard__course-schedule {
  padding-top: 1rem;
  padding-bottom: 1.5rem;
  border-top: 1px solid #e5e7eb;
}

.instructor-dashboard__course-schedule p {
  margin: 0.25rem 0;
  color: #6b7280;
  font-size: 0.875rem;
}

/* Responsive design */
@media (max-width: 768px) {
  .instructor-dashboard__header {
    padding: 1rem;
  }

  .instructor-dashboard__header-content {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .instructor-dashboard__nav {
    padding: 0 1rem;
    flex-wrap: wrap;
    gap: 1rem;
  }

  .instructor-dashboard__content {
    padding: 1rem;
  }

  .instructor-dashboard__courses-grid {
    grid-template-columns: 1fr;
  }

  .instructor-dashboard__course-stats {
    justify-content: space-around;
  }
}
