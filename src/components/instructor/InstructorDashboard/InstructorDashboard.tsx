import React, { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import Button from "../../ui/Button";
import Spinner from "../../ui/Spinner";
import StudentProgress from "../StudentProgress/StudentProgress";
import InstructorSessionCalendar from "../InstructorSessionCalendar/InstructorSessionCalendar";
import {
  instructorLogout,
  getInstructorSession,
  getInstructorCourses,
  getInstructorCourseCapacities,
  getInstructorCourseRegistrations,
  onInstructorAuthStateChanged,
} from "../../../services/instructorService";
import { getCourseAttendanceSummary } from "../../../services/attendanceService";
import { getInstructorSessions } from "../../../services/sessionService";
import "./InstructorDashboard.css";

interface InstructorDashboardProps {
  onLogout: () => void;
}

const InstructorDashboard: React.FC<InstructorDashboardProps> = ({
  onLogout,
}) => {
  const navigate = useNavigate();
  const { tab, courseId } = useParams<{ tab?: string; courseId?: string }>();

  const [courses, setCourses] = useState<any[]>([]);
  const [selectedCourse, setSelectedCourse] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [attendanceSummaries, setAttendanceSummaries] = useState<{
    [courseId: string]: any;
  }>({});
  const [sessionCounts, setSessionCounts] = useState<{
    [courseId: string]: number;
  }>({});

  // Get active tab from URL, default to overview
  const activeTab =
    (tab as "overview" | "sessions" | "attendance" | "progress") || "overview";

  const instructorUser = getInstructorSession();

  // Helper function to navigate to different tabs
  const navigateToTab = (tabName: string, courseIdParam?: string) => {
    if (courseIdParam) {
      navigate(`/instructor/${tabName}/${courseIdParam}`);
    } else {
      navigate(`/instructor/${tabName}`);
    }
  };

  // Load instructor data
  const loadData = async () => {
    try {
      setLoading(true);

      // Get assigned courses only
      const assignedCourses = await getInstructorCourses(
        instructorUser?.id || ""
      );

      // Get course capacities for assigned courses only
      const capacities = await getInstructorCourseCapacities();

      // Merge course data with secure registration data
      const coursesWithEnrollment = [];

      for (const course of assignedCourses) {
        const capacity = capacities.find(
          (c) => c.courseId === course.slug || c.courseId === course.id
        );

        // Get registrations securely for this course
        const registrations = await getInstructorCourseRegistrations(
          course.slug || course.id
        );
        const enrolledCount = registrations.filter(
          (r) =>
            r.enrollmentStatus === "enrolled" && r.paymentStatus !== "cancelled"
        ).length;

        coursesWithEnrollment.push({
          ...course,
          courseId: course.slug || course.id,
          enrolled: enrolledCount,
          capacity: capacity?.maxStudents || course.maxStudents || 8,
          registrations: registrations,
        });
      } // Show all assigned courses, even with 0 students

      console.log("📊 Final courses with enrollment:", coursesWithEnrollment);
      setCourses(coursesWithEnrollment);

      // Load attendance summaries and session counts for all courses
      const summaries: { [courseId: string]: any } = {};
      const counts: { [courseId: string]: number } = {};

      for (const course of coursesWithEnrollment) {
        // Get attendance summary
        const summary = await getCourseAttendanceSummary(course.courseId);
        summaries[course.courseId] = summary;

        // Get actual session count
        const sessions = await getInstructorSessions(
          instructorUser?.id || "",
          course.courseId
        );
        counts[course.courseId] = sessions.length;
        console.log(`📊 Course ${course.title}: ${sessions.length} sessions`);
      }

      setAttendanceSummaries(summaries);
      setSessionCounts(counts);

      // Set selected course if courseId is in URL
      if (courseId) {
        const course = coursesWithEnrollment.find(
          (c) => c.courseId === courseId
        );
        setSelectedCourse(course || null);
      }
    } catch (error) {
      console.error("Error loading instructor data:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, []);

  // Redirect to overview if no tab is specified, or redirect attendance to sessions
  useEffect(() => {
    if (!tab) {
      navigate("/instructor/overview", { replace: true });
    } else if (tab === "attendance") {
      navigate("/instructor/sessions", { replace: true });
    }
  }, [tab, navigate]);

  // Listen for auth state changes and logout if user becomes unauthenticated
  useEffect(() => {
    const unsubscribe = onInstructorAuthStateChanged((user) => {
      if (!user) {
        onLogout();
      }
    });

    return unsubscribe;
  }, [onLogout]);

  const handleLogout = async () => {
    try {
      await instructorLogout();
      onLogout();
    } catch (error) {
      console.error("Logout error:", error);
    }
  };

  const handleCourseSelect = (course: any) => {
    if (!course) {
      // Back to courses - clear selection but stay in current tab
      setSelectedCourse(null);
      navigateToTab(activeTab); // Stay in current tab, just remove courseId
      return;
    }

    if (!course.courseId) {
      console.error("Invalid course selected:", course);
      return;
    }
    setSelectedCourse(course);

    // If we're in the overview tab, navigate to sessions tab with the selected course
    // This provides a more meaningful interaction for course selection
    if (activeTab === "overview") {
      navigateToTab("sessions", course.courseId);
    } else {
      navigateToTab(activeTab, course.courseId); // Use current tab, add courseId
    }
  };

  if (loading) {
    return (
      <div className="instructor-dashboard__loading">
        <Spinner size="large" />
        <p>Loading instructor dashboard...</p>
      </div>
    );
  }

  return (
    <div className="instructor-dashboard">
      <header className="instructor-dashboard__header">
        <div className="instructor-dashboard__header-content">
          <div className="instructor-dashboard__brand">
            <h1>Instructor Portal</h1>
            <p>Welcome back, {instructorUser?.name}</p>
          </div>
          <Button variant="secondary" onClick={handleLogout}>
            Sign Out
          </Button>
        </div>
      </header>

      <nav className="instructor-dashboard__nav">
        <button
          className={`instructor-dashboard__nav-item ${
            activeTab === "overview" ? "active" : ""
          }`}
          onClick={() => navigateToTab("overview")}
        >
          Course Overview
        </button>
        <button
          className={`instructor-dashboard__nav-item ${
            activeTab === "sessions" ? "active" : ""
          }`}
          onClick={() => navigateToTab("sessions")}
        >
          Sessions & Attendance
        </button>
        <button
          className={`instructor-dashboard__nav-item ${
            activeTab === "progress" ? "active" : ""
          }`}
          onClick={() => navigateToTab("progress")}
        >
          Student Progress
        </button>
      </nav>

      <main className="instructor-dashboard__content">
        {activeTab === "overview" && (
          <div className="instructor-dashboard__overview">
            <h2>Your Courses</h2>

            {courses.length === 0 ? (
              <div className="instructor-dashboard__empty">
                <p>
                  No assigned courses found. Please contact an administrator to
                  assign courses to your account.
                </p>
              </div>
            ) : (
              <div className="instructor-dashboard__courses-grid">
                {courses.map((course) => {
                  const summary = attendanceSummaries[course.courseId];
                  const avgAttendance =
                    summary?.students.length > 0
                      ? summary.students.reduce(
                          (sum: number, s: any) => sum + s.attendanceRate,
                          0
                        ) / summary.students.length
                      : 0;

                  return (
                    <div
                      key={course.courseId}
                      className="instructor-dashboard__course-card"
                      onClick={() => handleCourseSelect(course)}
                    >
                      <h3>{course.title}</h3>
                      <div className="instructor-dashboard__course-stats">
                        <div className="instructor-dashboard__stat">
                          <span className="instructor-dashboard__stat-number">
                            {course.enrolled}
                          </span>
                          <span className="instructor-dashboard__stat-label">
                            Students
                          </span>
                        </div>
                        <div className="instructor-dashboard__stat">
                          <span className="instructor-dashboard__stat-number">
                            {sessionCounts[course.courseId] || 0}
                          </span>
                          <span className="instructor-dashboard__stat-label">
                            Sessions
                          </span>
                        </div>
                        <div className="instructor-dashboard__stat">
                          <span className="instructor-dashboard__stat-number">
                            {Math.round(avgAttendance)}%
                          </span>
                          <span className="instructor-dashboard__stat-label">
                            Avg Attendance
                          </span>
                        </div>
                      </div>
                      <div className="instructor-dashboard__course-schedule">
                        <p>{course.dayTime}</p>
                        <p>{course.startDate}</p>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        )}

        {activeTab === "sessions" && (
          <InstructorSessionCalendar
            courses={courses}
            selectedCourse={selectedCourse}
            onCourseSelect={handleCourseSelect}
            onRefresh={loadData}
          />
        )}

        {activeTab === "progress" && (
          <StudentProgress
            courses={courses}
            selectedCourse={selectedCourse}
            onCourseSelect={handleCourseSelect}
            onRefresh={loadData}
          />
        )}
      </main>
    </div>
  );
};

export default InstructorDashboard;
