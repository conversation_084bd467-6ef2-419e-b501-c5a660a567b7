import React, { useState } from "react";
import Button from "../../ui/Button";
import { CourseSession } from "../../../types";
import "./SessionCalendarView.css";

interface SessionCalendarViewProps {
  sessions: CourseSession[];
  courses: any[];
  onSessionClick: (session: CourseSession) => void;
  onEditSession: (session: CourseSession) => void;
  onDeleteSession: (sessionId: string) => void;
  onAttendanceClick: (session: CourseSession) => void;
  onSessionMove?: (sessionId: string, newDate: string) => void;
}

const SessionCalendarView: React.FC<SessionCalendarViewProps> = ({
  sessions,
  courses,
  onSessionClick,
  onEditSession,
  onDeleteSession,
  onAttendanceClick,
  onSessionMove,
}) => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [draggedSession, setDraggedSession] = useState<CourseSession | null>(
    null
  );
  const [dragOverDate, setDragOverDate] = useState<string | null>(null);

  // Get the first day of the current month
  const firstDayOfMonth = new Date(
    currentDate.getFullYear(),
    currentDate.getMonth(),
    1
  );
  const lastDayOfMonth = new Date(
    currentDate.getFullYear(),
    currentDate.getMonth() + 1,
    0
  );

  // Get the first day of the week for the calendar (Sunday)
  const firstDayOfWeek = new Date(firstDayOfMonth);
  firstDayOfWeek.setDate(firstDayOfMonth.getDate() - firstDayOfMonth.getDay());

  // Get the last day of the week for the calendar
  const lastDayOfWeek = new Date(lastDayOfMonth);
  lastDayOfWeek.setDate(
    lastDayOfMonth.getDate() + (6 - lastDayOfMonth.getDay())
  );

  // Generate calendar days
  const calendarDays = [];
  const currentDay = new Date(firstDayOfWeek);

  while (currentDay <= lastDayOfWeek) {
    calendarDays.push(new Date(currentDay));
    currentDay.setDate(currentDay.getDate() + 1);
  }

  // Get sessions for a specific date
  const getSessionsForDate = (date: Date) => {
    const dateString = date.toISOString().split("T")[0];
    return sessions.filter((session) => session.date === dateString);
  };

  // Get course info for a session
  const getCourseInfo = (session: CourseSession) => {
    return courses.find(
      (c) =>
        c.courseId === session.courseId ||
        c.id === session.courseId ||
        c.slug === session.courseId
    );
  };

  const formatTime = (timeString: string) => {
    const [hours, minutes] = timeString.split(":");
    const date = new Date();
    date.setHours(parseInt(hours), parseInt(minutes));
    return date.toLocaleTimeString("en-US", {
      hour: "numeric",
      minute: "2-digit",
      hour12: true,
    });
  };

  const navigateMonth = (direction: "prev" | "next") => {
    const newDate = new Date(currentDate);
    if (direction === "prev") {
      newDate.setMonth(newDate.getMonth() - 1);
    } else {
      newDate.setMonth(newDate.getMonth() + 1);
    }
    setCurrentDate(newDate);
  };

  const isToday = (date: Date) => {
    const today = new Date();
    return date.toDateString() === today.toDateString();
  };

  const isCurrentMonth = (date: Date) => {
    return date.getMonth() === currentDate.getMonth();
  };

  // Drag and drop handlers
  const handleDragStart = (e: React.DragEvent, session: CourseSession) => {
    setDraggedSession(session);
    e.dataTransfer.effectAllowed = "move";
    e.dataTransfer.setData("text/html", session.id);
  };

  const handleDragOver = (e: React.DragEvent, date: Date) => {
    e.preventDefault();
    e.stopPropagation();
    e.dataTransfer.dropEffect = "move";
    const dateString = date.toISOString().split("T")[0];
    setDragOverDate(dateString);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragOverDate(null);
  };

  const handleDrop = (e: React.DragEvent, date: Date) => {
    e.preventDefault();
    e.stopPropagation();
    setDragOverDate(null);

    if (draggedSession && onSessionMove) {
      const newDateString = date.toISOString().split("T")[0];

      if (newDateString !== draggedSession.date) {
        onSessionMove(draggedSession.id, newDateString);
      }
    }
    setDraggedSession(null);
  };

  const handleDragEnd = () => {
    setDraggedSession(null);
    setDragOverDate(null);
  };

  return (
    <div className="session-calendar-view">
      <div className="session-calendar-view__header">
        <Button variant="secondary" onClick={() => navigateMonth("prev")}>
          ← Previous
        </Button>
        <h3>
          {currentDate.toLocaleDateString("en-US", {
            month: "long",
            year: "numeric",
          })}
        </h3>
        <Button variant="secondary" onClick={() => navigateMonth("next")}>
          Next →
        </Button>
      </div>

      <div className="session-calendar-view__calendar">
        <div className="session-calendar-view__weekdays">
          {["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"].map((day) => (
            <div key={day} className="session-calendar-view__weekday">
              {day}
            </div>
          ))}
        </div>

        <div className="session-calendar-view__days">
          {calendarDays.map((date, index) => {
            const daySessions = getSessionsForDate(date);
            const dateString = date.toISOString().split("T")[0];
            const isDragOver = dragOverDate === dateString;

            return (
              <div
                key={index}
                className={`session-calendar-view__day ${
                  isCurrentMonth(date) ? "current-month" : "other-month"
                } ${isToday(date) ? "today" : ""} ${
                  isDragOver ? "drag-over" : ""
                }`}
                onDragOver={(e) => handleDragOver(e, date)}
                onDragLeave={handleDragLeave}
                onDrop={(e) => handleDrop(e, date)}
              >
                <div className="session-calendar-view__day-number">
                  {date.getDate()}
                </div>

                <div className="session-calendar-view__day-sessions">
                  {daySessions.map((session) => {
                    const courseInfo = getCourseInfo(session);
                    return (
                      <div
                        key={session.id}
                        className={`session-calendar-view__session ${
                          draggedSession?.id === session.id ? "dragging" : ""
                        }`}
                        draggable={!!onSessionMove}
                        onDragStart={(e) => handleDragStart(e, session)}
                        onDragEnd={handleDragEnd}
                        onClick={(e) => {
                          // Prevent click if we just finished dragging
                          if (draggedSession) {
                            e.preventDefault();
                            e.stopPropagation();
                            return;
                          }
                          onSessionClick(session);
                        }}
                      >
                        <div className="session-calendar-view__session-title">
                          {courseInfo?.title ||
                            session.courseTitle ||
                            "Unknown Course"}
                        </div>
                        <div className="session-calendar-view__session-time">
                          {formatTime(session.startTime)}
                        </div>
                        <div className="session-calendar-view__session-actions">
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              onAttendanceClick(session);
                            }}
                            className="session-calendar-view__action-btn"
                            title="Attendance"
                          >
                            📋
                          </button>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              onEditSession(session);
                            }}
                            className="session-calendar-view__action-btn"
                            title="Edit"
                          >
                            ✏️
                          </button>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default SessionCalendarView;
