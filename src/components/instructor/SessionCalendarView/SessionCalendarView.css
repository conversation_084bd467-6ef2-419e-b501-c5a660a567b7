.session-calendar-view {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.session-calendar-view__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.session-calendar-view__header h3 {
  margin: 0;
  color: #0a2d69;
  font-size: 1.5rem;
  font-weight: 600;
}

.session-calendar-view__calendar {
  width: 100%;
}

.session-calendar-view__weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
  background: #e5e7eb;
  border-radius: 6px 6px 0 0;
  overflow: hidden;
}

.session-calendar-view__weekday {
  background: #f8f9fa;
  padding: 0.75rem;
  text-align: center;
  font-weight: 600;
  color: #374151;
  font-size: 0.9rem;
}

.session-calendar-view__days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
  background: #e5e7eb;
  border-radius: 0 0 6px 6px;
  overflow: hidden;
}

.session-calendar-view__day {
  background: white;
  min-height: 120px;
  padding: 0.5rem;
  position: relative;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.session-calendar-view__day:hover {
  background: #f8f9fa;
}

.session-calendar-view__day.other-month {
  background: #f3f4f6;
  color: #9ca3af;
}

.session-calendar-view__day.today {
  background: #eff6ff;
  border: 2px solid #3b82f6;
}

.session-calendar-view__day.today .session-calendar-view__day-number {
  color: #3b82f6;
  font-weight: 700;
}

.session-calendar-view__day.drag-over {
  background: #e0f2fe !important;
  border: 2px dashed #0a2d69;
}

.session-calendar-view__day.drag-over .session-calendar-view__day-number {
  color: #0a2d69;
  font-weight: 700;
}

.session-calendar-view__day-number {
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.session-calendar-view__day-sessions {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.session-calendar-view__session {
  background: #0a2d69;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.session-calendar-view__session:hover {
  background: #1e40af;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.session-calendar-view__session.dragging {
  opacity: 0.5;
  transform: rotate(5deg);
  cursor: grabbing;
}

.session-calendar-view__session[draggable="true"] {
  cursor: grab;
}

.session-calendar-view__session[draggable="true"]:active {
  cursor: grabbing;
}

.session-calendar-view__session-title {
  font-weight: 600;
  margin-bottom: 0.125rem;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.session-calendar-view__session-time {
  font-size: 0.7rem;
  opacity: 0.9;
  margin-bottom: 0.25rem;
}

.session-calendar-view__session-actions {
  display: flex;
  gap: 0.25rem;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.session-calendar-view__session:hover .session-calendar-view__session-actions {
  opacity: 1;
}

.session-calendar-view__action-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 2px;
  padding: 0.125rem 0.25rem;
  cursor: pointer;
  font-size: 0.7rem;
  transition: background-color 0.2s ease;
}

.session-calendar-view__action-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Responsive design */
@media (max-width: 768px) {
  .session-calendar-view {
    padding: 1rem;
  }

  .session-calendar-view__header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .session-calendar-view__header h3 {
    font-size: 1.25rem;
  }

  .session-calendar-view__day {
    min-height: 80px;
    padding: 0.25rem;
  }

  .session-calendar-view__weekday {
    padding: 0.5rem;
    font-size: 0.8rem;
  }

  .session-calendar-view__day-number {
    font-size: 0.8rem;
  }

  .session-calendar-view__session {
    padding: 0.125rem 0.25rem;
    font-size: 0.7rem;
  }

  .session-calendar-view__session-title {
    font-size: 0.65rem;
  }

  .session-calendar-view__session-time {
    font-size: 0.6rem;
  }
}

@media (max-width: 480px) {
  .session-calendar-view__day {
    min-height: 60px;
  }

  .session-calendar-view__session {
    padding: 0.125rem;
    font-size: 0.65rem;
  }

  .session-calendar-view__session-actions {
    display: none; /* Hide action buttons on very small screens */
  }
}
