.session-attendance {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  max-width: 1000px;
  margin: 0 auto;
  overflow: hidden;
}

.session-attendance__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.session-attendance__header h2 {
  margin: 0 0 0.25rem 0;
  color: #2c3e50;
  font-size: 1.5rem;
}

.session-attendance__header p {
  margin: 0;
  color: #6c757d;
  font-size: 0.9rem;
}

.session-attendance__loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  gap: 1rem;
}

.session-attendance__content {
  padding: 1.5rem;
}

.session-attendance__students {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;
}

.session-attendance__student {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  gap: 1rem;
  align-items: center;
  padding: 1rem;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  background: #f8f9fa;
}

.session-attendance__student-info h4 {
  margin: 0 0 0.25rem 0;
  color: #2c3e50;
  font-size: 1rem;
}

.session-attendance__student-info p {
  margin: 0;
  color: #6c757d;
  font-size: 0.85rem;
}

.session-attendance__status-buttons {
  display: flex;
  gap: 0.5rem;
}

.session-attendance__status-btn {
  padding: 0.5rem 1rem;
  border: 2px solid;
  border-radius: 4px;
  background: transparent;
  cursor: pointer;
  font-size: 0.85rem;
  font-weight: 500;
  transition: all 0.2s ease;
  min-width: 70px;
}

.session-attendance__status-btn:hover {
  opacity: 0.8;
}

.session-attendance__status-btn.active {
  color: white !important;
}

.session-attendance__notes {
  display: flex;
  align-items: center;
}

.session-attendance__notes input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 0.85rem;
}

.session-attendance__notes input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.session-attendance__actions {
  display: flex;
  justify-content: center;
  padding-top: 1rem;
  border-top: 1px solid #e9ecef;
}

/* Responsive design */
@media (max-width: 768px) {
  .session-attendance__student {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }
  
  .session-attendance__status-buttons {
    justify-content: center;
  }
  
  .session-attendance__status-btn {
    min-width: 60px;
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
  }
  
  .session-attendance__header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .session-attendance {
    margin: 0.5rem;
  }
  
  .session-attendance__content {
    padding: 1rem;
  }
  
  .session-attendance__header {
    padding: 1rem;
  }
  
  .session-attendance__status-buttons {
    flex-wrap: wrap;
    gap: 0.25rem;
  }
  
  .session-attendance__status-btn {
    min-width: 50px;
    padding: 0.3rem 0.6rem;
    font-size: 0.75rem;
  }
}
