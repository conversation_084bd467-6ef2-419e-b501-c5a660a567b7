import React, { useState, useEffect } from "react";
import Button from "../../ui/Button";
import Spinner from "../../ui/Spinner";
import {
  markSessionAttendance,
  getSessionAttendanceRecords,
} from "../../../services/attendanceService";
import { CourseSession, SessionAttendanceRecord } from "../../../types";
import { getInstructorSession } from "../../../services/instructorService";
import "./SessionAttendance.css";

interface SessionAttendanceProps {
  session: CourseSession;
  students: any[]; // Course registrations
  onClose: () => void;
  onRefresh: () => void;
}

const SessionAttendance: React.FC<SessionAttendanceProps> = ({
  session,
  students,
  onClose,
  onRefresh,
}) => {
  const [attendance, setAttendance] = useState<{
    [studentId: string]: SessionAttendanceRecord;
  }>({});
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const instructorUser = getInstructorSession();

  useEffect(() => {
    loadAttendance();
  }, [session.id]);

  const loadAttendance = async () => {
    setLoading(true);
    try {
      // Load existing attendance from session
      if (session.attendance) {
        setAttendance(session.attendance);
      } else {
        // Initialize empty attendance for all students
        const initialAttendance: {
          [studentId: string]: SessionAttendanceRecord;
        } = {};
        students.forEach((student) => {
          initialAttendance[student.id] = {
            studentId: student.id,
            studentName: `${student.firstName} ${student.lastName}`,
            studentEmail: student.email,
            status: "absent", // Default to absent
          };
        });
        setAttendance(initialAttendance);
      }
    } catch (error) {
      console.error("Error loading attendance:", error);
    } finally {
      setLoading(false);
    }
  };

  const updateAttendance = (
    studentId: string,
    status: "present" | "absent" | "late" | "excused",
    notes?: string
  ) => {
    setAttendance((prev) => {
      const updatedRecord = {
        ...prev[studentId],
        status,
      };

      // Only include notes if they exist and are not empty
      if (notes !== undefined) {
        if (notes.trim() !== "") {
          updatedRecord.notes = notes.trim();
        } else {
          // Remove notes field if empty
          delete updatedRecord.notes;
        }
      }

      return {
        ...prev,
        [studentId]: updatedRecord,
      };
    });
  };

  const saveAttendance = async () => {
    if (!instructorUser?.id) return;

    setSaving(true);
    try {
      // Save each attendance record
      for (const [studentId, record] of Object.entries(attendance)) {
        // Create a clean record without undefined values
        const cleanRecord: Partial<SessionAttendanceRecord> = {
          studentId: record.studentId,
          studentName: record.studentName,
          studentEmail: record.studentEmail,
          status: record.status,
        };

        // Only add notes if they exist and are not empty
        if (record.notes && record.notes.trim() !== "") {
          cleanRecord.notes = record.notes.trim();
        }

        await markSessionAttendance(
          session.id,
          studentId,
          cleanRecord as Omit<SessionAttendanceRecord, "markedAt" | "markedBy">,
          instructorUser.id
        );
      }

      onRefresh();
      alert("Attendance saved successfully!");
    } catch (error) {
      console.error("Error saving attendance:", error);
      alert("Failed to save attendance. Please try again.");
    } finally {
      setSaving(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "present":
        return "#4CAF50";
      case "late":
        return "#FF9800";
      case "excused":
        return "#2196F3";
      case "absent":
        return "#F44336";
      default:
        return "#9E9E9E";
    }
  };

  if (loading) {
    return (
      <div className="session-attendance">
        <div className="session-attendance__header">
          <h2>Session Attendance</h2>
          <Button onClick={onClose}>Close</Button>
        </div>
        <div className="session-attendance__loading">
          <Spinner />
          <p>Loading attendance...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="session-attendance">
      <div className="session-attendance__header">
        <div>
          <h2>Session {session.sessionNumber} Attendance</h2>
          <p>
            {session.topic || "Session Topic"} - {session.date}
          </p>
        </div>
        <Button onClick={onClose}>Close</Button>
      </div>

      <div className="session-attendance__content">
        <div className="session-attendance__students">
          {students.map((student) => {
            const attendanceRecord = attendance[student.id];
            if (!attendanceRecord) return null;

            return (
              <div key={student.id} className="session-attendance__student">
                <div className="session-attendance__student-info">
                  <h4>
                    {student.firstName} {student.lastName}
                  </h4>
                  <p>{student.email}</p>
                </div>

                <div className="session-attendance__status-buttons">
                  {["present", "late", "excused", "absent"].map((status) => (
                    <button
                      key={status}
                      className={`session-attendance__status-btn ${
                        attendanceRecord.status === status ? "active" : ""
                      }`}
                      style={{
                        backgroundColor:
                          attendanceRecord.status === status
                            ? getStatusColor(status)
                            : "transparent",
                        color:
                          attendanceRecord.status === status
                            ? "white"
                            : getStatusColor(status),
                        borderColor: getStatusColor(status),
                      }}
                      onClick={() =>
                        updateAttendance(student.id, status as any)
                      }
                    >
                      {status.charAt(0).toUpperCase() + status.slice(1)}
                    </button>
                  ))}
                </div>

                <div className="session-attendance__notes">
                  <input
                    type="text"
                    placeholder="Notes (optional)"
                    value={attendanceRecord.notes || ""}
                    onChange={(e) =>
                      updateAttendance(
                        student.id,
                        attendanceRecord.status,
                        e.target.value
                      )
                    }
                  />
                </div>
              </div>
            );
          })}
        </div>

        <div className="session-attendance__actions">
          <Button onClick={saveAttendance} loading={saving} disabled={saving}>
            {saving ? "Saving..." : "Save Attendance"}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default SessionAttendance;
