import React, { useState } from "react";
import Button from "../../ui/Button";
import {
  instructorLogin,
  LoginCredentials,
} from "../../../services/instructorService";
import { sendPasswordReset } from "../../../services/userService";
import "./InstructorLogin.css";

interface InstructorLoginProps {
  onLoginSuccess: () => void;
}

const InstructorLogin: React.FC<InstructorLoginProps> = ({
  onLoginSuccess,
}) => {
  const [credentials, setCredentials] = useState<LoginCredentials>({
    email: "",
    password: "",
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [resetLoading, setResetLoading] = useState(false);
  const [resetMessage, setResetMessage] = useState("");

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setCredentials((prev) => ({
      ...prev,
      [name]: value,
    }));
    // Clear error when user starts typing
    if (error) setError(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      const result = await instructorLogin(credentials);
      if (result.success) {
        onLoginSuccess();
      } else {
        setError(result.error || "Login failed");
      }
    } catch (error) {
      setError("An unexpected error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  const handleForgotPassword = async () => {
    if (!credentials.email.trim()) {
      setError("Please enter your email address first");
      return;
    }

    // Check if this is a username (no @) - can't reset password for kiosk users
    if (!credentials.email.includes("@")) {
      setError(
        "Password reset is only available for email accounts. Contact your manager to reset kiosk passwords."
      );
      return;
    }

    setResetLoading(true);
    setError(null);
    setResetMessage("");

    try {
      await sendPasswordReset(credentials.email.trim());
      setResetMessage(
        "Password reset email sent! Check your inbox and spam folder."
      );
    } catch (error: any) {
      setError(error.message || "Failed to send password reset email");
    } finally {
      setResetLoading(false);
    }
  };

  return (
    <div className="instructor-login">
      <div className="instructor-login__container">
        <div className="instructor-login__header">
          <h1>Instructor Portal</h1>
          <p>Sign in to access your courses and manage attendance</p>
        </div>

        <form onSubmit={handleSubmit} className="instructor-login__form">
          {error && <div className="instructor-login__error">{error}</div>}
          {resetMessage && (
            <div className="instructor-login__success">{resetMessage}</div>
          )}

          <div className="instructor-login__field">
            <label htmlFor="email">Email / Username</label>
            <input
              type="text"
              id="email"
              name="email"
              value={credentials.email}
              onChange={handleInputChange}
              required
              disabled={isLoading}
              placeholder="Enter your email or kiosk username"
            />
          </div>

          <div className="instructor-login__field">
            <label htmlFor="password">Password</label>
            <input
              type="password"
              id="password"
              name="password"
              value={credentials.password}
              onChange={handleInputChange}
              required
              disabled={isLoading}
              placeholder="Enter your password"
            />
          </div>

          <Button
            type="submit"
            loading={isLoading}
            disabled={isLoading || !credentials.email || !credentials.password}
            className="instructor-login__submit"
          >
            {isLoading ? "Signing In..." : "Sign In"}
          </Button>

          <div className="instructor-login__forgot-password">
            <button
              type="button"
              onClick={handleForgotPassword}
              disabled={resetLoading}
              className="instructor-login__forgot-link"
            >
              {resetLoading ? "Sending..." : "Forgot your password?"}
            </button>
          </div>
        </form>

        <div className="instructor-login__footer">
          <p>
            Need help? Contact{" "}
            <a href="mailto:<EMAIL>">
              <EMAIL>
            </a>
          </p>
        </div>
      </div>
    </div>
  );
};

export default InstructorLogin;
