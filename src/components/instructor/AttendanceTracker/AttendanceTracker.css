.attendance-tracker {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.attendance-tracker h2 {
  margin: 0 0 2rem 0;
  color: #0a2d69;
  font-size: 1.5rem;
  font-weight: 600;
}

/* Course Selection */
.attendance-tracker__course-selection {
  text-align: center;
  padding: 2rem 0;
}

.attendance-tracker__course-selection p {
  margin: 0 0 2rem 0;
  color: #6b7280;
  font-size: 1.1rem;
}

.attendance-tracker__courses-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
  max-width: 800px;
  margin: 0 auto;
}

.attendance-tracker__course-card {
  background: #f8f9fa;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.attendance-tracker__course-card:hover {
  border-color: #0a2d69;
  box-shadow: 0 8px 24px rgba(10, 45, 105, 0.1);
  transform: translateY(-2px);
}

.attendance-tracker__course-card h3 {
  margin: 0 0 0.5rem 0;
  color: #0a2d69;
  font-size: 1.1rem;
  font-weight: 600;
}

.attendance-tracker__course-card p {
  margin: 0;
  color: #6b7280;
  font-size: 0.9rem;
}

/* Header */
.attendance-tracker__header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  gap: 2rem;
}

.attendance-tracker__course-info h2 {
  margin: 0 0 1rem 0;
}

.attendance-tracker__session-controls {
  display: flex;
  gap: 1rem;
  align-items: flex-end;
}

.attendance-tracker__field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.attendance-tracker__field label {
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
}

.attendance-tracker__field input {
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  min-width: 120px;
}

.attendance-tracker__field input:focus {
  outline: none;
  border-color: #0a2d69;
  box-shadow: 0 0 0 3px rgba(10, 45, 105, 0.1);
}

/* Loading */
.attendance-tracker__loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  gap: 1rem;
}

.attendance-tracker__loading p {
  color: #6b7280;
  font-size: 1.1rem;
}

/* Table */
.attendance-tracker__table-container {
  overflow-x: auto;
  margin-bottom: 2rem;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.attendance-tracker__table {
  width: 100%;
  border-collapse: collapse;
  background: white;
}

.attendance-tracker__table th {
  background: #f8f9fa;
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  color: #374151;
  border-bottom: 1px solid #e5e7eb;
  font-size: 0.875rem;
}

.attendance-tracker__table td {
  padding: 1rem;
  border-bottom: 1px solid #f3f4f6;
  vertical-align: middle;
}

.attendance-tracker__table tbody tr:hover {
  background: #f8f9fa;
}

.attendance-tracker__student-name {
  font-weight: 600;
  color: #0a2d69;
}

.attendance-tracker__student-email {
  color: #6b7280;
  font-size: 0.875rem;
}

.attendance-tracker__status-cell {
  width: 140px;
}

.attendance-tracker__status-select {
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  width: 100%;
  cursor: pointer;
}

.attendance-tracker__status-select--present {
  background: #f0fdf4;
  border-color: #22c55e;
  color: #15803d;
}

.attendance-tracker__status-select--late {
  background: #fffbeb;
  border-color: #f59e0b;
  color: #d97706;
}

.attendance-tracker__status-select--absent {
  background: #fef2f2;
  border-color: #ef4444;
  color: #dc2626;
}

.attendance-tracker__status-select--excused {
  background: #f3f4f6;
  border-color: #9ca3af;
  color: #6b7280;
}

.attendance-tracker__notes-cell {
  min-width: 200px;
}

.attendance-tracker__notes-input {
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  width: 100%;
}

.attendance-tracker__notes-input:focus {
  outline: none;
  border-color: #0a2d69;
  box-shadow: 0 0 0 2px rgba(10, 45, 105, 0.1);
}

/* Bulk Actions */
.attendance-tracker__bulk-actions {
  padding: 1rem 0;
  border-top: 1px solid #e5e7eb;
}

.attendance-tracker__bulk-buttons {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

/* Actions */
.attendance-tracker__actions {
  display: flex;
  justify-content: center;
  padding: 1rem 0;
}

/* Summary */
.attendance-tracker__summary {
  margin-top: 2rem;
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.attendance-tracker__summary h3 {
  margin: 0 0 1rem 0;
  color: #0a2d69;
  font-size: 1.1rem;
  font-weight: 600;
}

.attendance-tracker__summary-stats {
  display: flex;
  gap: 2rem;
}

.attendance-tracker__summary-stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.attendance-tracker__summary-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: #0a2d69;
  line-height: 1;
}

.attendance-tracker__summary-label {
  font-size: 0.75rem;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-top: 0.25rem;
}

/* Responsive design */
@media (max-width: 768px) {
  .attendance-tracker__header {
    flex-direction: column;
    align-items: stretch;
  }

  .attendance-tracker__session-controls {
    flex-direction: column;
  }

  .attendance-tracker__field input {
    min-width: auto;
  }

  .attendance-tracker__summary-stats {
    justify-content: space-around;
  }
}
