import React, { useState, useEffect } from "react";
import Button from "../../ui/Button";
import Spinner from "../../ui/Spinner";
import {
  markAttendance,
  getSessionAttendance,
  getCourseSessionAttendanceSummary,
  AttendanceRecord,
} from "../../../services/attendanceService";
import {
  getInstructorSession,
  getInstructorCourseRegistrations,
} from "../../../services/instructorService";
import "./AttendanceTracker.css";

interface AttendanceTrackerProps {
  courses: any[];
  selectedCourse: any;
  onCourseSelect: (course: any) => void;
  onRefresh: () => void;
}

const AttendanceTracker: React.FC<AttendanceTrackerProps> = ({
  courses,
  selectedCourse,
  onCourseSelect,
  onRefresh,
}) => {
  const [currentSession, setCurrentSession] = useState(1);
  const [sessionDate, setSessionDate] = useState(
    new Date().toISOString().split("T")[0]
  );
  const [attendanceRecords, setAttendanceRecords] = useState<
    AttendanceRecord[]
  >([]);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [attendanceSummary, setAttendanceSummary] = useState<any>(null);

  const instructorUser = getInstructorSession();

  // Load attendance data when course or session changes
  useEffect(() => {
    if (selectedCourse) {
      loadAttendanceData();
      loadAttendanceSummary();
    }
  }, [selectedCourse, currentSession]);

  const loadAttendanceData = async () => {
    if (!selectedCourse) return;

    setLoading(true);
    try {
      const records = await getSessionAttendance(
        selectedCourse.courseId,
        currentSession
      );

      // If no records exist, create initial records for enrolled students
      if (records.length === 0) {
        console.log(
          "🔍 Creating attendance records for course:",
          selectedCourse.title
        );

        // Get registrations securely - only for courses instructor is assigned to
        const courseRegistrations = await getInstructorCourseRegistrations(
          selectedCourse.courseId
        );

        // Filter to only enrolled students and exclude cancelled registrations
        const enrolledStudents = courseRegistrations.filter(
          (reg: any) =>
            reg.enrollmentStatus === "enrolled" &&
            reg.paymentStatus !== "cancelled"
        );

        console.log(
          "✅ Found enrolled students for this course:",
          enrolledStudents.map((r: any) => ({
            id: r.id,
            name: `${r.firstName} ${r.lastName}`,
            courseId: r.courseId,
          }))
        );

        const initialRecords = enrolledStudents.map((reg: any) => ({
          courseId: selectedCourse.courseId,
          courseTitle: selectedCourse.title,
          studentId: reg.id,
          studentName: `${reg.firstName} ${reg.lastName}`,
          studentEmail: reg.email,
          sessionDate,
          sessionNumber: currentSession,
          status: "present" as const,
          instructorId: instructorUser?.id || "",
          instructorName: instructorUser?.name || "",
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        }));
        setAttendanceRecords(initialRecords);
      } else {
        setAttendanceRecords(records);
      }
    } catch (error) {
      console.error("Error loading attendance data:", error);
    } finally {
      setLoading(false);
    }
  };

  const loadAttendanceSummary = async () => {
    if (!selectedCourse) return;

    try {
      const summary = await getCourseSessionAttendanceSummary(
        selectedCourse.courseId
      );
      setAttendanceSummary(summary);
    } catch (error) {
      console.error("Error getting course attendance summary:", error);
    }
  };

  const handleAttendanceChange = (
    studentId: string,
    status: "present" | "absent" | "late" | "excused"
  ) => {
    setAttendanceRecords((prev) =>
      prev.map((record) =>
        record.studentId === studentId
          ? { ...record, status, updatedAt: new Date().toISOString() }
          : record
      )
    );
  };

  const handleNotesChange = (studentId: string, notes: string) => {
    setAttendanceRecords((prev) =>
      prev.map((record) =>
        record.studentId === studentId
          ? { ...record, notes, updatedAt: new Date().toISOString() }
          : record
      )
    );
  };

  const saveAttendance = async () => {
    if (!selectedCourse || !instructorUser) return;

    setSaving(true);
    try {
      // Save all attendance records
      for (const record of attendanceRecords) {
        await markAttendance({
          courseId: record.courseId,
          courseTitle: record.courseTitle,
          studentId: record.studentId,
          studentName: record.studentName,
          studentEmail: record.studentEmail,
          sessionDate: sessionDate,
          sessionNumber: currentSession,
          status: record.status,
          notes: record.notes,
          instructorId: instructorUser.id,
          instructorName: instructorUser.name,
        });
      }

      await loadAttendanceSummary();
      onRefresh();
      alert("Attendance saved successfully!");
    } catch (error) {
      console.error("Error saving attendance:", error);
      alert("Failed to save attendance. Please try again.");
    } finally {
      setSaving(false);
    }
  };

  if (!selectedCourse) {
    return (
      <div className="attendance-tracker">
        <h2>Attendance Tracking</h2>
        <div className="attendance-tracker__course-selection">
          <p>Select a course to track attendance:</p>
          <div className="attendance-tracker__courses-grid">
            {courses.map((course) => (
              <div
                key={course.courseId}
                className="attendance-tracker__course-card"
                onClick={() => onCourseSelect(course)}
              >
                <h3>{course.title}</h3>
                <p>{course.enrolled} students enrolled</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="attendance-tracker">
      <div className="attendance-tracker__header">
        <div className="attendance-tracker__course-info">
          <h2>{selectedCourse.title} - Attendance</h2>
          <Button variant="secondary" onClick={() => onCourseSelect(null)}>
            ← Back to Courses
          </Button>
        </div>

        <div className="attendance-tracker__session-controls">
          <div className="attendance-tracker__field">
            <label htmlFor="session-number">Session Number</label>
            <input
              id="session-number"
              type="number"
              min="1"
              value={currentSession}
              onChange={(e) => setCurrentSession(parseInt(e.target.value) || 1)}
            />
          </div>

          <div className="attendance-tracker__field">
            <label htmlFor="session-date">Session Date</label>
            <input
              id="session-date"
              type="date"
              value={sessionDate}
              onChange={(e) => setSessionDate(e.target.value)}
            />
          </div>
        </div>
      </div>

      {loading ? (
        <div className="attendance-tracker__loading">
          <Spinner size="large" />
          <p>Loading attendance data...</p>
        </div>
      ) : (
        <>
          <div className="attendance-tracker__table-container">
            <table className="attendance-tracker__table">
              <thead>
                <tr>
                  <th>Student</th>
                  <th>Email</th>
                  <th>Status</th>
                  <th>Notes</th>
                </tr>
              </thead>
              <tbody>
                {attendanceRecords.map((record) => (
                  <tr key={record.studentId}>
                    <td className="attendance-tracker__student-name">
                      {record.studentName}
                    </td>
                    <td className="attendance-tracker__student-email">
                      {record.studentEmail}
                    </td>
                    <td className="attendance-tracker__status-cell">
                      <select
                        value={record.status}
                        onChange={(e) =>
                          handleAttendanceChange(
                            record.studentId,
                            e.target.value as
                              | "present"
                              | "absent"
                              | "late"
                              | "excused"
                          )
                        }
                        className={`attendance-tracker__status-select attendance-tracker__status-select--${record.status}`}
                      >
                        <option value="present">Present</option>
                        <option value="late">Late</option>
                        <option value="absent">Absent</option>
                        <option value="excused">Excused</option>
                      </select>
                    </td>
                    <td className="attendance-tracker__notes-cell">
                      <input
                        type="text"
                        placeholder="Add notes..."
                        value={record.notes || ""}
                        onChange={(e) =>
                          handleNotesChange(record.studentId, e.target.value)
                        }
                        className="attendance-tracker__notes-input"
                      />
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          <div className="attendance-tracker__bulk-actions">
            <div className="attendance-tracker__bulk-buttons">
              <Button
                variant="secondary"
                onClick={() => {
                  setAttendanceRecords((records) =>
                    records.map((record) => ({
                      ...record,
                      status: "present" as const,
                    }))
                  );
                }}
                disabled={attendanceRecords.length === 0}
              >
                Mark All Present
              </Button>
              <Button
                variant="secondary"
                onClick={() => {
                  setAttendanceRecords((records) =>
                    records.map((record) => ({
                      ...record,
                      status: "absent" as const,
                    }))
                  );
                }}
                disabled={attendanceRecords.length === 0}
              >
                Mark All Absent
              </Button>
            </div>
          </div>

          <div className="attendance-tracker__actions">
            <Button
              onClick={saveAttendance}
              loading={saving}
              disabled={saving || attendanceRecords.length === 0}
            >
              {saving ? "Saving..." : "Save Attendance"}
            </Button>
          </div>

          {attendanceSummary && (
            <div className="attendance-tracker__summary">
              <h3>Course Attendance Summary</h3>
              <div className="attendance-tracker__summary-stats">
                <div className="attendance-tracker__summary-stat">
                  <span className="attendance-tracker__summary-number">
                    {attendanceSummary.totalSessions}
                  </span>
                  <span className="attendance-tracker__summary-label">
                    Total Sessions
                  </span>
                </div>
                <div className="attendance-tracker__summary-stat">
                  <span className="attendance-tracker__summary-number">
                    {attendanceSummary.students.length}
                  </span>
                  <span className="attendance-tracker__summary-label">
                    Students Tracked
                  </span>
                </div>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default AttendanceTracker;
