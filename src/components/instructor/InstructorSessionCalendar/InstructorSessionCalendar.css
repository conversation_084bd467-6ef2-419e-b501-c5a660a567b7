.instructor-session-calendar {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.instructor-session-calendar__loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem;
  color: #6b7280;
}

.instructor-session-calendar__header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.instructor-session-calendar__title h2 {
  margin: 0 0 0.5rem 0;
  color: #0a2d69;
  font-size: 1.8rem;
  font-weight: 700;
}

.instructor-session-calendar__subtitle {
  margin: 0;
  color: #6b7280;
  font-size: 1rem;
}

.instructor-session-calendar__actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  flex-shrink: 0;
  align-items: center;
}

.instructor-session-calendar__filters {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.instructor-session-calendar__filter-select {
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  background: white;
  font-size: 0.9rem;
  color: #374151;
  cursor: pointer;
  min-width: 140px;
}

.instructor-session-calendar__filter-select:focus {
  outline: none;
  border-color: #0a2d69;
  box-shadow: 0 0 0 2px rgba(10, 45, 105, 0.1);
}

.instructor-session-calendar__filter-input {
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  background: white;
  font-size: 0.9rem;
  color: #374151;
  min-width: 160px;
}

.instructor-session-calendar__filter-input:focus {
  outline: none;
  border-color: #0a2d69;
  box-shadow: 0 0 0 2px rgba(10, 45, 105, 0.1);
}

.instructor-session-calendar__filter-input::placeholder {
  color: #9ca3af;
}

/* Course Selector */
.instructor-session-calendar__course-selector {
  margin-bottom: 2rem;
}

.instructor-session-calendar__course-selector h3 {
  margin: 0 0 1rem 0;
  color: #0a2d69;
  font-size: 1.2rem;
  font-weight: 600;
}

.instructor-session-calendar__course-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
}

.instructor-session-calendar__course-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.instructor-session-calendar__course-card:hover {
  border-color: #0a2d69;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.instructor-session-calendar__course-card h4 {
  margin: 0 0 0.5rem 0;
  color: #0a2d69;
  font-size: 1.1rem;
  font-weight: 600;
}

.instructor-session-calendar__course-card p {
  margin: 0 0 1rem 0;
  color: #6b7280;
  font-size: 0.9rem;
}

.instructor-session-calendar__course-stats {
  display: flex;
  gap: 1rem;
  font-size: 0.85rem;
  color: #6b7280;
}

/* Forms */
.instructor-session-calendar__add-form,
.instructor-session-calendar__edit-form {
  background: #f8f9fa;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.instructor-session-calendar__edit-form {
  background: #fff3cd;
  border-color: #ffeaa7;
}

.instructor-session-calendar__add-form h3,
.instructor-session-calendar__edit-form h3 {
  margin: 0 0 1rem 0;
  color: #0a2d69;
  font-size: 1.2rem;
  font-weight: 600;
}

.instructor-session-calendar__form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.instructor-session-calendar__field {
  display: flex;
  flex-direction: column;
}

.instructor-session-calendar__field label {
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #374151;
  font-size: 0.9rem;
}

.instructor-session-calendar__field input,
.instructor-session-calendar__field select,
.instructor-session-calendar__field textarea {
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.9rem;
  transition: border-color 0.2s ease;
}

.instructor-session-calendar__field input:focus,
.instructor-session-calendar__field select:focus,
.instructor-session-calendar__field textarea:focus {
  outline: none;
  border-color: #0a2d69;
  box-shadow: 0 0 0 3px rgba(10, 45, 105, 0.1);
}

.instructor-session-calendar__form-actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

/* Session List */
.instructor-session-calendar__list {
  margin-top: 2rem;
}

.instructor-session-calendar__empty {
  text-align: center;
  padding: 3rem;
  color: #6b7280;
}

.instructor-session-calendar__empty p {
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
}

.instructor-session-calendar__sessions {
  display: grid;
  gap: 1rem;
}

.instructor-session-calendar__session-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1.5rem;
  transition: all 0.2s ease;
}

.instructor-session-calendar__session-card.upcoming {
  border-left: 4px solid #0a2d69;
}

.instructor-session-calendar__session-card.past {
  border-left: 4px solid #9ca3af;
  opacity: 0.8;
}

.instructor-session-calendar__session-card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.instructor-session-calendar__session-card.editing {
  border: 2px solid #0a2d69;
  box-shadow: 0 4px 12px rgba(10, 45, 105, 0.15);
  background: #fafbfc;
  transform: none;
}

.instructor-session-calendar__session-card.editing:hover {
  transform: none;
}

.instructor-session-calendar__edit-field {
  margin: 0.5rem 0;
}

.instructor-session-calendar__edit-field label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.25rem;
}

.instructor-session-calendar__session-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.instructor-session-calendar__session-info h4 {
  margin: 0 0 0.25rem 0;
  color: #0a2d69;
  font-size: 1.1rem;
  font-weight: 600;
}

.instructor-session-calendar__session-number {
  margin: 0;
  color: #6b7280;
  font-size: 0.9rem;
  font-weight: 500;
}

.instructor-session-calendar__status-controls {
  display: flex;
  align-items: center;
}

.instructor-session-calendar__status-select {
  padding: 0.25rem 0.5rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  cursor: pointer;
  outline: none;
}

.instructor-session-calendar__status-select option {
  background: white;
  color: black;
  text-transform: capitalize;
}

.instructor-session-calendar__session-details {
  margin-bottom: 1rem;
}

.instructor-session-calendar__session-time {
  display: flex;
  gap: 1rem;
  margin-bottom: 0.5rem;
  flex-wrap: wrap;
}

.instructor-session-calendar__date {
  font-weight: 600;
  color: #0a2d69;
}

.instructor-session-calendar__time {
  color: #6b7280;
}

.instructor-session-calendar__location {
  margin-bottom: 0.5rem;
  color: #6b7280;
  font-size: 0.9rem;
}

.instructor-session-calendar__description {
  margin-bottom: 0.5rem;
  color: #374151;
  font-size: 0.9rem;
  line-height: 1.5;
}

.instructor-session-calendar__notes {
  margin-bottom: 0.5rem;
  color: #374151;
  font-size: 0.9rem;
  line-height: 1.5;
  background: #f3f4f6;
  padding: 0.75rem;
  border-radius: 6px;
  border-left: 3px solid #0a2d69;
}

.instructor-session-calendar__session-actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

/* Responsive Design */
@media (max-width: 768px) {
  .instructor-session-calendar {
    padding: 1rem;
  }

  .instructor-session-calendar__header {
    flex-direction: column;
    align-items: stretch;
  }

  .instructor-session-calendar__actions {
    justify-content: stretch;
  }

  .instructor-session-calendar__actions button {
    flex: 1;
  }

  .instructor-session-calendar__form-grid {
    grid-template-columns: 1fr;
  }

  .instructor-session-calendar__session-time {
    flex-direction: column;
    gap: 0.25rem;
  }

  .instructor-session-calendar__session-actions {
    justify-content: stretch;
  }

  .instructor-session-calendar__session-actions button {
    flex: 1;
  }
}

@media (max-width: 480px) {
  .instructor-session-calendar__course-grid {
    grid-template-columns: 1fr;
  }
}

/* Modal styles */
.instructor-session-calendar__modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.instructor-session-calendar__modal {
  background: white;
  border-radius: 8px;
  max-width: 90vw;
  max-height: 90vh;
  overflow: auto;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

@media (max-width: 768px) {
  .instructor-session-calendar__modal {
    max-width: 95vw;
    max-height: 95vh;
    margin: 0.5rem;
  }
}
