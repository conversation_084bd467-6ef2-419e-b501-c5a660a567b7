import React, { useState, useEffect } from "react";
import Button from "../../ui/Button";
import Spinner from "../../ui/Spinner";
import { CourseSession } from "../../../types";
import {
  getInstructorSessions,
  updateSession,
  createSession,
  deleteSession,
  getAllSessionsDebug,
  autoGenerateSessions,
} from "../../../services/sessionService";
import { getInstructorSession } from "../../../services/instructorService";
import SessionAttendance from "../SessionAttendance/SessionAttendance";
import SessionCalendarView from "../SessionCalendarView/SessionCalendarView";
import "./InstructorSessionCalendar.css";

interface InstructorSessionCalendarProps {
  courses: any[];
  selectedCourse: any;
  onCourseSelect: (course: any) => void;
  onRefresh: () => void;
}

const InstructorSessionCalendar: React.FC<InstructorSessionCalendarProps> = ({
  courses,
  selectedCourse,
  onCourseSelect,
  onRefresh,
}) => {
  const [sessions, setSessions] = useState<CourseSession[]>([]);
  const [loading, setLoading] = useState(true);
  const [editingSessionId, setEditingSessionId] = useState<string | null>(null);
  const [editingSessionData, setEditingSessionData] = useState<
    Partial<CourseSession>
  >({});
  const [showAddForm, setShowAddForm] = useState(false);
  const [showAutoGenerateForm, setShowAutoGenerateForm] = useState(false);
  const [autoGenerateCount, setAutoGenerateCount] = useState(6);
  const [saving, setSaving] = useState(false);
  const [showAttendance, setShowAttendance] = useState<CourseSession | null>(
    null
  );
  const [viewMode, setViewMode] = useState<"all" | "upcoming" | "past">(
    "upcoming"
  );
  const [displayMode, setDisplayMode] = useState<"list" | "calendar">("list");
  const [filters, setFilters] = useState({
    status: "all" as
      | "all"
      | "scheduled"
      | "completed"
      | "cancelled"
      | "rescheduled",
    dateRange: "all" as
      | "all"
      | "thisWeek"
      | "thisMonth"
      | "nextWeek"
      | "nextMonth",
    location: "all" as string,
  });
  const [newSession, setNewSession] = useState({
    sessionNumber: 1,
    date: "",
    startTime: "10:00",
    endTime: "11:30",
    topic: "",
    description: "",
    status: "scheduled" as const,
  });

  const instructorUser = getInstructorSession();

  useEffect(() => {
    loadSessions();
  }, [selectedCourse]);

  const loadSessions = async () => {
    if (!instructorUser?.id) {
      return;
    }

    setLoading(true);
    try {
      let allSessions: CourseSession[] = [];

      if (selectedCourse) {
        // Load sessions for specific course
        allSessions = await getInstructorSessions(
          instructorUser.id,
          selectedCourse.courseId
        );
      } else {
        // Load sessions for all instructor's courses
        for (const course of courses) {
          const courseSessions = await getInstructorSessions(
            instructorUser.id,
            course.courseId
          );
          allSessions = [...allSessions, ...courseSessions];
        }
      }

      // Sort sessions by date and time
      allSessions.sort((a, b) => {
        const dateA = new Date(`${a.date}T${a.startTime}`);
        const dateB = new Date(`${b.date}T${b.startTime}`);
        return dateA.getTime() - dateB.getTime();
      });

      setSessions(allSessions);
    } catch (error) {
      console.error("Error loading sessions:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddSession = async () => {
    if (!selectedCourse) {
      alert("Please select a course first");
      return;
    }

    setSaving(true);
    try {
      const sessionData = {
        ...newSession,
        courseId: selectedCourse.courseId,
      };
      await createSession(selectedCourse.courseId, sessionData);
      await loadSessions();
      setShowAddForm(false);
      setNewSession({
        sessionNumber: 1,
        date: "",
        startTime: "10:00",
        endTime: "11:30",
        topic: "",
        description: "",
        status: "scheduled",
      });
    } catch (error) {
      console.error("Error adding session:", error);
      alert("Failed to add session. Please try again.");
    } finally {
      setSaving(false);
    }
  };

  const handleAutoGenerateSessions = async () => {
    if (!selectedCourse) {
      alert("Please select a course first");
      return;
    }

    // Check if course has existing sessions
    const existingSessions = sessions.filter(
      (s) => s.courseId === selectedCourse.courseId
    );
    if (existingSessions.length > 0) {
      const confirmed = window.confirm(
        `This course already has ${existingSessions.length} session(s). Auto-generating will add ${autoGenerateCount} more sessions. Continue?`
      );
      if (!confirmed) return;
    }

    setSaving(true);
    try {
      await autoGenerateSessions(
        selectedCourse.courseId,
        {
          startDate: selectedCourse.startDate,
          schedule: selectedCourse.schedule,
          title: selectedCourse.title,
        },
        autoGenerateCount
      );
      await loadSessions();
      setShowAutoGenerateForm(false);
      alert(`Successfully generated ${autoGenerateCount} sessions!`);
    } catch (error) {
      console.error("Error auto-generating sessions:", error);
      alert(
        "Failed to generate sessions. Please check the course schedule and try again."
      );
    } finally {
      setSaving(false);
    }
  };

  const handleStartEdit = (session: CourseSession) => {
    setEditingSessionId(session.id);
    setEditingSessionData({ ...session });
  };

  const handleCancelEdit = () => {
    setEditingSessionId(null);
    setEditingSessionData({});
  };

  const handleSaveEdit = async () => {
    if (!editingSessionId || !editingSessionData) return;

    setSaving(true);
    try {
      const { id, createdAt, updatedAt, ...updates } =
        editingSessionData as CourseSession;
      await updateSession(editingSessionId, updates);
      await loadSessions();
      setEditingSessionId(null);
      setEditingSessionData({});
    } catch (error) {
      console.error("Error updating session:", error);
      alert("Failed to update session. Please try again.");
    } finally {
      setSaving(false);
    }
  };

  const handleEditFieldChange = (field: keyof CourseSession, value: any) => {
    setEditingSessionData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleDeleteSession = async (sessionId: string) => {
    if (!window.confirm("Are you sure you want to delete this session?")) {
      return;
    }

    try {
      await deleteSession(sessionId);
      await loadSessions();
    } catch (error) {
      console.error("Error deleting session:", error);
      alert("Failed to delete session. Please try again.");
    }
  };

  const handleQuickStatusUpdate = async (
    sessionId: string,
    newStatus: "scheduled" | "completed" | "cancelled" | "rescheduled"
  ) => {
    try {
      await updateSession(sessionId, { status: newStatus });

      // Update the session locally instead of full reload
      setSessions((prevSessions) =>
        prevSessions.map((session) =>
          session.id === sessionId ? { ...session, status: newStatus } : session
        )
      );
    } catch (error) {
      console.error("Error updating session status:", error);
      alert("Failed to update session status. Please try again.");
      // Only reload on error to get the correct state
      await loadSessions();
    }
  };

  const handleSessionMove = async (sessionId: string, newDate: string) => {
    try {
      await updateSession(sessionId, { date: newDate });

      // Update the session locally instead of full reload to preserve calendar state
      setSessions((prevSessions) =>
        prevSessions.map((session) =>
          session.id === sessionId ? { ...session, date: newDate } : session
        )
      );
    } catch (error) {
      console.error("Error moving session:", error);
      alert("Failed to move session. Please try again.");
      // Only reload on error to get the correct state
      await loadSessions();
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      weekday: "short",
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  };

  const formatTime = (timeString: string) => {
    const [hours, minutes] = timeString.split(":");
    const date = new Date();
    date.setHours(parseInt(hours), parseInt(minutes));
    return date.toLocaleTimeString("en-US", {
      hour: "numeric",
      minute: "2-digit",
      hour12: true,
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "scheduled":
        return "#0a2d69";
      case "completed":
        return "#10b981";
      case "cancelled":
        return "#ef4444";
      case "rescheduled":
        return "#f59e0b";
      default:
        return "#6b7280";
    }
  };

  const isUpcoming = (date: string, startTime: string) => {
    const sessionDateTime = new Date(`${date}T${startTime}`);
    return sessionDateTime > new Date();
  };

  const getDateRangeFilter = (dateRange: string) => {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    switch (dateRange) {
      case "thisWeek": {
        const startOfWeek = new Date(today);
        startOfWeek.setDate(today.getDate() - today.getDay());
        const endOfWeek = new Date(startOfWeek);
        endOfWeek.setDate(startOfWeek.getDate() + 6);
        return { start: startOfWeek, end: endOfWeek };
      }
      case "nextWeek": {
        const startOfNextWeek = new Date(today);
        startOfNextWeek.setDate(today.getDate() + (7 - today.getDay()));
        const endOfNextWeek = new Date(startOfNextWeek);
        endOfNextWeek.setDate(startOfNextWeek.getDate() + 6);
        return { start: startOfNextWeek, end: endOfNextWeek };
      }
      case "thisMonth": {
        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
        const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);
        return { start: startOfMonth, end: endOfMonth };
      }
      case "nextMonth": {
        const startOfNextMonth = new Date(
          now.getFullYear(),
          now.getMonth() + 1,
          1
        );
        const endOfNextMonth = new Date(
          now.getFullYear(),
          now.getMonth() + 2,
          0
        );
        return { start: startOfNextMonth, end: endOfNextMonth };
      }
      default:
        return null;
    }
  };

  const getFilteredSessions = () => {
    let filtered = sessions;

    // Apply time-based filter (upcoming/past/all)
    switch (viewMode) {
      case "upcoming":
        filtered = filtered.filter((session) =>
          isUpcoming(session.date, session.startTime)
        );
        break;
      case "past":
        filtered = filtered.filter(
          (session) => !isUpcoming(session.date, session.startTime)
        );
        break;
      case "all":
      default:
        break;
    }

    // Apply status filter
    if (filters.status !== "all") {
      filtered = filtered.filter(
        (session) => session.status === filters.status
      );
    }

    // Apply date range filter
    if (filters.dateRange !== "all") {
      const dateRange = getDateRangeFilter(filters.dateRange);
      if (dateRange) {
        filtered = filtered.filter((session) => {
          const sessionDate = new Date(session.date);
          return sessionDate >= dateRange.start && sessionDate <= dateRange.end;
        });
      }
    }

    // Apply location filter
    if (filters.location !== "all" && filters.location) {
      filtered = filtered.filter((session) =>
        session.location?.toLowerCase().includes(filters.location.toLowerCase())
      );
    }

    return filtered;
  };

  const filteredSessions = getFilteredSessions();

  if (loading) {
    return (
      <div className="instructor-session-calendar__loading">
        <Spinner size="large" />
        <p>Loading sessions...</p>
      </div>
    );
  }

  return (
    <div className="instructor-session-calendar">
      <div className="instructor-session-calendar__header">
        <div className="instructor-session-calendar__title">
          <h2>
            {selectedCourse
              ? `${selectedCourse.title} - Sessions`
              : "All Sessions"}
          </h2>
          <p className="instructor-session-calendar__subtitle">
            {filteredSessions.length} of {sessions.length} session
            {sessions.length !== 1 ? "s" : ""}{" "}
            {viewMode === "upcoming"
              ? "upcoming"
              : viewMode === "past"
              ? "past"
              : "total"}
          </p>
        </div>
        <div className="instructor-session-calendar__actions">
          <div className="instructor-session-calendar__filters">
            <select
              value={viewMode}
              onChange={(e) => setViewMode(e.target.value as any)}
              className="instructor-session-calendar__filter-select"
            >
              <option value="upcoming">Upcoming Sessions</option>
              <option value="past">Past Sessions</option>
              <option value="all">All Sessions</option>
            </select>

            <select
              value={filters.status}
              onChange={(e) =>
                setFilters({ ...filters, status: e.target.value as any })
              }
              className="instructor-session-calendar__filter-select"
            >
              <option value="all">All Status</option>
              <option value="scheduled">Scheduled</option>
              <option value="completed">Completed</option>
              <option value="cancelled">Cancelled</option>
              <option value="rescheduled">Rescheduled</option>
            </select>

            <select
              value={filters.dateRange}
              onChange={(e) =>
                setFilters({ ...filters, dateRange: e.target.value as any })
              }
              className="instructor-session-calendar__filter-select"
            >
              <option value="all">All Dates</option>
              <option value="thisWeek">This Week</option>
              <option value="nextWeek">Next Week</option>
              <option value="thisMonth">This Month</option>
              <option value="nextMonth">Next Month</option>
            </select>

            <input
              type="text"
              placeholder="Filter by location..."
              value={filters.location === "all" ? "" : filters.location}
              onChange={(e) =>
                setFilters({ ...filters, location: e.target.value || "all" })
              }
              className="instructor-session-calendar__filter-input"
            />

            <select
              value={displayMode}
              onChange={(e) => setDisplayMode(e.target.value as any)}
              className="instructor-session-calendar__filter-select"
            >
              <option value="list">List View</option>
              <option value="calendar">Calendar View</option>
            </select>
          </div>
          {selectedCourse && (
            <>
              <Button onClick={() => setShowAddForm(true)}>Add Session</Button>
              <Button
                variant="secondary"
                onClick={() => setShowAutoGenerateForm(true)}
              >
                Auto-Generate Sessions
              </Button>
            </>
          )}
          {selectedCourse && (
            <Button variant="secondary" onClick={() => onCourseSelect(null)}>
              Back to All Courses
            </Button>
          )}
        </div>
      </div>

      {!selectedCourse && courses.length > 0 && (
        <div className="instructor-session-calendar__course-selector">
          <h3>Select a course to manage sessions:</h3>
          <div className="instructor-session-calendar__course-grid">
            {courses.map((course) => (
              <div
                key={course.courseId}
                className="instructor-session-calendar__course-card"
                onClick={() => onCourseSelect(course)}
              >
                <h4>{course.title}</h4>
                <p>{course.dayTime}</p>
                <div className="instructor-session-calendar__course-stats">
                  <span>{course.enrolled} students</span>
                  <span>
                    {
                      sessions.filter((s) => s.courseId === course.courseId)
                        .length
                    }{" "}
                    sessions
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {showAddForm && selectedCourse && (
        <div className="instructor-session-calendar__add-form">
          <h3>Add New Session</h3>
          <div className="instructor-session-calendar__form-grid">
            <div className="instructor-session-calendar__field">
              <label>Session Number</label>
              <input
                type="number"
                value={newSession.sessionNumber}
                onChange={(e) =>
                  setNewSession({
                    ...newSession,
                    sessionNumber: parseInt(e.target.value),
                  })
                }
                min="1"
              />
            </div>
            <div className="instructor-session-calendar__field">
              <label>Date</label>
              <input
                type="date"
                value={newSession.date}
                onChange={(e) =>
                  setNewSession({ ...newSession, date: e.target.value })
                }
                required
              />
            </div>
            <div className="instructor-session-calendar__field">
              <label>Start Time</label>
              <input
                type="time"
                value={newSession.startTime}
                onChange={(e) =>
                  setNewSession({ ...newSession, startTime: e.target.value })
                }
                required
              />
            </div>
            <div className="instructor-session-calendar__field">
              <label>End Time</label>
              <input
                type="time"
                value={newSession.endTime}
                onChange={(e) =>
                  setNewSession({ ...newSession, endTime: e.target.value })
                }
                required
              />
            </div>
            <div className="instructor-session-calendar__field">
              <label>Topic</label>
              <input
                type="text"
                value={newSession.topic}
                onChange={(e) =>
                  setNewSession({ ...newSession, topic: e.target.value })
                }
                placeholder="Session topic"
              />
            </div>
          </div>
          <div className="instructor-session-calendar__field">
            <label>Description</label>
            <textarea
              value={newSession.description}
              onChange={(e) =>
                setNewSession({ ...newSession, description: e.target.value })
              }
              placeholder="Session description"
              rows={3}
            />
          </div>
          <div className="instructor-session-calendar__form-actions">
            <Button
              onClick={handleAddSession}
              loading={saving}
              disabled={saving}
            >
              {saving ? "Adding..." : "Add Session"}
            </Button>
            <Button
              variant="secondary"
              onClick={() => setShowAddForm(false)}
              disabled={saving}
            >
              Cancel
            </Button>
          </div>
        </div>
      )}

      {showAutoGenerateForm && selectedCourse && (
        <div className="instructor-session-calendar__add-form">
          <h3>Auto-Generate Sessions</h3>
          <div className="instructor-session-calendar__auto-generate-info">
            <p>
              <strong>Course:</strong> {selectedCourse.title}
            </p>
            <p>
              <strong>Start Date:</strong> {selectedCourse.startDate}
            </p>
            <p>
              <strong>Schedule:</strong>{" "}
              {selectedCourse.schedule?.daysOfWeek?.join(", ")} at{" "}
              {selectedCourse.schedule?.time}
            </p>
            <p>
              <strong>Duration:</strong> {selectedCourse.schedule?.duration}{" "}
              minutes
            </p>
          </div>
          <div className="instructor-session-calendar__field">
            <label>Number of Sessions to Generate</label>
            <input
              type="number"
              value={autoGenerateCount}
              onChange={(e) =>
                setAutoGenerateCount(parseInt(e.target.value) || 1)
              }
              min="1"
              max="20"
              placeholder="6"
            />
            <small style={{ color: "#666", fontSize: "0.85rem" }}>
              Sessions will be automatically scheduled based on the course's
              start date and weekly schedule.
            </small>
          </div>
          <div className="instructor-session-calendar__form-actions">
            <Button
              onClick={handleAutoGenerateSessions}
              loading={saving}
              disabled={saving}
            >
              {saving
                ? "Generating..."
                : `Generate ${autoGenerateCount} Sessions`}
            </Button>
            <Button
              variant="secondary"
              onClick={() => setShowAutoGenerateForm(false)}
              disabled={saving}
            >
              Cancel
            </Button>
          </div>
        </div>
      )}

      <div className="instructor-session-calendar__list">
        {displayMode === "calendar" ? (
          <SessionCalendarView
            sessions={filteredSessions}
            courses={courses}
            onSessionClick={(session) => {
              // Could open a session detail modal here
              console.log("Session clicked:", session);
            }}
            onEditSession={handleStartEdit}
            onDeleteSession={handleDeleteSession}
            onAttendanceClick={setShowAttendance}
            onSessionMove={handleSessionMove}
          />
        ) : filteredSessions.length === 0 ? (
          <div className="instructor-session-calendar__empty">
            <p>
              {sessions.length === 0
                ? "No sessions scheduled yet."
                : `No ${
                    viewMode === "upcoming"
                      ? "upcoming"
                      : viewMode === "past"
                      ? "past"
                      : ""
                  } sessions found.`}
            </p>
            {selectedCourse && sessions.length === 0 && (
              <Button onClick={() => setShowAddForm(true)}>
                Add First Session
              </Button>
            )}
          </div>
        ) : (
          <div className="instructor-session-calendar__sessions">
            {filteredSessions.map((session) => {
              // Try multiple ways to find the course info
              const courseInfo = courses.find(
                (c) =>
                  c.courseId === session.courseId ||
                  c.id === session.courseId ||
                  c.slug === session.courseId
              );
              const isEditing = editingSessionId === session.id;
              const editData = isEditing ? editingSessionData : session;

              return (
                <div
                  key={session.id}
                  className={`instructor-session-calendar__session-card ${
                    isUpcoming(session.date, session.startTime)
                      ? "upcoming"
                      : "past"
                  } ${isEditing ? "editing" : ""}`}
                >
                  <div className="instructor-session-calendar__session-header">
                    <div className="instructor-session-calendar__session-info">
                      <h4>
                        {courseInfo
                          ? courseInfo.title
                          : session.courseTitle || "Unknown Course"}
                        {isEditing ? (
                          <span>
                            {" - "}
                            <input
                              type="text"
                              value={editData.topic || ""}
                              onChange={(e) =>
                                handleEditFieldChange("topic", e.target.value)
                              }
                              placeholder="Session topic"
                              style={{
                                border: "1px solid #ddd",
                                padding: "2px 6px",
                                borderRadius: "4px",
                                fontSize: "inherit",
                                fontWeight: "inherit",
                              }}
                            />
                          </span>
                        ) : (
                          session.topic && ` - ${session.topic}`
                        )}
                      </h4>
                      <p className="instructor-session-calendar__session-number">
                        Session{" "}
                        {isEditing ? (
                          <input
                            type="number"
                            value={editData.sessionNumber || 1}
                            onChange={(e) =>
                              handleEditFieldChange(
                                "sessionNumber",
                                parseInt(e.target.value)
                              )
                            }
                            min="1"
                            style={{
                              border: "1px solid #ddd",
                              padding: "2px 6px",
                              borderRadius: "4px",
                              width: "60px",
                            }}
                          />
                        ) : (
                          session.sessionNumber
                        )}
                      </p>
                    </div>
                    <div className="instructor-session-calendar__status-controls">
                      <select
                        value={isEditing ? editData.status : session.status}
                        onChange={(e) => {
                          if (isEditing) {
                            handleEditFieldChange("status", e.target.value);
                          } else {
                            handleQuickStatusUpdate(
                              session.id,
                              e.target.value as any
                            );
                          }
                        }}
                        className="instructor-session-calendar__status-select"
                        style={{
                          backgroundColor: getStatusColor(
                            isEditing
                              ? editData.status || session.status
                              : session.status
                          ),
                          color: "white",
                          border: "none",
                        }}
                      >
                        <option value="scheduled">Scheduled</option>
                        <option value="completed">Completed</option>
                        <option value="cancelled">Cancelled</option>
                        <option value="rescheduled">Rescheduled</option>
                      </select>
                    </div>
                  </div>
                  <div className="instructor-session-calendar__session-details">
                    <div className="instructor-session-calendar__session-time">
                      <span className="instructor-session-calendar__date">
                        {isEditing ? (
                          <input
                            type="date"
                            value={editData.date || ""}
                            onChange={(e) =>
                              handleEditFieldChange("date", e.target.value)
                            }
                            style={{
                              border: "1px solid #ddd",
                              padding: "2px 6px",
                              borderRadius: "4px",
                            }}
                          />
                        ) : (
                          formatDate(session.date)
                        )}
                      </span>
                      <span className="instructor-session-calendar__time">
                        {isEditing ? (
                          <>
                            <input
                              type="time"
                              value={editData.startTime || ""}
                              onChange={(e) =>
                                handleEditFieldChange(
                                  "startTime",
                                  e.target.value
                                )
                              }
                              style={{
                                border: "1px solid #ddd",
                                padding: "2px 6px",
                                borderRadius: "4px",
                                marginRight: "4px",
                              }}
                            />
                            {" - "}
                            <input
                              type="time"
                              value={editData.endTime || ""}
                              onChange={(e) =>
                                handleEditFieldChange("endTime", e.target.value)
                              }
                              style={{
                                border: "1px solid #ddd",
                                padding: "2px 6px",
                                borderRadius: "4px",
                                marginLeft: "4px",
                              }}
                            />
                          </>
                        ) : (
                          `${formatTime(session.startTime)} - ${formatTime(
                            session.endTime
                          )}`
                        )}
                      </span>
                    </div>

                    {isEditing ? (
                      <>
                        <div className="instructor-session-calendar__edit-field">
                          <label>Description:</label>
                          <textarea
                            value={editData.description || ""}
                            onChange={(e) =>
                              handleEditFieldChange(
                                "description",
                                e.target.value
                              )
                            }
                            placeholder="Session description"
                            rows={2}
                            style={{
                              border: "1px solid #ddd",
                              padding: "6px",
                              borderRadius: "4px",
                              width: "100%",
                              resize: "vertical",
                            }}
                          />
                        </div>
                        <div className="instructor-session-calendar__edit-field">
                          <label>Instructor Notes:</label>
                          <textarea
                            value={editData.instructorNotes || ""}
                            onChange={(e) =>
                              handleEditFieldChange(
                                "instructorNotes",
                                e.target.value
                              )
                            }
                            placeholder="Private notes for this session"
                            rows={2}
                            style={{
                              border: "1px solid #ddd",
                              padding: "6px",
                              borderRadius: "4px",
                              width: "100%",
                              resize: "vertical",
                            }}
                          />
                        </div>
                      </>
                    ) : (
                      <>
                        {session.description && (
                          <div className="instructor-session-calendar__description">
                            {session.description}
                          </div>
                        )}
                        {session.instructorNotes && (
                          <div className="instructor-session-calendar__notes">
                            <strong>Notes:</strong> {session.instructorNotes}
                          </div>
                        )}
                      </>
                    )}
                  </div>
                  <div className="instructor-session-calendar__session-actions">
                    {isEditing ? (
                      <>
                        <Button
                          variant="primary"
                          size="small"
                          onClick={handleSaveEdit}
                          loading={saving}
                          disabled={saving}
                        >
                          {saving ? "Saving..." : "Save"}
                        </Button>
                        <Button
                          variant="secondary"
                          size="small"
                          onClick={handleCancelEdit}
                          disabled={saving}
                        >
                          Cancel
                        </Button>
                      </>
                    ) : (
                      <>
                        <Button
                          variant="secondary"
                          size="small"
                          onClick={() => {
                            console.log(
                              "🎯 Attendance button clicked for session:",
                              session
                            );
                            console.log("🎯 Session ID:", session.id);
                            console.log("🎯 Selected course:", selectedCourse);
                            setShowAttendance(session);
                            console.log(
                              "🎯 showAttendance state set to:",
                              session
                            );
                          }}
                        >
                          Attendance
                        </Button>
                        <Button
                          variant="secondary"
                          size="small"
                          onClick={() => handleStartEdit(session)}
                        >
                          Edit
                        </Button>
                        <Button
                          variant="secondary"
                          size="small"
                          onClick={() => handleDeleteSession(session.id)}
                        >
                          Delete
                        </Button>
                      </>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* Session Attendance Modal */}
      {(() => {
        console.log("🎯 Modal render check:");
        console.log("🎯 showAttendance:", showAttendance);
        console.log("🎯 selectedCourse:", selectedCourse);

        if (showAttendance) {
          // Find the course for this session
          const sessionCourse =
            selectedCourse ||
            courses.find(
              (course) => course.courseId === showAttendance.courseId
            );

          console.log("🎯 Session courseId:", showAttendance.courseId);
          console.log("🎯 Found session course:", sessionCourse);
          console.log(
            "🎯 Available courses:",
            courses.map((c) => ({ id: c.courseId, title: c.title }))
          );

          if (sessionCourse) {
            const enrolledStudents =
              sessionCourse.registrations?.filter(
                (reg: any) => reg.enrollmentStatus === "enrolled"
              ) || [];
            console.log(
              "🎯 Enrolled students for attendance:",
              enrolledStudents
            );

            return (
              <div className="instructor-session-calendar__modal-overlay">
                <div className="instructor-session-calendar__modal">
                  <SessionAttendance
                    session={showAttendance}
                    students={enrolledStudents}
                    onClose={() => {
                      console.log("🎯 Closing attendance modal");
                      setShowAttendance(null);
                    }}
                    onRefresh={() => {
                      console.log("🎯 Refreshing after attendance save");
                      loadSessions();
                      onRefresh();
                    }}
                  />
                </div>
              </div>
            );
          } else {
            console.log(
              "❌ No course found for session - cannot show attendance modal"
            );
            console.log(
              "💡 This might happen if course data hasn't loaded yet"
            );
          }
        }
        return null;
      })()}
    </div>
  );
};

export default InstructorSessionCalendar;
