.image-upload {
  width: 100%;
}

.image-upload__dropzone {
  position: relative;
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  background: #fafafa;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-upload__dropzone:hover {
  border-color: #3b82f6;
  background: #f8faff;
}

.image-upload__dropzone--drag-over {
  border-color: #3b82f6;
  background: #eff6ff;
  transform: scale(1.02);
}

.image-upload__dropzone--disabled {
  cursor: not-allowed;
  opacity: 0.6;
  background: #f5f5f5;
}

.image-upload__dropzone--disabled:hover {
  border-color: #d1d5db;
  background: #f5f5f5;
  transform: none;
}

.image-upload__dropzone--has-image {
  padding: 0;
  border: 1px solid #e5e7eb;
  background: transparent;
}

.image-upload__input {
  display: none;
}

.image-upload__placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.image-upload__icon {
  font-size: 3rem;
  opacity: 0.5;
}

.image-upload__text {
  margin: 0;
  font-size: 1rem;
  color: #374151;
  font-weight: 500;
}

.image-upload__hint {
  margin: 0;
  font-size: 0.875rem;
  color: #6b7280;
}

.image-upload__preview {
  position: relative;
  width: 100%;
  height: 200px;
  border-radius: 8px;
  overflow: hidden;
}

.image-upload__preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.image-upload__overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s ease;
  color: white;
  font-weight: 500;
}

.image-upload__preview:hover .image-upload__overlay {
  opacity: 1;
}

.image-upload__remove-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  font-size: 20px;
  line-height: 1;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  z-index: 2;
}

.image-upload__remove-btn:hover {
  background: rgba(220, 38, 38, 0.8);
  transform: scale(1.1);
}

.image-upload__progress {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  padding: 1rem;
  border-radius: 0 0 8px 8px;
}

.image-upload__progress-bar {
  width: 100%;
  height: 8px;
  background: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.image-upload__progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.image-upload__progress-text {
  font-size: 0.875rem;
  color: #374151;
  font-weight: 500;
}

.image-upload__error {
  margin-top: 0.5rem;
  padding: 0.75rem;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 6px;
  color: #dc2626;
  font-size: 0.875rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .image-upload__dropzone {
    padding: 1.5rem;
    min-height: 150px;
  }
  
  .image-upload__preview {
    height: 150px;
  }
  
  .image-upload__icon {
    font-size: 2rem;
  }
  
  .image-upload__text {
    font-size: 0.875rem;
  }
  
  .image-upload__hint {
    font-size: 0.75rem;
  }
}

/* Animation for successful upload */
@keyframes uploadSuccess {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.image-upload__dropzone--upload-success {
  animation: uploadSuccess 0.5s ease;
  border-color: #10b981;
  background: #f0fdf4;
}
