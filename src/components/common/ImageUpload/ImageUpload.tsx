import React, { useState, useRef, DragEvent, ChangeEvent } from "react";
import {
  uploadImageToS3,
  validateImageFile,
  S3_FOLDERS,
} from "../../../services/s3ImageUploadService";
import "./ImageUpload.css";

interface ImageUploadProps {
  onImageUploaded: (url: string) => void;
  currentImageUrl?: string;
  folder?: string;
  placeholder?: string;
  disabled?: boolean;
  allowStaticPath?: boolean;
}

const ImageUpload: React.FC<ImageUploadProps> = ({
  onImageUploaded,
  currentImageUrl,
  folder = S3_FOLDERS.COURSES,
  placeholder = "Drag & drop an image here, or click to browse",
  disabled = false,
}) => {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [isDragOver, setIsDragOver] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(
    currentImageUrl || null
  );

  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = async (file: File) => {
    if (disabled) return;

    setError(null);

    // Validate file
    const validation = validateImageFile(file);
    if (!validation.valid) {
      setError(validation.error || "Invalid file");
      return;
    }

    // Show preview immediately
    const objectUrl = URL.createObjectURL(file);
    setPreviewUrl(objectUrl);

    // Start upload
    setIsUploading(true);
    setUploadProgress(0);

    try {
      // Simulate progress for better UX
      const progressInterval = setInterval(() => {
        setUploadProgress((prev) => Math.min(prev + 10, 90));
      }, 100);

      const result = await uploadImageToS3(file, folder);

      clearInterval(progressInterval);
      setUploadProgress(100);

      if (result.success && result.url) {
        // Clean up object URL
        URL.revokeObjectURL(objectUrl);
        setPreviewUrl(result.url);
        onImageUploaded(result.url);

        setTimeout(() => {
          setIsUploading(false);
          setUploadProgress(0);
        }, 500);
      } else {
        setError(result.error || "Upload failed");
        setPreviewUrl(currentImageUrl || null);
        setIsUploading(false);
        setUploadProgress(0);
      }
    } catch (error) {
      setError("Upload failed. Please try again.");
      setPreviewUrl(currentImageUrl || null);
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  const handleDragOver = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    if (!disabled) {
      setIsDragOver(true);
    }
  };

  const handleDragLeave = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragOver(false);

    if (disabled) return;

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleFileInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleClick = () => {
    if (!disabled && fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleRemoveImage = () => {
    if (disabled) return;

    setPreviewUrl(null);
    onImageUploaded("");
    setError(null);

    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  return (
    <div className="image-upload">
      <div
        className={`image-upload__dropzone ${
          isDragOver ? "image-upload__dropzone--drag-over" : ""
        } ${disabled ? "image-upload__dropzone--disabled" : ""} ${
          previewUrl ? "image-upload__dropzone--has-image" : ""
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={handleClick}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleFileInputChange}
          className="image-upload__input"
          disabled={disabled}
        />

        {previewUrl ? (
          <div className="image-upload__preview">
            <img
              src={previewUrl}
              alt="Preview"
              className="image-upload__preview-image"
            />
            {!disabled && (
              <button
                type="button"
                onClick={(e) => {
                  e.stopPropagation();
                  handleRemoveImage();
                }}
                className="image-upload__remove-btn"
                title="Remove image"
              >
                ×
              </button>
            )}
            {!disabled && (
              <div className="image-upload__overlay">
                <span>Click to change image</span>
              </div>
            )}
          </div>
        ) : (
          <div className="image-upload__placeholder">
            <div className="image-upload__icon">📷</div>
            <p className="image-upload__text">{placeholder}</p>
            <p className="image-upload__hint">
              Supports JPG, PNG, GIF, WebP (max 5MB)
            </p>
          </div>
        )}

        {isUploading && (
          <div className="image-upload__progress">
            <div className="image-upload__progress-bar">
              <div
                className="image-upload__progress-fill"
                style={{ width: `${uploadProgress}%` }}
              />
            </div>
            <span className="image-upload__progress-text">
              Uploading... {uploadProgress}%
            </span>
          </div>
        )}
      </div>

      {error && <div className="image-upload__error">{error}</div>}
    </div>
  );
};

export default ImageUpload;
