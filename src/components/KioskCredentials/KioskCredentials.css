.kiosk-credentials {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 1.5rem;
  margin-top: 1rem;
}

.kiosk-credentials__header h3 {
  margin: 0 0 0.5rem 0;
  color: #0a2d69;
  font-size: 1.1rem;
}

.kiosk-credentials__header p {
  margin: 0 0 1rem 0;
  color: #666;
  font-size: 0.9rem;
}

.kiosk-credentials__current {
  background: #e8f4fd;
  border: 1px solid #b3d9ff;
  border-radius: 6px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.kiosk-credentials__info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.kiosk-credentials__info strong {
  color: #0a2d69;
  font-size: 0.9rem;
}

.kiosk-email {
  font-family: "Courier New", monospace;
  background: #fff;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  border: 1px solid #ddd;
  font-size: 0.85rem;
  color: #333;
}

.kiosk-credentials__note {
  margin: 0;
  color: #666;
  font-size: 0.85rem;
  font-style: italic;
}

.kiosk-credentials__empty {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 6px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.kiosk-credentials__empty p {
  margin: 0;
  color: #856404;
  font-size: 0.9rem;
}

.kiosk-credentials__error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
  border-radius: 6px;
  padding: 0.75rem;
  margin-bottom: 1rem;
  font-size: 0.9rem;
}

.kiosk-credentials__success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
  border-radius: 6px;
  padding: 0.75rem;
  margin-bottom: 1rem;
  font-size: 0.9rem;
}

.kiosk-credentials__form {
  margin-bottom: 1rem;
}

.kiosk-credentials__field {
  margin-bottom: 1rem;
}

.kiosk-credentials__field label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #333;
  font-size: 0.9rem;
}

.kiosk-credentials__field input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 0.9rem;
  box-sizing: border-box;
  transition: border-color 0.2s ease;
}

.kiosk-credentials__field input:focus {
  outline: none;
  border-color: #0a2d69;
  box-shadow: 0 0 0 2px rgba(10, 45, 105, 0.1);
}

.kiosk-credentials__field input:disabled {
  background-color: #f8f9fa;
  cursor: not-allowed;
  opacity: 0.6;
}

.kiosk-credentials__field small {
  display: block;
  margin-top: 0.25rem;
  color: #666;
  font-size: 0.8rem;
  font-style: italic;
}

.kiosk-credentials__actions {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.kiosk-credentials__instructions {
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid #e9ecef;
}

.kiosk-credentials__instructions h4 {
  margin: 0 0 0.75rem 0;
  color: #0a2d69;
  font-size: 1rem;
}

.kiosk-credentials__instructions ol {
  margin: 0;
  padding-left: 1.5rem;
  color: #666;
}

.kiosk-credentials__instructions li {
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  line-height: 1.4;
}

.kiosk-credentials__instructions code {
  background: #f8f9fa;
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  font-family: "Courier New", monospace;
  font-size: 0.85rem;
  color: #0a2d69;
  border: 1px solid #e9ecef;
}

@media (max-width: 768px) {
  .kiosk-credentials {
    padding: 1rem;
  }

  .kiosk-credentials__actions {
    flex-direction: column;
  }

  .kiosk-credentials__info {
    gap: 0.25rem;
  }

  .kiosk-email {
    font-size: 0.8rem;
    word-break: break-all;
  }
}
