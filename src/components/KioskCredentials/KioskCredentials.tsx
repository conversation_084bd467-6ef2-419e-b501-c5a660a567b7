import React, { useState } from "react";
import { Location } from "../../types";
import {
  setupKioskCredentials,
  checkKioskUsernameAvailability,
} from "../../services/adminBuddyFirebaseService";
import Button from "../ui/Button";
import "./KioskCredentials.css";

interface KioskCredentialsProps {
  location: Location;
  tenantId: string;
  onUpdate: () => void;
}

const KioskCredentials: React.FC<KioskCredentialsProps> = ({
  location,
  tenantId,
  onUpdate,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [username, setUsername] = useState(location.kioskUsername || "");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");

  // Track the current displayed username (updated immediately on success)
  const [currentDisplayUsername, setCurrentDisplayUsername] = useState(
    location.kioskUsername || ""
  );

  const handleSetupCredentials = async () => {
    if (!username.trim()) {
      setError("Username is required");
      return;
    }

    if (username.length < 4 || username.length > 15) {
      setError("Username must be between 4 and 15 characters");
      return;
    }

    if (!/^[a-zA-Z0-9_-]+$/.test(username)) {
      setError(
        "Username can only contain letters, numbers, hyphens, and underscores"
      );
      return;
    }

    if (!password.trim()) {
      setError("Password is required");
      return;
    }

    if (password !== confirmPassword) {
      setError("Passwords do not match");
      return;
    }

    if (password.length < 6) {
      setError("Password must be at least 6 characters");
      return;
    }

    setLoading(true);
    setError("");
    setSuccess("");

    try {
      // Check username availability first
      const availability = await checkKioskUsernameAvailability(
        tenantId,
        username.trim(),
        location.id
      );
      if (!availability.available) {
        setError(availability.error || "Username is not available");
        setLoading(false);
        return;
      }

      const result = await setupKioskCredentials(
        tenantId,
        location.id,
        username.trim(),
        password
      );

      if (result.success) {
        const newUsername = result.username || username.trim();
        setSuccess(`Kiosk credentials updated! Username: ${newUsername}`);

        // Update both the form username and display username immediately
        setUsername(newUsername);
        setCurrentDisplayUsername(newUsername);

        setPassword("");
        setConfirmPassword("");
        setIsEditing(false);
        onUpdate();
      } else {
        setError(result.error || "Failed to setup kiosk credentials");
      }
    } catch (error: any) {
      setError(error.message || "Failed to setup kiosk credentials");
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setIsEditing(false);
    setUsername(location.kioskUsername || "");
    setPassword("");
    setConfirmPassword("");
    setError("");
    setSuccess("");
  };

  return (
    <div className="kiosk-credentials">
      <div className="kiosk-credentials__header">
        <h3>Kiosk Access</h3>
        <p>
          Set up dedicated credentials for employees to access this location's
          kiosk
        </p>
      </div>

      {currentDisplayUsername && !isEditing && (
        <div className="kiosk-credentials__current">
          <div className="kiosk-credentials__info">
            <strong>Kiosk Username:</strong>
            <span className="kiosk-email">{currentDisplayUsername}</span>
          </div>
          <p className="kiosk-credentials__note">
            Employees can use this username and password to access the kiosk for
            this location.
          </p>
        </div>
      )}

      {!currentDisplayUsername && !isEditing && (
        <div className="kiosk-credentials__empty">
          <p>
            No kiosk credentials configured. Set up credentials to allow
            employees to access the kiosk.
          </p>
        </div>
      )}

      {error && <div className="kiosk-credentials__error">{error}</div>}
      {success && <div className="kiosk-credentials__success">{success}</div>}

      {isEditing ? (
        <div className="kiosk-credentials__form">
          <div className="kiosk-credentials__field">
            <label htmlFor="kiosk-username">Kiosk Username</label>
            <input
              type="text"
              id="kiosk-username"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              placeholder="Enter username (4-15 characters)"
              disabled={loading}
              maxLength={15}
            />
            <small>
              Username can only contain letters, numbers, hyphens, and
              underscores
            </small>
          </div>

          <div className="kiosk-credentials__field">
            <label htmlFor="kiosk-password">Kiosk Password</label>
            <input
              type="password"
              id="kiosk-password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="Enter password for kiosk access"
              disabled={loading}
            />
          </div>

          <div className="kiosk-credentials__field">
            <label htmlFor="kiosk-confirm-password">Confirm Password</label>
            <input
              type="password"
              id="kiosk-confirm-password"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              placeholder="Confirm password"
              disabled={loading}
            />
          </div>

          <div className="kiosk-credentials__actions">
            <Button
              variant="primary"
              onClick={handleSetupCredentials}
              loading={loading}
              disabled={loading}
            >
              {currentDisplayUsername
                ? "Update Credentials"
                : "Setup Credentials"}
            </Button>
            <Button
              variant="secondary"
              onClick={handleCancel}
              disabled={loading}
            >
              Cancel
            </Button>
          </div>
        </div>
      ) : (
        <div className="kiosk-credentials__actions">
          <Button variant="primary" onClick={() => setIsEditing(true)}>
            {currentDisplayUsername
              ? "Update Kiosk Credentials"
              : "Setup Kiosk Access"}
          </Button>
        </div>
      )}

      {currentDisplayUsername && (
        <div className="kiosk-credentials__instructions">
          <h4>For Employees:</h4>
          <ol>
            <li>Go to the kiosk login page</li>
            <li>
              Enter username: <code>{currentDisplayUsername}</code>
            </li>
            <li>Enter the password you set above</li>
            <li>Access tasks for {location.name}</li>
          </ol>
        </div>
      )}
    </div>
  );
};

export default KioskCredentials;
