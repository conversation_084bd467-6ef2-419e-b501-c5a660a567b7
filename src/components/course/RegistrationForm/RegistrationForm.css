.registration-form-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: 1rem;
}

.registration-form {
  background: white;
  border-radius: 16px;
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  position: relative;
}

.registration-form__header {
  background: linear-gradient(135deg, #0a2d69 0%, #1e4a8c 100%);
  color: white;
  padding: 2rem;
  border-radius: 16px 16px 0 0;
  position: relative;
}

.registration-form__header h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  color: white;
}

.registration-form__price {
  font-size: 1.2rem;
  color: #ffd46f;
  font-weight: 600;
  margin: 0;
}

.registration-form__waitlist-notice {
  background: #fff3cd;
  color: #856404;
  padding: 0.75rem;
  border-radius: 6px;
  margin: 1rem 0 0 0;
  font-size: 0.9rem;
  border-left: 4px solid #ffa300;
}

.registration-form__close {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  border: none;
  color: white;
  font-size: 2rem;
  cursor: pointer;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.registration-form__close:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.registration-form__form {
  padding: 2rem;
}

.registration-form__section {
  margin-bottom: 2rem;
}

.registration-form__section h3 {
  color: #0a2d69;
  margin: 0 0 1.5rem 0;
  font-size: 1.2rem;
  border-bottom: 2px solid #ffa300;
  padding-bottom: 0.5rem;
}

.registration-form__section-note {
  color: #6b7280;
  font-size: 0.9rem;
  margin: -1rem 0 1.5rem 0;
  font-style: italic;
}

.registration-form__row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1rem;
}

@media (max-width: 600px) {
  .registration-form__row {
    grid-template-columns: 1fr;
  }
}

.registration-form__field {
  margin-bottom: 1rem;
}

.registration-form__field label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #0a2d69;
}

.registration-form__field input,
.registration-form__field select,
.registration-form__field textarea {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e8f4f8;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
  box-sizing: border-box;
}

.registration-form__field input:focus,
.registration-form__field select:focus,
.registration-form__field textarea:focus {
  outline: none;
  border-color: #ffa300;
}

.registration-form__field input.error,
.registration-form__field select.error,
.registration-form__field textarea.error {
  border-color: #dc3545;
}

.registration-form__field input:disabled,
.registration-form__field select:disabled,
.registration-form__field textarea:disabled {
  background-color: #f8f9fa;
  cursor: not-allowed;
}

.registration-form__error {
  color: #dc3545;
  font-size: 0.875rem;
  margin-top: 0.25rem;
  display: block;
}

.registration-form__actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #e8f4f8;
}

@media (max-width: 600px) {
  .registration-form__actions {
    flex-direction: column-reverse;
  }
}

.registration-form__note {
  background: #f8f9fa;
  padding: 1.5rem 2rem;
  border-radius: 0 0 16px 16px;
  border-top: 1px solid #e8f4f8;
}

.registration-form__note p {
  margin: 0;
  font-size: 0.9rem;
  line-height: 1.5;
  color: #555;
}

.registration-form__note strong {
  color: #0a2d69;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .registration-form-overlay {
    padding: 0.5rem;
  }

  .registration-form {
    max-height: 95vh;
  }

  .registration-form__header {
    padding: 1.5rem;
  }

  .registration-form__header h2 {
    font-size: 1.3rem;
  }

  .registration-form__form {
    padding: 1.5rem;
  }

  .registration-form__note {
    padding: 1rem 1.5rem;
  }
}

/* Animation */
.registration-form-overlay {
  animation: fadeIn 0.3s ease;
}

.registration-form {
  animation: slideUp 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
