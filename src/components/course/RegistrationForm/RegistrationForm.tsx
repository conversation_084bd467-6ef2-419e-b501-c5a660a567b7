import React, { useState } from "react";
import Button from "../../ui/Button";
import "./RegistrationForm.css";

interface RegistrationFormProps {
  courseTitle: string;
  coursePrice: string;
  onSubmit: (formData: RegistrationData) => void;
  onCancel: () => void;
  isSubmitting?: boolean;
  isWaitlist?: boolean;
}

export interface RegistrationData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  experienceLevel: string;
  specialRequirements: string;
  emergencyContactName: string;
  emergencyContactPhone: string;
  emergencyContactRelationship: string;
}

const RegistrationForm: React.FC<RegistrationFormProps> = ({
  courseTitle,
  coursePrice,
  onSubmit,
  onCancel,
  isSubmitting = false,
  isWaitlist = false,
}) => {
  const [formData, setFormData] = useState<RegistrationData>({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    experienceLevel: "",
    specialRequirements: "",
    emergencyContactName: "",
    emergencyContactPhone: "",
    emergencyContactRelationship: "",
  });

  const [errors, setErrors] = useState<Partial<RegistrationData>>({});

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // Clear error when user starts typing
    if (errors[name as keyof RegistrationData]) {
      setErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<RegistrationData> = {};

    if (!formData.firstName.trim()) {
      newErrors.firstName = "First name is required";
    }

    if (!formData.lastName.trim()) {
      newErrors.lastName = "Last name is required";
    }

    if (!formData.email.trim()) {
      newErrors.email = "Email is required";
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Please enter a valid email address";
    }

    if (!formData.phone.trim()) {
      newErrors.phone = "Phone number is required";
    }

    if (!formData.experienceLevel) {
      newErrors.experienceLevel = "Please select your experience level";
    }

    if (!formData.emergencyContactName.trim()) {
      newErrors.emergencyContactName = "Emergency contact name is required";
    }

    if (!formData.emergencyContactPhone.trim()) {
      newErrors.emergencyContactPhone = "Emergency contact phone is required";
    }

    if (!formData.emergencyContactRelationship.trim()) {
      newErrors.emergencyContactRelationship =
        "Emergency contact relationship is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (validateForm()) {
      onSubmit(formData);
    }
  };

  return (
    <div className="registration-form-overlay">
      <div className="registration-form">
        <div className="registration-form__header">
          <h2>
            {isWaitlist ? "Join Waitlist for" : "Register for"} {courseTitle}
          </h2>
          <p className="registration-form__price">Course Fee: {coursePrice}</p>
          {isWaitlist && (
            <p className="registration-form__waitlist-notice">
              This course is currently full. You'll be added to the waitlist and
              contacted if a spot becomes available.
            </p>
          )}
          <button
            className="registration-form__close"
            onClick={onCancel}
            aria-label="Close registration form"
          >
            ×
          </button>
        </div>

        <form onSubmit={handleSubmit} className="registration-form__form">
          <div className="registration-form__section">
            <h3>Student Information</h3>

            <div className="registration-form__row">
              <div className="registration-form__field">
                <label htmlFor="firstName">First Name *</label>
                <input
                  type="text"
                  id="firstName"
                  name="firstName"
                  value={formData.firstName}
                  onChange={handleInputChange}
                  className={errors.firstName ? "error" : ""}
                  disabled={isSubmitting}
                />
                {errors.firstName && (
                  <span className="registration-form__error">
                    {errors.firstName}
                  </span>
                )}
              </div>

              <div className="registration-form__field">
                <label htmlFor="lastName">Last Name *</label>
                <input
                  type="text"
                  id="lastName"
                  name="lastName"
                  value={formData.lastName}
                  onChange={handleInputChange}
                  className={errors.lastName ? "error" : ""}
                  disabled={isSubmitting}
                />
                {errors.lastName && (
                  <span className="registration-form__error">
                    {errors.lastName}
                  </span>
                )}
              </div>
            </div>

            <div className="registration-form__field">
              <label htmlFor="email">Email Address *</label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                className={errors.email ? "error" : ""}
                disabled={isSubmitting}
              />
              {errors.email && (
                <span className="registration-form__error">{errors.email}</span>
              )}
            </div>

            <div className="registration-form__field">
              <label htmlFor="phone">Phone Number *</label>
              <input
                type="tel"
                id="phone"
                name="phone"
                value={formData.phone}
                onChange={handleInputChange}
                className={errors.phone ? "error" : ""}
                disabled={isSubmitting}
                placeholder="(*************"
              />
              {errors.phone && (
                <span className="registration-form__error">{errors.phone}</span>
              )}
            </div>

            <div className="registration-form__field">
              <label htmlFor="experienceLevel">Programming Experience *</label>
              <select
                id="experienceLevel"
                name="experienceLevel"
                value={formData.experienceLevel}
                onChange={handleInputChange}
                className={errors.experienceLevel ? "error" : ""}
                disabled={isSubmitting}
              >
                <option value="">Select your experience level</option>
                <option value="complete-beginner">Complete Beginner</option>
                <option value="some-experience">Some Experience</option>
                <option value="intermediate">Intermediate</option>
                <option value="advanced">Advanced</option>
              </select>
              {errors.experienceLevel && (
                <span className="registration-form__error">
                  {errors.experienceLevel}
                </span>
              )}
            </div>

            <div className="registration-form__field">
              <label htmlFor="specialRequirements">
                Special Requirements or Questions
              </label>
              <textarea
                id="specialRequirements"
                name="specialRequirements"
                value={formData.specialRequirements}
                onChange={handleInputChange}
                disabled={isSubmitting}
                rows={3}
                placeholder="Any accessibility needs, dietary restrictions, or questions about the course..."
              />
            </div>
          </div>

          <div className="registration-form__section">
            <h3>Emergency Contact Information</h3>
            <p className="registration-form__section-note">
              Required for safety purposes during the course.
            </p>

            <div className="registration-form__field">
              <label htmlFor="emergencyContactName">
                Emergency Contact Name *
              </label>
              <input
                type="text"
                id="emergencyContactName"
                name="emergencyContactName"
                value={formData.emergencyContactName}
                onChange={handleInputChange}
                className={errors.emergencyContactName ? "error" : ""}
                disabled={isSubmitting}
                placeholder="Full name of emergency contact"
              />
              {errors.emergencyContactName && (
                <span className="registration-form__error">
                  {errors.emergencyContactName}
                </span>
              )}
            </div>

            <div className="registration-form__row">
              <div className="registration-form__field">
                <label htmlFor="emergencyContactPhone">
                  Emergency Contact Phone *
                </label>
                <input
                  type="tel"
                  id="emergencyContactPhone"
                  name="emergencyContactPhone"
                  value={formData.emergencyContactPhone}
                  onChange={handleInputChange}
                  className={errors.emergencyContactPhone ? "error" : ""}
                  disabled={isSubmitting}
                  placeholder="(*************"
                />
                {errors.emergencyContactPhone && (
                  <span className="registration-form__error">
                    {errors.emergencyContactPhone}
                  </span>
                )}
              </div>

              <div className="registration-form__field">
                <label htmlFor="emergencyContactRelationship">
                  Relationship *
                </label>
                <select
                  id="emergencyContactRelationship"
                  name="emergencyContactRelationship"
                  value={formData.emergencyContactRelationship}
                  onChange={handleInputChange}
                  className={errors.emergencyContactRelationship ? "error" : ""}
                  disabled={isSubmitting}
                >
                  <option value="">Select relationship</option>
                  <option value="parent">Parent</option>
                  <option value="guardian">Guardian</option>
                  <option value="grandparent">Grandparent</option>
                  <option value="sibling">Sibling</option>
                  <option value="aunt-uncle">Aunt/Uncle</option>
                  <option value="family-friend">Family Friend</option>
                  <option value="other">Other</option>
                </select>
                {errors.emergencyContactRelationship && (
                  <span className="registration-form__error">
                    {errors.emergencyContactRelationship}
                  </span>
                )}
              </div>
            </div>
          </div>

          <div className="registration-form__actions">
            <Button
              type="button"
              variant="secondary"
              onClick={onCancel}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button type="submit" variant="primary" disabled={isSubmitting}>
              {isSubmitting
                ? "Submitting..."
                : isWaitlist
                ? "Join Waitlist"
                : "Register Now"}
            </Button>
          </div>
        </form>

        <div className="registration-form__note">
          <p>
            <strong>Next Steps:</strong> After submitting this form, you'll
            receive an email with{" "}
            {isWaitlist
              ? "waitlist confirmation"
              : "payment instructions and course details"}
            .
            {!isWaitlist &&
              " Payment is required to secure your spot in the course."}
            {isWaitlist && " We'll contact you if a spot becomes available."}
          </p>
        </div>
      </div>
    </div>
  );
};

export default RegistrationForm;
