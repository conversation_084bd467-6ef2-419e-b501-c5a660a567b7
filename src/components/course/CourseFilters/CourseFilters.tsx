import React from "react";
import "./CourseFilters.css";

interface CourseFiltersProps {
  availableCategories: string[];
  selectedCategories: string[];
  onCategoryToggle: (category: string) => void;
  onClearFilters: () => void;
  courseCount: number;
  totalCourses: number;
}

const CourseFilters: React.FC<CourseFiltersProps> = ({
  availableCategories,
  selectedCategories,
  onCategoryToggle,
  onClearFilters,
  courseCount,
  totalCourses,
}) => {
  // Group categories by type
  const subjectCategories = availableCategories.filter(cat =>
    !["Introductory", "Foundational", "Intermediate", "Advanced"].includes(cat)
  );

  const difficultyCategories = availableCategories.filter(cat =>
    ["Introductory", "Foundational", "Intermediate", "Advanced"].includes(cat)
  );

  return (
    <div className="course-filters">
      <div className="course-filters__header">
        <h3>Filter Courses</h3>
        <div className="course-filters__count">
          Showing {courseCount} of {totalCourses} courses
        </div>
      </div>

      <div className="course-filters__content">
        {subjectCategories.length > 0 && (
          <div className="course-filters__group">
            <h4>Subject Areas</h4>
            <div className="course-filters__pills">
              {subjectCategories.map((category) => (
                <button
                  key={category}
                  className={`course-filters__pill ${
                    selectedCategories.includes(category) ? "course-filters__pill--active" : ""
                  }`}
                  onClick={() => onCategoryToggle(category)}
                >
                  {category}
                </button>
              ))}
            </div>
          </div>
        )}

        {difficultyCategories.length > 0 && (
          <div className="course-filters__group">
            <h4>Difficulty Level</h4>
            <div className="course-filters__pills">
              {difficultyCategories.map((category) => (
                <button
                  key={category}
                  className={`course-filters__pill ${
                    selectedCategories.includes(category) ? "course-filters__pill--active" : ""
                  }`}
                  onClick={() => onCategoryToggle(category)}
                >
                  {category}
                </button>
              ))}
            </div>
          </div>
        )}

        <div className="course-filters__actions">
          <button
            className={`course-filters__clear ${
              selectedCategories.length === 0 ? "course-filters__clear--disabled" : ""
            }`}
            onClick={onClearFilters}
            disabled={selectedCategories.length === 0}
          >
            Clear All Filters
          </button>
        </div>
      </div>
    </div>
  );
};

export default CourseFilters;
