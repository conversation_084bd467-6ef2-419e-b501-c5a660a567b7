.calendar-card-link {
  text-decoration: none;
  color: inherit;
  display: block;
}

.calendar-card {
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.2s ease;
  display: flex;
  flex-direction: column;
  height: 100%;
  cursor: pointer;
}

.calendar-card:hover {
  transform: scale(1.02);
}

.calendar-card__header {
  background: linear-gradient(135deg, #0a2d69 0%, #1e4a8c 100%);
  padding: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 120px;
}

.calendar-card__initials {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  font-size: 2rem;
  font-weight: bold;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.calendar-card__content {
  padding: 1.5rem;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.calendar-card__title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #0a2d69;
  line-height: 1.3;
}

.calendar-card__schedule {
  color: #666;
  font-size: 0.9rem;
  font-weight: 500;
}

.calendar-card__start-date {
  color: #888;
  font-size: 0.85rem;
  font-style: italic;
}
