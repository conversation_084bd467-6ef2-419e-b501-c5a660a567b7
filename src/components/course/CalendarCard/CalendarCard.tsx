import React from "react";
import { Link } from "react-router-dom";
import { CalendarCardProps } from "../../../types";
import "./CalendarCard.css";

const CalendarCard: React.FC<CalendarCardProps> = ({ course }) => {
  // Generate initials from course title
  const getInitials = (title: string): string => {
    return title
      .split(" ")
      .map((word) => word.charAt(0))
      .join("")
      .toUpperCase()
      .slice(0, 3);
  };

  return (
    <Link to={`/course/${course.slug}`} className="calendar-card-link">
      <div className="calendar-card">
        <div className="calendar-card__header">
          <div className="calendar-card__initials">
            {getInitials(course.title)}
          </div>
        </div>
        <div className="calendar-card__content">
          <h3 className="calendar-card__title">{course.title}</h3>
          <div className="calendar-card__schedule">
            {course.schedule
              ? `${course.schedule.daysOfWeek.join(", ")} at ${
                  course.schedule.time
                }`
              : "Schedule TBD"}
          </div>
          <div className="calendar-card__start-date">
            Starts: {course.startDate}
          </div>
        </div>
      </div>
    </Link>
  );
};

export default CalendarCard;
