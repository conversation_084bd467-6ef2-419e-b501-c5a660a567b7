import React, { memo, useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { CourseCardProps } from "../../../types";
import {
  useImagePreload,
  useIntersectionObserver,
  useAnimation,
  usePerformance,
} from "../../../hooks";
import { getCourseCapacity } from "../../../services/firebaseCourseCapacityService";
import { formatPrice } from "../../../utils/priceUtils";
import LoadingSkeleton from "../../ui/LoadingSkeleton";
import "./CourseCard.css";

const CourseCard: React.FC<CourseCardProps> = memo(
  ({ title, description, price, startDate, schedule, slug, category }) => {
    // Add performance monitoring
    usePerformance({
      componentName: "CourseCard",
      threshold: 16, // 60fps target
    });

    const { ref, hasIntersected } = useIntersectionObserver({
      threshold: 0.1,
      triggerOnce: true,
    });
    const { animationClass } = useAnimation(
      "fade-in-up",
      hasIntersected ? 0 : 0
    );

    // Generate initials from course title
    const getInitials = (title: string): string => {
      return title
        .split(" ")
        .map((word) => word.charAt(0))
        .join("")
        .toUpperCase()
        .slice(0, 3);
    };

    // Get course capacity information
    const [capacity, setCapacity] = useState<any>(null);

    useEffect(() => {
      const loadCapacity = async () => {
        try {
          const capacityData = await getCourseCapacity(slug);
          setCapacity(capacityData);
        } catch (error) {
          console.error("Error loading course capacity:", error);
        }
      };

      loadCapacity();
    }, [slug]);

    return (
      <Link
        to={`/course/${slug}`}
        className="course-card-link"
        aria-label={`View details for ${title}`}
      >
        <article
          ref={ref}
          className={`course-card hover-lift ${
            hasIntersected ? animationClass : "animate-on-scroll"
          } ${hasIntersected ? "in-view" : ""}`}
        >
          <div className="course-card__header">
            <div className="course-card__initials">{getInitials(title)}</div>
          </div>
          <div className="course-card__content">
            {category && (
              <div className="course-card__category">{category}</div>
            )}
            <h3 className="course-card__title">{title}</h3>
            <p className="course-card__description">{description}</p>
            {price && (
              <strong className="course-card__price">
                {formatPrice(price)}
              </strong>
            )}
            <p className="course-card__start-date">
              <em>Start: {startDate}</em>
            </p>
            <p className="course-card__schedule">
              <em>
                {schedule
                  ? `${schedule.daysOfWeek.join(", ")} at ${schedule.time}`
                  : "Schedule TBD"}
              </em>
            </p>
            {capacity && (
              <div className="course-card__capacity">
                <div
                  className={`course-card__status course-card__status--${capacity.status}`}
                >
                  {capacity.status === "open" && "Registration Open"}
                  {capacity.status === "full" && "Join Waitlist"}
                </div>
              </div>
            )}
          </div>
        </article>
      </Link>
    );
  }
);

CourseCard.displayName = "CourseCard";

export default CourseCard;
