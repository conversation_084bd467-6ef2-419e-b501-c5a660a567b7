/* Base card styles */
.course-card-no-image-link {
  text-decoration: none;
  color: inherit;
  display: block;
}

.course-card-no-image-link:hover {
  text-decoration: none;
}

.course-card-no-image-link:focus {
  text-decoration: none;
}

.course-card-no-image {
  display: flex;
  flex-direction: column;
  background-color: white;
  border: 2px solid #d1e7f0;
  border-radius: 16px;
  box-shadow: 0 6px 20px rgba(10, 45, 105, 0.12);
  overflow: hidden;
  cursor: pointer;
  height: 100%;
  min-height: 460px;
  position: relative;
  transition: all 0.3s ease;
}

.course-card-no-image:hover {
  transform: translateY(-6px);
  box-shadow: 0 12px 40px rgba(10, 45, 105, 0.2);
  border-color: #ffa300;
}

/* Focus styles for accessibility */
.course-card-no-image-link:focus {
  outline: none;
}

.course-card-no-image-link:focus .course-card-no-image {
  box-shadow: 0 0 0 3px rgba(255, 163, 0, 0.5),
    0 12px 40px rgba(10, 45, 105, 0.2);
  border-color: #ffa300;
}

/* Header area (replaces image) */
.course-card-no-image__header {
  position: relative;
  width: 100%;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

/* Icon + Typography design */
.course-card-no-image__header--icon {
  flex-direction: column;
  gap: 0.5rem;
  padding: 2rem;
}

.course-card-no-image__icon {
  font-size: 3rem;
  line-height: 1;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

/* Typography-focused design */
.course-card-no-image__header--typography {
  flex-direction: column;
  gap: 1rem;
  padding: 2rem;
}

.course-card-no-image__initials {
  font-size: 4rem;
  font-weight: 900;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  letter-spacing: 0.1em;
  line-height: 1;
}

.course-card-no-image__header--icon .course-card-no-image__initials {
  font-size: 2.5rem;
}

/* Title + Hexagon pattern design */
.course-card-no-image__header--title {
  position: relative;
  flex-direction: column;
  justify-content: center;
  padding: 2rem 1.5rem;
  overflow: hidden;
}

.course-card-no-image__hexagon-pattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.15;
  background-image: url("data:image/svg+xml,%3Csvg width='56' height='100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' stroke='rgba(255,255,255,0.8)' stroke-width='1'%3E%3Cpolygon points='28,1 53,15 53,43 28,57 3,43 3,15'/%3E%3C/g%3E%3C/svg%3E");
  background-size: 56px 100px;
  background-repeat: repeat;
}

.course-card-no-image__hexagon-pattern::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("data:image/svg+xml,%3Csvg width='56' height='100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' stroke='rgba(255,255,255,0.6)' stroke-width='0.8'%3E%3Cpolygon points='28,1 53,15 53,43 28,57 3,43 3,15'/%3E%3C/g%3E%3C/svg%3E");
  background-size: 56px 100px;
  background-repeat: repeat;
  background-position: 28px 50px;
}

.course-card-no-image__header-content {
  position: relative;
  z-index: 2;
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
}

.course-card-no-image__header-title {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.6), 0 1px 3px rgba(0, 0, 0, 0.8);
  line-height: 1.2;
  text-align: center;
}

.course-card-no-image__categories {
  display: flex;
  flex-wrap: wrap;
  gap: 0.4rem;
  justify-content: center;
  margin-top: 0.5rem;
}

.course-card-no-image__category-badge {
  background: rgba(255, 163, 0, 0.9);
  color: #0a2d69;
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 163, 0, 0.8);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  white-space: nowrap;
}

.course-card-no-image__category-badge--more {
  background: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.3);
  font-size: 0.65rem;
}

/* Geometric pattern design (alternative) */
.course-card-no-image__header--geometric {
  position: relative;
  overflow: hidden;
}

.course-card-no-image__pattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.3;
}

.course-card-no-image__shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.4);
}

.course-card-no-image__shape--1 {
  width: 120px;
  height: 120px;
  top: -30px;
  right: -30px;
  animation: float 6s ease-in-out infinite;
}

.course-card-no-image__shape--2 {
  width: 80px;
  height: 80px;
  bottom: -20px;
  left: -20px;
  animation: float 8s ease-in-out infinite reverse;
}

.course-card-no-image__shape--3 {
  width: 60px;
  height: 60px;
  top: 50%;
  left: 20px;
  transform: translateY(-50%);
  animation: float 10s ease-in-out infinite;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

.course-card-no-image__category-overlay {
  position: relative;
  z-index: 2;
  background: rgba(255, 255, 255, 0.9);
  color: #0a2d69;
  padding: 0.5rem 1rem;
  border-radius: 25px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Content area */
.course-card-no-image__content {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  padding: 1.5rem;
  background: white;
}

.course-card-no-image__title {
  font-size: 1.2rem;
  margin: 0 0 1rem 0;
  min-height: 3rem;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  font-weight: 700;
  color: #0a2d69;
}

.course-card-no-image__description {
  flex-grow: 1;
  margin: 0 0 1.5rem 0;
  color: #555;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  font-size: 1rem;
  line-height: 1.6;
  min-height: 4.8rem;
}

.course-card-no-image__details {
  margin-bottom: 1rem;
  display: grid;
  gap: 0.75rem;
}

.course-card-no-image__price {
  font-size: 1.4rem;
  color: #ffa300;
  font-weight: 700;
  display: block;
  margin: 0;
}

.course-card-no-image__schedule-info {
  display: grid;
  gap: 0.5rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #ffa300;
}

.course-card-no-image__schedule-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #555;
  margin: 0;
}

.course-card-no-image__schedule-label {
  font-weight: 600;
  color: #0a2d69;
  min-width: 4rem;
  font-style: normal;
}

.course-card-no-image__schedule-value {
  color: #333;
  font-style: normal;
}

.course-card-no-image__capacity {
  margin-top: auto;
}

.course-card-no-image__status {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.course-card-no-image__status--open {
  background: #d4edda;
  color: #155724;
}

.course-card-no-image__status--full {
  background: #fff3cd;
  color: #856404;
}

/* Responsive design */
@media (max-width: 767px) {
  .course-card-no-image {
    min-height: 400px;
    border-radius: 8px;
  }

  .course-card-no-image__initials {
    font-size: 3rem;
  }

  .course-card-no-image__header {
    height: 160px;
  }

  .course-card-no-image__header-title {
    font-size: 1.2rem;
  }
}

@media (min-width: 768px) {
  .course-card-no-image__title {
    font-size: 1.3rem;
  }

  .course-card-no-image__price {
    font-size: 1.5rem;
  }

  .course-card-no-image__schedule-item {
    font-size: 0.95rem;
  }
}

/* Remove hover effects on touch devices */
@media (hover: none) {
  .course-card-no-image:hover {
    transform: none;
    box-shadow: 0 6px 20px rgba(10, 45, 105, 0.12);
    border-color: #d1e7f0;
  }
}
