.course-card-link {
  text-decoration: none;
  color: inherit;
  display: block;
}

.course-card-link:hover {
  text-decoration: none;
}

.course-card-link:focus {
  text-decoration: none;
}

.course-card-link:visited {
  text-decoration: none;
}

.course-card-link:active {
  text-decoration: none;
}

.course-card {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  background-color: white;
  border: 2px solid #d1e7f0;
  border-radius: 16px;
  box-shadow: 0 6px 20px rgba(10, 45, 105, 0.12);
  overflow: hidden;
  cursor: pointer;
  height: 100%;
  min-height: 460px;
  position: relative;
  transition: all 0.3s ease;
}

/* Mobile-first responsive design */
@media (max-width: 767px) {
  .course-card {
    min-height: 400px;
    border-radius: 8px;
  }
}

/* Tablet adjustments */
@media (min-width: 768px) and (max-width: 1023px) {
  .course-card {
    min-height: 440px;
  }
}

.course-card:hover {
  transform: translateY(-6px);
  box-shadow: 0 12px 40px rgba(10, 45, 105, 0.2);
  border-color: #ffa300;
}

/* Remove hover effects on touch devices */
@media (hover: none) {
  .course-card:hover {
    transform: none;
    box-shadow: 0 6px 20px rgba(10, 45, 105, 0.12);
    border-color: #d1e7f0;
  }
}

/* Focus styles for keyboard navigation */
.course-card-link:focus {
  outline: none;
}

.course-card-link:focus .course-card {
  box-shadow: 0 0 0 3px rgba(255, 163, 0, 0.5),
    0 12px 40px rgba(10, 45, 105, 0.2);
  border-color: #ffa300;
}

.course-card__header {
  background: linear-gradient(135deg, #0a2d69 0%, #1e4a8c 100%);
  padding: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 140px;
  border-bottom: 1px solid #d1e7f0;
}

.course-card__initials {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  font-size: 1.8rem;
  font-weight: bold;
  width: 70px;
  height: 70px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.course-card__content {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  padding: 1.5rem;
  background: white;
}

.course-card__category {
  background: #eff6ff;
  color: #0a2d69;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  align-self: flex-start;
  margin-bottom: 0.75rem;
}

.course-card__title {
  font-size: 1.2rem;
  margin: 0 0 1rem 0;
  min-height: 3rem;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  font-weight: 700;
  color: #0a2d69;
}

@media (min-width: 768px) {
  .course-card__title {
    font-size: 1.3rem;
  }
}

.course-card__description {
  flex-grow: 1;
  margin: 0 0 1.5rem 0;
  color: #555;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  font-size: 1rem;
  line-height: 1.6;
  min-height: 4.8rem; /* 3 lines × 1.6 line-height */
}

@media (min-width: 768px) {
  .course-card__description {
    font-size: 1rem;
  }
}

.course-card__price {
  font-size: 1.2rem;
  color: #ffa300;
  font-weight: 700;
  margin-bottom: 0.75rem;
  display: block;
}

.course-card__start-date,
.course-card__day-time {
  font-size: 0.9rem;
  display: block;
  margin-top: 0.25rem;
  color: #666;
  font-style: italic;
}

@media (min-width: 768px) {
  .course-card__price {
    font-size: 1.3rem;
  }

  .course-card__start-date,
  .course-card__day-time {
    font-size: 0.95rem;
  }
}

.course-card__capacity {
  margin-top: 1rem;
}

.course-card__status {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.course-card__status--open {
  background: #d4edda;
  color: #155724;
}

.course-card__status--full {
  background: #fff3cd;
  color: #856404;
}
