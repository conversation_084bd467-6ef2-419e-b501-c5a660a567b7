import React, { memo, useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { CourseCardProps } from "../../../types";
import {
  useIntersectionObserver,
  useAnimation,
  usePerformance,
} from "../../../hooks";
import { getCourseCapacity } from "../../../services/firebaseCourseCapacityService";
import { formatPrice } from "../../../utils/priceUtils";
import "./CourseCardNoImage.css";

interface CourseCardNoImageProps extends CourseCardProps {
  variant?: "icon-initials" | "title-hexagon";
}

const CourseCardNoImage: React.FC<CourseCardNoImageProps> = memo(
  ({
    title,
    description,
    price,
    startDate,
    schedule,
    slug,
    category,
    categories,
    variant = "icon-initials",
  }) => {
    // Add performance monitoring
    usePerformance({
      componentName: "CourseCardNoImage",
      threshold: 16, // 60fps target
    });

    const { ref, hasIntersected } = useIntersectionObserver({
      threshold: 0.1,
      triggerOnce: true,
    });
    const { animationClass } = useAnimation(
      "fade-in-up",
      hasIntersected ? 0 : 0
    );

    // Get course capacity information
    const [capacity, setCapacity] = useState<any>(null);

    useEffect(() => {
      const loadCapacity = async () => {
        try {
          const capacityData = await getCourseCapacity(slug);
          setCapacity(capacityData);
        } catch (error) {
          console.error("Error loading course capacity:", error);
        }
      };

      loadCapacity();
    }, [slug]);

    // Generate course initials for typography design
    const getInitials = (title: string) => {
      return title
        .split(" ")
        .map((word) => word[0])
        .join("")
        .toUpperCase()
        .slice(0, 3);
    };

    // Get category icon (uniform brand colors for all)
    const getCategoryIcon = (category?: string) => {
      const categoryIcons = {
        Python: "🐍",
        JavaScript: "⚡",
        "Web Development": "🌐",
        "Game Development": "🎮",
        "Data Science": "📊",
        "Mobile Development": "📱",
        default: "💻",
      };

      return (
        categoryIcons[category as keyof typeof categoryIcons] ||
        categoryIcons.default
      );
    };

    // Uniform brand colors for all courses
    const brandColors = {
      primary: "#0a2d69",
      secondary: "#1e4a8c",
      gradient: "linear-gradient(135deg, #0a2d69 0%, #1e4a8c 100%)",
    };

    const categoryIcon = getCategoryIcon(category);

    // Render category badges (new categories array or fallback to legacy category)
    const renderCategoryBadges = () => {
      const categoriesToShow =
        categories && categories.length > 0
          ? categories
          : category
          ? [category]
          : [];

      if (categoriesToShow.length === 0) return null;

      // Show max 2 categories, with a "+X" indicator if there are more
      const visibleCategories = categoriesToShow.slice(0, 2);
      const remainingCount = categoriesToShow.length - 2;

      return (
        <div className="course-card-no-image__categories">
          {visibleCategories.map((cat, index) => (
            <div key={index} className="course-card-no-image__category-badge">
              {cat}
            </div>
          ))}
          {remainingCount > 0 && (
            <div className="course-card-no-image__category-badge course-card-no-image__category-badge--more">
              +{remainingCount}
            </div>
          )}
        </div>
      );
    };

    return (
      <Link
        to={`/course/${slug}`}
        className="course-card-no-image-link"
        aria-label={`View details for ${title}`}
      >
        <article
          ref={ref}
          className={`course-card-no-image hover-lift ${
            hasIntersected ? animationClass : "animate-on-scroll"
          } ${hasIntersected ? "in-view" : ""}`}
        >
          {variant === "icon-initials" ? (
            /* Icon + Initials variant */
            <div
              className="course-card-no-image__header course-card-no-image__header--icon"
              style={{ background: brandColors.gradient }}
            >
              <div className="course-card-no-image__icon">{categoryIcon}</div>
              <div className="course-card-no-image__initials">
                {getInitials(title)}
              </div>
              {renderCategoryBadges()}
            </div>
          ) : (
            /* Title + Hexagon pattern variant */
            <div
              className="course-card-no-image__header course-card-no-image__header--title"
              style={{ background: brandColors.gradient }}
            >
              <div className="course-card-no-image__hexagon-pattern"></div>
              <div className="course-card-no-image__header-content">
                <h3 className="course-card-no-image__header-title">{title}</h3>
                {renderCategoryBadges()}
              </div>
            </div>
          )}

          {/* Option 2: Geometric pattern header (commented out for now) */}
          {/*
          <div
            className="course-card-no-image__header course-card-no-image__header--geometric"
            style={{ background: brandColors.gradient }}
          >
            <div className="course-card-no-image__pattern">
              <div className="course-card-no-image__shape course-card-no-image__shape--1"></div>
              <div className="course-card-no-image__shape course-card-no-image__shape--2"></div>
              <div className="course-card-no-image__shape course-card-no-image__shape--3"></div>
            </div>
            {category && (
              <div className="course-card-no-image__category-overlay">
                {category}
              </div>
            )}
          </div>
          */}

          <div className="course-card-no-image__content">
            {variant === "icon-initials" && (
              <h3 className="course-card-no-image__title">{title}</h3>
            )}
            <p className="course-card-no-image__description">{description}</p>

            <div className="course-card-no-image__details">
              <div className="course-card-no-image__schedule-info">
                <div className="course-card-no-image__schedule-item">
                  <span className="course-card-no-image__schedule-label">
                    Start:
                  </span>
                  <span className="course-card-no-image__schedule-value">
                    {startDate}
                  </span>
                </div>
                <div className="course-card-no-image__schedule-item">
                  <span className="course-card-no-image__schedule-label">
                    Schedule:
                  </span>
                  <span className="course-card-no-image__schedule-value">
                    {schedule
                      ? `${schedule.daysOfWeek.join(", ")} at ${schedule.time}`
                      : "Schedule TBD"}
                  </span>
                </div>
              </div>
            </div>

            {capacity && (
              <div className="course-card-no-image__capacity">
                <div
                  className={`course-card-no-image__status course-card-no-image__status--${capacity.status}`}
                >
                  {capacity.status === "open" && "Registration Open"}
                  {capacity.status === "full" && "Join Waitlist"}
                </div>
              </div>
            )}
          </div>
        </article>
      </Link>
    );
  }
);

CourseCardNoImage.displayName = "CourseCardNoImage";

export default CourseCardNoImage;
