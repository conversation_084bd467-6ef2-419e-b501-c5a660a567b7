import React from 'react';
import { LoadingSkeletonProps } from '../../../types';
import './LoadingSkeleton.css';

const LoadingSkeleton: React.FC<LoadingSkeletonProps> = ({ 
  width = '100%', 
  height = '20px', 
  className = '' 
}) => {
  const style = {
    width: typeof width === 'number' ? `${width}px` : width,
    height: typeof height === 'number' ? `${height}px` : height,
  };

  return (
    <div 
      className={`loading-skeleton ${className}`}
      style={style}
    />
  );
};

export default LoadingSkeleton;
