.spinner {
  display: inline-block;
  position: relative;
}

.spinner__circle {
  border-radius: 50%;
  border-style: solid;
  animation: spinner-rotate 1s linear infinite;
}

/* Size variants */
.spinner--small .spinner__circle {
  width: 16px;
  height: 16px;
  border-width: 2px;
}

.spinner--medium .spinner__circle {
  width: 20px;
  height: 20px;
  border-width: 2px;
}

.spinner--large .spinner__circle {
  width: 24px;
  height: 24px;
  border-width: 3px;
}

/* Color variants */
.spinner--primary .spinner__circle {
  border-color: #0a2d69 transparent #0a2d69 transparent;
}

.spinner--white .spinner__circle {
  border-color: white transparent white transparent;
}

.spinner--gray .spinner__circle {
  border-color: #666 transparent #666 transparent;
}

@keyframes spinner-rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .spinner__circle {
    animation: none;
    border-left-color: transparent;
    border-right-color: transparent;
  }
}
