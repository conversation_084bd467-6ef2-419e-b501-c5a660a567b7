.error-boundary {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 2rem;
  background-color: #f9f9f9;
}

.error-boundary__content {
  text-align: center;
  max-width: 500px;
  padding: 2rem;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #dc3545;
}

.error-boundary__content h2 {
  color: #dc3545;
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.error-boundary__content p {
  color: #666;
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.error-boundary__details {
  margin-top: 1.5rem;
  text-align: left;
}

.error-boundary__details summary {
  cursor: pointer;
  font-weight: bold;
  color: #0a2d69;
  margin-bottom: 0.5rem;
}

.error-boundary__details pre {
  background-color: #f8f9fa;
  padding: 1rem;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 0.875rem;
  color: #dc3545;
  white-space: pre-wrap;
  word-break: break-word;
}
