import React from "react";
import "./Spinner.css";

interface SpinnerProps {
  size?: "small" | "medium" | "large";
  color?: "primary" | "white" | "gray";
}

const Spinner: React.FC<SpinnerProps> = ({ 
  size = "medium", 
  color = "primary" 
}) => {
  return (
    <div 
      className={`spinner spinner--${size} spinner--${color}`}
      role="status"
      aria-label="Loading"
    >
      <div className="spinner__circle"></div>
    </div>
  );
};

export default Spinner;
