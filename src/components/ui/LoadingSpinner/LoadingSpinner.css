.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-spinner__circle {
  border: 3px solid #f3f3f3;
  border-top: 3px solid #ffa300;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-spinner--small .loading-spinner__circle {
  width: 20px;
  height: 20px;
  border-width: 2px;
}

.loading-spinner--medium .loading-spinner__circle {
  width: 40px;
  height: 40px;
  border-width: 3px;
}

.loading-spinner--large .loading-spinner__circle {
  width: 60px;
  height: 60px;
  border-width: 4px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
