import React, { memo } from "react";
import { ButtonProps } from "../../../types";
import Spinner from "../Spinner";
import "./Button.css";

const Button: React.FC<ButtonProps> = memo(
  ({
    children,
    variant = "primary",
    size = "medium",
    href,
    target,
    rel,
    onClick,
    disabled = false,
    loading = false,
    className = "",
    type = "button",
    ...props
  }) => {
    const isDisabled = disabled || loading;
    const baseClasses = `btn btn--${variant} btn--${size} ${
      loading ? "btn--loading" : ""
    } ${className}`;

    // Add proper accessibility attributes for external links
    const linkProps =
      href && target === "_blank"
        ? {
            rel: rel || "noopener noreferrer",
            "aria-describedby": "external-link-warning",
          }
        : { rel };

    if (href) {
      return (
        <a
          href={href}
          target={target}
          {...linkProps}
          className={baseClasses}
          {...props}
        >
          {children}
          {target === "_blank" && (
            <span id="external-link-warning" className="sr-only">
              (opens in new tab)
            </span>
          )}
        </a>
      );
    }

    return (
      <button
        type={type}
        className={baseClasses}
        onClick={onClick}
        disabled={isDisabled}
        aria-disabled={isDisabled}
        {...props}
      >
        {loading && (
          <Spinner
            size={size === "small" ? "small" : "medium"}
            color={variant === "primary" ? "primary" : "gray"}
          />
        )}
        <span className={loading ? "btn__text--loading" : ""}>{children}</span>
      </button>
    );
  }
);

Button.displayName = "Button";

export default Button;
