.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  border: none;
  border-radius: 8px;
  text-decoration: none;
  transition: all 0.2s ease;
  cursor: pointer;
  font-family: inherit;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn--loading {
  position: relative;
  pointer-events: none;
}

.btn--loading .btn__text--loading {
  opacity: 0.7;
  margin-left: 0.5rem;
}

/* Variants */
.btn--primary {
  background-color: #ffa300;
  color: #0a2d69;
}

.btn--primary:hover:not(:disabled) {
  background-color: #ffb733;
}

.btn--secondary {
  background-color: transparent;
  color: #0a2d69;
  border: 2px solid #0a2d69;
}

.btn--secondary:hover:not(:disabled) {
  background-color: #0a2d69;
  color: white;
}

/* Sizes */
.btn--small {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}

.btn--medium {
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
}

.btn--large {
  padding: 1rem 2rem;
  font-size: 1.125rem;
}
