import React, { ComponentType, useEffect } from 'react';

interface PerformanceMetrics {
  componentName: string;
  renderTime: number;
  mountTime: number;
}

const withPerformanceMonitoring = <P extends object>(
  WrappedComponent: ComponentType<P>,
  componentName?: string
) => {
  const WithPerformanceMonitoring: React.FC<P> = (props) => {
    const name = componentName || WrappedComponent.displayName || WrappedComponent.name || 'Component';

    useEffect(() => {
      const startTime = performance.now();

      return () => {
        const endTime = performance.now();
        const renderTime = endTime - startTime;

        // Only log in development
        if (process.env.NODE_ENV === 'development') {
          const metrics: PerformanceMetrics = {
            componentName: name,
            renderTime,
            mountTime: startTime,
          };

          console.log(`🚀 Performance Metrics for ${name}:`, metrics);

          // Warn if component takes too long to render
          if (renderTime > 100) {
            console.warn(`⚠️ ${name} took ${renderTime.toFixed(2)}ms to render. Consider optimization.`);
          }
        }
      };
    }, [name]);

    return <WrappedComponent {...props} />;
  };

  WithPerformanceMonitoring.displayName = `withPerformanceMonitoring(${name})`;

  return WithPerformanceMonitoring;
};

export default withPerformanceMonitoring;
