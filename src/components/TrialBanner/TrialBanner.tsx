import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useTenant } from "../../contexts/TenantContext";
import {
  getTrialStatus,
  getTrialMessage,
  getTrialWarningLevel,
} from "../../services/trialService";
import "./TrialBanner.css";

interface TrialBannerProps {
  className?: string;
  dismissible?: boolean;
}

const TrialBanner: React.FC<TrialBannerProps> = ({
  className = "",
  dismissible = false,
}) => {
  const { userProfile } = useTenant();
  const navigate = useNavigate();
  const [isDismissed, setIsDismissed] = useState(false);

  const trialStatus = getTrialStatus(userProfile);
  const message = getTrialMessage(userProfile);
  const warningLevel = getTrialWarningLevel(userProfile);

  // Check if banner was dismissed for this session
  useEffect(() => {
    if (dismissible && userProfile?.uid) {
      const dismissedKey = `trial_banner_dismissed_${userProfile.uid}`;
      const lastDismissed = localStorage.getItem(dismissedKey);
      const lastLogin = userProfile.lastLoginAt;

      // If dismissed after last login, keep it dismissed
      if (lastDismissed && lastLogin && lastDismissed > lastLogin) {
        setIsDismissed(true);
      }
    }
  }, [dismissible, userProfile]);

  // Don't show banner if user has active subscription, dismissed, or no warning needed
  if (
    trialStatus.hasActiveSubscription ||
    warningLevel === "none" ||
    (dismissible && isDismissed)
  ) {
    return null;
  }

  const handleSubscribeClick = () => {
    navigate("/billing");
  };

  const handleDismiss = () => {
    if (dismissible && userProfile?.uid) {
      const dismissedKey = `trial_banner_dismissed_${userProfile.uid}`;
      localStorage.setItem(dismissedKey, new Date().toISOString());
      setIsDismissed(true);
    }
  };

  const getBannerClass = () => {
    switch (warningLevel) {
      case "error":
        return "trial-banner trial-banner--error";
      case "warning":
        return "trial-banner trial-banner--warning";
      case "info":
        return "trial-banner trial-banner--info";
      default:
        return "trial-banner";
    }
  };

  const getIcon = () => {
    switch (warningLevel) {
      case "error":
        return "⚠️";
      case "warning":
        return "⏰";
      case "info":
        return "ℹ️";
      default:
        return "ℹ️";
    }
  };

  return (
    <div className={`${getBannerClass()} ${className}`}>
      <div className="trial-banner__content">
        <div className="trial-banner__message">
          <span className="trial-banner__icon">{getIcon()}</span>
          <span className="trial-banner__text">{message}</span>
        </div>
        <div className="trial-banner__actions">
          <button
            className="trial-banner__button"
            onClick={handleSubscribeClick}
          >
            {trialStatus.isExpired ? "Subscribe Now" : "Upgrade Now"}
          </button>
          {dismissible && (
            <button
              className="trial-banner__dismiss"
              onClick={handleDismiss}
              title="Dismiss until next login"
            >
              ✕
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default TrialBanner;
