.trial-banner {
  padding: 12px 20px;
  margin-bottom: 20px;
  border-radius: 8px;
  border: 1px solid;
  background-color: #f8f9fa;
  border-color: #dee2e6;
  color: #495057;
}

.trial-banner__content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
}

.trial-banner__actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.trial-banner__message {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.trial-banner__icon {
  font-size: 18px;
  flex-shrink: 0;
}

.trial-banner__text {
  font-size: 14px;
  font-weight: 500;
  line-height: 1.4;
}

.trial-banner__button {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  background-color: #007bff;
  color: white;
}

.trial-banner__button:hover {
  background-color: #0056b3;
  transform: translateY(-1px);
}

.trial-banner__dismiss {
  padding: 6px 8px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: transparent;
  color: inherit;
  opacity: 0.7;
  min-width: auto;
}

.trial-banner__dismiss:hover {
  opacity: 1;
  background-color: rgba(0, 0, 0, 0.1);
}

/* Info level (early trial) */
.trial-banner--info {
  background-color: #e7f3ff;
  border-color: #b3d9ff;
  color: #0066cc;
}

.trial-banner--info .trial-banner__button {
  background-color: #0066cc;
  color: white;
}

.trial-banner--info .trial-banner__button:hover {
  background-color: #0052a3;
}

/* Warning level (trial ending soon) */
.trial-banner--warning {
  background-color: #fff8e1;
  border-color: #ffcc02;
  color: #b8860b;
}

.trial-banner--warning .trial-banner__button {
  background-color: #ff9800;
  color: white;
}

.trial-banner--warning .trial-banner__button:hover {
  background-color: #e68900;
}

/* Error level (trial expired or critical) */
.trial-banner--error {
  background-color: #ffebee;
  border-color: #f44336;
  color: #c62828;
}

.trial-banner--error .trial-banner__button {
  background-color: #f44336;
  color: white;
}

.trial-banner--error .trial-banner__button:hover {
  background-color: #d32f2f;
}

/* Responsive design */
@media (max-width: 768px) {
  .trial-banner {
    padding: 10px 16px;
    margin-bottom: 16px;
  }

  .trial-banner__content {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .trial-banner__message {
    justify-content: center;
    text-align: center;
  }

  .trial-banner__button {
    width: 100%;
    padding: 12px 16px;
  }
}
