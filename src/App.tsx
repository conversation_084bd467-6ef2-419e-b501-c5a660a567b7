import React, { Suspense } from "react";
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import Layout from "./components/layout/Layout";
import ErrorBoundary from "./components/ui/ErrorBoundary";
import LoadingSpinner from "./components/ui/LoadingSpinner";
import ProtectedRoute from "./components/auth/ProtectedRoute";
import PerformanceDashboard from "./components/dev/PerformanceDashboard";
import { TenantProvider } from "./contexts/TenantContext";
import { ROUTES } from "./constants";
import "./styles/globals.css";

// AdminBuddy Pages - Lazy loaded for better performance
const Home = React.lazy(() => import("./pages/Home"));
const About = React.lazy(() => import("./pages/About"));
const Features = React.lazy(() => import("./pages/Features"));
const BusinessRoutines = React.lazy(() => import("./pages/BusinessRoutines"));
const Pricing = React.lazy(() => import("./pages/Pricing"));
const Contact = React.lazy(() => import("./pages/Contact"));
const Login = React.lazy(() => import("./pages/Login"));
const SimpleLogin = React.lazy(() => import("./pages/SimpleLogin"));
const Signup = React.lazy(() => import("./pages/Signup"));
const Onboarding = React.lazy(() => import("./pages/Onboarding"));

// Dashboard Pages (no layout)
const OwnerDashboard = React.lazy(() => import("./pages/OwnerDashboard"));
const ManagerDashboard = React.lazy(() => import("./pages/ManagerDashboard"));
const KioskInterface = React.lazy(() => import("./pages/KioskInterface"));
const BillingPage = React.lazy(() => import("./pages/BillingPage/BillingPage"));

// Legacy pages (for reference during transition)
const Admin = React.lazy(() => import("./pages/Admin"));
const Instructor = React.lazy(() => import("./pages/Instructor"));

const LoadingFallback: React.FC = () => (
  <div
    style={{
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      minHeight: "400px",
    }}
  >
    <LoadingSpinner size="large" />
  </div>
);

const App: React.FC = () => {
  return (
    <ErrorBoundary>
      <TenantProvider>
        <Router>
          <Suspense fallback={<LoadingFallback />}>
            <Routes>
              {/* Authentication routes */}
              <Route path="/login" element={<SimpleLogin />} />
              <Route path="/login-firebase" element={<Login />} />
              <Route path="/signup" element={<Signup />} />
              <Route path="/onboarding" element={<Onboarding />} />

              {/* Dashboard routes - full page without layout - PROTECTED */}
              <Route
                path="/dashboard"
                element={
                  <ProtectedRoute allowedRoles={["owner"]}>
                    <OwnerDashboard />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/dashboard/daily-tasks"
                element={
                  <ProtectedRoute allowedRoles={["owner"]}>
                    <OwnerDashboard />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/dashboard/reports"
                element={
                  <ProtectedRoute allowedRoles={["owner"]}>
                    <OwnerDashboard />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/dashboard/locations"
                element={
                  <ProtectedRoute allowedRoles={["owner"]}>
                    <OwnerDashboard />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/dashboard/routines"
                element={
                  <ProtectedRoute allowedRoles={["owner"]}>
                    <OwnerDashboard />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/dashboard/routine-tasks"
                element={
                  <ProtectedRoute allowedRoles={["owner"]}>
                    <OwnerDashboard />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/dashboard/schedules"
                element={
                  <ProtectedRoute allowedRoles={["owner"]}>
                    <OwnerDashboard />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/dashboard/roles"
                element={
                  <ProtectedRoute allowedRoles={["owner"]}>
                    <OwnerDashboard />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/dashboard/watchlist"
                element={
                  <ProtectedRoute allowedRoles={["owner"]}>
                    <OwnerDashboard />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/dashboard/help"
                element={
                  <ProtectedRoute allowedRoles={["owner"]}>
                    <OwnerDashboard />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/billing"
                element={
                  <ProtectedRoute>
                    <BillingPage />
                  </ProtectedRoute>
                }
              />

              {/* Legacy dashboard routes - PROTECTED */}
              <Route
                path="/owner"
                element={
                  <ProtectedRoute allowedRoles={["owner"]}>
                    <OwnerDashboard />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/owner/:tab"
                element={
                  <ProtectedRoute allowedRoles={["owner"]}>
                    <OwnerDashboard />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/manager"
                element={
                  <ProtectedRoute allowedRoles={["manager", "owner"]}>
                    <ManagerDashboard />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/manager/:tab"
                element={
                  <ProtectedRoute allowedRoles={["manager", "owner"]}>
                    <ManagerDashboard />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/kiosk"
                element={
                  <ProtectedRoute>
                    <KioskInterface />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/kiosk/:locationId"
                element={
                  <ProtectedRoute>
                    <KioskInterface />
                  </ProtectedRoute>
                }
              />

              {/* Legacy routes (for transition) */}
              <Route path="/admin" element={<Admin />} />
              <Route path="/admin/:tab" element={<Admin />} />
              <Route path="/instructor" element={<Instructor />} />
              <Route path="/instructor/:tab" element={<Instructor />} />

              {/* Marketing site routes with layout */}
              <Route
                path="*"
                element={
                  <Layout>
                    <Routes>
                      <Route path="/" element={<Home />} />
                      <Route path="/features" element={<Features />} />
                      <Route
                        path="/business-routines"
                        element={<BusinessRoutines />}
                      />
                      <Route path="/pricing" element={<Pricing />} />
                      <Route path="/about" element={<About />} />
                      <Route path="/contact" element={<Contact />} />
                    </Routes>
                  </Layout>
                }
              />
            </Routes>
          </Suspense>
          {process.env.REACT_APP_PERFORMANCE_MODE === "true" && (
            <PerformanceDashboard />
          )}
        </Router>
      </TenantProvider>
    </ErrorBoundary>
  );
};

export default App;
