import React, { useState, useEffect } from "react";
import AdminLogin from "../../components/admin/AdminLogin";
import AdminDashboard from "../../components/admin/AdminDashboard";
import {
  isAdminAuthenticated,
  onAdminAuthStateChanged,
} from "../../services/adminService";
import { initializeCourseCapacities } from "../../services/firebaseCourseCapacityService";
import { usePerformance } from "../../hooks";

const Admin: React.FC = () => {
  usePerformance({
    componentName: "Admin",
    threshold: 25,
  });

  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Listen for auth state changes instead of just checking once
    const unsubscribe = onAdminAuthStateChanged((user) => {
      setIsAuthenticated(!!user);
      setIsLoading(false);
    });

    // Cleanup subscription on unmount
    return () => unsubscribe();
  }, []);

  const handleLoginSuccess = () => {
    setIsAuthenticated(true);
  };

  const handleLogout = () => {
    setIsAuthenticated(false);
  };

  if (isLoading) {
    return (
      <div
        style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          minHeight: "100vh",
          fontSize: "1.2rem",
          color: "#666",
        }}
      >
        Loading...
      </div>
    );
  }

  return (
    <>
      {isAuthenticated ? (
        <AdminDashboard onLogout={handleLogout} />
      ) : (
        <AdminLogin onLoginSuccess={handleLoginSuccess} />
      )}
    </>
  );
};

export default Admin;
