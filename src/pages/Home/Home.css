/* AdminBuddy Home Page Styles */
.home__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Hero Section */
.home__hero {
  background: linear-gradient(135deg, #0a2d69 0%, #1e4a8c 100%);
  color: white;
  padding: 3rem 2rem;
  text-align: center;
}

.home__hero-content {
  max-width: 800px;
  margin: 0 auto;
}

.home__hero h1 {
  font-size: 3.5rem;
  margin-bottom: 1.5rem;
  line-height: 1.2;
  font-weight: 700;
}

.home__hero-subtitle {
  font-size: 1.3rem;
  line-height: 1.6;
  color: #e8f4f8;
  margin-bottom: 2rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.home__hero-cta {
  margin-bottom: 1.5rem;
}

.home__hero-note {
  font-size: 0.9rem;
  color: #e8f4f8;
  text-align: center;
  margin: 0;
  font-style: italic;
}

/* Hero section button overrides for dark background */
.home__hero-cta .btn--secondary {
  background-color: transparent;
  color: white;
  border: 2px solid white;
}

.home__hero-cta .btn--secondary:hover:not(:disabled) {
  background-color: white;
  color: #0a2d69;
  border-color: white;
}

/* Demo Section */
.home__demo {
  padding: 4rem 2rem;
  background: white;
}

.home__demo-content {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.home__demo-text h2 {
  font-size: 2.5rem;
  color: #1a1a1a;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.home__demo-text p {
  font-size: 1.1rem;
  color: #666;
  line-height: 1.6;
  margin-bottom: 2rem;
}

.home__demo-image {
  display: flex;
  justify-content: center;
  align-items: center;
}

.home__demo-screenshot {
  width: 100%;
  max-width: 500px;
  height: auto;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  border: 1px solid #e2e8f0;
}

.home__demo-placeholder {
  width: 100%;
  max-width: 500px;
  aspect-ratio: 16/10;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  border: 2px solid #cbd5e1;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 2rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.home__demo-placeholder span {
  color: #475569;
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  display: block;
}

.home__demo-placeholder small {
  color: #64748b;
  font-size: 0.875rem;
  font-style: italic;
}

/* Stats Section */
.home__stats {
  padding: 4rem 2rem;
  background: #f8fafc;
  text-align: center;
}

.home__stats-content h2 {
  font-size: 2.5rem;
  color: #1a1a1a;
  margin-bottom: 3rem;
}

.home__stats-grid {
  display: flex;
  justify-content: center;
  gap: 3rem;
  flex-wrap: wrap;
  max-width: 800px;
  margin: 0 auto;
}

.home__stat {
  text-align: center;
}

.home__stat-number {
  display: block;
  font-size: 2.5rem;
  font-weight: 700;
  color: #0a2d69;
  margin-bottom: 0.5rem;
}

.home__stat-label {
  font-size: 1rem;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
}

/* Problem Section */
.home__problem {
  padding: 4rem 2rem;
  background: #f8fafc;
}

.home__problem h2 {
  font-size: 2.5rem;
  text-align: center;
  margin-bottom: 3rem;
  color: #1a1a1a;
}

.home__problem-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.home__problem-item {
  background: white;
  padding: 2.5rem;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.home__problem-icon {
  font-size: 3rem;
  display: block;
  margin-bottom: 1.5rem;
}

.home__problem-item h3 {
  font-size: 1.3rem;
  margin-bottom: 1rem;
  color: #1a1a1a;
}

.home__problem-item p {
  color: #666;
  line-height: 1.6;
  margin: 0;
}

/* Solution Section */
.home__solution {
  padding: 4rem 2rem;
  background: white;
}

.home__solution h2 {
  font-size: 2.5rem;
  text-align: center;
  margin-bottom: 3rem;
  color: #1a1a1a;
}

.home__solution-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.home__solution-item {
  background: #f8fafc;
  padding: 2.5rem;
  border-radius: 12px;
  text-align: center;
  border: 2px solid transparent;
  transition: border-color 0.2s;
}

.home__solution-item:hover {
  border-color: #0a2d69;
}

.home__solution-icon {
  font-size: 3rem;
  display: block;
  margin-bottom: 1.5rem;
}

.home__solution-item h3 {
  font-size: 1.3rem;
  margin-bottom: 1rem;
  color: #0a2d69;
}

.home__solution-item p {
  color: #666;
  line-height: 1.6;
  margin: 0;
}

/* Social Proof Section */
.home__social-proof {
  padding: 4rem 2rem;
  background: #f8fafc;
}

.home__social-proof h2 {
  font-size: 2.5rem;
  text-align: center;
  margin-bottom: 3rem;
  color: #1a1a1a;
}

.home__testimonials {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  max-width: 1000px;
  margin: 0 auto;
}

.home__testimonial {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  text-align: center;
}

.home__testimonial p {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #333;
  margin-bottom: 1.5rem;
  font-style: italic;
}

.home__testimonial cite {
  color: #0a2d69;
  font-weight: 600;
  font-style: normal;
  font-size: 0.9rem;
}

/* CTA Section */
.home__cta {
  padding: 4rem 2rem;
  background: linear-gradient(135deg, #0a2d69 0%, #1e4a8c 100%);
  color: white;
  text-align: center;
}

.home__cta h2 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.home__cta p {
  font-size: 1.2rem;
  color: #e8f4f8;
  margin-bottom: 2.5rem;
}

.home__cta-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: 1.5rem;
}

.home__cta-note {
  font-size: 0.9rem;
  color: #e8f4f8;
  margin: 0;
  font-style: italic;
}

/* CTA section button overrides for dark background */
.home__cta-buttons .btn--secondary {
  background-color: transparent;
  color: white;
  border: 2px solid white;
}

.home__cta-buttons .btn--secondary:hover:not(:disabled) {
  background-color: white;
  color: #0a2d69;
  border-color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
  .home__container {
    padding: 0 1rem;
  }

  .home__hero {
    padding: 2rem 1rem;
  }

  .home__hero h1 {
    font-size: 2.5rem;
  }

  .home__hero-subtitle {
    font-size: 1.1rem;
  }

  /* Demo responsive */
  .home__demo {
    padding: 3rem 1rem;
  }

  .home__demo-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .home__demo-text h2 {
    font-size: 2rem;
  }

  .home__demo-screenshot,
  .home__demo-placeholder {
    max-width: 100%;
  }

  /* Stats responsive */
  .home__stats {
    padding: 3rem 1rem;
  }

  .home__stats-content h2 {
    font-size: 2rem;
  }

  .home__stats-grid {
    gap: 2rem;
  }

  .home__stat-number {
    font-size: 2rem;
  }

  .home__problem,
  .home__solution,
  .home__cta {
    padding: 3rem 1rem;
  }

  .home__problem h2,
  .home__solution h2,
  .home__cta h2 {
    font-size: 2rem;
  }

  .home__problem-grid,
  .home__solution-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .home__problem-item,
  .home__solution-item {
    padding: 2rem;
  }

  .home__cta-buttons {
    flex-direction: column;
    align-items: center;
  }
}
