import React from "react";
import Button from "../../components/ui/Button";
import { usePerformance } from "../../hooks";
import "./Home.css";

const Home: React.FC = () => {
  // Add performance monitoring
  usePerformance({
    componentName: "Home",
    threshold: 30,
  });

  return (
    <>
      {/* Hero Section */}
      <section className="home__hero">
        <div className="home__hero-content">
          <h1>
            Ensure Your Business Runs the Same Whether You're There or Not
          </h1>
          <p className="home__hero-subtitle">
            AdminBuddy creates consistent operations across all locations and
            shifts. No more wondering if critical routines got done or
            explaining the same expectations over and over.
          </p>

          <div className="home__hero-cta">
            <Button variant="primary" size="large" href="/signup">
              Start Free Trial
            </Button>
          </div>

          <p className="home__hero-note">
            ✓ 14-day free trial • ✓ No credit card required
          </p>
        </div>
      </section>

      {/* Demo Section */}
      <section className="home__demo">
        <div className="home__container">
          <div className="home__demo-content">
            <div className="home__demo-text">
              <h2>Predictable Operations, Every Shift</h2>
              <p>
                Your team sees exactly what's expected, when it's due, and can
                confirm completion with a simple tap. No more "I didn't know I
                was supposed to do that" or wondering if critical routines got
                done.
              </p>
              <Button variant="secondary" href="/features">
                Explore All Features
              </Button>
            </div>
            <div className="home__demo-image">
              <img
                src="/images/screenshots/kiosk-interface.png"
                alt="AdminBuddy kiosk interface showing daily tasks"
                className="home__demo-screenshot"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.style.display = "none";
                  const placeholder = target.nextElementSibling as HTMLElement;
                  if (placeholder) placeholder.style.display = "flex";
                }}
              />
              <div
                className="home__demo-placeholder"
                style={{ display: "none" }}
              >
                <span>Kiosk Interface Demo</span>
                <small>Screenshot coming soon</small>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="home__stats">
        <div className="home__container">
          <div className="home__stats-content">
            <h2>Simple, Transparent Pricing</h2>
            <div className="home__stats-grid">
              <div className="home__stat">
                <span className="home__stat-number">$20</span>
                <span className="home__stat-label">Per Location/Month</span>
              </div>
              <div className="home__stat">
                <span className="home__stat-number">2 Week</span>
                <span className="home__stat-label">Free Trial</span>
              </div>
              <div className="home__stat">
                <span className="home__stat-number">No Limit</span>
                <span className="home__stat-label">Users Per Location</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Problem Section */}
      <section className="home__problem">
        <div className="home__container">
          <h2>The Hidden Cost of Inconsistent Operations</h2>
          <div className="home__problem-grid">
            <div className="home__problem-item">
              <span className="home__problem-icon">🔄</span>
              <h3>"Did the evening shift do the closing checklist?"</h3>
              <p>
                Critical routines get skipped during busy periods or shift
                changes. You discover missed tasks the next morning, after
                they've already impacted operations or customer experience.
              </p>
            </div>
            <div className="home__problem-item">
              <span className="home__problem-icon">😤</span>
              <h3>"I thought someone else was handling that"</h3>
              <p>
                Critical routines fall through the cracks during busy periods or
                shift changes. You discover problems after they've already
                impacted customers or operations.
              </p>
            </div>
            <div className="home__problem-item">
              <span className="home__problem-icon">⏰</span>
              <h3>"I spend all my time explaining the same things"</h3>
              <p>
                New employees need constant guidance, and even experienced staff
                need reminders. You can't scale your business if it depends on
                you being there to manage every detail.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Solution Section */}
      <section className="home__solution">
        <div className="home__container">
          <h2>Build a Business That Runs Without You</h2>
          <div className="home__solution-grid">
            <div className="home__solution-item">
              <span className="home__solution-icon">✅</span>
              <h3>Never Miss Critical Routines</h3>
              <p>
                Define what needs to happen and when. Tasks appear automatically
                at the right time, and your team can confirm completion with a
                simple tap. No more wondering if something got done.
              </p>
            </div>
            <div className="home__solution-item">
              <span className="home__solution-icon">🔄</span>
              <h3>Consistent Routine Completion Across All Locations</h3>
              <p>
                Set up your routines once and they automatically appear at every
                location. Morning procedures, closing checklists, weekly
                maintenance—all happen when they're supposed to.
              </p>
            </div>
            <div className="home__solution-item">
              <span className="home__solution-icon">📊</span>
              <h3>Know What's Done Without Asking</h3>
              <p>
                See completion status in real-time from anywhere. Get automatic
                reports of what was completed and what was missed. No more
                guessing or constant check-ins.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="home__cta">
        <div className="home__container">
          <h2>Ready for Predictable Operations?</h2>
          <p>
            Stop wondering if critical routines got done. Create consistent
            operations that don't depend on you being there. Start your free
            trial today—no credit card required.
          </p>
          <div className="home__cta-buttons">
            <Button variant="primary" size="large" href="/signup">
              Start Free Trial
            </Button>
            <Button variant="secondary" size="large" href="/pricing">
              View Pricing
            </Button>
          </div>
          <p className="home__cta-note">
            ✓ 14-day free trial • ✓ 5-minute setup • ✓ Cancel anytime
          </p>
        </div>
      </section>
    </>
  );
};

export default Home;
