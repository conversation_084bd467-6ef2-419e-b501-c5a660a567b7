import React, { useState } from "react";
import CourseCard from "../../components/course/CourseCard";
import CourseCardNoImage from "../../components/course/CourseCard/CourseCardNoImage";
import { useCourses } from "../../hooks";
import "./CourseExperiment.css";

const CourseExperiment: React.FC = () => {
  const { courses, loading, error } = useCourses();
  const [selectedExperiment, setSelectedExperiment] = useState<
    "all" | "icon-initials" | "title-hexagon"
  >("all");

  if (loading) {
    return (
      <div className="course-experiment">
        <div className="course-experiment__loading">Loading courses...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="course-experiment">
        <div className="course-experiment__error">
          <h2>Unable to load courses</h2>
          <p>{error}</p>
        </div>
      </div>
    );
  }

  // Take first 3 courses for comparison
  const sampleCourses = courses.slice(0, 3);

  return (
    <div className="course-experiment">
      <div className="course-experiment__header">
        <h1>Course Card Design Experiment</h1>
        <p>Comparing image-based vs. image-free course card designs</p>

        <div className="course-experiment__controls">
          <label htmlFor="experiment-select">View Experiment:</label>
          <select
            id="experiment-select"
            value={selectedExperiment}
            onChange={(e) => setSelectedExperiment(e.target.value as any)}
            className="course-experiment__select"
          >
            <option value="all">All Experiments</option>
            <option value="icon-initials">Icon + Initials Only</option>
            <option value="title-hexagon">Title + Hexagon Pattern Only</option>
          </select>
        </div>
      </div>

      {selectedExperiment === "all" && (
        <div className="course-experiment__section">
          <h2>Current Design (With Images)</h2>
          <div className="course-experiment__grid">
            {sampleCourses.map((course) => (
              <CourseCard
                key={`current-${course.slug}`}
                title={course.title}
                description={course.description}
                price={course.price}
                startDate={course.startDate}
                schedule={course.schedule}
                slug={course.slug}
                category={course.category}
              />
            ))}
          </div>
        </div>
      )}

      {(selectedExperiment === "all" ||
        selectedExperiment === "icon-initials") && (
        <div className="course-experiment__section">
          <h2>Icon + Initials Design (No Images)</h2>
          <p className="course-experiment__description">
            Uses course initials, category-based color schemes, and icons to
            create visual interest. Colors automatically adapt based on course
            category (Python = blue/yellow, JavaScript = yellow/black, etc.)
          </p>
          <div className="course-experiment__grid">
            {sampleCourses.map((course) => (
              <CourseCardNoImage
                key={`icon-initials-${course.slug}`}
                title={course.title}
                description={course.description}
                price={course.price}
                startDate={course.startDate}
                schedule={course.schedule}
                slug={course.slug}
                category={course.category}
                variant="icon-initials"
              />
            ))}
          </div>
        </div>
      )}

      {(selectedExperiment === "all" ||
        selectedExperiment === "title-hexagon") && (
        <div className="course-experiment__section">
          <h2>Title + Hexagon Pattern Design (No Images)</h2>
          <p className="course-experiment__description">
            Places the course title in the header with a subtle hexagonal tech
            pattern overlay. Removes title duplication and creates a clean,
            modern tech aesthetic. The hexagon pattern adds texture while
            maintaining readability.
          </p>
          <div className="course-experiment__grid">
            {sampleCourses.map((course) => (
              <CourseCardNoImage
                key={`title-hexagon-${course.slug}`}
                title={course.title}
                description={course.description}
                price={course.price}
                startDate={course.startDate}
                schedule={course.schedule}
                slug={course.slug}
                category={course.category}
                variant="title-hexagon"
              />
            ))}
          </div>
        </div>
      )}

      <div className="course-experiment__analysis">
        <h2>Design Analysis</h2>
        <div className="course-experiment__comparison">
          <div className="course-experiment__pros-cons">
            <h3>Image-Based Design</h3>
            <div className="course-experiment__pros">
              <h4>Pros:</h4>
              <ul>
                <li>Familiar and expected by users</li>
                <li>Can show specific course content/tools</li>
                <li>Potentially more engaging for some users</li>
              </ul>
            </div>
            <div className="course-experiment__cons">
              <h4>Cons:</h4>
              <ul>
                <li>Requires creating/sourcing quality images</li>
                <li>Images may not appeal to everyone</li>
                <li>Additional maintenance overhead</li>
                <li>Potential copyright/licensing issues</li>
              </ul>
            </div>
          </div>

          <div className="course-experiment__pros-cons">
            <h3>Typography-Focused Design</h3>
            <div className="course-experiment__pros">
              <h4>Pros:</h4>
              <ul>
                <li>No image creation/maintenance needed</li>
                <li>Consistent, professional appearance</li>
                <li>Category-based color coding aids recognition</li>
                <li>Fast loading, no image dependencies</li>
                <li>Accessible and scalable</li>
              </ul>
            </div>
            <div className="course-experiment__cons">
              <h4>Cons:</h4>
              <ul>
                <li>May seem less visually engaging initially</li>
                <li>Relies more on typography and color</li>
                <li>Less conventional approach</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CourseExperiment;
