.course-experiment {
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
}

.course-experiment__header {
  text-align: center;
  margin-bottom: 3rem;
  padding: 2rem;
  background: linear-gradient(135deg, #0a2d69 0%, #1e4a8c 100%);
  color: white;
  border-radius: 12px;
}

.course-experiment__header h1 {
  margin: 0 0 1rem 0;
  font-size: 2.5rem;
  font-weight: 700;
}

.course-experiment__header p {
  margin: 0 0 2rem 0;
  font-size: 1.2rem;
  opacity: 0.9;
}

.course-experiment__controls {
  display: flex;
  align-items: center;
  gap: 1rem;
  justify-content: center;
  margin-top: 1rem;
}

.course-experiment__controls label {
  font-weight: 600;
  font-size: 1rem;
}

.course-experiment__select {
  padding: 0.5rem 1rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 1rem;
  font-weight: 500;
  backdrop-filter: blur(10px);
  cursor: pointer;
  transition: all 0.2s ease;
}

.course-experiment__select:focus {
  outline: none;
  border-color: #ffa300;
  background: rgba(255, 255, 255, 0.2);
}

.course-experiment__select option {
  background: #0a2d69;
  color: white;
}

.course-experiment__section {
  margin-bottom: 4rem;
}

.course-experiment__section h2 {
  color: #0a2d69;
  font-size: 2rem;
  margin-bottom: 1rem;
  border-bottom: 3px solid #ffa300;
  padding-bottom: 0.5rem;
}

.course-experiment__description {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 2rem;
  color: #555;
  font-style: italic;
  border-left: 4px solid #0a2d69;
}

.course-experiment__grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

@media (min-width: 768px) {
  .course-experiment__grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1200px) {
  .course-experiment__grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

.course-experiment__analysis {
  background: white;
  border: 2px solid #d1e7f0;
  border-radius: 12px;
  padding: 2rem;
  margin-top: 3rem;
}

.course-experiment__analysis h2 {
  color: #0a2d69;
  text-align: center;
  margin-bottom: 2rem;
}

.course-experiment__comparison {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
}

@media (min-width: 768px) {
  .course-experiment__comparison {
    grid-template-columns: repeat(2, 1fr);
  }
}

.course-experiment__pros-cons {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
}

.course-experiment__pros-cons h3 {
  color: #0a2d69;
  margin: 0 0 1rem 0;
  font-size: 1.3rem;
  text-align: center;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #0a2d69;
}

.course-experiment__pros,
.course-experiment__cons {
  margin-bottom: 1.5rem;
}

.course-experiment__pros h4 {
  color: #28a745;
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
}

.course-experiment__cons h4 {
  color: #dc3545;
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
}

.course-experiment__pros ul,
.course-experiment__cons ul {
  margin: 0;
  padding-left: 1.5rem;
}

.course-experiment__pros li,
.course-experiment__cons li {
  margin-bottom: 0.5rem;
  line-height: 1.5;
}

.course-experiment__loading,
.course-experiment__error {
  text-align: center;
  padding: 3rem;
  color: #0a2d69;
  font-size: 1.2rem;
}

.course-experiment__error {
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 8px;
  color: #721c24;
}

/* Mobile responsiveness */
@media (max-width: 767px) {
  .course-experiment {
    padding: 1rem;
  }

  .course-experiment__header h1 {
    font-size: 2rem;
  }

  .course-experiment__header p {
    font-size: 1rem;
  }

  .course-experiment__section h2 {
    font-size: 1.5rem;
  }

  .course-experiment__analysis {
    padding: 1rem;
  }
}
