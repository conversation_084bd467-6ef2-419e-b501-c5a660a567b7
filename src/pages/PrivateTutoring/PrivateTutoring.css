.tutoring {
  min-height: 100vh;
}

.tutoring__hero {
  background: linear-gradient(135deg, #0a2d69 0%, #1e4a8c 100%);
  color: white;
  padding: 4rem 2rem;
  text-align: center;
}

@media (max-width: 767px) {
  .tutoring__hero {
    padding: 2rem 1rem;
  }
}

.tutoring__hero-content {
  max-width: 800px;
  margin: 0 auto;
}

.tutoring__hero h1 {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: white;
}

@media (max-width: 767px) {
  .tutoring__hero h1 {
    font-size: 2rem;
  }
}

.tutoring__subtitle {
  font-size: 1.4rem;
  color: #ffd46f;
  margin-bottom: 1.5rem;
  font-weight: 500;
}

@media (max-width: 767px) {
  .tutoring__subtitle {
    font-size: 1.2rem;
  }
}

.tutoring__intro {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #e8f4f8;
  margin: 0;
  max-width: 700px;
  margin: 0 auto;
}

@media (max-width: 767px) {
  .tutoring__intro {
    font-size: 1rem;
  }
}

.tutoring__content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 4rem 2rem;
}

@media (max-width: 767px) {
  .tutoring__content {
    padding: 2rem 1rem;
  }
}

.tutoring__section {
  margin-bottom: 4rem;
}

.tutoring__section h2 {
  font-size: 2.2rem;
  color: #0a2d69;
  margin-bottom: 2rem;
  text-align: center;
  border-bottom: 3px solid #ffa300;
  padding-bottom: 0.5rem;
}

@media (max-width: 767px) {
  .tutoring__section h2 {
    font-size: 1.8rem;
  }
}

.tutoring__benefits {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

@media (max-width: 767px) {
  .tutoring__benefits {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

.tutoring__benefit {
  background: #f8f9fa;
  padding: 2rem;
  border-radius: 12px;
  border-left: 4px solid #ffa300;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.tutoring__benefit:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.tutoring__benefit h3 {
  font-size: 1.3rem;
  color: #0a2d69;
  margin-bottom: 1rem;
}

.tutoring__benefit p {
  margin: 0;
  line-height: 1.6;
  color: #333;
}

.tutoring__options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

@media (max-width: 767px) {
  .tutoring__options {
    grid-template-columns: 1fr;
  }
}

.tutoring__option {
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 16px;
  padding: 2.5rem;
  text-align: center;
  position: relative;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.tutoring__option:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
}

.tutoring__option--clickable {
  cursor: pointer;
  user-select: none;
}

.tutoring__option--clickable:hover {
  transform: translateY(-6px);
  box-shadow: 0 12px 40px rgba(10, 45, 105, 0.2);
  border-color: #ffa300;
}

.tutoring__option--clickable:focus {
  outline: 3px solid rgba(255, 163, 0, 0.5);
  outline-offset: 2px;
}

.tutoring__option--clickable:active {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(10, 45, 105, 0.15);
}

/* Remove hover effects on touch devices for clickable options */
@media (hover: none) {
  .tutoring__option--clickable:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(10, 45, 105, 0.15);
  }
}

.tutoring__option--popular {
  border-color: #ffa300;
  background: white;
  box-shadow: 0 8px 32px rgba(255, 163, 0, 0.15);
}

.tutoring__popular-badge {
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background: #ffa300;
  color: white;
  padding: 0.5rem 1.5rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
}

.tutoring__option h3 {
  font-size: 1.5rem;
  color: #0a2d69;
  margin-bottom: 1rem;
}

.tutoring__price {
  font-size: 2rem;
  font-weight: 700;
  color: #ffa300;
  margin-bottom: 1.5rem;
}

.tutoring__features {
  list-style: none;
  padding: 0;
  margin: 1.5rem 0;
  text-align: left;
}

.tutoring__features li {
  padding: 0.5rem 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  position: relative;
  padding-left: 1.5rem;
}

.tutoring__features li:before {
  content: "✓";
  position: absolute;
  left: 0;
  color: #28a745;
  font-weight: bold;
}

.tutoring__features li:last-child {
  border-bottom: none;
}

.tutoring__ideal {
  background: #f8f9fa;
  border: 1px solid #e8f4f8;
  border-left: 3px solid #ffa300;
  padding: 1rem;
  border-radius: 8px;
  font-size: 0.95rem;
  line-height: 1.5;
  margin-top: 1.5rem;
}

.tutoring__differences {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

@media (max-width: 767px) {
  .tutoring__differences {
    grid-template-columns: 1fr;
  }
}

.tutoring__difference {
  background: white;
  padding: 2rem;
  border-radius: 16px;
  border: 2px solid #d1e7f0;
  box-shadow: 0 6px 20px rgba(10, 45, 105, 0.12);
}

.tutoring__difference h4 {
  font-size: 1.2rem;
  color: #0a2d69;
  margin-bottom: 1rem;
}

.tutoring__difference p {
  margin: 0;
  line-height: 1.6;
  color: #333;
}

.tutoring__topics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

@media (max-width: 767px) {
  .tutoring__topics {
    grid-template-columns: 1fr;
  }
}

.tutoring__topic-category {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
  border-top: 4px solid #ffa300;
}

.tutoring__topic-category h4 {
  color: #0a2d69;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.tutoring__topic-category ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.tutoring__topic-category li {
  padding: 0.3rem 0;
  color: #333;
  font-size: 0.95rem;
}

.tutoring__process {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

@media (max-width: 767px) {
  .tutoring__process {
    grid-template-columns: 1fr;
  }
}

.tutoring__step {
  text-align: center;
  padding: 2rem;
}

.tutoring__step-number {
  width: 60px;
  height: 60px;
  background: #ffa300;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: bold;
  margin: 0 auto 1rem;
}

.tutoring__step h4 {
  color: #0a2d69;
  margin-bottom: 1rem;
  font-size: 1.2rem;
}

.tutoring__step p {
  margin: 0;
  line-height: 1.6;
  color: #333;
}

.tutoring__cta {
  background: linear-gradient(135deg, #0a2d69 0%, #1e4a8c 100%);
  color: white;
  padding: 4rem;
  border-radius: 16px;
  text-align: center;
  margin-top: 4rem;
}

@media (max-width: 767px) {
  .tutoring__cta {
    padding: 2rem 1rem;
    margin-top: 2rem;
  }
}

.tutoring__cta h2 {
  color: white;
  border: none;
  margin-bottom: 1rem;
}

.tutoring__cta > p {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  color: #e8f4f8;
}

@media (max-width: 767px) {
  .tutoring__cta > p {
    font-size: 1rem;
  }
}

.tutoring__cta-content {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 2rem;
  align-items: center;
  max-width: 800px;
  margin: 0 auto;
}

@media (max-width: 767px) {
  .tutoring__cta-content {
    grid-template-columns: 1fr;
    text-align: center;
  }
}

.tutoring__cta-text {
  text-align: left;
}

@media (max-width: 767px) {
  .tutoring__cta-text {
    text-align: center;
  }
}

.tutoring__cta-text h3 {
  color: #ffd46f;
  margin-bottom: 0.5rem;
  font-size: 1.3rem;
}

.tutoring__cta-text p {
  margin: 0;
  color: #e8f4f8;
  line-height: 1.5;
}

.tutoring__cta-buttons {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  min-width: 200px;
}

@media (max-width: 767px) {
  .tutoring__cta-buttons {
    min-width: auto;
  }
}

/* Fix secondary button contrast in CTA section */
.tutoring__cta .btn--secondary {
  background-color: transparent;
  color: white;
  border: 2px solid white;
}

.tutoring__cta .btn--secondary:hover:not(:disabled) {
  background-color: white;
  color: #0a2d69;
}
