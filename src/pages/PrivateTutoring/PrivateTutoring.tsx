import React from "react";
import { usePerformance } from "../../hooks";
import ErrorBoundary from "../../components/ui/ErrorBoundary";
import Button from "../../components/ui/Button";
import { ROUTES, EXTERNAL_LINKS } from "../../constants";
import "./PrivateTutoring.css";

const PrivateTutoring: React.FC = () => {
  usePerformance({
    componentName: "PrivateTutoring",
    threshold: 25,
  });

  const scrollToConsultation = () => {
    const ctaSection = document.getElementById("consultation-cta");
    if (ctaSection) {
      ctaSection.scrollIntoView({
        behavior: "smooth",
        block: "start",
      });
    }
  };

  return (
    <ErrorBoundary>
      <div className="tutoring">
        <div className="tutoring__hero">
          <div className="tutoring__hero-content">
            <h1>Private & Semi-Private Tutoring</h1>
            <p className="tutoring__subtitle">
              Accelerate your learning with personalized instruction tailored to
              your goals, pace, and interests.
            </p>
            <p className="tutoring__intro">
              Get the full attention of an experienced engineering manager who
              can adapt to your learning style, dive deep into theory, and
              provide immediate feedback on your code and concepts.
            </p>
          </div>
        </div>

        <div className="tutoring__content">
          <section className="tutoring__section">
            <h2>Why Choose Private Tutoring?</h2>
            <div className="tutoring__benefits">
              <div className="tutoring__benefit">
                <h3>🎯 100% Focused Attention</h3>
                <p>
                  Every minute is dedicated to your learning. No waiting for
                  other students, no distractions—just pure, focused instruction
                  tailored to your needs and goals.
                </p>
              </div>
              <div className="tutoring__benefit">
                <h3>💬 Deep Theory Discussions</h3>
                <p>
                  Engage in back-and-forth conversations about programming
                  concepts, design patterns, and best practices. Ask "why"
                  questions and get detailed explanations that stick.
                </p>
              </div>
              <div className="tutoring__benefit">
                <h3>⚡ Your Pace, Your Schedule</h3>
                <p>
                  Move quickly through concepts you grasp easily, spend extra
                  time on challenging topics, and schedule sessions that fit
                  your life and commitments.
                </p>
              </div>
              <div className="tutoring__benefit">
                <h3>🛠️ Custom Projects</h3>
                <p>
                  Work on projects that align with your specific interests,
                  career goals, or business needs. Build exactly what you want
                  to learn.
                </p>
              </div>
            </div>
          </section>

          <section className="tutoring__section">
            <h2>Tutoring Options</h2>
            <div className="tutoring__options">
              <div
                className="tutoring__option tutoring__option--clickable"
                onClick={scrollToConsultation}
                role="button"
                tabIndex={0}
                onKeyDown={(e) => {
                  if (e.key === "Enter" || e.key === " ") {
                    e.preventDefault();
                    scrollToConsultation();
                  }
                }}
              >
                <h3>1-on-1 Private Tutoring</h3>
                <div className="tutoring__price">$120/hour</div>
                <ul className="tutoring__features">
                  <li>Complete instructor attention</li>
                  <li>Fully customized curriculum</li>
                  <li>Flexible scheduling</li>
                  <li>Deep dive into theory and concepts</li>
                  <li>Real-time code review and feedback</li>
                  <li>Career guidance and mentorship</li>
                </ul>
                <p className="tutoring__ideal">
                  <strong>Ideal for:</strong> Professionals changing careers,
                  students preparing for interviews, entrepreneurs building
                  specific applications, or anyone wanting maximum learning
                  efficiency.
                </p>
              </div>

              <div
                className="tutoring__option tutoring__option--popular tutoring__option--clickable"
                onClick={scrollToConsultation}
                role="button"
                tabIndex={0}
                onKeyDown={(e) => {
                  if (e.key === "Enter" || e.key === " ") {
                    e.preventDefault();
                    scrollToConsultation();
                  }
                }}
              >
                <div className="tutoring__popular-badge">Most Popular</div>
                <h3>Semi-Private (2 Students)</h3>
                <div className="tutoring__price">$75/hour per person</div>
                <ul className="tutoring__features">
                  <li>Shared learning with a partner</li>
                  <li>Collaborative problem-solving</li>
                  <li>Cost-effective personalized instruction</li>
                  <li>Peer learning and motivation</li>
                  <li>Still highly customized content</li>
                  <li>Great for friends or colleagues</li>
                </ul>
                <p className="tutoring__ideal">
                  <strong>Ideal for:</strong> Friends learning together, couples
                  building a project, colleagues upskilling, or parent-teen
                  pairs exploring coding together.
                </p>
              </div>
            </div>
          </section>

          <section className="tutoring__section">
            <h2>What Makes Our Tutoring Different</h2>
            <div className="tutoring__differences">
              <div className="tutoring__difference">
                <h4>🧠 Theory Meets Practice</h4>
                <p>
                  Unlike online tutorials that just show you what to type, we
                  dive into the "why" behind every concept. Understand the
                  reasoning, explore alternatives, and build a solid foundation
                  through conversation and exploration.
                </p>
              </div>
              <div className="tutoring__difference">
                <h4>🎓 Engineering Leadership Experience</h4>
                <p>
                  Learn from someone who's not just coded, but led engineering
                  teams, made architectural decisions, and mentored developers
                  throughout their careers. Get insights you won't find in
                  textbooks.
                </p>
              </div>
              <div className="tutoring__difference">
                <h4>🔄 Immediate Feedback Loop</h4>
                <p>
                  Write code, get instant feedback, ask questions, and iterate
                  in real-time. No waiting for assignment grades or forum
                  responses—learn and improve continuously throughout each
                  session.
                </p>
              </div>
              <div className="tutoring__difference">
                <h4>🎯 Goal-Oriented Learning</h4>
                <p>
                  Whether you're preparing for a job interview, building a
                  startup MVP, or exploring a career change, every session is
                  designed to move you closer to your specific goals.
                </p>
              </div>
            </div>
          </section>

          <section className="tutoring__section">
            <h2>Popular Tutoring Topics</h2>
            <div className="tutoring__topics">
              <div className="tutoring__topic-category">
                <h4>Career Preparation</h4>
                <ul>
                  <li>Technical interview preparation</li>
                  <li>Portfolio project development</li>
                  <li>Resume and GitHub optimization</li>
                  <li>Industry best practices and standards</li>
                </ul>
              </div>
              <div className="tutoring__topic-category">
                <h4>Specific Technologies</h4>
                <ul>
                  <li>Web development (React, Node.js, databases)</li>
                  <li>Mobile app development</li>
                  <li>Cloud platforms and DevOps</li>
                  <li>AI/ML integration and APIs</li>
                </ul>
              </div>
              <div className="tutoring__topic-category">
                <h4>Business Applications</h4>
                <ul>
                  <li>Custom business software development</li>
                  <li>E-commerce and payment integration</li>
                  <li>Data analysis and automation</li>
                  <li>API development and integration</li>
                </ul>
              </div>
              <div className="tutoring__topic-category">
                <h4>Academic Support</h4>
                <ul>
                  <li>Computer science coursework help</li>
                  <li>Project guidance and mentorship</li>
                  <li>Concept clarification and theory</li>
                  <li>Assignment review and optimization</li>
                </ul>
              </div>
            </div>
          </section>

          <section className="tutoring__section">
            <h2>How It Works</h2>
            <div className="tutoring__process">
              <div className="tutoring__step">
                <div className="tutoring__step-number">1</div>
                <h4>Initial Consultation</h4>
                <p>
                  Free 15-minute call to discuss your goals, current skill
                  level, and what you want to achieve. We'll design a learning
                  plan together.
                </p>
              </div>
              <div className="tutoring__step">
                <div className="tutoring__step-number">2</div>
                <h4>Schedule Sessions</h4>
                <p>
                  Book sessions that fit your schedule. We can meet weekly,
                  bi-weekly, or intensively—whatever works best for your
                  learning style and timeline.
                </p>
              </div>
              <div className="tutoring__step">
                <div className="tutoring__step-number">3</div>
                <h4>Learn & Build</h4>
                <p>
                  Each session combines theory, hands-on coding, and real-world
                  application. You'll leave with new knowledge and practical
                  skills you can use immediately.
                </p>
              </div>
              <div className="tutoring__step">
                <div className="tutoring__step-number">4</div>
                <h4>Ongoing Support</h4>
                <p>
                  Get email support between sessions for quick questions, code
                  reviews, and guidance on your independent practice and
                  projects.
                </p>
              </div>
            </div>
          </section>

          <section
            id="consultation-cta"
            className="tutoring__section tutoring__cta"
          >
            <h2>Ready to Accelerate Your Learning?</h2>
            <p>
              Whether you're looking to advance your career, build a specific
              project, or dive deep into programming concepts, private tutoring
              offers the fastest path to your goals.
            </p>
            <div className="tutoring__cta-content">
              <div className="tutoring__cta-text">
                <h3>Start with a Free Consultation</h3>
                <p>
                  Let's discuss your goals and create a personalized learning
                  plan. No commitment required—just a conversation about how we
                  can help you succeed.
                </p>
              </div>
              <div className="tutoring__cta-buttons">
                <Button href={EXTERNAL_LINKS.EMAIL} variant="primary">
                  Schedule Free Consultation
                </Button>
                <Button href={ROUTES.COURSES} variant="secondary">
                  View Group Courses
                </Button>
              </div>
            </div>
          </section>
        </div>
      </div>
    </ErrorBoundary>
  );
};

export default PrivateTutoring;
