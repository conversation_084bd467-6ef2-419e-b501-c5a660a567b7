import React from "react";
import { Link } from "react-router-dom";
import "./BusinessRoutines.css";

const BusinessRoutines: React.FC = () => {
  const businessTypes = [
    {
      icon: "🧼",
      title: "Cleaning Service Company",
      description:
        "Keep your cleaning crews organized and efficient with automated task scheduling.",
      routines: [
        {
          task: "Restock cleaning supplies in van",
          schedule: "Daily - Start of shift",
          type: "daily",
        },
        {
          task: "Clean and sanitize vacuum filters",
          schedule: "Weekly - End of week",
          type: "weekly",
        },
        {
          task: "Wash and dry microfiber cloths",
          schedule: "Daily - After job batch",
          type: "daily",
        },
        {
          task: "Review and confirm next day's appointments",
          schedule: "Daily - End of shift",
          type: "daily",
        },
        {
          task: "Log completed jobs into system",
          schedule: "Daily - End of shift",
          type: "daily",
        },
      ],
    },
    {
      icon: "☕",
      title: "Coffee Shop / Café",
      description:
        "Ensure consistent quality and cleanliness with automated daily and weekly routines.",
      routines: [
        {
          task: "Deep clean espresso machine",
          schedule: "Daily - End of day",
          type: "daily",
        },
        {
          task: "Restock cup sleeves, napkins, lids",
          schedule: "Daily - Morning prep",
          type: "daily",
        },
        {
          task: "Check milk/dairy inventory and refill",
          schedule: "Daily - Morning prep",
          type: "daily",
        },
        {
          task: "Clean and organize pastry display",
          schedule: "Weekly - Deep clean day",
          type: "weekly",
        },
        {
          task: "Record fridge/freezer temperature",
          schedule: "Daily - Temperature log",
          type: "daily",
        },
      ],
    },
    {
      icon: "🛠️",
      title: "Auto Repair Shop",
      description:
        "Maintain safety standards and track inventory with scheduled maintenance tasks.",
      routines: [
        {
          task: "Sweep garage floors",
          schedule: "Daily - End of shift",
          type: "daily",
        },
        {
          task: "Restock oil and filters",
          schedule: "Weekly - Inventory check",
          type: "weekly",
        },
        {
          task: "Log job details in tracking system",
          schedule: "Daily - End of shift",
          type: "daily",
        },
        {
          task: "Empty and log hazardous waste containers",
          schedule: "Weekly - Safety protocol",
          type: "weekly",
        },
        {
          task: "Follow up on pending parts orders",
          schedule: "Daily - Administrative",
          type: "daily",
        },
      ],
    },
    {
      icon: "🏪",
      title: "Retail Boutique / Gift Shop",
      description:
        "Keep your store looking fresh and inventory well-stocked with flexible scheduling.",
      routines: [
        {
          task: "Dust shelves and product displays",
          schedule: "Weekly - Store maintenance",
          type: "weekly",
        },
        {
          task: "Refill gift bag/tissue paper station",
          schedule: "Daily - Customer service prep",
          type: "daily",
        },
        {
          task: "Inventory and reorder low-stock items",
          schedule: "Twice weekly - Inventory management",
          type: "biweekly",
        },
        {
          task: "Update and rotate signage for sales/promos",
          schedule: "Weekly - Marketing refresh",
          type: "weekly",
        },
        {
          task: "Clean fitting rooms and mirrors",
          schedule: "Daily - Customer experience",
          type: "daily",
        },
      ],
    },
    {
      icon: "💇‍♀️",
      title: "Salon / Barbershop",
      description:
        "Maintain hygiene standards and client satisfaction with consistent routines.",
      routines: [
        {
          task: "Restock and organize clean towels",
          schedule: "Daily - Morning setup",
          type: "daily",
        },
        {
          task: "Disinfect tools and equipment",
          schedule: "Daily - End of day checklist",
          type: "daily",
        },
        {
          task: "Sanitize shampoo bowls",
          schedule: "Daily - Between clients",
          type: "daily",
        },
        {
          task: "Refill product retail shelf",
          schedule: "Twice weekly - Inventory",
          type: "biweekly",
        },
        {
          task: "End-of-day station cleanup",
          schedule: "Daily - Closing routine",
          type: "daily",
        },
      ],
    },
    {
      icon: "👶",
      title: "Daycare / Childcare Center",
      description:
        "Ensure child safety and regulatory compliance with automated tracking.",
      routines: [
        {
          task: "Sanitize high-touch toys and learning materials",
          schedule: "Daily - Health protocol",
          type: "daily",
        },
        {
          task: "Record daily child attendance",
          schedule: "Daily - Morning check-in",
          type: "daily",
        },
        {
          task: "Review allergy list and label snacks/lunches",
          schedule: "Daily - Safety prep",
          type: "daily",
        },
        {
          task: "Restock diaper changing stations",
          schedule: "Daily - Facility maintenance",
          type: "daily",
        },
        {
          task: "Log nap times and feeding for each child",
          schedule: "Daily - Care documentation",
          type: "daily",
        },
      ],
    },
    {
      icon: "🏋️‍♂️",
      title: "Gym / Fitness Studio",
      description:
        "Keep equipment clean and members safe with scheduled maintenance routines.",
      routines: [
        {
          task: "Deep clean all fitness equipment",
          schedule: "Daily - End of day",
          type: "daily",
        },
        {
          task: "Refill cleaning supplies and towels",
          schedule: "Daily - Member service",
          type: "daily",
        },
        {
          task: "Review and print class registration list",
          schedule: "Daily - Morning prep",
          type: "daily",
        },
        {
          task: 'Update whiteboard with "Workout of the Day"',
          schedule: "Daily - Member engagement",
          type: "daily",
        },
        {
          task: "Inspect first-aid kits and emergency exits",
          schedule: "Weekly - Safety check",
          type: "weekly",
        },
      ],
    },
    {
      icon: "🐾",
      title: "Veterinary Clinic",
      description:
        "Maintain medical standards and patient care with comprehensive task tracking.",
      routines: [
        {
          task: "End-of-day sanitization checklist for all exam rooms",
          schedule: "Daily - Medical protocol",
          type: "daily",
        },
        {
          task: "Record daily fridge and med storage temperatures",
          schedule: "Daily - Compliance log",
          type: "daily",
        },
        {
          task: "Refill exam/treatment room supplies",
          schedule: "Daily - Morning prep",
          type: "daily",
        },
        {
          task: "Dispose of medical waste and log removal",
          schedule: "Weekly - Waste management",
          type: "weekly",
        },
        {
          task: "Ensure all patient files are updated",
          schedule: "Daily - End of shift",
          type: "daily",
        },
      ],
    },
    {
      icon: "🛒",
      title: "Grocery Store / Small Market",
      description:
        "Maintain freshness and customer experience with flexible scheduling.",
      routines: [
        {
          task: "Rotate produce displays",
          schedule: "Daily - Morning before open",
          type: "daily",
        },
        {
          task: "Refill receipt paper at all tills",
          schedule: "Daily - Operations prep",
          type: "daily",
        },
        {
          task: "Clean fridge/freezer glass doors",
          schedule: "Twice weekly - Store appearance",
          type: "biweekly",
        },
        {
          task: "Record and log spoilage or expired items",
          schedule: "Daily - Inventory management",
          type: "daily",
        },
        {
          task: "Check and mark expiry dates in dairy section",
          schedule: "Weekly - Food safety",
          type: "weekly",
        },
      ],
    },
  ];

  const getScheduleTypeColor = (type: string) => {
    switch (type) {
      case "daily":
        return "#4CAF50";
      case "weekly":
        return "#2196F3";
      case "biweekly":
        return "#FF9800";
      case "monthly":
        return "#9C27B0";
      default:
        return "#757575";
    }
  };

  const getScheduleTypeLabel = (type: string) => {
    switch (type) {
      case "daily":
        return "Daily";
      case "weekly":
        return "Weekly";
      case "biweekly":
        return "Bi-weekly";
      case "monthly":
        return "Monthly";
      default:
        return "Custom";
    }
  };

  return (
    <div className="business-routines">
      {/* Hero Section */}
      <section className="business-routines__hero">
        <div className="container">
          <h1>Consistent Operations Across Every Industry</h1>
          <p className="business-routines__subtitle">
            See how AdminBuddy ensures critical routines never get missed,
            creating predictable operations that don't depend on individual
            employees remembering what to do.
          </p>
        </div>
      </section>

      {/* Introduction Section */}
      <section className="business-routines__intro">
        <div className="container">
          <div className="intro-content">
            <p className="intro-text">
              Every business has critical routines that must happen consistently
              to maintain quality and compliance. The challenge isn't knowing
              what needs to be done—it's ensuring it actually gets done, every
              time, regardless of who's working. AdminBuddy eliminates the "I
              forgot" and "I thought someone else did it" problems that cost
              businesses money and reputation.
            </p>
            <div className="business-routines__features">
              <div className="feature-highlight">
                <span className="feature-icon">📅</span>
                <span>Daily, Weekly & Monthly Scheduling</span>
              </div>
              <div className="feature-highlight">
                <span className="feature-icon">⏰</span>
                <span>Time-Based Task Visibility</span>
              </div>
              <div className="feature-highlight">
                <span className="feature-icon">📊</span>
                <span>Automated Progress Tracking</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Business Types Grid */}
      <section className="business-routines__content">
        <div className="container">
          <div className="business-routines__grid">
            {businessTypes.map((business, index) => (
              <div key={index} className="business-card">
                <div className="business-card__header">
                  <span className="business-card__icon">{business.icon}</span>
                  <h3 className="business-card__title">{business.title}</h3>
                </div>
                <p className="business-card__description">
                  {business.description}
                </p>

                <div className="business-card__routines">
                  <h4>Example Routines:</h4>
                  <ul className="routine-list">
                    {business.routines.map((routine, routineIndex) => (
                      <li key={routineIndex} className="routine-item">
                        <div className="routine-task">
                          <span className="routine-checkmark">✅</span>
                          <span className="routine-text">{routine.task}</span>
                        </div>
                        <div className="routine-schedule">
                          <span
                            className="schedule-badge"
                            style={{
                              backgroundColor: getScheduleTypeColor(
                                routine.type
                              ),
                            }}
                          >
                            {getScheduleTypeLabel(routine.type)}
                          </span>
                          <span className="schedule-details">
                            {routine.schedule}
                          </span>
                        </div>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Scheduling Power Section */}
      <section className="scheduling-power">
        <div className="container">
          <h2>The Power of Smart Scheduling</h2>
          <div className="scheduling-grid">
            <div className="scheduling-feature">
              <div className="scheduling-icon">📅</div>
              <h3>Daily Routines</h3>
              <p>
                Perfect for opening/closing procedures, cleaning tasks, and
                daily maintenance. Tasks appear automatically every day.
              </p>
              <div className="example-badge daily">
                Example: "Clean espresso machine" - Every day at 9 PM
              </div>
            </div>

            <div className="scheduling-feature">
              <div className="scheduling-icon">📆</div>
              <h3>Weekly Routines</h3>
              <p>
                Ideal for deep cleaning, inventory checks, and maintenance
                tasks. Choose specific days of the week.
              </p>
              <div className="example-badge weekly">
                Example: "Deep clean equipment" - Every Friday
              </div>
            </div>

            <div className="scheduling-feature">
              <div className="scheduling-icon">🗓️</div>
              <h3>Bi-weekly & Monthly</h3>
              <p>
                For comprehensive audits, major maintenance, and periodic
                reviews. Automatically scheduled at the right intervals.
              </p>
              <div className="example-badge monthly">
                Example: "Safety inspection" - First Monday of each month
              </div>
            </div>

            <div className="scheduling-feature">
              <div className="scheduling-icon">⏰</div>
              <h3>Time-Based Visibility</h3>
              <p>
                Tasks only appear when they need to be done. No more confusion
                about when to start closing routines.
              </p>
              <div className="example-badge timed">
                Example: "Closing checklist" - Visible after 8 PM
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="business-routines__cta">
        <div className="container">
          <h2>Ready to Streamline Your Business Routines?</h2>
          <p>
            Start your 14-day free trial and see how AdminBuddy can transform
            your daily operations.
          </p>
          <div className="cta-buttons">
            <Link
              to="/signup"
              className="btn btn-primary btn-large btn-prominent"
            >
              Start Your Free 14-Day Trial
            </Link>
          </div>
          <p className="cta-note">
            No credit card required • Setup in under 5 minutes
          </p>
        </div>
      </section>
    </div>
  );
};

export default BusinessRoutines;
