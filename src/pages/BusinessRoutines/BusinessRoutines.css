.business-routines {
  /* Remove padding to allow full-width sections */
}

/* Hero Section */
.business-routines__hero {
  background: linear-gradient(135deg, #0a2d69 0%, #1e4a8c 100%);
  color: white;
  padding: 3rem 2rem;
  text-align: center;
}

.business-routines__hero .container {
  max-width: 800px;
  margin: 0 auto;
}

.business-routines__hero h1 {
  font-size: 3rem;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.business-routines__subtitle {
  font-size: 1.2rem;
  line-height: 1.6;
  color: #e8f4f8;
  margin: 0;
}

/* Introduction Section */
.business-routines__intro {
  background: white;
  padding: 3rem 2rem;
  border-bottom: 1px solid #e5e7eb;
}

.intro-content {
  max-width: 900px;
  margin: 0 auto;
  text-align: center;
}

.intro-text {
  font-size: 1.1rem;
  line-height: 1.7;
  color: #4b5563;
  margin-bottom: 2.5rem;
  text-align: left;
}

.business-routines__features {
  display: flex;
  justify-content: center;
  gap: 2rem;
  flex-wrap: wrap;
}

.feature-highlight {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #f8fafc;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  border: 1px solid #e2e8f0;
  color: #1e293b;
  font-weight: 500;
}

.feature-icon {
  font-size: 1.2rem;
}

/* Content Section */
.business-routines__content {
  background: #f8f9fa;
  padding: 60px 0;
}

.business-routines__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

/* Business Cards */
.business-card {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid #e9ecef;
}

.business-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 48px rgba(0, 0, 0, 0.15);
}

.business-card__header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.business-card__icon {
  font-size: 2.5rem;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}

.business-card__title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.business-card__description {
  color: #6c757d;
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.business-card__routines h4 {
  color: #495057;
  margin-bottom: 1rem;
  font-weight: 600;
}

.routine-list {
  list-style: none;
  padding: 0;
  margin: 0 0 1.5rem 0;
}

.routine-item {
  margin-bottom: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.routine-task {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.routine-checkmark {
  font-size: 1rem;
  margin-top: 0.1rem;
}

.routine-text {
  font-weight: 500;
  color: #2c3e50;
  flex: 1;
}

.routine-schedule {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-left: 1.5rem;
}

.schedule-badge {
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.schedule-details {
  color: #6c757d;
  font-size: 0.9rem;
}

/* Screenshot Placeholder */
.business-card__screenshot {
  margin-top: 1.5rem;
}

.screenshot-placeholder {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
  color: #6c757d;
}

.screenshot-placeholder span {
  font-size: 1.5rem;
  display: block;
  margin-bottom: 0.5rem;
}

.screenshot-placeholder p {
  margin: 0;
  font-size: 0.9rem;
}

/* Scheduling Power Section */
.scheduling-power {
  background: white;
  padding: 80px 0;
}

.scheduling-power h2 {
  text-align: center;
  font-size: 2.5rem;
  color: #2c3e50;
  margin-bottom: 3rem;
  font-weight: 700;
}

.scheduling-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.scheduling-feature {
  text-align: center;
  padding: 2rem;
  background: #f8f9fa;
  border-radius: 16px;
  border: 1px solid #e9ecef;
}

.scheduling-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.scheduling-feature h3 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-weight: 600;
}

.scheduling-feature p {
  color: #6c757d;
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.example-badge {
  display: inline-block;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
  color: white;
}

.example-badge.daily {
  background: #4caf50;
}
.example-badge.weekly {
  background: #2196f3;
}
.example-badge.monthly {
  background: #9c27b0;
}
.example-badge.timed {
  background: #ff9800;
}

/* CTA Section */
.business-routines__cta {
  background: linear-gradient(135deg, #0a2d69 0%, #1e4a8c 100%);
  padding: 4rem 2rem;
  text-align: center;
  color: white;
}

.business-routines__cta .container {
  max-width: 600px;
  margin: 0 auto;
}

.business-routines__cta h2 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  line-height: 1.2;
}

.business-routines__cta p {
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 2.5rem;
  color: #e8f4f8;
}

.cta-buttons {
  display: flex;
  justify-content: center;
  margin-bottom: 1.5rem;
}

.btn-large {
  padding: 1.25rem 3rem;
  font-size: 1.2rem;
  font-weight: 600;
  border-radius: 8px;
  text-decoration: none;
  transition: all 0.3s ease;
}

.btn-prominent {
  background: #22c55e;
  color: white;
  border: none;
  box-shadow: 0 4px 20px rgba(34, 197, 94, 0.3);
  transform: translateY(0);
}

.btn-prominent:hover {
  background: #16a34a;
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(34, 197, 94, 0.4);
}

.cta-note {
  font-size: 0.9rem;
  opacity: 0.8;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .business-routines__hero {
    padding: 2rem 1rem;
  }

  .business-routines__hero h1 {
    font-size: 2rem;
  }

  .business-routines__subtitle {
    font-size: 1rem;
  }

  .business-routines__intro {
    padding: 2rem 1rem;
  }

  .intro-text {
    font-size: 1rem;
    text-align: center;
  }

  .business-routines__features {
    flex-direction: column;
    align-items: center;
  }

  .business-routines__grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .business-card {
    padding: 1.5rem;
  }

  .scheduling-grid {
    grid-template-columns: 1fr;
  }

  .business-routines__cta {
    padding: 3rem 1rem;
  }

  .business-routines__cta h2 {
    font-size: 2rem;
  }

  .btn-large {
    padding: 1rem 2rem;
    font-size: 1.1rem;
  }
}
