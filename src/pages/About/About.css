.about {
  min-height: 100vh;
}

.about__hero {
  background: linear-gradient(135deg, #0a2d69 0%, #1e4a8c 100%);
  color: white;
  padding: 3rem 2rem;
  text-align: center;
}

@media (max-width: 767px) {
  .about__hero {
    padding: 2rem 1rem;
  }
}

.about__hero-content {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.about__hero h1 {
  font-size: 3rem;
  margin-bottom: 1.5rem;
  line-height: 1.2;
  color: white;
}

@media (max-width: 767px) {
  .about__hero h1 {
    font-size: 2rem;
  }
}

.about__intro {
  font-size: 1.2rem;
  line-height: 1.6;
  color: #e8f4f8;
  margin: 0;
}

@media (max-width: 767px) {
  .about__intro {
    font-size: 1rem;
  }
}

.about__content {
  padding: 0;
}

/* Problem/Solution Sections */
.about__problem,
.about__solution {
  padding: 4rem 2rem;
}

.about__problem {
  background: #f8fafc;
}

.about__solution {
  background: white;
}

.about__problem-content,
.about__solution-content {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.about__solution-content {
  grid-template-columns: 1fr 1fr;
}

.about__solution-image {
  order: 1;
}

.about__solution-text {
  order: 2;
}

.about__problem h2,
.about__solution h2 {
  font-size: 2.5rem;
  color: #1a1a1a;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.about__problem p,
.about__solution p {
  font-size: 1.1rem;
  color: #666;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.about__problem p:last-child,
.about__solution p:last-child {
  margin-bottom: 0;
}

.about__image {
  width: 100%;
  max-width: 500px;
  height: auto;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.about__image-placeholder {
  width: 100%;
  max-width: 500px;
  aspect-ratio: 4/3;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  border: 2px solid #cbd5e1;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 2rem;
}

.about__image-placeholder span {
  color: #475569;
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  display: block;
}

.about__image-placeholder small {
  color: #64748b;
  font-size: 0.875rem;
  font-style: italic;
}

/* Core Values Section */
.about__section {
  padding: 4rem 2rem;
  background: #f8fafc;
}

.about__section h2 {
  font-size: 2.5rem;
  color: #1a1a1a;
  margin-bottom: 3rem;
  text-align: center;
}

@media (max-width: 767px) {
  .about__section h2 {
    font-size: 2rem;
  }
}

.about__section .about__philosophy,
.about__section .about__differences {
  max-width: 1200px;
  margin: 0 auto;
}

/* Founder Section */
.about__founder {
  padding: 4rem 2rem;
  background: white;
}

.about__founder-content {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 4rem;
  align-items: center;
}

.about__founder-image {
  display: flex;
  justify-content: center;
}

.about__founder-photo {
  width: 250px;
  height: 250px;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid #0a2d69;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.about__founder-placeholder {
  width: 250px;
  height: 250px;
  border-radius: 50%;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  border: 4px solid #cbd5e1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.about__founder-placeholder span {
  font-size: 3rem;
  margin-bottom: 0.5rem;
}

.about__founder-placeholder small {
  color: #64748b;
  font-size: 0.875rem;
}

.about__founder-text h2 {
  font-size: 2.5rem;
  color: #1a1a1a;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.about__founder-text p {
  font-size: 1.1rem;
  color: #666;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  font-style: italic;
}

.about__founder-text cite {
  color: #0a2d69;
  font-weight: 600;
  font-style: normal;
  font-size: 1rem;
}

.about__philosophy {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

@media (max-width: 767px) {
  .about__philosophy {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

.about__philosophy-item {
  background: #f8f9fa;
  padding: 2rem;
  border-radius: 12px;
  border-left: 4px solid #ffa300;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.about__philosophy-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.about__philosophy-item h3 {
  font-size: 1.3rem;
  color: #0a2d69;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.about__philosophy-item p {
  margin: 0;
  font-size: 1rem;
  line-height: 1.6;
}

.about__differences {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

@media (max-width: 767px) {
  .about__differences {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

.about__difference-item {
  background: #ffffff;
  padding: 2rem;
  border-radius: 16px;
  border: 1px solid #e8f4f8;
  box-shadow: 0 4px 16px rgba(10, 45, 105, 0.08);
  transition: all 0.3s ease;
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
}

.about__difference-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(10, 45, 105, 0.15);
  border-color: #ffa300;
}

@media (max-width: 767px) {
  .about__difference-item {
    padding: 1.5rem;
    gap: 1rem;
  }
}

.about__difference-icon {
  font-size: 2.5rem;
  flex-shrink: 0;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #ffa300 0%, #ffb84d 100%);
  border-radius: 50%;
  box-shadow: 0 4px 12px rgba(255, 163, 0, 0.3);
}

@media (max-width: 767px) {
  .about__difference-icon {
    font-size: 2rem;
    width: 50px;
    height: 50px;
  }
}

.about__difference-content h3 {
  font-size: 1.4rem;
  color: #0a2d69;
  margin: 0 0 0.75rem 0;
  font-weight: 600;
}

@media (max-width: 767px) {
  .about__difference-content h3 {
    font-size: 1.2rem;
  }
}

.about__difference-content p {
  margin: 0;
  font-size: 1rem;
  line-height: 1.6;
  color: #555;
}

@media (max-width: 767px) {
  .about__difference-content p {
    font-size: 0.95rem;
  }
}

.about__cta {
  background: linear-gradient(135deg, #0a2d69 0%, #1e4a8c 100%);
  color: white;
  padding: 4rem 2rem;
  text-align: center;
}

.about__cta-content {
  max-width: 600px;
  margin: 0 auto;
}

.about__cta h2 {
  font-size: 2.5rem;
  color: white;
  margin-bottom: 1rem;
  line-height: 1.2;
}

.about__cta p {
  font-size: 1.2rem;
  color: #e8f4f8;
  line-height: 1.6;
  margin-bottom: 2.5rem;
}

.about__cta-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: 1.5rem;
}

.about__cta-note {
  font-size: 0.9rem;
  color: #e8f4f8;
  margin: 0;
  font-style: italic;
}

/* CTA section button overrides for dark background */
.about__cta-buttons .btn--secondary {
  background-color: transparent;
  color: white;
  border: 2px solid white;
}

.about__cta-buttons .btn--secondary:hover:not(:disabled) {
  background-color: white;
  color: #0a2d69;
  border-color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
  .about__problem,
  .about__solution,
  .about__section,
  .about__founder,
  .about__cta {
    padding: 3rem 1rem;
  }

  .about__problem-content,
  .about__solution-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .about__solution-image {
    order: unset;
  }

  .about__solution-text {
    order: unset;
  }

  .about__problem h2,
  .about__solution h2,
  .about__founder-text h2,
  .about__cta h2 {
    font-size: 2rem;
  }

  .about__founder-content {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }

  .about__founder-photo,
  .about__founder-placeholder {
    width: 200px;
    height: 200px;
  }

  .about__founder-placeholder span {
    font-size: 2.5rem;
  }

  .about__cta-buttons {
    flex-direction: column;
    align-items: center;
  }
}
