.features {
  /* Remove padding to allow full-width sections */
}

.features__hero {
  background: linear-gradient(135deg, #0a2d69 0%, #1e4a8c 100%);
  color: white;
  padding: 3rem 2rem;
  text-align: center;
}

.features__hero-content {
  max-width: 800px;
  margin: 0 auto;
}

.features__hero h1 {
  font-size: 3rem;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.features__hero p {
  font-size: 1.2rem;
  line-height: 1.6;
  color: #e8f4f8;
  margin: 0;
}

/* Feature Showcase Section */
.features__showcase {
  padding: 0;
  background: white;
}

.features__showcase-container {
  max-width: 1200px;
  margin: 0 auto;
}

.features__showcase-item {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
  padding: 4rem 2rem;
  border-bottom: 1px solid #e5e7eb;
}

.features__showcase-item:last-child {
  border-bottom: none;
}

.features__showcase-item--reverse {
  background: #f8fafc;
}

.features__showcase-item--reverse .features__showcase-content {
  order: 2;
}

.features__showcase-item--reverse .features__showcase-image {
  order: 1;
}

.features__showcase-content h2 {
  font-size: 2.5rem;
  color: #1a1a1a;
  margin-bottom: 0.5rem;
  line-height: 1.2;
}

.features__showcase-subtitle {
  font-size: 1.2rem;
  color: #0a2d69;
  font-weight: 600;
  margin-bottom: 1.5rem;
  line-height: 1.3;
}

.features__showcase-description {
  font-size: 1.1rem;
  color: #666;
  line-height: 1.6;
  margin-bottom: 2rem;
}

.features__showcase-benefits {
  list-style: none;
  padding: 0;
  margin: 0;
}

.features__showcase-benefits li {
  color: #0a2d69;
  font-weight: 500;
  padding: 0.5rem 0;
  position: relative;
  padding-left: 1.5rem;
  font-size: 1rem;
}

.features__showcase-benefits li::before {
  content: "✓";
  position: absolute;
  left: 0;
  color: #22c55e;
  font-weight: bold;
}

.features__showcase-image {
  display: flex;
  justify-content: center;
  align-items: center;
}

.features__screenshot {
  width: 100%;
  max-width: 650px;
  height: auto;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  border: 1px solid #e2e8f0;
  transition: transform 0.2s, box-shadow 0.2s;
}

.features__screenshot:hover {
  transform: translateY(-4px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.features__screenshot-placeholder {
  width: 100%;
  max-width: 650px;
  aspect-ratio: 16/10;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  border: 2px solid #cbd5e1;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 2rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.features__screenshot-text {
  color: #475569;
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  display: block;
}

.features__screenshot-note {
  color: #64748b;
  font-size: 0.875rem;
  font-style: italic;
}

.features__grid {
  padding: 4rem 2rem;
  background: #f8fafc;
}

.features__grid-header {
  text-align: center;
  margin-bottom: 4rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.features__grid-header h2 {
  font-size: 2.5rem;
  color: #1a1a1a;
  margin-bottom: 1rem;
}

.features__grid-header p {
  font-size: 1.1rem;
  color: #666;
  line-height: 1.6;
  margin: 0;
}

.features__container {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 3rem;
}

.features__card {
  background: white;
  padding: 2.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  text-align: center;
  transition: transform 0.2s, box-shadow 0.2s;
}

.features__card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.features__card-icon {
  font-size: 3rem;
  margin-bottom: 1.5rem;
}

.features__card h3 {
  font-size: 1.5rem;
  color: #1a1a1a;
  margin-bottom: 1rem;
}

.features__card p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.features__benefits {
  list-style: none;
  padding: 0;
  margin: 0;
}

.features__benefits li {
  color: #0a2d69;
  font-weight: 500;
  padding: 0.25rem 0;
  position: relative;
  padding-left: 1.5rem;
}

.features__benefits li::before {
  content: "✓";
  position: absolute;
  left: 0;
  color: #22c55e;
  font-weight: bold;
}

.features__cta {
  background: white;
  padding: 4rem 2rem;
  text-align: center;
}

.features__cta-content {
  max-width: 600px;
  margin: 0 auto;
}

.features__cta h2 {
  font-size: 2.5rem;
  color: #1a1a1a;
  margin-bottom: 1rem;
}

.features__cta p {
  font-size: 1.1rem;
  color: #666;
  line-height: 1.6;
  margin-bottom: 2.5rem;
}

.features__cta-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

@media (max-width: 768px) {
  .features__hero {
    padding: 2rem 1rem;
  }

  .features__hero h1 {
    font-size: 2rem;
  }

  /* Showcase responsive */
  .features__showcase-item {
    grid-template-columns: 1fr;
    gap: 2rem;
    padding: 3rem 1rem;
  }

  .features__showcase-item--reverse .features__showcase-content,
  .features__showcase-item--reverse .features__showcase-image {
    order: unset;
  }

  .features__showcase-content h2 {
    font-size: 2rem;
  }

  .features__showcase-subtitle {
    font-size: 1.1rem;
  }

  .features__showcase-description {
    font-size: 1rem;
  }

  .features__screenshot-placeholder {
    max-width: 100%;
    padding: 1.5rem;
  }

  .features__grid {
    padding: 3rem 1rem;
  }

  .features__grid-header h2 {
    font-size: 2rem;
  }

  .features__container {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .features__card {
    padding: 2rem;
  }

  .features__cta {
    padding: 3rem 1rem;
  }

  .features__cta h2 {
    font-size: 2rem;
  }

  .features__cta-buttons {
    flex-direction: column;
    align-items: center;
  }
}
