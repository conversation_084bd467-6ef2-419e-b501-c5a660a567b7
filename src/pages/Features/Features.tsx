import React from "react";
import Button from "../../components/ui/Button";
import "./Features.css";

const Features: React.FC = () => {
  const showcaseFeatures = [
    {
      id: "kiosk",
      title: "Tablet Kiosk Interface",
      subtitle: "Never wonder if critical routines got done",
      description:
        "Perfect for retail stores, cafes, and service businesses. Staff see exactly what needs to be done and confirm completion with a simple tap. No more 'I forgot' or 'I thought someone else did it' - just reliable routine completion.",
      benefits: [
        "No per-user licensing costs",
        "Clear routine visibility",
        "Simple completion tracking",
        "Touch-optimized interface",
      ],
      screenshotPath: "/images/screenshots/kiosk-interface.png",
      screenshotAlt:
        "Kiosk interface showing daily tasks organized by routine with completion progress",
    },
    {
      id: "scheduling",
      title: "Routine Scheduling",
      subtitle: "Critical routines happen when they're supposed to",
      description:
        "Create routines like 'Open Store' or 'Close Store' with specific days and times. Tasks automatically appear when needed, ensuring nothing gets forgotten during busy periods or shift changes.",
      benefits: [
        "Automated routine generation",
        "Flexible scheduling options",
        "Location-specific routines",
        "Prevents missed routines",
      ],
      screenshotPath: "/images/screenshots/routine-schedules.png",
      screenshotAlt:
        "Routine schedules page showing weekly schedules for different store routines",
    },
    {
      id: "tasks",
      title: "Smart Task Organization",
      subtitle: "Organized by routines, not random lists",
      description:
        "Tasks are grouped into logical routines like 'Opening Procedures' or 'Maintenance Tasks' with clear priorities and deadlines. No more hunting through endless task lists.",
      benefits: [
        "Routine-based grouping",
        "Priority levels",
        "Expiry management",
        "Role assignments",
      ],
      screenshotPath: "/images/screenshots/routine-tasks.png",
      screenshotAlt:
        "Routine tasks page showing tasks organized by routine with priority indicators",
    },
    {
      id: "dashboard",
      title: "Manager Dashboard",
      subtitle: "Know what's done without asking",
      description:
        "Monitor routine completion across all your locations in real-time. See what's been completed and what's still pending - all from one central dashboard. No more wondering if critical routines got done.",
      benefits: [
        "Real-time completion status",
        "Multi-location oversight",
        "Routine completion tracking",
        "Instant visibility",
      ],
      screenshotPath: "/images/screenshots/manager-dashboard.png",
      screenshotAlt:
        "Today's tasks dashboard showing task completion status across multiple locations",
    },
  ];

  const additionalFeatures = [
    {
      icon: "🏢",
      title: "Multi-Location Management",
      description: "Centralized control across all your business locations",
      benefits: [
        "Location-specific settings",
        "Centralized oversight",
        "Manager assignments",
      ],
    },
    {
      icon: "👥",
      title: "Role-Based Access",
      description: "Different interfaces for owners, managers, and staff",
      benefits: [
        "Appropriate access levels",
        "Simplified interfaces",
        "Security controls",
      ],
    },
    {
      icon: "📈",
      title: "Performance Tracking",
      description:
        "Monitor completion rates and identify improvement opportunities",
      benefits: [
        "Completion metrics",
        "Trend analysis",
        "Accountability tools",
      ],
    },
    {
      icon: "🔄",
      title: "Seamless Handoffs",
      description: "Smooth shift transitions with notes and task summaries",
      benefits: ["Shift communication", "Task continuity", "Issue tracking"],
    },
  ];

  return (
    <div className="features">
      <section className="features__hero">
        <div className="features__hero-content">
          <h1>See How AdminBuddy Ensures Routine Reliability</h1>
          <p>
            Discover how AdminBuddy eliminates missed routines and creates
            consistent operations with intuitive interfaces designed for
            real-world businesses.
          </p>
        </div>
      </section>

      {/* Feature Showcase Section */}
      <section className="features__showcase">
        <div className="features__showcase-container">
          {showcaseFeatures.map((feature, index) => (
            <div
              key={feature.id}
              className={`features__showcase-item ${
                index % 2 === 1 ? "features__showcase-item--reverse" : ""
              }`}
            >
              <div className="features__showcase-content">
                <h2>{feature.title}</h2>
                <h3 className="features__showcase-subtitle">
                  {feature.subtitle}
                </h3>
                <p className="features__showcase-description">
                  {feature.description}
                </p>
                <ul className="features__showcase-benefits">
                  {feature.benefits.map((benefit, i) => (
                    <li key={i}>{benefit}</li>
                  ))}
                </ul>
              </div>
              <div className="features__showcase-image">
                <img
                  src={feature.screenshotPath}
                  alt={feature.screenshotAlt}
                  className="features__screenshot"
                  onError={(e) => {
                    // Fallback to placeholder if image doesn't exist
                    const target = e.target as HTMLImageElement;
                    target.style.display = "none";
                    const placeholder =
                      target.nextElementSibling as HTMLElement;
                    if (placeholder) placeholder.style.display = "flex";
                  }}
                />
                <div
                  className="features__screenshot-placeholder"
                  style={{ display: "none" }}
                >
                  <span className="features__screenshot-text">
                    Screenshot: {feature.screenshotAlt}
                  </span>
                  <small className="features__screenshot-note">
                    Coming soon - {feature.screenshotPath.split("/").pop()}
                  </small>
                </div>
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* Additional Features Grid */}
      <section className="features__grid">
        <div className="features__grid-header">
          <h2>Everything You Need to Succeed</h2>
          <p>
            Additional features that make AdminBuddy the complete solution for
            your business
          </p>
        </div>
        <div className="features__container">
          {additionalFeatures.map((feature, index) => (
            <div key={index} className="features__card">
              <div className="features__card-icon">{feature.icon}</div>
              <h3>{feature.title}</h3>
              <p>{feature.description}</p>
              <ul className="features__benefits">
                {feature.benefits.map((benefit, i) => (
                  <li key={i}>{benefit}</li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </section>

      <section className="features__cta">
        <div className="features__cta-content">
          <h2>Ready for reliable routine completion?</h2>
          <p>
            Start your free trial today and see the difference consistent
            operations make for your business.
          </p>
          <div className="features__cta-buttons">
            <Button variant="primary" size="large" href="/signup">
              Start Free Trial
            </Button>
            <Button variant="secondary" size="large" href="/pricing">
              View Pricing
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Features;
