import React, { useState, useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import {
  signInUser,
  sendPasswordReset,
  getCurrentUser,
} from "../../services/userService";
import Button from "../../components/ui/Button";
import "./Login.css";

const Login: React.FC = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [resetLoading, setResetLoading] = useState(false);
  const [resetMessage, setResetMessage] = useState("");
  const [checkingAuth, setCheckingAuth] = useState(true);
  const navigate = useNavigate();

  // Check if user is already authenticated on component mount
  useEffect(() => {
    console.log("🔐 Login: useEffect triggered - starting auth check");

    const checkExistingAuth = async () => {
      try {
        console.log("🔐 Login: Checking for existing authentication...");

        // Check localStorage for auth data (same as kiosk interface)
        const authData = localStorage.getItem("adminbuddy_auth");
        console.log(
          "🔐 Login: Raw auth data from localStorage:",
          authData ? "Found" : "Not found"
        );

        if (!authData) {
          console.log("🔐 Login: No auth data found in localStorage");
          setCheckingAuth(false);
          return;
        }

        const { uid, email } = JSON.parse(authData);
        console.log("🔐 Login: Found auth data for:", email);
        console.log("🔐 Login: User UID:", uid);

        // Get fresh user profile from Firestore
        console.log("🔐 Login: Importing getUserProfile service...");
        const { getUserProfile } = await import(
          "../../services/adminBuddyFirebaseService"
        );
        console.log("🔐 Login: Calling getUserProfile for UID:", uid);
        const userProfile = await getUserProfile(uid);
        console.log("🔐 Login: getUserProfile result:", userProfile);

        if (!userProfile) {
          console.log("🔐 Login: Could not load user profile from Firestore");
          localStorage.removeItem("adminbuddy_auth");
          setCheckingAuth(false);
          return;
        }

        console.log(
          "🔐 Login: User already authenticated, redirecting...",
          userProfile.email
        );
        console.log("🔐 Login: User role:", userProfile.role);
        console.log(
          "🔐 Login: User kioskLocationId:",
          userProfile.kioskLocationId
        );

        // Check if there's a stored redirect URL
        const redirectUrl = localStorage.getItem(
          "adminbuddy_redirect_after_login"
        );
        console.log("📍 Login: Checking for stored redirect URL:", redirectUrl);
        if (redirectUrl) {
          console.log("📍 Login: Found stored redirect URL:", redirectUrl);
          localStorage.removeItem("adminbuddy_redirect_after_login");
          console.log("📍 Login: Navigating to stored URL:", redirectUrl);
          navigate(redirectUrl);
          return;
        }

        // Redirect based on user role
        console.log(
          "🔀 Login: No stored redirect URL, using role-based redirect"
        );
        console.log("🔀 Login: User role for redirect:", userProfile.role);
        switch (userProfile.role) {
          case "owner":
            console.log("👑 Login: Owner user, redirecting to /dashboard");
            navigate("/dashboard");
            break;
          case "manager":
            console.log("👨‍💼 Login: Manager user, redirecting to /manager");
            navigate("/manager");
            break;
          case "kiosk":
            console.log(
              "🏪 Login: Kiosk user detected, redirecting to kiosk interface"
            );
            // For kiosk users, redirect to their specific location if available
            if (userProfile.kioskLocationId) {
              const kioskUrl = `/kiosk/${userProfile.kioskLocationId}`;
              console.log(
                "🏪 Login: Redirecting to specific kiosk location:",
                kioskUrl
              );
              navigate(kioskUrl);
            } else {
              console.log(
                "🏪 Login: No specific location, redirecting to /kiosk"
              );
              navigate("/kiosk");
            }
            break;
          default:
            console.log("🏠 Login: Default case, redirecting to /");
            navigate("/");
        }
      } catch (error) {
        console.log("🔐 Login: Error checking authentication:", error);
        // Clear potentially corrupted auth data
        localStorage.removeItem("adminbuddy_auth");
      } finally {
        setCheckingAuth(false);
      }
    };

    checkExistingAuth();
  }, [navigate]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    try {
      const user = await signInUser(email, password);

      if (user) {
        // Redirect based on user role
        switch (user.role) {
          case "owner":
            navigate("/dashboard");
            break;
          case "manager":
            navigate("/manager");
            break;
          case "kiosk":
            console.log(
              "🏪 Kiosk user detected, redirecting to kiosk interface"
            );
            navigate("/kiosk");
            break;
          default:
            navigate("/");
        }
      }
    } catch (error: any) {
      setError(error.message || "Failed to sign in");
    } finally {
      setLoading(false);
    }
  };

  const handleForgotPassword = async () => {
    if (!email.trim()) {
      setError("Please enter your email address first");
      return;
    }

    // Check if this is a username (no @) - can't reset password for kiosk users
    if (!email.includes("@")) {
      setError(
        "Password reset is only available for email accounts. Contact your manager to reset kiosk passwords."
      );
      return;
    }

    setResetLoading(true);
    setError("");
    setResetMessage("");

    try {
      await sendPasswordReset(email.trim());
      setResetMessage(
        "Password reset email sent! Check your inbox and spam folder."
      );
    } catch (error: any) {
      setError(error.message || "Failed to send password reset email");
    } finally {
      setResetLoading(false);
    }
  };

  // Show loading state while checking authentication
  if (checkingAuth) {
    return (
      <div className="login">
        <div className="login__container">
          <div className="login__header">
            <Link to="/" className="login__logo">
              AdminBuddy
            </Link>
            <h1>Checking Authentication...</h1>
            <p>Please wait while we verify your login status</p>
          </div>
          <div className="login__loading">
            <div className="spinner"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="login">
      <div className="login__container">
        <div className="login__header">
          <Link to="/" className="login__logo">
            AdminBuddy
          </Link>
          <h1>Sign In</h1>
          <p>Access your AdminBuddy dashboard</p>
        </div>

        <form onSubmit={handleSubmit} className="login__form">
          {error && <div className="login__error">{error}</div>}
          {resetMessage && <div className="login__success">{resetMessage}</div>}

          <div className="login__field">
            <label htmlFor="email">Email / Username</label>
            <input
              type="text"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              placeholder="Enter your email or kiosk username"
            />
          </div>

          <div className="login__field">
            <label htmlFor="password">Password</label>
            <input
              type="password"
              id="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              placeholder="Enter your password"
            />
          </div>

          <Button
            type="submit"
            variant="primary"
            size="large"
            loading={loading}
            className="login__submit"
          >
            {loading ? "Signing In..." : "Sign In"}
          </Button>

          <div className="login__forgot-password">
            <button
              type="button"
              onClick={handleForgotPassword}
              disabled={resetLoading}
              className="login__forgot-link"
            >
              {resetLoading ? "Sending..." : "Forgot your password?"}
            </button>
          </div>
        </form>

        <div className="login__footer">
          <p>
            Don't have an account?{" "}
            <Link to="/signup">Start your free trial</Link>
          </p>
          <p>
            <Link to="/">← Back to AdminBuddy</Link>
          </p>
        </div>
      </div>
    </div>
  );
};

export default Login;
