import React, { useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { useTenant } from "../../contexts/TenantContext";
import Button from "../../components/ui/Button";
import "./Signup.css";

const Signup: React.FC = () => {
  const navigate = useNavigate();
  const { signup } = useTenant();

  const [formData, setFormData] = useState({
    email: "",
    password: "",
    confirmPassword: "",
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    // Clear error when user starts typing
    if (error) setError("");
  };

  const validateForm = () => {
    if (!formData.email.trim()) {
      setError("Email is required");
      return false;
    }

    if (!formData.email.includes("@")) {
      setError("Please enter a valid email address");
      return false;
    }

    if (!formData.password) {
      setError("Password is required");
      return false;
    }

    if (formData.password.length < 6) {
      setError("Password must be at least 6 characters");
      return false;
    }

    if (formData.password !== formData.confirmPassword) {
      setError("Passwords don't match");
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    setLoading(true);
    setError("");

    try {
      await signup(formData.email.trim(), formData.password);
      // Navigate to onboarding (we'll create this next)
      navigate("/onboarding");
    } catch (error: any) {
      console.error("Signup error:", error);
      setError(error.message || "Failed to create account. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="signup">
      <div className="signup__container">
        <div className="signup__header">
          <Link to="/" className="signup__logo">
            AdminBuddy
          </Link>
          <h1>Start Your Free Trial</h1>
          <p>Get organized in minutes, no credit card required</p>
        </div>

        <form className="signup__form" onSubmit={handleSubmit}>
          {error && <div className="signup__error">{error}</div>}

          <div className="signup__field">
            <label htmlFor="email">Email Address</label>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              placeholder="<EMAIL>"
              disabled={loading}
              autoFocus
            />
          </div>

          <div className="signup__field">
            <label htmlFor="password">Password</label>
            <input
              type="password"
              id="password"
              name="password"
              value={formData.password}
              onChange={handleInputChange}
              placeholder="At least 6 characters"
              disabled={loading}
            />
          </div>

          <div className="signup__field">
            <label htmlFor="confirmPassword">Confirm Password</label>
            <input
              type="password"
              id="confirmPassword"
              name="confirmPassword"
              value={formData.confirmPassword}
              onChange={handleInputChange}
              placeholder="Confirm your password"
              disabled={loading}
            />
          </div>

          <Button
            type="submit"
            variant="primary"
            size="large"
            disabled={loading}
            className="signup__submit"
          >
            {loading ? "Creating Account..." : "Start Free Trial"}
          </Button>
        </form>

        <div className="signup__footer">
          <p>
            Already have an account? <Link to="/login">Sign in here</Link>
          </p>
          <p>
            <Link to="/">← Back to AdminBuddy</Link>
          </p>
        </div>
      </div>
    </div>
  );
};

export default Signup;
