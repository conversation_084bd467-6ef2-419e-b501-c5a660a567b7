.signup {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #0a2d69 0%, #1e4a8c 100%);
  padding: 2rem;
}

.signup__container {
  background: white;
  border-radius: 12px;
  padding: 3rem;
  width: 100%;
  max-width: 400px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.signup__logo {
  font-size: 1.5rem;
  font-weight: bold;
  color: #0a2d69;
  text-decoration: none;
  display: block;
  margin-bottom: 1rem;
}

.signup__header h1 {
  font-size: 2rem;
  color: #1a1a1a;
  margin-bottom: 0.5rem;
}

.signup__header p {
  color: #666;
  margin: 0 0 2rem 0;
}

.signup__form {
  margin: 2rem 0;
  text-align: left;
}

.signup__error {
  background: #fee;
  color: #c33;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
  border: 1px solid #fcc;
  font-size: 0.9rem;
}

.signup__field {
  margin-bottom: 1.5rem;
}

.signup__field label {
  display: block;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.signup__field input {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.2s, box-shadow 0.2s;
  box-sizing: border-box;
}

.signup__field input:focus {
  outline: none;
  border-color: #0a2d69;
  box-shadow: 0 0 0 3px rgba(10, 45, 105, 0.1);
}

.signup__field input:disabled {
  background-color: #f8fafc;
  cursor: not-allowed;
  opacity: 0.7;
}

.signup__field input::placeholder {
  color: #94a3b8;
}

.signup__submit {
  width: 100%;
  margin-top: 1rem;
}

.signup__footer {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #eee;
}

.signup__footer p {
  margin: 0.5rem 0;
  color: #666;
  font-size: 0.9rem;
}

.signup__footer a {
  color: #0a2d69;
  text-decoration: none;
  font-weight: 500;
}

.signup__footer a:hover {
  text-decoration: underline;
}
