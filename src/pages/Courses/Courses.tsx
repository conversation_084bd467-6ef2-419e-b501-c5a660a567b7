import React from "react";
import CourseCardNoImage from "../../components/course/CourseCard/CourseCardNoImage";
import CourseCardSkeleton from "../../components/course/CourseCardSkeleton";
import ErrorBoundary from "../../components/ui/ErrorBoundary";
import { useCourses, useScrollToElement, usePerformance } from "../../hooks";
import "./Courses.css";

const Courses: React.FC = () => {
  // Add performance monitoring
  usePerformance({
    componentName: "Courses",
    threshold: 50, // Allow more time for data loading
  });

  const { courses, loading, error } = useCourses();

  const { setRef } = useScrollToElement(
    courses,
    (course, scrollTarget) => course.title === scrollTarget
  );

  if (error) {
    return (
      <div className="courses">
        <div className="courses__error">
          <h2>Unable to load courses</h2>
          <p>{error}</p>
        </div>
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <div className="courses">
        <section className="courses__header">
          <h2>Explore Our Courses</h2>
          <p className="courses__subtitle">
            Build real projects while learning programming fundamentals in a
            supportive, collaborative environment.
          </p>
          <p>
            All our classes are project-based and hands-on, designed to be
            supportive and engaging — no prior experience needed.
          </p>
        </section>

        <div className="courses__content">
          <div className="courses__grid">
            {loading
              ? Array.from({ length: 6 }, (_, index) => (
                  <CourseCardSkeleton key={index} />
                ))
              : courses.map((course, index) => (
                  <div key={course.slug} ref={setRef(index)}>
                    <CourseCardNoImage
                      title={course.title}
                      description={course.description}
                      price={course.price}
                      startDate={course.startDate}
                      schedule={course.schedule}
                      slug={course.slug}
                      category={course.category}
                      categories={course.categories}
                      variant="title-hexagon"
                    />
                  </div>
                ))}
          </div>
        </div>
      </div>
    </ErrorBoundary>
  );
};

export default Courses;
