.courses {
  /* Remove padding to allow full-width header */
}

.courses__content {
  padding: 1rem;
  max-width: 1400px;
  margin: 0 auto;
}

@media (min-width: 768px) {
  .courses__content {
    padding: 2rem;
  }
}

.courses__header {
  background: linear-gradient(135deg, #0a2d69 0%, #1e4a8c 100%);
  color: white;
  padding: 4rem 2rem;
  text-align: center;
  margin-bottom: 3rem;
}

@media (max-width: 767px) {
  .courses__header {
    padding: 2rem 1rem;
  }
}

@media (min-width: 768px) {
  .courses__header {
    margin-bottom: 4rem;
  }
}

.courses__header h2 {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: white;
}

@media (max-width: 767px) {
  .courses__header h2 {
    font-size: 2rem;
  }
}

.courses__subtitle {
  font-size: 1.4rem;
  color: #ffd46f;
  margin-bottom: 1.5rem;
  font-weight: 500;
}

@media (max-width: 767px) {
  .courses__subtitle {
    font-size: 1.2rem;
  }
}

.courses__header p {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #e8f4f8;
  margin: 0;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
}

.courses__header .courses__subtitle {
  color: #ffd46f !important;
}

@media (max-width: 767px) {
  .courses__header p {
    font-size: 1rem;
  }
}

.courses__grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
  align-items: stretch;
}

@media (min-width: 640px) {
  .courses__grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .courses__grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }
}

@media (min-width: 1400px) {
  .courses__grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

.courses__error {
  text-align: center;
  padding: 3rem 2rem;
  background-color: white;
  border-radius: 12px;
  border-left: 4px solid #dc3545;
}

.courses__error h2 {
  color: #dc3545;
  margin-bottom: 1rem;
}

.courses__error p {
  color: #666;
}

.courses__no-results {
  text-align: center;
  padding: 3rem 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  border: 1px solid #e8f4f8;
  margin-top: 2rem;
}

.courses__no-results h3 {
  margin: 0 0 1rem 0;
  color: #0a2d69;
  font-size: 1.5rem;
}

.courses__no-results p {
  margin: 0;
  color: #666;
  font-size: 1rem;
}
