import React from "react";
import Button from "../../components/ui/Button";
import "./Contact.css";

const Contact: React.FC = () => {
  return (
    <div className="contact">
      <section className="contact__hero">
        <div className="contact__hero-content">
          <h1>Get in Touch</h1>
          <p>
            Ready to streamline your business operations? We're here to help you
            get started.
          </p>
        </div>
      </section>

      {/* Contact Options Section */}
      <section className="contact__options">
        <div className="contact__container">
          <div className="contact__options-grid">
            <div className="contact__option">
              <div className="contact__option-icon">💬</div>
              <h3>Get Support</h3>
              <p>
                Need help with setup or have questions about features? We're
                here to help.
              </p>
              <div className="contact__option-details">
                <strong>Email:</strong> <EMAIL>
                <br />
                <strong>Response:</strong> Within 24 hours
              </div>
            </div>

            <div className="contact__option">
              <div className="contact__option-icon">🚀</div>
              <h3>Sales & Demos</h3>
              <p>
                Want to see AdminBuddy in action or discuss enterprise needs?
              </p>
              <div className="contact__option-details">
                <strong>Email:</strong> <EMAIL>
                <br />
                <strong>Response:</strong> Same business day
              </div>
            </div>

            <div className="contact__option">
              <div className="contact__option-icon">🤝</div>
              <h3>Partnerships</h3>
              <p>
                Interested in partnering with AdminBuddy or integrating our
                solution?
              </p>
              <div className="contact__option-details">
                <strong>Email:</strong> <EMAIL>
                <br />
                <strong>Response:</strong> Within 3 business days
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="contact__faq">
        <div className="contact__container">
          <h2>Frequently Asked Questions</h2>
          <div className="contact__faq-grid">
            <div className="contact__faq-item">
              <h3>How quickly can we get started?</h3>
              <p>
                Most businesses are up and running within 5 minutes. Create your
                account, add your locations, set up a few routines, and you're
                ready to go.
              </p>
            </div>
            <div className="contact__faq-item">
              <h3>Do we need special hardware?</h3>
              <p>
                Any tablet or computer with a web browser works. We recommend
                mounting a tablet in your break room or office for easy staff
                access.
              </p>
            </div>
            <div className="contact__faq-item">
              <h3>What if we have multiple locations?</h3>
              <p>
                Perfect! AdminBuddy is designed for multi-location businesses.
                Manage all your locations from one dashboard with
                location-specific tasks and routines.
              </p>
            </div>
            <div className="contact__faq-item">
              <h3>Can we customize it for our business?</h3>
              <p>
                Absolutely. Create custom routines, set your own task
                priorities, and organize everything the way your business works.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="contact__cta">
        <div className="contact__cta-content">
          <h2>Ready to Get Organized?</h2>
          <p>
            Start your free trial today and see how AdminBuddy can transform
            your team's task management.
          </p>
          <div className="contact__cta-buttons">
            <Button variant="primary" size="large" href="/signup">
              Start Free Trial
            </Button>
            <Button variant="secondary" size="large" href="/features">
              See How It Works
            </Button>
          </div>
          <p className="contact__cta-note">
            ✓ 14-day free trial • ✓ No credit card required
          </p>
        </div>
      </section>
    </div>
  );
};

export default Contact;
