.contact__hero {
  background: linear-gradient(135deg, #0a2d69 0%, #1e4a8c 100%);
  color: white;
  padding: 3rem 2rem;
  text-align: center;
}

.contact__hero-content {
  max-width: 600px;
  margin: 0 auto;
}

.contact__hero h1 {
  font-size: 3rem;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.contact__hero p {
  font-size: 1.2rem;
  color: #e8f4f8;
  margin: 0;
}

/* Contact Options Section */
.contact__options {
  padding: 4rem 2rem;
  background: white;
}

.contact__container {
  max-width: 1200px;
  margin: 0 auto;
}

.contact__options-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.contact__option {
  background: #f8fafc;
  padding: 2.5rem;
  border-radius: 12px;
  text-align: center;
  border: 1px solid #e2e8f0;
  transition: transform 0.2s, box-shadow 0.2s;
}

.contact__option:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.contact__option-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.contact__option h3 {
  font-size: 1.5rem;
  color: #1a1a1a;
  margin-bottom: 1rem;
}

.contact__option p {
  font-size: 1rem;
  color: #666;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.contact__option-details {
  background: white;
  padding: 1rem;
  border-radius: 8px;
  font-size: 0.9rem;
  color: #666;
  line-height: 1.5;
  text-align: left;
}

.contact__option-details strong {
  color: #0a2d69;
}

/* FAQ Section */
.contact__faq {
  padding: 4rem 2rem;
  background: #f8fafc;
}

.contact__faq h2 {
  font-size: 2.5rem;
  color: #1a1a1a;
  text-align: center;
  margin-bottom: 3rem;
}

.contact__faq-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  max-width: 1000px;
  margin: 0 auto;
}

.contact__faq-item {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.contact__faq-item h3 {
  font-size: 1.2rem;
  color: #0a2d69;
  margin-bottom: 1rem;
  line-height: 1.3;
}

.contact__faq-item p {
  font-size: 1rem;
  color: #666;
  line-height: 1.6;
  margin: 0;
}

/* CTA Section */
.contact__cta {
  background: linear-gradient(135deg, #0a2d69 0%, #1e4a8c 100%);
  color: white;
  padding: 4rem 2rem;
  text-align: center;
}

.contact__cta-content {
  max-width: 600px;
  margin: 0 auto;
}

.contact__cta h2 {
  font-size: 2.5rem;
  color: white;
  margin-bottom: 1rem;
  line-height: 1.2;
}

.contact__cta p {
  font-size: 1.2rem;
  color: #e8f4f8;
  line-height: 1.6;
  margin-bottom: 2.5rem;
}

.contact__cta-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: 1.5rem;
}

.contact__cta-note {
  font-size: 0.9rem;
  color: #e8f4f8;
  margin: 0;
  font-style: italic;
}

/* CTA section button overrides for dark background */
.contact__cta-buttons .btn--secondary {
  background-color: transparent;
  color: white;
  border: 2px solid white;
}

.contact__cta-buttons .btn--secondary:hover:not(:disabled) {
  background-color: white;
  color: #0a2d69;
  border-color: white;
}

@media (max-width: 768px) {
  .contact__hero {
    padding: 2rem 1rem;
  }

  .contact__hero h1 {
    font-size: 2rem;
  }

  .contact__options,
  .contact__faq,
  .contact__cta {
    padding: 3rem 1rem;
  }

  .contact__options-grid,
  .contact__faq-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .contact__option {
    padding: 2rem;
  }

  .contact__option-icon {
    font-size: 2.5rem;
  }

  .contact__faq h2,
  .contact__cta h2 {
    font-size: 2rem;
  }

  .contact__cta-buttons {
    flex-direction: column;
    align-items: center;
  }
}
