import React, { useState, useEffect } from "react";
import { useParams, Link } from "react-router-dom";
import { EXTERNAL_LINKS, ROUTES } from "../../constants";
import Button from "../../components/ui/Button";
import LoadingSpinner from "../../components/ui/LoadingSpinner";
import ErrorBoundary from "../../components/ui/ErrorBoundary";
import RegistrationForm, {
  RegistrationData,
} from "../../components/course/RegistrationForm";
import { useCourses, usePerformance } from "../../hooks";
import { submitRegistration } from "../../services/registrationService";
import { getCourseCapacity } from "../../services/firebaseCourseCapacityService";
import { getCourseSessionsService } from "../../services/sessionService";
import { CourseSession } from "../../types";
import { formatPrice } from "../../utils/priceUtils";
import "./CourseDetail.css";

const CourseDetail: React.FC = () => {
  // Add performance monitoring
  usePerformance({
    componentName: "CourseDetail",
    threshold: 25, // Allow reasonable time for course lookup
  });

  const { slug } = useParams<{ slug: string }>();
  const { loading, error, getCourseBySlug } = useCourses();

  // Registration state
  const [showRegistrationForm, setShowRegistrationForm] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [registrationMessage, setRegistrationMessage] = useState<string | null>(
    null
  );
  const [capacity, setCapacity] = useState<any>(null);
  const [sessions, setSessions] = useState<CourseSession[]>([]);

  const course = slug ? getCourseBySlug(slug) : undefined;

  // Load course capacity information and sessions
  useEffect(() => {
    const loadCourseData = async () => {
      if (course?.id && course?.slug) {
        try {
          // Load capacity data
          const capacityData = await getCourseCapacity(course.slug);
          setCapacity(capacityData);

          // Load sessions - try both course ID and slug
          let sessionsData = await getCourseSessionsService(course.id);

          // If no sessions found with ID, try with slug
          if (sessionsData.length === 0 && course.slug !== course.id) {
            sessionsData = await getCourseSessionsService(course.slug);
          }
          setSessions(sessionsData);
        } catch (error) {
          console.error("Error loading course data:", error);
        }
      }
    };

    loadCourseData();
  }, [course?.id, course?.slug]);

  const handleRegistrationSubmit = async (formData: RegistrationData) => {
    if (!course) return;

    setIsSubmitting(true);
    setRegistrationMessage(null);

    try {
      const result = await submitRegistration(
        course.slug,
        course.title,
        course.price || "Contact for pricing",
        formData
      );

      if (result.success) {
        setShowRegistrationForm(false);
        setRegistrationMessage(result.message);
        // Scroll to top to show success message
        window.scrollTo({ top: 0, behavior: "smooth" });
      } else {
        setRegistrationMessage(result.message);
      }
    } catch (error) {
      setRegistrationMessage("An unexpected error occurred. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleRegistrationCancel = () => {
    setShowRegistrationForm(false);
    setRegistrationMessage(null);
  };

  if (loading) {
    return (
      <div className="course-detail">
        <div className="course-detail__loading">
          <LoadingSpinner size="large" />
          <p>Loading course details...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="course-detail">
        <div className="course-detail__error">
          <h2>Unable to load course</h2>
          <p>{error}</p>
        </div>
      </div>
    );
  }

  if (!course) {
    return (
      <div className="course-detail">
        <p className="course-detail__not-found">Course not found.</p>
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <div className="course-detail">
        <nav className="course-detail__breadcrumb">
          <Link to={ROUTES.HOME}>Home</Link>
          <span className="course-detail__breadcrumb-separator">›</span>
          <Link to={ROUTES.COURSES}>Courses</Link>
          <span className="course-detail__breadcrumb-separator">›</span>
          <span>{course.title}</span>
        </nav>

        {registrationMessage && (
          <div
            className={`course-detail__message ${
              registrationMessage.includes("success") ? "success" : "error"
            }`}
          >
            {registrationMessage}
          </div>
        )}

        <div className="course-detail__content">
          <div className="course-detail__layout">
            <aside className="course-detail__sidebar">
              <div className="course-detail__sidebar-pattern"></div>
              <div className="course-detail__sidebar-content">
                <div className="course-detail__categories">
                  {course.categories && course.categories.length > 0 ? (
                    course.categories.map((cat, index) => (
                      <span
                        key={index}
                        className="course-detail__category-label"
                      >
                        {cat}
                      </span>
                    ))
                  ) : course.category ? (
                    <span className="course-detail__category-label">
                      {course.category}
                    </span>
                  ) : null}
                </div>

                <div className="course-detail__info">
                  <div className="course-detail__info-item">
                    <span className="course-detail__info-label">Start:</span>
                    <span className="course-detail__info-value">
                      {course.startDate}
                    </span>
                  </div>
                  <div className="course-detail__info-item">
                    <span className="course-detail__info-label">Schedule:</span>
                    <span className="course-detail__info-value">
                      {course.schedule
                        ? `${course.schedule.daysOfWeek.join(", ")} at ${
                            course.schedule.time
                          }`
                        : "Schedule TBD"}
                    </span>
                  </div>
                  {course.price && (
                    <div className="course-detail__info-item course-detail__info-item--price">
                      <span className="course-detail__info-label">Fee:</span>
                      <span className="course-detail__info-value">
                        {formatPrice(course.price)}
                      </span>
                    </div>
                  )}
                </div>

                <div className="course-detail__actions">
                  <Button
                    variant="primary"
                    size="large"
                    onClick={() => setShowRegistrationForm(true)}
                  >
                    {capacity?.status === "full"
                      ? "Join Waitlist"
                      : "Register Now"}
                  </Button>
                  <Button
                    href={EXTERNAL_LINKS.REGISTRATION_FORM}
                    target="_blank"
                    rel="noopener noreferrer"
                    variant="secondary"
                    size="medium"
                  >
                    Ask Questions
                  </Button>
                </div>
              </div>
            </aside>

            <main className="course-detail__main">
              <h1 className="course-detail__title">{course.title}</h1>
              <p className="course-detail__description">{course.description}</p>

              <div className="course-detail__sections">
                <section>
                  <h2>What You'll Learn</h2>
                  <ul>
                    {course.outcomes.map((goal, i) => (
                      <li key={i}>{goal}</li>
                    ))}
                  </ul>
                </section>

                <section>
                  <h2>Course Breakdown</h2>
                  {sessions.length > 0 ? (
                    <ul>
                      {sessions.map((session, i) => (
                        <li key={session.id}>
                          <strong>Session {session.sessionNumber}:</strong>{" "}
                          {session.topic || `Class ${i + 1}`}
                          {session.description && (
                            <div
                              style={{
                                marginTop: "0.5rem",
                                fontSize: "0.9rem",
                                color: "#666",
                              }}
                            >
                              {session.description}
                            </div>
                          )}
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p style={{ color: "#666", fontStyle: "italic" }}>
                      Course sessions will be available once the course is
                      scheduled.
                    </p>
                  )}
                </section>
              </div>
            </main>
          </div>
        </div>

        {showRegistrationForm && (
          <RegistrationForm
            courseTitle={course.title}
            coursePrice={course.price || "Contact for pricing"}
            onSubmit={handleRegistrationSubmit}
            onCancel={handleRegistrationCancel}
            isSubmitting={isSubmitting}
            isWaitlist={capacity?.status === "full"}
          />
        )}
      </div>
    </ErrorBoundary>
  );
};

export default CourseDetail;
