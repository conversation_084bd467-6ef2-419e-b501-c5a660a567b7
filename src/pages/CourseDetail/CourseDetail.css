.course-detail {
  max-width: 1000px;
  margin: 0 auto;
  padding: 2rem;
}

.course-detail__not-found {
  text-align: center;
  font-size: 1.2rem;
  color: #0a2d69;
  padding: 2rem;
}

.course-detail__breadcrumb {
  font-size: 0.9rem;
  padding: 0.75rem 1.5rem;
  background-color: #f8f9fa;
  border: 1px solid #e8f4f8;
  border-radius: 8px;
  color: #0a2d69;
  margin-bottom: 1rem;
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
  align-items: center;
}

.course-detail__breadcrumb a {
  color: #0a2d69;
  text-decoration: none;
  font-weight: bold;
}

.course-detail__breadcrumb a:hover {
  text-decoration: underline;
}

.course-detail__breadcrumb-separator {
  color: #0a2d69;
  margin: 0 0.25rem;
}

.course-detail__layout {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.course-detail__sidebar {
  position: relative;
  background: linear-gradient(135deg, #0a2d69 0%, #1e4a8c 100%);
  border-radius: 12px;
  padding: 2rem;
  overflow: hidden;
  height: fit-content;
}

.course-detail__sidebar-pattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.15;
  background-image: url("data:image/svg+xml,%3Csvg width='56' height='100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' stroke='rgba(255,255,255,0.8)' stroke-width='1'%3E%3Cpolygon points='28,1 53,15 53,43 28,57 3,43 3,15'/%3E%3C/g%3E%3C/svg%3E");
  background-size: 56px 100px;
  background-repeat: repeat;
}

.course-detail__sidebar-pattern::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("data:image/svg+xml,%3Csvg width='56' height='100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' stroke='rgba(255,255,255,0.6)' stroke-width='0.8'%3E%3Cpolygon points='28,1 53,15 53,43 28,57 3,43 3,15'/%3E%3C/g%3E%3C/svg%3E");
  background-size: 56px 100px;
  background-repeat: repeat;
  background-position: 28px 50px;
}

.course-detail__sidebar-content {
  position: relative;
  z-index: 2;
}

.course-detail__categories {
  margin-bottom: 2rem;
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.course-detail__category-label {
  background: rgba(255, 163, 0, 0.9);
  color: #0a2d69;
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  white-space: nowrap;
}

.course-detail__main {
  padding: 2rem 0;
}

.course-detail__title {
  margin: 0 0 1rem 0;
  font-size: 2.2rem;
  color: #0a2d69;
  line-height: 1.2;
}

.course-detail__description {
  font-size: 1.1rem;
  color: #555;
  line-height: 1.6;
  margin: 0;
}

.course-detail__info {
  margin: 2rem 0;
}

.course-detail__info-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(255, 163, 0, 0.4);
}

.course-detail__info-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.course-detail__info-label {
  font-weight: 600;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  flex-shrink: 0;
}

.course-detail__info-value {
  color: white;
  font-weight: 500;
  font-size: 1.1rem;
  text-align: right;
}

.course-detail__info-item--price {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(255, 163, 0, 0.4);
}

.course-detail__info-item--price .course-detail__info-label,
.course-detail__info-item--price .course-detail__info-value {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
}

.course-detail__sections {
  margin-top: 2rem;
}

.course-detail__sections section {
  margin-bottom: 2.5rem;
}

.course-detail__sections h2 {
  border-bottom: 2px solid #ffa300;
  padding-bottom: 0.25rem;
  margin-bottom: 1rem;
  font-size: 1.4rem;
  color: #0a2d69;
}

.course-detail__sections ul {
  padding-left: 1.5rem;
}

.course-detail__sections li {
  margin-bottom: 0.5rem;
}

.course-detail__faq dt {
  font-weight: bold;
  margin-top: 1rem;
  color: #0a2d69;
}

.course-detail__faq dd {
  margin-left: 1rem;
  margin-bottom: 1rem;
}

.course-detail__loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

.course-detail__loading p {
  margin-top: 1rem;
  color: #666;
  font-size: 1.1rem;
}

.course-detail__error {
  text-align: center;
  padding: 3rem 2rem;
  background-color: white;
  border-radius: 12px;
  border-left: 4px solid #dc3545;
}

.course-detail__error h2 {
  color: #dc3545;
  margin-bottom: 1rem;
}

.course-detail__error p {
  color: #666;
}

.course-detail__message {
  padding: 1rem 1.5rem;
  border-radius: 8px;
  margin-bottom: 2rem;
  font-weight: 500;
}

.course-detail__message.success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.course-detail__message.error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.course-detail__actions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 1.5rem;
}

/* Override button styles for better visibility on blue background */
.course-detail__sidebar .btn--secondary {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.course-detail__sidebar .btn--secondary:hover:not(:disabled) {
  background-color: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  color: white;
}

/* Responsive design for sidebar layout */
@media (max-width: 768px) {
  .course-detail__layout {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .course-detail__sidebar {
    order: 2;
  }

  .course-detail__main {
    order: 1;
    padding: 0;
  }

  .course-detail__title {
    font-size: 1.8rem;
  }
}
