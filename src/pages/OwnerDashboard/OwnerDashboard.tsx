import React, { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useViewMode } from "../../hooks/useViewMode";
import SetupWizard from "../../components/SetupWizard";
import SetupProgress from "../../components/SetupProgress";
import TrialBanner from "../../components/TrialBanner/TrialBanner";
import LocationLimitGuard from "../../components/LocationLimitGuard/LocationLimitGuard";
import ScheduleCalendar from "../../components/ScheduleCalendar/ScheduleCalendar";
import KioskCredentials from "../../components/KioskCredentials/KioskCredentials";

import "./OwnerDashboard.css";
import {
  getAllLocations,
  getAllRoutines,
  getAllRoutineTasks,
  getAllLocationRoutineSchedules,
  getTenantRoles,
  createDefaultTenantRoles,
  createTenantRole,
  updateTenantRole,
  deleteTenantRole,
  createLocation,
  updateLocation,
  deleteLocation,
  createRoutine,
  updateRoutine,
  deleteRoutine,
  createRoutineTask,
  updateRoutineTask,
  deleteRoutineTask,
  createLocationRoutineSchedule,
  updateLocationRoutineSchedule,
  deleteLocationRoutineSchedule,
  generateDailyTasksForToday,
  generateDailyTasksForDate,
  getAllDailyTasks,
  updateDailyTaskStatus,
  createDailyTask,
  createAdHocTask,
  getAllAdHocTasks,
  updateAdHocTask,
  updateAdHocTaskStatus,
  deleteAdHocTask,
  callManualGenerateTasks,
  callManualExpireTasks,
  getFunctionStats,
  getWatchlistTasks,
  createWatchlistTask,
  updateWatchlistTask,
  deleteWatchlistTask,
  addWatchlistCheck,
  getWatchlistChecks,
  type Location,
  type Routine,
  type RoutineTask,
  type LocationRoutineSchedule,
  type TenantRole,
  type DailyTask,
  type AdHocTask,
  type WatchlistTask,
  type WatchlistCheck,
} from "../../services/adminBuddyFirebaseService";

const OwnerDashboard: React.FC = () => {
  // Use simple user service instead of broken TenantContext
  const [currentUser, setCurrentUser] = useState<any>(null);
  const [tenantLoading, setTenantLoading] = useState(true);

  useEffect(() => {
    // Check authentication using localStorage (set by our REST API login)
    const checkAuth = async () => {
      console.log("🔐 Dashboard: Checking authentication...");

      // Check if we have auth data from our REST API login
      const authData = localStorage.getItem("adminbuddy_auth");

      if (!authData) {
        console.log("❌ Dashboard: No auth data found, redirecting to login");
        setTenantLoading(false);
        return;
      }

      try {
        const { uid, email } = JSON.parse(authData);
        console.log("✅ Dashboard: Found auth data for:", email);

        // Get user profile using our REST API approach
        const { getUserProfile } = await import(
          "../../services/adminBuddyFirebaseService"
        );
        const profile = await getUserProfile(uid);

        if (profile) {
          console.log("👤 Dashboard: User profile loaded:", profile.email);
          setCurrentUser(profile);
        } else {
          console.log("❌ Dashboard: User profile not found");
        }
      } catch (error) {
        console.error("❌ Dashboard: Error checking auth:", error);
      } finally {
        setTenantLoading(false);
      }
    };

    checkAuth();
  }, []);

  // Extract tenant info from user
  const tenantId = currentUser?.tenantId || null;
  const user = currentUser;

  // Simple logout function
  const logout = async () => {
    console.log("🔓 Owner dashboard logout initiated");

    try {
      // Store current URL for redirect after login
      const currentUrl = window.location.pathname + window.location.search;
      localStorage.setItem("adminbuddy_redirect_after_login", currentUrl);
      console.log(
        "💾 Owner dashboard logout - stored redirect URL:",
        currentUrl
      );

      // Clear dismissal state for this account so setup guide shows on next login if not complete
      // BUT only if setup is not completed - if completed, leave dismissal state alone
      if (tenantId) {
        const setupCompletedKey = `adminbuddy_setup_completed_${tenantId}`;
        const setupDismissedKey = `adminbuddy_setup_dismissed_${tenantId}`;
        const setupCompleted =
          localStorage.getItem(setupCompletedKey) === "true";

        if (!setupCompleted) {
          // Only clear dismissal if setup is not completed
          localStorage.removeItem(setupDismissedKey);
          console.log(
            `🧹 Cleared dismissal state for tenant ${tenantId} on logout (setup not completed)`
          );
        } else {
          console.log(
            `✅ Setup completed for tenant ${tenantId} - keeping dismissal state on logout`
          );
        }
      }

      // Clear the REST API auth data from localStorage (this is the key fix!)
      localStorage.removeItem("adminbuddy_auth");
      console.log("🧹 Cleared localStorage auth data");

      // Also clear Firebase SDK auth state for completeness
      const { signOutUser } = await import("../../services/userService");
      await signOutUser();

      // Clear local component state
      setCurrentUser(null);

      console.log("✅ Owner dashboard logout completed successfully");
      navigate("/login");
    } catch (error) {
      console.error("❌ Error during logout:", error);
      // Even if there's an error, clear the auth data and redirect
      localStorage.removeItem("adminbuddy_auth");
      setCurrentUser(null);
      navigate("/login");
    }
  };

  // Setup wizard handlers
  const handleOpenSetupWizard = () => {
    setShowSetupWizard(true);
  };

  const handleCloseSetupWizard = () => {
    setShowSetupWizard(false);
  };

  const handleCompleteSetup = () => {
    if (!tenantId) return;

    setShowSetupWizard(false);
    setShowSetupProgress(false);
    setSetupDismissed(true);

    // Permanently store completion state for this account only
    const setupCompletedKey = `adminbuddy_setup_completed_${tenantId}`;
    localStorage.setItem(setupCompletedKey, "true");

    console.log(
      `🎉 Setup completed for tenant ${tenantId} and marked as complete permanently`
    );
    showToast(
      "🎉 Setup completed! Try generating tasks from the Schedules page and test your kiosk interface.",
      "success"
    );
  };

  const handleDismissSetupProgress = () => {
    if (!tenantId) return;

    setShowSetupProgress(false);
    setSetupDismissed(true);

    // Store dismissal state for this account (until next login)
    const setupDismissedKey = `adminbuddy_setup_dismissed_${tenantId}`;
    localStorage.setItem(setupDismissedKey, "true");

    console.log(
      `📋 Setup guide dismissed for tenant ${tenantId} until next login`
    );
  };

  // Setup action handlers - open modals and navigate to tabs
  const handleSetupAction = (stepId: string) => {
    console.log(`🚀 Setup action triggered for step: ${stepId}`);

    switch (stepId) {
      case "locations":
        changeTab("locations");
        setShowLocationModal(true);
        setEditingItem(null);
        break;
      case "routines":
        changeTab("routines");
        setShowRoutineModal(true);
        setEditingItem(null);
        break;
      case "routine-tasks":
        changeTab("routine-tasks");
        setShowRoutineTaskModal(true);
        setEditingItem(null);
        break;
      case "schedules":
        changeTab("schedules");
        setShowScheduleModal(true);
        setEditingItem(null);
        break;
      case "kiosk":
        // For kiosk, just navigate - no modal needed
        navigate("/kiosk");
        break;
      default:
        console.warn(`Unknown setup step: ${stepId}`);
    }
  };

  // Function to refresh setup progress after data changes
  const refreshSetupProgress = () => {
    console.log("🔄 Refreshing setup progress after data change...");
    // Force re-render of SetupProgress component by updating a key
    setSetupProgressKey((prev) => prev + 1);

    // Check if setup should be automatically completed
    checkAutoCompletion();
  };

  // Check if setup is complete and auto-complete if so
  const checkAutoCompletion = async () => {
    if (!tenantId || setupDismissed) return;

    try {
      // Fetch all data to check completion
      const [locations, routines, routineTasks, schedules] = await Promise.all([
        getAllLocations(tenantId),
        getAllRoutines(tenantId),
        getAllRoutineTasks(tenantId),
        getAllLocationRoutineSchedules(tenantId),
      ]);

      // Check completion criteria - require all basic setup PLUS task generation and kiosk test
      const basicSetupComplete =
        locations.length > 0 &&
        routines.length > 0 &&
        routineTasks.length > 0 &&
        schedules.length > 0;

      // For now, don't auto-complete setup - let users manually complete after testing
      // This ensures they go through task generation and kiosk testing
      const isComplete = false; // Disable auto-completion

      console.log(`📋 Setup progress check:`, {
        locations: locations.length,
        routines: routines.length,
        routineTasks: routineTasks.length,
        schedules: schedules.length,
        basicSetupComplete,
        autoCompleteDisabled: true,
      });

      // Check if setup is complete for this account
      const setupCompletedKey = `adminbuddy_setup_completed_${tenantId}`;
      if (isComplete && !localStorage.getItem(setupCompletedKey)) {
        console.log(`🎉 Setup automatically completed for tenant ${tenantId}!`);
        handleCompleteSetup();
      }
    } catch (error) {
      console.error("❌ Error checking auto-completion:", error);
    }
  };

  // Component mounted with user data

  // URL routing for tabs
  const navigate = useNavigate();
  const location = useLocation();

  // Map URL paths to tab names
  const getTabFromPath = (
    pathname: string
  ):
    | "daily-tasks"
    | "reports"
    | "locations"
    | "routines"
    | "routine-tasks"
    | "schedules"
    | "roles"
    | "watchlist"
    | "help" => {
    const pathMap: Record<
      string,
      | "daily-tasks"
      | "reports"
      | "locations"
      | "routines"
      | "routine-tasks"
      | "schedules"
      | "roles"
      | "watchlist"
      | "help"
    > = {
      "/dashboard": "daily-tasks",
      "/dashboard/daily-tasks": "daily-tasks",
      "/dashboard/reports": "reports",
      "/dashboard/locations": "locations",
      "/dashboard/routines": "routines",
      "/dashboard/routine-tasks": "routine-tasks",
      "/dashboard/schedules": "schedules",
      "/dashboard/roles": "roles",
      "/dashboard/watchlist": "watchlist",
      "/dashboard/help": "help",
    };
    return pathMap[pathname] || "daily-tasks";
  };

  // Tab state - updated for new structure with URL routing
  const [activeTab, setActiveTab] = useState<
    | "daily-tasks"
    | "reports"
    | "locations"
    | "routines"
    | "routine-tasks"
    | "schedules"
    | "roles"
    | "watchlist"
    | "help"
  >(getTabFromPath(location.pathname));

  // Update tab when URL changes
  useEffect(() => {
    const newTab = getTabFromPath(location.pathname);
    setActiveTab(newTab);
  }, [location.pathname]);

  // Function to change tab and update URL
  const changeTab = (tab: string) => {
    const pathMap: Record<string, string> = {
      "daily-tasks": "/dashboard/daily-tasks",
      reports: "/dashboard/reports",
      locations: "/dashboard/locations",
      routines: "/dashboard/routines",
      "routine-tasks": "/dashboard/routine-tasks",
      schedules: "/dashboard/schedules",
      roles: "/dashboard/roles",
      watchlist: "/dashboard/watchlist",
      help: "/dashboard/help",
    };
    navigate(pathMap[tab] || "/dashboard/daily-tasks");
  };

  // Data state
  const [locations, setLocations] = useState<Location[]>([]);
  const [routines, setRoutines] = useState<Routine[]>([]);
  const [routineTasks, setRoutineTasks] = useState<RoutineTask[]>([]);
  const [schedules, setSchedules] = useState<LocationRoutineSchedule[]>([]);
  const [tenantRoles, setTenantRoles] = useState<TenantRole[]>([]);
  const [dailyTasks, setDailyTasks] = useState<DailyTask[]>([]);
  const [adHocTasks, setAdHocTasks] = useState<AdHocTask[]>([]);
  const [watchlistTasks, setWatchlistTasks] = useState<WatchlistTask[]>([]);
  const [loading, setLoading] = useState(true);

  // Modal states
  const [showLocationModal, setShowLocationModal] = useState(false);
  const [showRoutineModal, setShowRoutineModal] = useState(false);
  const [showRoutineTaskModal, setShowRoutineTaskModal] = useState(false);
  const [showScheduleModal, setShowScheduleModal] = useState(false);
  const [showAdHocTaskModal, setShowAdHocTaskModal] = useState(false);
  const [showRoleModal, setShowRoleModal] = useState(false);
  const [showWatchlistModal, setShowWatchlistModal] = useState(false);
  const [editingItem, setEditingItem] = useState<any>(null);
  const [selectedRoutine, setSelectedRoutine] = useState<Routine | null>(null);

  // Toast notification state
  const [toast, setToast] = useState<{
    message: string;
    type: "success" | "error" | "info";
    visible: boolean;
  }>({ message: "", type: "info", visible: false });

  // Task generation loading state (separate from global loading)
  const [taskGenerationLoading, setTaskGenerationLoading] = useState(false);

  // Sidebar state
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  // Setup wizard state
  const [showSetupWizard, setShowSetupWizard] = useState(false);
  const [showSetupProgress, setShowSetupProgress] = useState(false);
  const [setupDismissed, setSetupDismissed] = useState(false);
  const [setupProgressKey, setSetupProgressKey] = useState(0);

  // Toast notification function
  const showToast = (
    message: string,
    type: "success" | "error" | "info" = "info"
  ) => {
    setToast({ message, type, visible: true });
    // Auto-hide after 4 seconds
    setTimeout(() => {
      setToast((prev) => ({ ...prev, visible: false }));
    }, 4000);
  };

  // Initialize setup wizard state based on user data and localStorage
  useEffect(() => {
    if (!tenantId || !currentUser) return;

    // Create account-specific localStorage keys
    const setupCompletedKey = `adminbuddy_setup_completed_${tenantId}`;
    const setupDismissedKey = `adminbuddy_setup_dismissed_${tenantId}`;

    // Check localStorage for setup completion and dismissal (per account)
    const setupCompleted = localStorage.getItem(setupCompletedKey) === "true";
    const setupDismissedThisSession =
      localStorage.getItem(setupDismissedKey) === "true";

    console.log("🔧 Setup wizard initialization:", {
      setupCompleted,
      setupDismissedThisSession,
      userCreatedAt: currentUser.createdAt,
      tenantId,
      setupCompletedKey,
      setupDismissedKey,
    });

    // Check completion first
    if (setupCompleted) {
      // User completed setup - never show again for this account
      setShowSetupProgress(false);
      setSetupDismissed(true);
      console.log(
        "✅ Setup already completed for this account - hiding guide permanently"
      );
    } else if (setupDismissedThisSession) {
      // User dismissed setup for this session - don't show until next login
      setShowSetupProgress(false);
      setSetupDismissed(true);
      console.log(
        "📋 Setup dismissed for this session - hiding until next login"
      );
    } else {
      // User hasn't completed or dismissed setup - show the guide
      setShowSetupProgress(true);
      setSetupDismissed(false);
      console.log("📋 Setup not completed - showing setup guide");
    }
  }, [tenantId, currentUser]);

  // Load data from Firebase
  useEffect(() => {
    const loadData = async () => {
      if (!tenantId) return;

      try {
        setLoading(true);
        console.log("🔥 Loading AdminBuddy V2 data for tenant:", tenantId);

        // Load tenant roles first, create defaults if none exist
        let rolesData = await getTenantRoles(tenantId);
        if (rolesData.length === 0) {
          console.log("👥 No roles found, creating default roles...");
          await createDefaultTenantRoles(tenantId);
          rolesData = await getTenantRoles(tenantId);
        }

        const today = new Date().toISOString().split("T")[0];

        const [
          locationsData,
          routinesData,
          routineTasksData,
          schedulesData,
          dailyTasksData,
          adHocTasksData,
          watchlistTasksData,
        ] = await Promise.all([
          getAllLocations(tenantId),
          getAllRoutines(tenantId),
          getAllRoutineTasks(tenantId),
          getAllLocationRoutineSchedules(tenantId),
          getAllDailyTasks(tenantId, today),
          getAllAdHocTasks(tenantId),
          getWatchlistTasks(tenantId),
        ]);

        setLocations(locationsData);
        setRoutines(routinesData);
        setRoutineTasks(routineTasksData);
        setSchedules(schedulesData);
        setTenantRoles(rolesData);
        setDailyTasks(dailyTasksData);
        setAdHocTasks(adHocTasksData);
        setWatchlistTasks(watchlistTasksData);

        console.log(
          "✅ AdminBuddy V2 data loaded successfully for tenant:",
          tenantId
        );
      } catch (error) {
        console.error("❌ Error loading AdminBuddy V2 data:", error);
      } finally {
        setLoading(false);
      }
    };

    if (!tenantLoading) {
      loadData();
    }
  }, [tenantId, tenantLoading]);

  // CRUD Handlers for Locations
  const handleCreateLocation = async (
    locationData: Omit<Location, "id" | "createdAt" | "tenantId">
  ) => {
    if (!tenantId) return;
    try {
      const locationWithTenant = {
        ...locationData,
        tenantId,
      };
      const newLocationId = await createLocation(locationWithTenant);
      const newLocation: Location = {
        ...locationWithTenant,
        id: newLocationId,
        createdAt: new Date().toISOString(),
      };
      setLocations((prev) => [...prev, newLocation]);
      setShowLocationModal(false);
      setEditingItem(null);

      // Refresh setup progress after creating location
      refreshSetupProgress();
    } catch (error) {
      console.error("Error creating location:", error);
      alert("Failed to create location. Please try again.");
    }
  };

  const handleUpdateLocation = async (
    locationData: Omit<Location, "id" | "createdAt" | "tenantId">
  ) => {
    if (!editingItem || !tenantId) return;
    try {
      // Include tenantId in the update data for security rules validation
      const locationWithTenant = { ...locationData, tenantId };
      await updateLocation(editingItem.id, locationWithTenant);
      setLocations((prev) =>
        prev.map((loc) =>
          loc.id === editingItem.id ? { ...loc, ...locationData } : loc
        )
      );
      setShowLocationModal(false);
      setEditingItem(null);
    } catch (error) {
      console.error("Error updating location:", error);
      alert("Failed to update location. Please try again.");
    }
  };

  const handleDeleteLocation = async (locationId: string) => {
    const location = locations.find((l) => l.id === locationId);
    const locationName = location?.name || "this location";

    if (
      window.confirm(
        `Are you sure you want to delete "${locationName}"?\n\n` +
          `⚠️ This will permanently delete:\n` +
          `• The location\n` +
          `• All schedules for this location\n` +
          `• All daily tasks for this location\n` +
          `• All To Do tasks for this location\n\n` +
          `This action cannot be undone.`
      )
    ) {
      try {
        setLoading(true);
        await deleteLocation(locationId);

        // Update UI state
        setLocations((prev) => prev.filter((loc) => loc.id !== locationId));
        setSchedules((prev) => prev.filter((s) => s.locationId !== locationId));
        setDailyTasks((prev) =>
          prev.filter((t) => t.locationId !== locationId)
        );
        setAdHocTasks((prev) =>
          prev.filter((t) => t.locationId !== locationId)
        );

        alert(
          `✅ Location "${locationName}" and all associated data deleted successfully.`
        );
      } catch (error) {
        console.error("Error deleting location:", error);
        alert("Failed to delete location. Please try again.");
      } finally {
        setLoading(false);
      }
    }
  };

  // CRUD Handlers for Routines
  const handleCreateRoutine = async (
    routineData: Omit<Routine, "id" | "createdAt" | "tenantId">
  ) => {
    if (!tenantId) return;
    try {
      const routineWithTenant = { ...routineData, tenantId };
      const newRoutineId = await createRoutine(routineWithTenant);
      const newRoutine: Routine = {
        ...routineWithTenant,
        id: newRoutineId,
        createdAt: new Date().toISOString(),
      };
      setRoutines((prev) => [...prev, newRoutine]);
      setShowRoutineModal(false);
      setEditingItem(null);

      // Refresh setup progress after creating routine
      refreshSetupProgress();
    } catch (error) {
      console.error("Error creating routine:", error);
      alert("Failed to create routine. Please try again.");
    }
  };

  const handleUpdateRoutine = async (
    routineData: Omit<Routine, "id" | "createdAt" | "tenantId">
  ) => {
    if (!editingItem || !tenantId) return;
    try {
      // Include tenantId in the update data for security rules validation
      const routineWithTenant = { ...routineData, tenantId };
      await updateRoutine(editingItem.id, routineWithTenant);
      setRoutines((prev) =>
        prev.map((routine) =>
          routine.id === editingItem.id
            ? { ...routine, ...routineData }
            : routine
        )
      );
      setShowRoutineModal(false);
      setEditingItem(null);
    } catch (error) {
      console.error("Error updating routine:", error);
      alert("Failed to update routine. Please try again.");
    }
  };

  const handleDeleteRoutine = async (routineId: string) => {
    const routine = routines.find((r) => r.id === routineId);
    const routineName = routine?.name || "this routine";
    const relatedTasks = routineTasks.filter(
      (rt) => rt.routineId === routineId
    );
    const relatedSchedules = schedules.filter((s) => s.routineId === routineId);

    if (
      window.confirm(
        `Are you sure you want to delete "${routineName}"?\n\n` +
          `⚠️ This will permanently delete:\n` +
          `• The routine\n` +
          `• ${relatedTasks.length} routine tasks\n` +
          `• ${relatedSchedules.length} schedules\n` +
          `• All daily tasks generated from this routine\n\n` +
          `This action cannot be undone.`
      )
    ) {
      try {
        setLoading(true);
        await deleteRoutine(routineId);

        // Update UI state
        setRoutines((prev) =>
          prev.filter((routine) => routine.id !== routineId)
        );
        setRoutineTasks((prev) =>
          prev.filter((rt) => rt.routineId !== routineId)
        );
        setSchedules((prev) => prev.filter((s) => s.routineId !== routineId));

        // Note: Daily tasks will be cleaned up by the service, but we could refresh them here
        // For now, they'll be cleaned up on next page load

        alert(
          `✅ Routine "${routineName}" and all associated data deleted successfully.`
        );
      } catch (error) {
        console.error("Error deleting routine:", error);
        alert("Failed to delete routine. Please try again.");
      } finally {
        setLoading(false);
      }
    }
  };

  // CRUD Handlers for Routine Tasks
  const handleCreateRoutineTask = async (
    routineTaskData: Omit<RoutineTask, "id" | "createdAt" | "tenantId">
  ) => {
    if (!tenantId) return;
    try {
      const routineTaskWithTenant = { ...routineTaskData, tenantId };
      const newRoutineTaskId = await createRoutineTask(routineTaskWithTenant);
      const newRoutineTask: RoutineTask = {
        ...routineTaskWithTenant,
        id: newRoutineTaskId,
        createdAt: new Date().toISOString(),
      };
      setRoutineTasks((prev) => [...prev, newRoutineTask]);
      setShowRoutineTaskModal(false);
      setEditingItem(null);

      // Refresh setup progress after creating routine task
      refreshSetupProgress();
    } catch (error) {
      console.error("Error creating routine task:", error);
      alert("Failed to create routine task. Please try again.");
    }
  };

  const handleUpdateRoutineTask = async (
    routineTaskData: Omit<RoutineTask, "id" | "createdAt" | "tenantId">
  ) => {
    if (!editingItem || !tenantId) return;
    try {
      // Include tenantId in the update data for security rules validation
      const routineTaskWithTenant = { ...routineTaskData, tenantId };
      await updateRoutineTask(editingItem.id, routineTaskWithTenant);
      setRoutineTasks((prev) =>
        prev.map((rt) =>
          rt.id === editingItem.id ? { ...rt, ...routineTaskData } : rt
        )
      );
      setShowRoutineTaskModal(false);
      setEditingItem(null);
    } catch (error) {
      console.error("Error updating routine task:", error);
      alert("Failed to update routine task. Please try again.");
    }
  };

  const handleDeleteRoutineTask = async (routineTaskId: string) => {
    const routineTask = routineTasks.find((rt) => rt.id === routineTaskId);
    const taskTitle = routineTask?.title || "this routine task";

    if (
      window.confirm(
        `Are you sure you want to delete "${taskTitle}"?\n\n` +
          `⚠️ This will permanently delete:\n` +
          `• The routine task\n` +
          `• All daily tasks generated from this routine task\n\n` +
          `This action cannot be undone.`
      )
    ) {
      try {
        setLoading(true);
        await deleteRoutineTask(routineTaskId);
        setRoutineTasks((prev) => prev.filter((rt) => rt.id !== routineTaskId));

        alert(
          `✅ Routine task "${taskTitle}" and all associated daily tasks deleted successfully.`
        );
      } catch (error) {
        console.error("Error deleting routine task:", error);
        alert("Failed to delete routine task. Please try again.");
      } finally {
        setLoading(false);
      }
    }
  };

  // CRUD Handlers for Location Routine Schedules
  const handleCreateSchedule = async (
    scheduleData: Omit<LocationRoutineSchedule, "id" | "createdAt" | "tenantId">
  ) => {
    if (!tenantId) return;
    try {
      const scheduleWithTenant = { ...scheduleData, tenantId };
      const newScheduleId = await createLocationRoutineSchedule(
        scheduleWithTenant
      );
      const newSchedule: LocationRoutineSchedule = {
        ...scheduleWithTenant,
        id: newScheduleId,
        createdAt: new Date().toISOString(),
      };
      setSchedules((prev) => [...prev, newSchedule]);
      setShowScheduleModal(false);
      setEditingItem(null);

      // Refresh setup progress after creating schedule
      refreshSetupProgress();
    } catch (error) {
      console.error("Error creating schedule:", error);
      alert("Failed to create schedule. Please try again.");
    }
  };

  // Daily Task Generation Handler (using Cloud Function)
  const handleGenerateDailyTasks = async (date?: string) => {
    if (!tenantId) return;
    try {
      setLoading(true);
      const targetDate = date || new Date().toISOString().split("T")[0];

      console.log(
        `☁️ Calling Cloud Function to generate daily tasks for ${targetDate}...`
      );

      const result = await callManualGenerateTasks(tenantId, targetDate);

      // Reload daily tasks
      const updatedDailyTasks = await getAllDailyTasks(tenantId, targetDate);
      setDailyTasks(updatedDailyTasks);

      alert(`✅ ${result.message}`);
    } catch (error) {
      console.error("Error generating daily tasks:", error);
      alert("Failed to generate daily tasks. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Generate tasks for a specific schedule
  const handleGenerateTasksForSchedule = async (
    schedule: LocationRoutineSchedule
  ) => {
    if (!tenantId) return;
    try {
      setTaskGenerationLoading(true);
      const today = new Date().toISOString().split("T")[0];

      console.log("🔍 Debug info for task generation:", {
        tenantId,
        scheduleId: schedule.id,
        locationId: schedule.locationId,
        routineId: schedule.routineId,
        daysOfWeek: schedule.daysOfWeek,

        targetDate: today,
      });

      console.log(
        `🚀 Generating tasks for schedule: ${schedule.id} on ${today}`
      );

      // Generate tasks for this specific schedule only
      const result = await callManualGenerateTasks(
        tenantId,
        today,
        schedule.id
      );

      // Reload daily tasks to show the new ones
      const updatedDailyTasks = await getAllDailyTasks(tenantId, today);
      setDailyTasks(updatedDailyTasks);

      // Find the routine name for better messaging
      const routine = routines.find((r) => r.id === schedule.routineId);
      const location = locations.find((l) => l.id === schedule.locationId);

      // Show subtle success notification
      showToast(
        `✅ Generated ${result.tasksCreated} tasks for "${
          routine?.name || "Unknown Routine"
        }" at "${location?.name || "Unknown Location"}"`,
        "success"
      );
    } catch (error) {
      console.error("Error generating tasks for schedule:", error);
      showToast("Failed to generate tasks. Please try again.", "error");
    } finally {
      setTaskGenerationLoading(false);
    }
  };

  // Manual Task Expiry Handler
  const handleExpireTasks = async () => {
    if (!tenantId) return;
    try {
      setLoading(true);
      console.log("☁️ Calling Cloud Function to expire overdue tasks...");
      const result = await callManualExpireTasks();

      // Reload tasks to see updated statuses
      const today = new Date().toISOString().split("T")[0];
      const [updatedDailyTasks, updatedAdHocTasks] = await Promise.all([
        getAllDailyTasks(tenantId, today),
        getAllAdHocTasks(tenantId),
      ]);
      setDailyTasks(updatedDailyTasks);
      setAdHocTasks(updatedAdHocTasks);

      alert(`✅ ${result.message}`);
    } catch (error) {
      console.error("Error expiring tasks:", error);
      alert("Failed to expire tasks. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Task Status Update Handlers
  const handleUpdateDailyTaskStatus = async (
    taskId: string,
    status: "pending" | "completed" | "expired"
  ) => {
    try {
      await updateDailyTaskStatus(taskId, status);
      setDailyTasks((prev) =>
        prev.map((task) => (task.id === taskId ? { ...task, status } : task))
      );
    } catch (error) {
      console.error("Error updating daily task status:", error);
      alert("Failed to update task status. Please try again.");
    }
  };

  const handleUpdateAdHocTaskStatus = async (
    taskId: string,
    status: "pending" | "completed" | "expired"
  ) => {
    try {
      await updateAdHocTaskStatus(taskId, status);
      setAdHocTasks((prev) =>
        prev.map((task) => (task.id === taskId ? { ...task, status } : task))
      );
    } catch (error) {
      console.error("Error updating To Do task status:", error);
      alert("Failed to update task status. Please try again.");
    }
  };

  // Ad-hoc Task Handlers
  const handleCreateAdHocTask = async (
    taskData: Omit<AdHocTask, "id" | "createdAt" | "tenantId">
  ) => {
    if (!tenantId) return;
    try {
      const taskWithTenant = { ...taskData, tenantId };
      const newTaskId = await createAdHocTask(taskWithTenant);
      const newTask: AdHocTask = {
        ...taskWithTenant,
        id: newTaskId,
        createdAt: new Date().toISOString(),
      };
      setAdHocTasks((prev) => [...prev, newTask]);
      setShowAdHocTaskModal(false);
      setEditingItem(null);
    } catch (error) {
      console.error("Error creating To Do task:", error);
      alert("Failed to create To Do task. Please try again.");
    }
  };

  const handleUpdateAdHocTask = async (
    taskData: Omit<AdHocTask, "id" | "createdAt" | "tenantId">
  ) => {
    if (!editingItem || !tenantId) return;
    try {
      // Include tenantId in the update data for security rules validation
      const taskWithTenant = { ...taskData, tenantId };
      await updateAdHocTask(editingItem.id, taskWithTenant);
      setAdHocTasks((prev) =>
        prev.map((task) =>
          task.id === editingItem.id ? { ...task, ...taskData } : task
        )
      );
      setShowAdHocTaskModal(false);
      setEditingItem(null);
    } catch (error) {
      console.error("Error updating To Do task:", error);
      alert("Failed to update To Do task. Please try again.");
    }
  };

  const handleUpdateSchedule = async (
    scheduleData: Omit<LocationRoutineSchedule, "id" | "createdAt" | "tenantId">
  ) => {
    if (!editingItem || !tenantId) return;
    try {
      // Include tenantId in the update data for security rules validation
      const scheduleWithTenant = { ...scheduleData, tenantId };
      await updateLocationRoutineSchedule(editingItem.id, scheduleWithTenant);
      setSchedules((prev) =>
        prev.map((schedule) =>
          schedule.id === editingItem.id
            ? { ...schedule, ...scheduleData }
            : schedule
        )
      );
      setShowScheduleModal(false);
      setEditingItem(null);
    } catch (error) {
      console.error("Error updating schedule:", error);
      alert("Failed to update schedule. Please try again.");
    }
  };

  const handleDeleteSchedule = async (scheduleId: string) => {
    if (window.confirm("Are you sure you want to delete this schedule?")) {
      try {
        await deleteLocationRoutineSchedule(scheduleId);
        setSchedules((prev) =>
          prev.filter((schedule) => schedule.id !== scheduleId)
        );
      } catch (error) {
        console.error("Error deleting schedule:", error);
        alert("Failed to delete schedule. Please try again.");
      }
    }
  };

  // Role Handlers
  const handleCreateRole = async (
    roleData: Omit<TenantRole, "id" | "createdAt" | "tenantId">
  ) => {
    if (!tenantId) return;
    try {
      const roleWithTenant = { ...roleData, tenantId };
      const newRoleId = await createTenantRole(roleWithTenant);
      const newRole: TenantRole = {
        ...roleWithTenant,
        id: newRoleId,
        createdAt: new Date().toISOString(),
      };
      setTenantRoles((prev) => [...prev, newRole]);
      setShowRoleModal(false);
      setEditingItem(null);

      // Refresh setup progress after creating role
      refreshSetupProgress();
    } catch (error) {
      console.error("Error creating role:", error);
      alert("Failed to create role. Please try again.");
    }
  };

  const handleUpdateRole = async (
    roleData: Omit<TenantRole, "id" | "createdAt" | "tenantId">
  ) => {
    if (!editingItem || !tenantId) return;
    try {
      // Include tenantId in the update data for security rules validation
      const roleWithTenant = { ...roleData, tenantId };
      await updateTenantRole(editingItem.id, roleWithTenant);
      setTenantRoles((prev) =>
        prev.map((role) =>
          role.id === editingItem.id ? { ...role, ...roleData } : role
        )
      );
      setShowRoleModal(false);
      setEditingItem(null);
    } catch (error) {
      console.error("Error updating role:", error);
      alert("Failed to update role. Please try again.");
    }
  };

  const handleDeleteRole = async (roleId: string) => {
    if (window.confirm("Are you sure you want to delete this role?")) {
      try {
        await deleteTenantRole(roleId);
        setTenantRoles((prev) => prev.filter((role) => role.id !== roleId));
      } catch (error) {
        console.error("Error deleting role:", error);
        alert("Failed to delete role. Please try again.");
      }
    }
  };

  // Watchlist handlers
  const handleCreateWatchlistTask = async (
    taskData: Omit<WatchlistTask, "id" | "createdAt" | "checkHistory">
  ) => {
    if (!tenantId) return;

    try {
      await createWatchlistTask(taskData);
      showToast("Watchlist task created successfully!", "success");

      // Reload watchlist tasks
      const watchlistData = await getWatchlistTasks(tenantId);
      setWatchlistTasks(watchlistData);

      setShowWatchlistModal(false);
      setEditingItem(null);
    } catch (error) {
      console.error("Error creating watchlist task:", error);
      showToast("Failed to create watchlist task", "error");
    }
  };

  const handleUpdateWatchlistTask = async (
    taskData: Omit<WatchlistTask, "id" | "createdAt" | "checkHistory">
  ) => {
    if (!tenantId || !editingItem?.id) return;

    try {
      await updateWatchlistTask(editingItem.id, taskData);
      showToast("Watchlist task updated successfully!", "success");

      // Reload watchlist tasks
      const watchlistData = await getWatchlistTasks(tenantId);
      setWatchlistTasks(watchlistData);

      setShowWatchlistModal(false);
      setEditingItem(null);
    } catch (error) {
      console.error("Error updating watchlist task:", error);
      showToast("Failed to update watchlist task", "error");
    }
  };

  const handleDeleteWatchlistTask = async (taskId: string) => {
    if (!tenantId) return;

    if (
      !confirm(
        "Are you sure you want to delete this watchlist task? This action cannot be undone."
      )
    ) {
      return;
    }

    try {
      await deleteWatchlistTask(taskId);
      showToast("Watchlist task deleted successfully!", "success");

      // Reload watchlist tasks
      const watchlistData = await getWatchlistTasks(tenantId);
      setWatchlistTasks(watchlistData);
    } catch (error) {
      console.error("Error deleting watchlist task:", error);
      showToast("Failed to delete watchlist task", "error");
    }
  };

  const handleAddWatchlistCheck = async (
    taskId: string,
    checkData: Omit<WatchlistCheck, "id">
  ) => {
    if (!tenantId) return;

    try {
      await addWatchlistCheck(taskId, checkData);
      showToast("Check recorded successfully!", "success");

      // Reload watchlist tasks to show updated history
      const watchlistData = await getWatchlistTasks(tenantId);
      setWatchlistTasks(watchlistData);
    } catch (error) {
      console.error("Error adding watchlist check:", error);
      showToast("Failed to record check", "error");
    }
  };

  // Redirect to login if not authenticated (after all hooks are declared)
  if (!tenantLoading && !user) {
    // Store current URL for redirect after login
    const currentUrl = window.location.pathname + window.location.search;
    localStorage.setItem("adminbuddy_redirect_after_login", currentUrl);
    console.log(
      "💾 Owner dashboard auth redirect - stored redirect URL:",
      currentUrl
    );

    window.location.href = "/login";
    return null;
  }

  if (loading) {
    return (
      <div className="owner-dashboard">
        <div className="owner-dashboard__loading">
          <h2>Loading AdminBuddy dashboard...</h2>
        </div>
      </div>
    );
  }

  return (
    <div
      className={`owner-dashboard ${
        sidebarCollapsed ? "sidebar-collapsed" : ""
      }`}
    >
      {/* Header */}
      <header className="owner-dashboard__header">
        <div className="owner-dashboard__header-content">
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <div>
              <h1>AdminBuddy - Business Owner Dashboard</h1>
              <p>Manage locations, routines, and task scheduling</p>
              <div className="header-info">
                <div className="user-info">
                  <span className="user-badge">
                    👤 {currentUser?.email || "Unknown User"}
                  </span>
                  {process.env.NODE_ENV === "development" && (
                    <span className="tenant-badge">
                      🏢{" "}
                      {tenantId
                        ? `Company ID: ${tenantId.split("-")[1]}`
                        : "No Company"}
                    </span>
                  )}
                </div>
                {process.env.NODE_ENV === "development" && (
                  <div className="dev-info">
                    Full Tenant ID: {tenantId || "Not set"}
                    {tenantId === "tenant-d3sctaeG" && (
                      <span style={{ color: "#00a8ff", marginLeft: "10px" }}>
                        🔄 Using Fallback Tenant
                      </span>
                    )}
                  </div>
                )}
              </div>
            </div>
            <div
              style={{ display: "flex", gap: "0.5rem", alignItems: "center" }}
            >
              <button
                onClick={logout}
                style={{
                  padding: "8px 16px",
                  backgroundColor: "#ff6b35",
                  color: "white",
                  border: "none",
                  borderRadius: "4px",
                  cursor: "pointer",
                  fontSize: "14px",
                }}
              >
                🔓 Logout
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="owner-dashboard__body">
        {/* Sidebar Navigation */}
        <nav
          className={`owner-dashboard__sidebar ${
            sidebarCollapsed ? "collapsed" : ""
          }`}
        >
          <div className="sidebar-header">
            <button
              className="sidebar-toggle"
              onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
              title={sidebarCollapsed ? "Expand sidebar" : "Collapse sidebar"}
            >
              <svg
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className={`toggle-arrow ${
                  sidebarCollapsed ? "collapsed" : ""
                }`}
              >
                <polyline points="15,18 9,12 15,6"></polyline>
              </svg>
            </button>
            {!sidebarCollapsed && (
              <span className="sidebar-title">Navigation</span>
            )}
          </div>

          <div className="sidebar-nav">
            {/* Daily Operations */}
            <div className="nav-section">
              {!sidebarCollapsed && (
                <div className="nav-section-title">Daily Operations</div>
              )}
              <button
                className={`sidebar-nav-btn ${
                  activeTab === "daily-tasks" ? "active" : ""
                }`}
                onClick={() => changeTab("daily-tasks")}
                title={`Today's Tasks (${dailyTasks.length})`}
              >
                <span className="nav-icon">📝</span>
                {!sidebarCollapsed && (
                  <>
                    <span className="nav-text">Today's Tasks</span>
                    <span className="nav-badge">{dailyTasks.length}</span>
                  </>
                )}
              </button>
              <button
                className={`sidebar-nav-btn ${
                  activeTab === "reports" ? "active" : ""
                }`}
                onClick={() => changeTab("reports")}
                title="Location Reports"
              >
                <span className="nav-icon">📊</span>
                {!sidebarCollapsed && (
                  <>
                    <span className="nav-text">Reports</span>
                  </>
                )}
              </button>
              <button
                className={`sidebar-nav-btn ${
                  activeTab === "watchlist" ? "active" : ""
                }`}
                onClick={() => changeTab("watchlist")}
                title={`Watchlist (${watchlistTasks.length})`}
              >
                <span className="nav-icon">📋</span>
                {!sidebarCollapsed && (
                  <>
                    <span className="nav-text">Watchlist</span>
                    <span className="nav-badge">{watchlistTasks.length}</span>
                  </>
                )}
              </button>
            </div>

            {/* Setup & Configuration */}
            <div className="nav-section">
              {!sidebarCollapsed && (
                <div className="nav-section-title">Setup & Configuration</div>
              )}
              <button
                className={`sidebar-nav-btn ${
                  activeTab === "locations" ? "active" : ""
                }`}
                onClick={() => changeTab("locations")}
                title={`Locations (${locations.length})`}
              >
                <span className="nav-icon">🏢</span>
                {!sidebarCollapsed && (
                  <>
                    <span className="nav-text">Locations</span>
                    <span className="nav-badge">{locations.length}</span>
                  </>
                )}
              </button>
              <button
                className={`sidebar-nav-btn ${
                  activeTab === "routines" ? "active" : ""
                }`}
                onClick={() => changeTab("routines")}
                title={`Routines (${routines.length})`}
              >
                <span className="nav-icon">🔄</span>
                {!sidebarCollapsed && (
                  <>
                    <span className="nav-text">Routines</span>
                    <span className="nav-badge">{routines.length}</span>
                  </>
                )}
              </button>
              <button
                className={`sidebar-nav-btn ${
                  activeTab === "routine-tasks" ? "active" : ""
                }`}
                onClick={() => changeTab("routine-tasks")}
                title={`Routine Tasks (${routineTasks.length})`}
              >
                <span className="nav-icon">📋</span>
                {!sidebarCollapsed && (
                  <>
                    <span className="nav-text">Routine Tasks</span>
                    <span className="nav-badge">{routineTasks.length}</span>
                  </>
                )}
              </button>
              <button
                className={`sidebar-nav-btn ${
                  activeTab === "schedules" ? "active" : ""
                }`}
                onClick={() => changeTab("schedules")}
                title={`Schedules (${schedules.length})`}
              >
                <span className="nav-icon">📅</span>
                {!sidebarCollapsed && (
                  <>
                    <span className="nav-text">Schedules</span>
                    <span className="nav-badge">{schedules.length}</span>
                  </>
                )}
              </button>
            </div>

            {/* Team Management */}
            <div className="nav-section">
              {!sidebarCollapsed && (
                <div className="nav-section-title">Team Management</div>
              )}
              <button
                className={`sidebar-nav-btn ${
                  activeTab === "roles" ? "active" : ""
                }`}
                onClick={() => changeTab("roles")}
                title={`Employee Roles (${tenantRoles.length})`}
              >
                <span className="nav-icon">👥</span>
                {!sidebarCollapsed && (
                  <>
                    <span className="nav-text">Employee Roles</span>
                    <span className="nav-badge">{tenantRoles.length}</span>
                  </>
                )}
              </button>
            </div>

            {/* Account & Settings */}
            <div className="nav-section">
              {!sidebarCollapsed && (
                <div className="nav-section-title">Account & Settings</div>
              )}
              <button
                className="sidebar-nav-btn"
                title="Billing & Plans"
                onClick={() => window.open("/billing", "_blank")}
              >
                <span className="nav-icon">💳</span>
                {!sidebarCollapsed && (
                  <>
                    <span className="nav-text">Billing & Plans</span>
                  </>
                )}
              </button>
              <button
                className="sidebar-nav-btn coming-soon"
                title="Coming Soon - Notifications"
                disabled
              >
                <span className="nav-icon">🔔</span>
                {!sidebarCollapsed && (
                  <>
                    <span className="nav-text">Notifications</span>
                    <span className="nav-badge coming-soon">Soon</span>
                  </>
                )}
              </button>
            </div>

            {/* Help & Support */}
            <div className="nav-section">
              {!sidebarCollapsed && (
                <div className="nav-section-title">Help & Support</div>
              )}
              <button
                className={`sidebar-nav-btn ${
                  activeTab === "help" ? "active" : ""
                }`}
                onClick={() => changeTab("help")}
                title="Help & Getting Started"
              >
                <span className="nav-icon">❓</span>
                {!sidebarCollapsed && (
                  <span className="nav-text">Help & Getting Started</span>
                )}
              </button>
            </div>
          </div>
        </nav>

        {/* Main Content */}
        <main className="owner-dashboard__content">
          {/* Global Trial Banner */}
          <TrialBanner dismissible={true} />

          {/* Setup Progress - Show for new users */}
          {showSetupProgress && !setupDismissed && (
            <SetupProgress
              key={setupProgressKey}
              onOpenWizard={handleOpenSetupWizard}
              onDismiss={handleDismissSetupProgress}
              onComplete={handleCompleteSetup}
              onSetupAction={handleSetupAction}
              compact={false}
              tenantId={tenantId}
            />
          )}

          {activeTab === "help" && <HelpTabV2 />}
          {activeTab === "locations" && (
            <LocationsTabV2
              locations={locations}
              onAddLocation={() => setShowLocationModal(true)}
              onEditLocation={(location) => {
                setEditingItem(location);
                setShowLocationModal(true);
              }}
              onDeleteLocation={handleDeleteLocation}
              userId={user?.uid}
              userProfile={currentUser}
            />
          )}
          {activeTab === "routines" && (
            <RoutinesTabV2
              routines={routines}
              routineTasks={routineTasks}
              onAddRoutine={() => setShowRoutineModal(true)}
              onEditRoutine={(routine) => {
                setEditingItem(routine);
                setShowRoutineModal(true);
              }}
              onDeleteRoutine={handleDeleteRoutine}
              onSelectRoutine={(routine) => {
                setSelectedRoutine(routine);
                changeTab("routine-tasks");
              }}
              userId={user?.uid}
            />
          )}
          {activeTab === "routine-tasks" && (
            <RoutineTasksTabV2
              routines={routines}
              routineTasks={routineTasks}
              selectedRoutine={selectedRoutine}
              onAddRoutineTask={() => setShowRoutineTaskModal(true)}
              onEditRoutineTask={(routineTask) => {
                setEditingItem(routineTask);
                setShowRoutineTaskModal(true);
              }}
              onDeleteRoutineTask={handleDeleteRoutineTask}
              onSelectRoutine={setSelectedRoutine}
              onSwitchToRoutines={() => changeTab("routines")}
            />
          )}
          {activeTab === "schedules" && (
            <SchedulesTabV2
              locations={locations}
              routines={routines}
              schedules={schedules}
              onAddSchedule={() => setShowScheduleModal(true)}
              onEditSchedule={(schedule) => {
                setEditingItem(schedule);
                setShowScheduleModal(true);
              }}
              onDeleteSchedule={handleDeleteSchedule}
              onGenerateTasksForSchedule={handleGenerateTasksForSchedule}
              userId={user?.uid}
              loading={taskGenerationLoading}
            />
          )}
          {activeTab === "daily-tasks" && (
            <DailyTasksTabV2
              locations={locations}
              dailyTasks={dailyTasks}
              adHocTasks={adHocTasks}
              onAddAdHocTask={() => setShowAdHocTaskModal(true)}
              onUpdateDailyTaskStatus={handleUpdateDailyTaskStatus}
              onUpdateAdHocTaskStatus={handleUpdateAdHocTaskStatus}
            />
          )}
          {activeTab === "reports" && (
            <ReportsTabV2 tenantId={tenantId} locations={locations} />
          )}
          {activeTab === "roles" && (
            <RolesTabV2
              tenantRoles={tenantRoles}
              onAddRole={() => setShowRoleModal(true)}
              onEditRole={(role) => {
                setEditingItem(role);
                setShowRoleModal(true);
              }}
              onDeleteRole={handleDeleteRole}
              userId={user?.uid}
            />
          )}
          {activeTab === "watchlist" && (
            <WatchlistTabV2
              watchlistTasks={watchlistTasks}
              locations={locations}
              tenantRoles={tenantRoles}
              onAddWatchlistTask={() => setShowWatchlistModal(true)}
              onEditWatchlistTask={(task) => {
                setEditingItem(task);
                setShowWatchlistModal(true);
              }}
              onDeleteWatchlistTask={handleDeleteWatchlistTask}
              onAddCheck={handleAddWatchlistCheck}
              userId={user?.uid}
            />
          )}
        </main>

        {/* Modals */}
        {showLocationModal && !editingItem && (
          <LocationLimitGuard
            userProfile={currentUser}
            currentLocationCount={locations.length}
            onCanCreate={() => {
              // Location creation is allowed, modal will show
            }}
          >
            <LocationModalV2
              location={editingItem as Location}
              tenantId={tenantId}
              onSave={handleCreateLocation}
              onCancel={() => {
                setShowLocationModal(false);
                setEditingItem(null);
              }}
              onRefreshLocations={async () => {
                const updatedLocations = await getAllLocations(tenantId);
                setLocations(updatedLocations);
              }}
            />
          </LocationLimitGuard>
        )}
        {showLocationModal && editingItem && (
          <LocationModalV2
            location={editingItem as Location}
            tenantId={tenantId}
            onSave={handleUpdateLocation}
            onCancel={() => {
              setShowLocationModal(false);
              setEditingItem(null);
            }}
            onRefreshLocations={async () => {
              const updatedLocations = await getAllLocations(tenantId);
              setLocations(updatedLocations);
            }}
          />
        )}

        {showRoutineModal && (
          <RoutineModalV2
            routine={editingItem as Routine}
            onSave={editingItem ? handleUpdateRoutine : handleCreateRoutine}
            onCancel={() => {
              setShowRoutineModal(false);
              setEditingItem(null);
            }}
          />
        )}

        {showRoutineTaskModal && (
          <RoutineTaskModalV2
            routineTask={editingItem as RoutineTask}
            routines={routines}
            selectedRoutine={selectedRoutine}
            tenantRoles={tenantRoles}
            onSave={
              editingItem ? handleUpdateRoutineTask : handleCreateRoutineTask
            }
            onCancel={() => {
              setShowRoutineTaskModal(false);
              setEditingItem(null);
            }}
          />
        )}

        {showScheduleModal && (
          <ScheduleModalV2
            schedule={editingItem as LocationRoutineSchedule}
            locations={locations}
            routines={routines}
            onSave={editingItem ? handleUpdateSchedule : handleCreateSchedule}
            onCancel={() => {
              setShowScheduleModal(false);
              setEditingItem(null);
            }}
          />
        )}

        {showAdHocTaskModal && (
          <AdHocTaskModalV2
            adHocTask={editingItem as AdHocTask}
            locations={locations}
            tenantRoles={tenantRoles}
            onSave={editingItem ? handleUpdateAdHocTask : handleCreateAdHocTask}
            onCancel={() => {
              setShowAdHocTaskModal(false);
              setEditingItem(null);
            }}
          />
        )}

        {showRoleModal && (
          <RoleModalV2
            role={editingItem}
            onClose={() => {
              setShowRoleModal(false);
              setEditingItem(null);
            }}
            onSubmit={editingItem ? handleUpdateRole : handleCreateRole}
          />
        )}

        {showWatchlistModal && (
          <WatchlistModalV2
            task={editingItem}
            locations={locations}
            tenantRoles={tenantRoles}
            onClose={() => {
              setShowWatchlistModal(false);
              setEditingItem(null);
            }}
            onSubmit={
              editingItem
                ? handleUpdateWatchlistTask
                : handleCreateWatchlistTask
            }
          />
        )}

        {/* Setup Wizard Modal */}
        {showSetupWizard && (
          <SetupWizard
            onClose={handleCloseSetupWizard}
            onComplete={handleCompleteSetup}
            onSetupAction={handleSetupAction}
            tenantId={tenantId}
            userEmail={currentUser?.email}
            onRefresh={refreshSetupProgress}
          />
        )}

        {/* Toast Notification */}
        {toast.visible && (
          <div className={`toast toast--${toast.type}`}>
            <div className="toast__content">
              <span className="toast__message">{toast.message}</span>
              <button
                className="toast__close"
                onClick={() =>
                  setToast((prev) => ({ ...prev, visible: false }))
                }
              >
                ×
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// Placeholder tab components - we'll implement these next
const OverviewTabV2: React.FC<{
  locations: Location[];
  routines: Routine[];
  routineTasks: RoutineTask[];
  schedules: LocationRoutineSchedule[];
  dailyTasks: DailyTask[];
  adHocTasks: AdHocTask[];
  onGenerateDailyTasks: (date?: string) => void;
  onExpireTasks?: () => void;
}> = ({
  locations,
  routines,
  routineTasks,
  schedules,
  dailyTasks,
  adHocTasks,
  onGenerateDailyTasks,
  onExpireTasks,
}) => {
  return (
    <div className="overview-tab">
      <h2>AdminBuddy Overview</h2>
      <div className="overview-tab__stats">
        <div className="stat-card">
          <div className="stat-card__icon">🏢</div>
          <div className="stat-card__content">
            <h3>{locations.length}</h3>
            <p>Locations</p>
          </div>
        </div>
        <div className="stat-card">
          <div className="stat-card__icon">🔄</div>
          <div className="stat-card__content">
            <h3>{routines.length}</h3>
            <p>Routines</p>
          </div>
        </div>
        <div className="stat-card">
          <div className="stat-card__icon">📋</div>
          <div className="stat-card__content">
            <h3>{routineTasks.length}</h3>
            <p>Routine Tasks</p>
          </div>
        </div>
        <div className="stat-card">
          <div className="stat-card__icon">📅</div>
          <div className="stat-card__content">
            <h3>{schedules.length}</h3>
            <p>Schedules</p>
          </div>
        </div>
        <div className="stat-card">
          <div className="stat-card__icon">📝</div>
          <div className="stat-card__content">
            <h3>{dailyTasks.filter((dt) => dt.status === "pending").length}</h3>
            <p>Pending Today</p>
          </div>
        </div>
        <div className="stat-card">
          <div className="stat-card__icon">⚡</div>
          <div className="stat-card__content">
            <h3>
              {adHocTasks.filter((aht) => aht.status === "pending").length}
            </h3>
            <p>To Do Tasks</p>
          </div>
        </div>
      </div>

      <div className="overview-workflow">
        <h3>AdminBuddy Workflow</h3>
        <div className="workflow-steps">
          <div className="workflow-step">
            <span className="step-number">1</span>
            <h4>Create Locations</h4>
            <p>Set up your physical business locations</p>
          </div>
          <div className="workflow-step">
            <span className="step-number">2</span>
            <h4>Design Routines</h4>
            <p>Create routine templates (Opening, Closing, etc.)</p>
          </div>
          <div className="workflow-step">
            <span className="step-number">3</span>
            <h4>Add Routine Tasks</h4>
            <p>Define tasks within each routine with expiry rules</p>
          </div>
          <div className="workflow-step">
            <span className="step-number">4</span>
            <h4>Schedule Routines</h4>
            <p>Set when routines run at each location</p>
          </div>
          <div className="workflow-step">
            <span className="step-number">5</span>
            <h4>Generate Daily Tasks</h4>
            <p>Tasks created automatically from schedules</p>
          </div>
          <div className="workflow-step">
            <span className="step-number">6</span>
            <h4>Add To Do Tasks</h4>
            <p>Quick one-off tasks as needed</p>
          </div>
        </div>
      </div>
    </div>
  );
};

// Help & Getting Started Tab
const HelpTabV2: React.FC = () => {
  return (
    <div className="help-tab">
      <h2>Help & Getting Started</h2>

      <div className="help-section">
        <h3>🚀 AdminBuddy Workflow</h3>
        <p>Follow these steps to set up your task coordination system:</p>
        <div className="workflow-steps">
          <div className="workflow-step">
            <span className="step-number">1</span>
            <h4>Create Locations</h4>
            <p>
              Set up your physical business locations (stores, offices, etc.)
            </p>
          </div>
          <div className="workflow-step">
            <span className="step-number">2</span>
            <h4>Design Routines</h4>
            <p>
              Create routine templates like "Opening Store", "Closing Store",
              "Weekly Cleaning"
            </p>
          </div>
          <div className="workflow-step">
            <span className="step-number">3</span>
            <h4>Add Routine Tasks</h4>
            <p>
              Define specific tasks within each routine with priorities and
              expiry rules
            </p>
          </div>
          <div className="workflow-step">
            <span className="step-number">4</span>
            <h4>Create Employee Roles</h4>
            <p>
              Set up roles like "Manager", "Keyholder", "Cashier" to organize
              task assignments
            </p>
          </div>
          <div className="workflow-step">
            <span className="step-number">5</span>
            <h4>Create Schedules</h4>
            <p>Assign routines to locations with day-of-week patterns</p>
          </div>
          <div className="workflow-step">
            <span className="step-number">6</span>
            <h4>Generate Daily Tasks</h4>
            <p>Tasks are created automatically from schedules each day</p>
          </div>
          <div className="workflow-step">
            <span className="step-number">7</span>
            <h4>Add To Do Tasks</h4>
            <p>Create one-off tasks as needed for special situations</p>
          </div>
          <div className="workflow-step">
            <span className="step-number">8</span>
            <h4>Use Kiosk Interface</h4>
            <p>
              Employees complete tasks using the simple kiosk interface at each
              location
            </p>
          </div>
        </div>
      </div>

      <div className="help-section">
        <h3>💡 Quick Tips</h3>
        <div className="tips-grid">
          <div className="tip-card">
            <h4>🔄 Routine vs To Do Tasks</h4>
            <p>
              Use routines for recurring tasks (daily opening, weekly cleaning).
              Use To Do tasks for one-time needs (special events, maintenance).
            </p>
          </div>
          <div className="tip-card">
            <h4>⏰ Task Expiry</h4>
            <p>
              Set expiry times to ensure time-sensitive tasks are completed
              promptly. Tasks can expire at end of day or at specific times.
            </p>
          </div>
          <div className="tip-card">
            <h4>👥 Role Organization</h4>
            <p>
              Use roles to organize tasks by employee type or department. Tasks
              can be grouped by role in the kiosk interface.
            </p>
          </div>
          <div className="tip-card">
            <h4>📱 Kiosk Access</h4>
            <p>
              Share the kiosk URL with your team. Each location has its own
              kiosk interface showing only relevant tasks.
            </p>
          </div>
        </div>
      </div>

      <div className="help-section">
        <h3>📞 Contact & Support</h3>
        <div className="contact-info">
          <div className="contact-card">
            <h4>📧 Email Support</h4>
            <p>For questions, feedback, or technical support:</p>
            <a href="mailto:<EMAIL>" className="contact-link">
              <EMAIL>
            </a>
          </div>
          <div className="contact-card">
            <h4>🐛 Report Issues</h4>
            <p>Found a bug or have a feature request?</p>
            <a href="mailto:<EMAIL>" className="contact-link">
              <EMAIL>
            </a>
          </div>
          <div className="contact-card">
            <h4>💬 Feedback</h4>
            <p>
              We'd love to hear how AdminBuddy is working for your business:
            </p>
            <a href="mailto:<EMAIL>" className="contact-link">
              <EMAIL>
            </a>
          </div>
        </div>
      </div>

      <div className="help-section">
        <h3>🎯 Getting the Most from AdminBuddy</h3>
        <div className="best-practices">
          <div className="practice-item">
            <h4>Start Small</h4>
            <p>
              Begin with one location and a few simple routines. Add complexity
              as your team gets comfortable with the system.
            </p>
          </div>
          <div className="practice-item">
            <h4>Clear Task Descriptions</h4>
            <p>
              Write clear, specific task descriptions so employees know exactly
              what needs to be done.
            </p>
          </div>
          <div className="practice-item">
            <h4>Regular Review</h4>
            <p>
              Review completed tasks regularly to identify patterns and optimize
              your routines.
            </p>
          </div>
          <div className="practice-item">
            <h4>Team Training</h4>
            <p>
              Train your team on the kiosk interface and explain how task
              completion helps the business run smoothly.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

// Tab Components
const LocationsTabV2: React.FC<{
  locations: Location[];
  onAddLocation: () => void;
  onEditLocation: (location: Location) => void;
  onDeleteLocation: (locationId: string) => void;
  userId?: string;
  userProfile?: any;
}> = ({
  locations,
  onAddLocation,
  onEditLocation,
  onDeleteLocation,
  userId,
  userProfile,
}) => {
  const { viewMode, setViewMode } = useViewMode({
    userId,
    page: "locations",
    defaultMode: "cards",
  });

  // Check if user can create more locations
  const [canCreateLocation, setCanCreateLocation] = useState(true);
  const [locationLimitMessage, setLocationLimitMessage] = useState<
    string | null
  >(null);

  useEffect(() => {
    const checkLocationAccess = async () => {
      if (!userProfile) return;

      try {
        const { checkFeatureAccess } = await import(
          "../../services/featureEnforcementService"
        );
        const access = await checkFeatureAccess(userProfile, locations.length);
        setCanCreateLocation(access.canCreateLocations);
        setLocationLimitMessage(access.reason || null);
      } catch (error) {
        console.error("Error checking location access:", error);
      }
    };

    checkLocationAccess();
  }, [userProfile, locations.length]);

  // Sort locations alphabetically by name
  const sortedLocations = [...locations].sort((a, b) =>
    a.name.localeCompare(b.name)
  );

  return (
    <div className="locations-tab">
      <div className="tab-header">
        <div className="tab-title">
          <h2>Manage Locations</h2>
        </div>
        <div className="tab-header-actions">
          <div className="view-toggle">
            <button
              className={`view-toggle-btn ${
                viewMode === "cards" ? "active" : ""
              }`}
              onClick={() => setViewMode("cards")}
              title="Card View"
            >
              ⊞
            </button>
            <button
              className={`view-toggle-btn ${
                viewMode === "list" ? "active" : ""
              }`}
              onClick={() => setViewMode("list")}
              title="List View"
            >
              ☰
            </button>
          </div>
          {canCreateLocation ? (
            <button className="btn btn--primary" onClick={onAddLocation}>
              <span>+</span> Add Location
            </button>
          ) : (
            <div className="location-limit-info">
              <button
                className="btn btn--disabled"
                disabled
                title={locationLimitMessage || "Location limit reached"}
              >
                <span>+</span> Add Location
              </button>
              {locationLimitMessage && (
                <p className="limit-message">{locationLimitMessage}</p>
              )}
            </div>
          )}
        </div>
      </div>

      {viewMode === "cards" ? (
        <div className="locations-grid">
          {sortedLocations.map((location) => (
            <div key={location.id} className="location-card">
              <div className="location-card__header">
                <h3>{location.name}</h3>
              </div>
              <p className="location-card__address">{location.address}</p>
              <div className="location-card__kiosk">
                <div className="kiosk-info">
                  <h4>📱 Kiosk Interface</h4>
                  <p>
                    Tablet-optimized interface for task completion at this
                    location
                  </p>
                </div>
                <button
                  className="btn btn--primary btn--kiosk"
                  onClick={() => window.open(`/kiosk/${location.id}`, "_blank")}
                >
                  🖥️ Open Kiosk
                </button>
              </div>
              <div className="location-card__actions">
                <button
                  className="btn btn--secondary"
                  onClick={() => onEditLocation(location)}
                >
                  Edit
                </button>
                <button
                  className="btn btn--danger"
                  onClick={() => onDeleteLocation(location.id)}
                >
                  Delete
                </button>
              </div>
            </div>
          ))}

          {sortedLocations.length === 0 && (
            <div className="empty-state">
              <h3>No locations yet</h3>
              <p>Create your first location to get started with AdminBuddy.</p>
              {canCreateLocation ? (
                <button className="btn btn--primary" onClick={onAddLocation}>
                  <span>+</span> Add Your First Location
                </button>
              ) : (
                <div className="location-limit-info">
                  <button
                    className="btn btn--disabled"
                    disabled
                    title={locationLimitMessage || "Location limit reached"}
                  >
                    <span>+</span> Add Your First Location
                  </button>
                  {locationLimitMessage && (
                    <p className="limit-message">{locationLimitMessage}</p>
                  )}
                </div>
              )}
            </div>
          )}
        </div>
      ) : (
        <div className="locations-table">
          <div className="table-header">
            <div className="table-cell header-cell">Location Name</div>
            <div className="table-cell header-cell">Address</div>
            <div className="table-cell header-cell">Status</div>
            <div className="table-cell header-cell">Kiosk</div>
            <div className="table-cell header-cell">Actions</div>
          </div>
          {sortedLocations.map((location) => (
            <div key={location.id} className="table-row">
              <div className="table-cell" data-label="Location Name">
                <span className="location-name-list">{location.name}</span>
              </div>
              <div className="table-cell" data-label="Address">
                <span className="location-address-list">
                  {location.address}
                </span>
              </div>

              <div className="table-cell" data-label="Kiosk">
                <button
                  className="btn btn--primary btn--sm"
                  onClick={() => window.open(`/kiosk/${location.id}`, "_blank")}
                >
                  🖥️ Open
                </button>
              </div>
              <div className="table-cell" data-label="Actions">
                <div className="location-actions">
                  <button
                    className="btn btn--secondary btn--sm"
                    onClick={() => onEditLocation(location)}
                  >
                    Edit
                  </button>
                  <button
                    className="btn btn--danger btn--sm"
                    onClick={() => onDeleteLocation(location.id)}
                  >
                    Delete
                  </button>
                </div>
              </div>
            </div>
          ))}

          {sortedLocations.length === 0 && (
            <div className="empty-state">
              <h3>No locations yet</h3>
              <p>Create your first location to get started with AdminBuddy.</p>
              {canCreateLocation ? (
                <button className="btn btn--primary" onClick={onAddLocation}>
                  <span>+</span> Add Your First Location
                </button>
              ) : (
                <div className="location-limit-info">
                  <button
                    className="btn btn--disabled"
                    disabled
                    title={locationLimitMessage || "Location limit reached"}
                  >
                    <span>+</span> Add Your First Location
                  </button>
                  {locationLimitMessage && (
                    <p className="limit-message">{locationLimitMessage}</p>
                  )}
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

const RoutinesTabV2: React.FC<{
  routines: Routine[];
  routineTasks: RoutineTask[];
  onAddRoutine: () => void;
  onEditRoutine: (routine: Routine) => void;
  onDeleteRoutine: (routineId: string) => void;
  onSelectRoutine: (routine: Routine) => void;
  userId?: string;
}> = ({
  routines,
  routineTasks,
  onAddRoutine,
  onEditRoutine,
  onDeleteRoutine,
  onSelectRoutine,
  userId,
}) => {
  const { viewMode, setViewMode } = useViewMode({
    userId,
    page: "routines",
    defaultMode: "cards",
  });

  // Sort routines alphabetically by name
  const sortedRoutines = [...routines].sort((a, b) =>
    a.name.localeCompare(b.name)
  );

  return (
    <div className="routines-tab">
      <div className="tab-header">
        <div className="tab-title">
          <h2>Manage Routines</h2>
        </div>
        <div className="tab-header-actions">
          <div className="view-toggle">
            <button
              className={`view-toggle-btn ${
                viewMode === "cards" ? "active" : ""
              }`}
              onClick={() => setViewMode("cards")}
              title="Card View"
            >
              ⊞
            </button>
            <button
              className={`view-toggle-btn ${
                viewMode === "list" ? "active" : ""
              }`}
              onClick={() => setViewMode("list")}
              title="List View"
            >
              ☰
            </button>
          </div>
          <button className="btn btn--primary" onClick={onAddRoutine}>
            <span>+</span> Add Routine
          </button>
        </div>
      </div>

      {viewMode === "cards" ? (
        <div className="routines-grid">
          {sortedRoutines.map((routine) => {
            const taskCount = routineTasks.filter(
              (rt) => rt.routineId === routine.id
            ).length;

            return (
              <div key={routine.id} className="routine-card">
                <div className="routine-card__header">
                  <h3>{routine.name}</h3>
                </div>

                {routine.description && (
                  <p className="routine-card__description">
                    {routine.description}
                  </p>
                )}

                <div className="routine-card__stats">
                  <span className="task-count">
                    📋 {taskCount} task{taskCount !== 1 ? "s" : ""}
                  </span>
                </div>

                <div className="routine-card__actions">
                  <button
                    className="btn btn--secondary"
                    onClick={() => onEditRoutine(routine)}
                  >
                    Edit
                  </button>
                  <button
                    className="btn btn--secondary"
                    onClick={() => onSelectRoutine(routine)}
                  >
                    Manage Tasks
                  </button>
                  <button
                    className="btn btn--danger"
                    onClick={() => onDeleteRoutine(routine.id)}
                  >
                    Delete
                  </button>
                </div>
              </div>
            );
          })}

          {sortedRoutines.length === 0 && (
            <div className="empty-state">
              <h3>No routines yet</h3>
              <p>
                Create your first routine template to organize recurring tasks.
              </p>
              <button className="btn btn--primary" onClick={onAddRoutine}>
                <span>+</span> Create Your First Routine
              </button>
            </div>
          )}
        </div>
      ) : (
        <div className="routines-table">
          <div className="table-header">
            <div className="table-cell header-cell">Routine Name</div>
            <div className="table-cell header-cell">Description</div>
            <div className="table-cell header-cell">Tasks</div>

            <div className="table-cell header-cell">Actions</div>
          </div>
          {sortedRoutines.map((routine) => {
            const taskCount = routineTasks.filter(
              (rt) => rt.routineId === routine.id
            ).length;

            return (
              <div key={routine.id} className="table-row">
                <div className="table-cell" data-label="Routine Name">
                  <span className="routine-name-list">{routine.name}</span>
                </div>
                <div className="table-cell" data-label="Description">
                  <span className="routine-description-list">
                    {routine.description || "No description"}
                  </span>
                </div>
                <div className="table-cell" data-label="Tasks">
                  <span className="task-count-list">
                    📋 {taskCount} task{taskCount !== 1 ? "s" : ""}
                  </span>
                </div>

                <div className="table-cell" data-label="Actions">
                  <div className="routine-actions">
                    <button
                      className="btn btn--secondary btn--sm"
                      onClick={() => onEditRoutine(routine)}
                    >
                      Edit
                    </button>
                    <button
                      className="btn btn--secondary btn--sm"
                      onClick={() => onSelectRoutine(routine)}
                    >
                      Tasks
                    </button>
                    <button
                      className="btn btn--danger btn--sm"
                      onClick={() => onDeleteRoutine(routine.id)}
                    >
                      Delete
                    </button>
                  </div>
                </div>
              </div>
            );
          })}

          {sortedRoutines.length === 0 && (
            <div className="empty-state">
              <h3>No routines yet</h3>
              <p>
                Create your first routine template to organize recurring tasks.
              </p>
              <button className="btn btn--primary" onClick={onAddRoutine}>
                <span>+</span> Create Your First Routine
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

const RoutineTasksTabV2: React.FC<{
  routines: Routine[];
  routineTasks: RoutineTask[];
  selectedRoutine: Routine | null;
  onAddRoutineTask: () => void;
  onEditRoutineTask: (routineTask: RoutineTask) => void;
  onDeleteRoutineTask: (routineTaskId: string) => void;
  onSelectRoutine: (routine: Routine | null) => void;
  onSwitchToRoutines: () => void;
}> = ({
  routines,
  routineTasks,
  selectedRoutine,
  onAddRoutineTask,
  onEditRoutineTask,
  onDeleteRoutineTask,
  onSelectRoutine,
  onSwitchToRoutines,
}) => {
  const [collapsedSections, setCollapsedSections] = useState<Set<string>>(
    new Set()
  );

  // Helper function to get priority weight for sorting (higher number = higher priority)
  const getPriorityWeight = (priority: string): number => {
    switch (priority) {
      case "high":
        return 3;
      case "medium":
        return 2;
      case "low":
        return 1;
      default:
        return 0;
    }
  };

  // Helper function to sort tasks by priority (high to low) then alphabetically
  const sortTasksByPriorityAndName = <
    T extends { priority: string; title: string }
  >(
    tasks: T[]
  ): T[] => {
    return tasks.sort((a, b) => {
      // First sort by priority (high to low)
      const priorityDiff =
        getPriorityWeight(b.priority) - getPriorityWeight(a.priority);
      if (priorityDiff !== 0) return priorityDiff;

      // Then sort alphabetically by title
      return a.title.localeCompare(b.title);
    });
  };

  const toggleSection = (sectionId: string) => {
    setCollapsedSections((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(sectionId)) {
        newSet.delete(sectionId);
      } else {
        newSet.add(sectionId);
      }
      return newSet;
    });
  };

  // Sort routines alphabetically by name first
  const sortedRoutines = [...routines].sort((a, b) =>
    a.name.localeCompare(b.name)
  );

  // Group tasks by routine (using sorted routines)
  const tasksByRoutine = sortedRoutines.reduce((groups, routine) => {
    const routineTasks_filtered = routineTasks.filter(
      (task) => task.routineId === routine.id
    );
    if (
      routineTasks_filtered.length > 0 ||
      selectedRoutine?.id === routine.id
    ) {
      groups[routine.id] = {
        routine,
        tasks: routineTasks_filtered,
      };
    }
    return groups;
  }, {} as Record<string, { routine: Routine; tasks: RoutineTask[] }>);

  // If a specific routine is selected, only show that one
  const displayGroups = selectedRoutine
    ? {
        [selectedRoutine.id]: tasksByRoutine[selectedRoutine.id] || {
          routine: selectedRoutine,
          tasks: [],
        },
      }
    : tasksByRoutine;

  return (
    <div className="routine-tasks-tab">
      <div className="tab-header">
        <div className="tab-header-left">
          <h2>Routine Tasks</h2>
          {selectedRoutine && (
            <div className="routine-selector">
              <span className="routine-badge">{selectedRoutine.name}</span>
              <button
                className="btn btn--small btn--secondary"
                onClick={() => onSelectRoutine(null)}
              >
                ← View All Routines
              </button>
            </div>
          )}
        </div>
        <div className="tab-header-actions">
          {!selectedRoutine && routines.length > 0 && (
            <select
              className="filter-select"
              onChange={(e) => {
                const routineId = e.target.value;
                const routine = routineId
                  ? routines.find((r) => r.id === routineId)
                  : null;
                onSelectRoutine(routine || null);
              }}
              value=""
            >
              <option value="">🔍 Focus on a routine...</option>
              {sortedRoutines.map((routine) => (
                <option key={routine.id} value={routine.id}>
                  {routine.name} (
                  {
                    routineTasks.filter((t) => t.routineId === routine.id)
                      .length
                  }{" "}
                  tasks)
                </option>
              ))}
            </select>
          )}
          <button
            className="btn btn--primary"
            onClick={onAddRoutineTask}
            disabled={routines.length === 0}
          >
            <span>+</span> Add Task
          </button>
        </div>
      </div>

      {routines.length === 0 ? (
        <div className="empty-state">
          <h3>No routines available</h3>
          <p>Create a routine first before adding tasks to it.</p>
          <button className="btn btn--secondary" onClick={onSwitchToRoutines}>
            Go to Routines →
          </button>
        </div>
      ) : Object.keys(displayGroups).length === 0 ? (
        <div className="empty-state">
          <h3>No routine tasks yet</h3>
          <p>
            Start by adding tasks to your routines to define the work that needs
            to be done.
          </p>
          <button className="btn btn--primary" onClick={onAddRoutineTask}>
            <span>+</span> Add Your First Task
          </button>
        </div>
      ) : (
        <div className="routine-tasks-by-routine">
          {Object.values(displayGroups).map(({ routine, tasks }) => {
            const isCollapsed = collapsedSections.has(routine.id);

            return (
              <div key={routine.id} className="routine-task-group">
                <div className="routine-task-header">
                  <div className="routine-task-header-content">
                    <button
                      className="section-toggle"
                      onClick={() => toggleSection(routine.id)}
                    >
                      <div className="toggle-icon">
                        <svg
                          width="14"
                          height="14"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2.5"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className={`section-arrow ${
                            isCollapsed ? "collapsed" : ""
                          }`}
                        >
                          <polyline points="6,9 12,15 18,9"></polyline>
                        </svg>
                      </div>
                      <div className="routine-info">
                        <h3>
                          <span className="routine-icon">🔄</span>
                          {routine.name}
                        </h3>
                        {routine.description && (
                          <p className="routine-description">
                            {routine.description}
                          </p>
                        )}
                      </div>
                      <div className="routine-stats">
                        <span className="task-count">
                          {tasks.length} task{tasks.length !== 1 ? "s" : ""}
                        </span>
                      </div>
                    </button>
                    {!selectedRoutine && (
                      <button
                        className="btn btn--small btn--secondary focus-btn"
                        onClick={() => onSelectRoutine(routine)}
                      >
                        Focus →
                      </button>
                    )}
                  </div>
                </div>

                <div
                  className={`routine-tasks ${isCollapsed ? "collapsed" : ""}`}
                >
                  {tasks.length === 0 ? (
                    <div className="empty-routine-tasks">
                      <p>No tasks in this routine yet.</p>
                      <button
                        className="btn btn--secondary btn--small"
                        onClick={() => {
                          onSelectRoutine(routine);
                          onAddRoutineTask();
                        }}
                      >
                        Add First Task
                      </button>
                    </div>
                  ) : (
                    <div className="tasks-list">
                      {sortTasksByPriorityAndName(tasks).map((task) => (
                        <div key={task.id} className="task-card">
                          <div className="task-content">
                            <div className="task-main">
                              <h4 className="task-title">{task.title}</h4>
                              {task.description && (
                                <p className="task-description">
                                  {task.description}
                                </p>
                              )}
                            </div>
                            <div className="task-meta">
                              <div className="task-badges">
                                <span
                                  className={`priority-badge priority-${task.priority}`}
                                >
                                  {task.priority === "high"
                                    ? "🔴"
                                    : task.priority === "medium"
                                    ? "🟡"
                                    : "🟢"}
                                  {task.priority}
                                </span>
                                <span
                                  className={`role-badge ${
                                    task.requiredRole === "keyholder"
                                      ? "keyholder"
                                      : task.requiredRole === "manager"
                                      ? "manager"
                                      : "any"
                                  }`}
                                >
                                  {task.requiredRole === "keyholder"
                                    ? "🔑 Keyholder"
                                    : task.requiredRole === "manager"
                                    ? "👔 Manager"
                                    : "👤 Any"}
                                </span>
                                {task.dueTime && (
                                  <span className="time-badge">
                                    ⏰ {task.dueTime}
                                  </span>
                                )}
                                <span
                                  className={`expiry-badge expiry-${task.expiryType}`}
                                >
                                  {task.expiryType === "set_datetime"
                                    ? "⏰ Set Time"
                                    : task.expiryType === "no_expiry"
                                    ? "♾️ No Expiry"
                                    : "📋 Next Report"}
                                </span>
                              </div>
                            </div>
                          </div>
                          <div className="task-actions">
                            <button
                              className="btn btn--small btn--secondary"
                              onClick={() => onEditRoutineTask(task)}
                              title="Edit task"
                            >
                              ✏️
                            </button>
                            <button
                              className="btn btn--small btn--danger"
                              onClick={() => onDeleteRoutineTask(task.id)}
                              title="Delete task"
                            >
                              🗑️
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            );
          })}

          {/* Show routines with no tasks when viewing all */}
          {!selectedRoutine &&
            routines
              .filter((routine) => !tasksByRoutine[routine.id])
              .map((routine) => (
                <div
                  key={routine.id}
                  className="routine-task-group routine-task-group--empty"
                >
                  <div className="routine-task-header">
                    <div className="routine-info">
                      <h3>
                        <span className="routine-icon">🔄</span>
                        {routine.name}
                      </h3>
                      {routine.description && (
                        <p className="routine-description">
                          {routine.description}
                        </p>
                      )}
                    </div>
                    <div className="routine-stats">
                      <span className="task-count">0 tasks</span>
                    </div>
                  </div>
                  <div className="empty-routine-tasks">
                    <p>
                      No tasks in this routine yet. Add tasks to define the work
                      that needs to be done.
                    </p>
                    <button
                      className="btn btn--secondary btn--small"
                      onClick={() => {
                        onSelectRoutine(routine);
                        onAddRoutineTask();
                      }}
                    >
                      Add First Task
                    </button>
                  </div>
                </div>
              ))}
        </div>
      )}
    </div>
  );
};

const SchedulesTabV2: React.FC<{
  locations: Location[];
  routines: Routine[];
  schedules: LocationRoutineSchedule[];
  onAddSchedule: () => void;
  onEditSchedule: (schedule: LocationRoutineSchedule) => void;
  onDeleteSchedule: (scheduleId: string) => void;
  onGenerateTasksForSchedule: (schedule: LocationRoutineSchedule) => void;
  userId?: string;
  loading?: boolean;
}> = ({
  locations,
  routines,
  schedules,
  onAddSchedule,
  onEditSchedule,
  onDeleteSchedule,
  onGenerateTasksForSchedule,
  userId,
  loading = false,
}) => {
  const { viewMode, setViewMode } = useViewMode({
    userId,
    page: "schedules",
    defaultMode: "grouped",
  });

  // Calendar view state
  const [selectedLocationId, setSelectedLocationId] = useState<string>("");
  const [collapsedSections, setCollapsedSections] = useState<Set<string>>(
    new Set()
  );

  const toggleSection = (sectionId: string) => {
    setCollapsedSections((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(sectionId)) {
        newSet.delete(sectionId);
      } else {
        newSet.add(sectionId);
      }
      return newSet;
    });
  };

  const getDayName = (dayNum: number) => {
    const days = [
      "Sunday",
      "Monday",
      "Tuesday",
      "Wednesday",
      "Thursday",
      "Friday",
      "Saturday",
    ];
    return days[dayNum];
  };

  const formatDaysOfWeek = (daysOfWeek: number[]) => {
    if (daysOfWeek.length === 7) return "Every day";
    if (
      daysOfWeek.length === 5 &&
      !daysOfWeek.includes(0) &&
      !daysOfWeek.includes(6)
    )
      return "Weekdays";
    if (
      daysOfWeek.length === 2 &&
      daysOfWeek.includes(0) &&
      daysOfWeek.includes(6)
    )
      return "Weekends";

    return daysOfWeek
      .sort((a, b) => a - b)
      .map((day) => getDayName(day).slice(0, 3))
      .join(", ");
  };

  // Sort locations alphabetically by name
  const sortedLocations = [...locations].sort((a, b) =>
    a.name.localeCompare(b.name)
  );

  // Group schedules by location (using sorted locations)
  const schedulesByLocation = sortedLocations.reduce((groups, location) => {
    const locationSchedules = schedules.filter(
      (schedule) => schedule.locationId === location.id
    );
    if (locationSchedules.length > 0) {
      // Sort schedules within each location by routine name
      const sortedSchedules = locationSchedules.sort((a, b) => {
        const routineA = routines.find((r) => r.id === a.routineId);
        const routineB = routines.find((r) => r.id === b.routineId);
        return (routineA?.name || "").localeCompare(routineB?.name || "");
      });

      groups[location.id] = {
        location,
        schedules: sortedSchedules,
      };
    }
    return groups;
  }, {} as Record<string, { location: Location; schedules: LocationRoutineSchedule[] }>);

  return (
    <div className="schedules-tab">
      <div className="tab-header">
        <div className="tab-title">
          <h2>Routine Schedules</h2>
        </div>
        <div className="tab-header-actions">
          <div className="view-toggle">
            <button
              className={`view-toggle-btn ${
                viewMode === "grouped" ? "active" : ""
              }`}
              onClick={() => setViewMode("grouped")}
              title="Grouped View"
            >
              📍
            </button>
            <button
              className={`view-toggle-btn ${
                viewMode === "list" ? "active" : ""
              }`}
              onClick={() => setViewMode("list")}
              title="List View"
            >
              ☰
            </button>
            <button
              className={`view-toggle-btn ${
                viewMode === "calendar" ? "active" : ""
              }`}
              onClick={() => setViewMode("calendar")}
              title="Calendar View"
            >
              📅
            </button>
          </div>
          <button
            className="btn btn--primary"
            onClick={onAddSchedule}
            disabled={locations.length === 0 || routines.length === 0}
          >
            <span>+</span> Add Schedule
          </button>
        </div>
      </div>

      {locations.length === 0 || routines.length === 0 ? (
        <div className="empty-state">
          <h3>Prerequisites needed</h3>
          <p>
            {locations.length === 0 && "Create locations first. "}
            {routines.length === 0 && "Create routines first. "}
            Both are required to set up schedules.
          </p>
        </div>
      ) : schedules.length === 0 ? (
        <div className="empty-state">
          <h3>No schedules yet</h3>
          <p>
            Create your first schedule to define when routines run at each
            location.
          </p>
          <button className="btn btn--primary" onClick={onAddSchedule}>
            <span>+</span> Create Your First Schedule
          </button>
        </div>
      ) : viewMode === "calendar" ? (
        <ScheduleCalendar
          locations={locations}
          routines={routines}
          schedules={schedules}
          selectedLocationId={selectedLocationId}
          onLocationChange={setSelectedLocationId}
        />
      ) : viewMode === "grouped" ? (
        <div className="schedules-by-location">
          {Object.values(schedulesByLocation).map(
            ({ location, schedules: locationSchedules }) => {
              const isCollapsed = collapsedSections.has(location.id);

              return (
                <div key={location.id} className="location-schedule-group">
                  <div className="location-header">
                    <button
                      className="section-toggle"
                      onClick={() => toggleSection(location.id)}
                    >
                      <div className="toggle-icon">
                        <svg
                          width="14"
                          height="14"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2.5"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className={`section-arrow ${
                            isCollapsed ? "collapsed" : ""
                          }`}
                        >
                          <polyline points="6,9 12,15 18,9"></polyline>
                        </svg>
                      </div>
                      <div className="location-info">
                        <h3>{location.name}</h3>
                        <p>{location.address}</p>
                      </div>
                      <div className="location-stats">
                        <span className="schedule-count">
                          {locationSchedules.length} schedule
                          {locationSchedules.length !== 1 ? "s" : ""}
                        </span>
                      </div>
                    </button>
                  </div>

                  <div
                    className={`location-schedules ${
                      isCollapsed ? "collapsed" : ""
                    }`}
                  >
                    <div className="schedules-table">
                      <div className="table-header">
                        <div className="table-col">Routine</div>
                        <div className="table-col">Days</div>
                        <div className="table-col">Cadence</div>
                        <div className="table-col">Visible From</div>
                        <div className="table-col">Status</div>
                        <div className="table-col">Actions</div>
                      </div>

                      {locationSchedules.map((schedule) => {
                        const routine = routines.find(
                          (r) => r.id === schedule.routineId
                        );

                        return (
                          <div key={schedule.id} className="table-row">
                            <div className="table-col">
                              <div className="routine-info">
                                <span className="routine-badge">
                                  {routine?.name || "Unknown Routine"}
                                </span>
                                {routine?.description && (
                                  <p className="routine-description">
                                    {routine.description}
                                  </p>
                                )}
                              </div>
                            </div>
                            <div className="table-col">
                              <span className="days-badge">
                                {formatDaysOfWeek(schedule.daysOfWeek)}
                              </span>
                            </div>
                            <div className="table-col">
                              <span className="cadence-badge">
                                {schedule.cadence}
                              </span>
                            </div>
                            <div className="table-col">
                              <span className="time-badge">
                                {schedule.visibleFromTime || "-"}
                              </span>
                            </div>

                            <div className="table-col">
                              <div className="schedule-actions">
                                <button
                                  className="btn btn--small btn--success"
                                  onClick={() =>
                                    onGenerateTasksForSchedule(schedule)
                                  }
                                  disabled={loading}
                                  title="Generate tasks for this routine now"
                                >
                                  Generate
                                </button>
                                <button
                                  className="btn btn--small"
                                  onClick={() => onEditSchedule(schedule)}
                                >
                                  Edit
                                </button>
                                <button
                                  className="btn btn--small btn--danger"
                                  onClick={() => onDeleteSchedule(schedule.id)}
                                >
                                  Delete
                                </button>
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                </div>
              );
            }
          )}

          {/* Show locations with no schedules */}
          {sortedLocations
            .filter((loc) => !schedulesByLocation[loc.id])
            .map((location) => (
              <div
                key={location.id}
                className="location-schedule-group location-schedule-group--empty"
              >
                <div className="location-header">
                  <div className="location-info">
                    <h3>{location.name}</h3>
                    <p>{location.address}</p>
                  </div>
                  <div className="location-stats">
                    <span className="schedule-count">No schedules</span>
                  </div>
                </div>
                <div className="empty-location-schedules">
                  <p>No schedules configured for this location yet.</p>
                  <button
                    className="btn btn--secondary btn--small"
                    onClick={onAddSchedule}
                  >
                    Add First Schedule
                  </button>
                </div>
              </div>
            ))}
        </div>
      ) : (
        <div className="schedules-table">
          <div className="table-header">
            <div className="table-cell header-cell">Location</div>
            <div className="table-cell header-cell">Routine</div>
            <div className="table-cell header-cell">Days</div>
            <div className="table-cell header-cell">Cadence</div>
            <div className="table-cell header-cell">Visible From</div>
            <div className="table-cell header-cell">Status</div>
            <div className="table-cell header-cell">Actions</div>
          </div>
          {[...schedules]
            .sort((a, b) => {
              // Sort by location name first, then by routine name
              const locationA = locations.find((l) => l.id === a.locationId);
              const locationB = locations.find((l) => l.id === b.locationId);
              const locationCompare = (locationA?.name || "").localeCompare(
                locationB?.name || ""
              );

              if (locationCompare !== 0) return locationCompare;

              const routineA = routines.find((r) => r.id === a.routineId);
              const routineB = routines.find((r) => r.id === b.routineId);
              return (routineA?.name || "").localeCompare(routineB?.name || "");
            })
            .map((schedule) => {
              const location = locations.find(
                (l) => l.id === schedule.locationId
              );
              const routine = routines.find((r) => r.id === schedule.routineId);

              return (
                <div key={schedule.id} className="table-row">
                  <div className="table-cell" data-label="Location">
                    <span className="location-name-list">
                      {location?.name || "Unknown Location"}
                    </span>
                  </div>
                  <div className="table-cell" data-label="Routine">
                    <span className="routine-name-list">
                      {routine?.name || "Unknown Routine"}
                    </span>
                  </div>
                  <div className="table-cell" data-label="Days">
                    <span className="days-badge">
                      {formatDaysOfWeek(schedule.daysOfWeek)}
                    </span>
                  </div>
                  <div className="table-cell" data-label="Cadence">
                    <span className="cadence-badge">{schedule.cadence}</span>
                  </div>
                  <div className="table-cell" data-label="Visible From">
                    <span className="time-badge">
                      {schedule.visibleFromTime || "-"}
                    </span>
                  </div>

                  <div className="table-cell" data-label="Actions">
                    <div className="schedule-actions">
                      <button
                        className="btn btn--success btn--sm"
                        onClick={() => onGenerateTasksForSchedule(schedule)}
                        disabled={loading}
                        title="Generate tasks for this routine now"
                      >
                        Generate
                      </button>
                      <button
                        className="btn btn--secondary btn--sm"
                        onClick={() => onEditSchedule(schedule)}
                      >
                        Edit
                      </button>
                      <button
                        className="btn btn--danger btn--sm"
                        onClick={() => onDeleteSchedule(schedule.id)}
                      >
                        Delete
                      </button>
                    </div>
                  </div>
                </div>
              );
            })}

          {schedules.length === 0 && (
            <div className="empty-state">
              <h3>No schedules yet</h3>
              <p>
                Create your first schedule to define when routines run at each
                location.
              </p>
              <button className="btn btn--primary" onClick={onAddSchedule}>
                <span>+</span> Create Your First Schedule
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

// Today's Tasks Tab - Combines Daily Tasks and To Do Tasks, Grouped by Location
const DailyTasksTabV2: React.FC<{
  locations: Location[];
  dailyTasks: DailyTask[];
  adHocTasks: AdHocTask[];
  onAddAdHocTask: () => void;
  onUpdateDailyTaskStatus: (
    taskId: string,
    status: "pending" | "completed" | "expired"
  ) => void;
  onUpdateAdHocTaskStatus: (
    taskId: string,
    status: "pending" | "completed" | "expired"
  ) => void;
}> = ({
  locations,
  dailyTasks,
  adHocTasks,
  onAddAdHocTask,
  onUpdateDailyTaskStatus,
  onUpdateAdHocTaskStatus,
}) => {
  const [filter, setFilter] = useState<
    "all" | "pending" | "completed" | "expired"
  >("pending");
  const [selectedLocation, setSelectedLocation] = useState<Location | null>(
    null
  );
  const [collapsedSections, setCollapsedSections] = useState<Set<string>>(
    new Set()
  );

  const toggleSection = (sectionId: string) => {
    setCollapsedSections((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(sectionId)) {
        newSet.delete(sectionId);
      } else {
        newSet.add(sectionId);
      }
      return newSet;
    });
  };

  const today = new Date().toISOString().split("T")[0];

  // Get today's To Do tasks
  const todaysAdHocTasks = adHocTasks.filter((task) => task.dueDate === today);

  // Group tasks by location
  const tasksByLocation = locations.reduce(
    (groups, location) => {
      const locationDailyTasks = dailyTasks.filter(
        (task) => task.locationId === location.id
      );
      const locationAdHocTasks = todaysAdHocTasks.filter(
        (task) => task.locationId === location.id
      );

      // Apply status filter
      const filteredDailyTasks = locationDailyTasks.filter(
        (task) => filter === "all" || task.status === filter
      );
      const filteredAdHocTasks = locationAdHocTasks.filter(
        (task) => filter === "all" || task.status === filter
      );

      const allTasks = [...filteredDailyTasks, ...filteredAdHocTasks];

      if (allTasks.length > 0 || selectedLocation?.id === location.id) {
        groups[location.id] = {
          location,
          dailyTasks: filteredDailyTasks,
          adHocTasks: filteredAdHocTasks,
          allTasks,
        };
      }
      return groups;
    },
    {} as Record<
      string,
      {
        location: Location;
        dailyTasks: DailyTask[];
        adHocTasks: AdHocTask[];
        allTasks: (DailyTask | AdHocTask)[];
      }
    >
  );

  // If a specific location is selected, only show that one
  const displayGroups = selectedLocation
    ? {
        [selectedLocation.id]: tasksByLocation[selectedLocation.id] || {
          location: selectedLocation,
          dailyTasks: [],
          adHocTasks: [],
          allTasks: [],
        },
      }
    : tasksByLocation;

  const formatTime = (timeString?: string) => {
    if (!timeString) return "-";
    return timeString;
  };

  // Helper function to get priority weight for sorting (higher number = higher priority)
  const getPriorityWeight = (priority: string): number => {
    switch (priority) {
      case "high":
        return 3;
      case "medium":
        return 2;
      case "low":
        return 1;
      default:
        return 0;
    }
  };

  // Helper function to sort tasks by priority (high to low) then alphabetically
  const sortTasksByPriorityAndName = <
    T extends { priority: string; title: string }
  >(
    tasks: T[]
  ): T[] => {
    return tasks.sort((a, b) => {
      // First sort by priority (high to low)
      const priorityDiff =
        getPriorityWeight(b.priority) - getPriorityWeight(a.priority);
      if (priorityDiff !== 0) return priorityDiff;

      // Then sort alphabetically by title
      return a.title.localeCompare(b.title);
    });
  };

  // Calculate totals for summary
  const totalDailyTasks = dailyTasks.length;
  const totalAdHocTasks = todaysAdHocTasks.length;
  const pendingDaily = dailyTasks.filter((t) => t.status === "pending").length;
  const completedDaily = dailyTasks.filter(
    (t) => t.status === "completed"
  ).length;
  const expiredDaily = dailyTasks.filter((t) => t.status === "expired").length;
  const pendingAdHoc = todaysAdHocTasks.filter(
    (t) => t.status === "pending"
  ).length;
  const completedAdHoc = todaysAdHocTasks.filter(
    (t) => t.status === "completed"
  ).length;
  const expiredAdHoc = todaysAdHocTasks.filter(
    (t) => t.status === "expired"
  ).length;

  return (
    <div className="daily-tasks-tab">
      <div className="tab-header">
        <div className="tab-header-left">
          <h2>Today's Tasks - {today}</h2>
          {selectedLocation && (
            <div className="location-selector">
              <span className="location-badge">{selectedLocation.name}</span>
              <button
                className="btn btn--small btn--secondary"
                onClick={() => setSelectedLocation(null)}
              >
                ← View All Locations
              </button>
            </div>
          )}
        </div>
        <div className="tab-header-actions">
          {!selectedLocation && locations.length > 0 && (
            <select
              className="filter-select"
              onChange={(e) => {
                const locationId = e.target.value;
                const location = locationId
                  ? locations.find((l) => l.id === locationId)
                  : null;
                setSelectedLocation(location || null);
              }}
              value=""
            >
              <option value="">🔍 Focus on a location...</option>
              {locations.map((location) => {
                const dailyCount = dailyTasks.filter(
                  (t) => t.locationId === location.id
                ).length;
                const adHocCount = todaysAdHocTasks.filter(
                  (t) => t.locationId === location.id
                ).length;
                const totalCount = dailyCount + adHocCount;
                return (
                  <option key={location.id} value={location.id}>
                    {location.name} ({totalCount} tasks)
                  </option>
                );
              })}
            </select>
          )}
          <div className="filter-group">
            <select
              value={filter}
              onChange={(e) => setFilter(e.target.value as any)}
              className="filter-select"
            >
              <option value="pending">⏳ Pending</option>
              <option value="all">📋 All Tasks</option>
              <option value="completed">✅ Completed</option>
              <option value="expired">⏰ Expired</option>
            </select>
          </div>
          <button className="btn btn--primary" onClick={onAddAdHocTask}>
            <span>+</span> Add To Do Task
          </button>
        </div>
      </div>

      {/* Task Summary */}
      <div className="task-summary">
        <div className="summary-card">
          <h4>📅 Daily Tasks ({totalDailyTasks})</h4>
          <div className="summary-stats">
            <span className="stat pending">⏳ {pendingDaily} Pending</span>
            <span className="stat completed">
              ✅ {completedDaily} Completed
            </span>
            <span className="stat expired">⏰ {expiredDaily} Expired</span>
          </div>
        </div>

        <div className="summary-card">
          <h4>⚡ To Do Tasks ({totalAdHocTasks})</h4>
          <div className="summary-stats">
            <span className="stat pending">⏳ {pendingAdHoc} Pending</span>
            <span className="stat completed">
              ✅ {completedAdHoc} Completed
            </span>
            <span className="stat expired">⏰ {expiredAdHoc} Expired</span>
          </div>
        </div>
      </div>

      {locations.length === 0 ? (
        <div className="empty-state">
          <h3>No locations available</h3>
          <p>Create locations first to organize your daily tasks.</p>
        </div>
      ) : Object.keys(displayGroups).length === 0 ? (
        <div className="empty-state">
          <h3>No tasks found</h3>
          <p>
            {filter === "pending"
              ? "No pending tasks for today."
              : `No ${filter} tasks found.`}
          </p>
          {filter === "pending" && (
            <button className="btn btn--primary" onClick={onAddAdHocTask}>
              <span>+</span> Add To Do Task
            </button>
          )}
        </div>
      ) : (
        <div className="tasks-by-location">
          {Object.values(displayGroups).map(
            ({
              location,
              dailyTasks: locationDailyTasks,
              adHocTasks: locationAdHocTasks,
              allTasks,
            }) => {
              const isCollapsed = collapsedSections.has(location.id);

              return (
                <div key={location.id} className="location-task-group">
                  <div className="location-task-header">
                    <button
                      className="section-toggle"
                      onClick={() => toggleSection(location.id)}
                    >
                      <div className="toggle-icon">
                        <svg
                          width="14"
                          height="14"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2.5"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className={`section-arrow ${
                            isCollapsed ? "collapsed" : ""
                          }`}
                        >
                          <polyline points="6,9 12,15 18,9"></polyline>
                        </svg>
                      </div>
                      <div className="location-info">
                        <h3>
                          <span className="location-icon">🏢</span>
                          {location.name}
                        </h3>
                        {location.address && (
                          <p className="location-address">{location.address}</p>
                        )}
                      </div>
                      <div className="location-stats">
                        <span className="task-count">
                          {allTasks.length} task
                          {allTasks.length !== 1 ? "s" : ""}
                        </span>
                        <span className="task-breakdown">
                          📅 {locationDailyTasks.length} daily • ⚡{" "}
                          {locationAdHocTasks.length} to do
                        </span>
                      </div>
                    </button>
                    {!selectedLocation && (
                      <button
                        className="btn btn--small btn--secondary location-focus-btn"
                        onClick={() => setSelectedLocation(location)}
                      >
                        Focus →
                      </button>
                    )}
                  </div>

                  <div
                    className={`location-tasks ${
                      isCollapsed ? "collapsed" : ""
                    }`}
                  >
                    {allTasks.length === 0 ? (
                      <div className="empty-location-tasks">
                        <p>
                          No {filter === "all" ? "" : filter} tasks for this
                          location today.
                        </p>
                        <button
                          className="btn btn--secondary btn--small"
                          onClick={onAddAdHocTask}
                        >
                          Add To Do Task
                        </button>
                      </div>
                    ) : (
                      <div className="tasks-list">
                        {/* Daily Tasks */}
                        {locationDailyTasks.length > 0 && (
                          <div className="task-type-section">
                            <h4 className="task-type-header">
                              📅 Daily Tasks ({locationDailyTasks.length})
                            </h4>
                            <div className="task-cards">
                              {sortTasksByPriorityAndName(
                                locationDailyTasks
                              ).map((task) => (
                                <div key={task.id} className="daily-task-card">
                                  <div className="task-content">
                                    <div className="task-main">
                                      <h5 className="task-title">
                                        {task.title}
                                      </h5>
                                      {task.description && (
                                        <p className="task-description">
                                          {task.description}
                                        </p>
                                      )}
                                    </div>
                                    <div className="task-meta">
                                      <div className="task-badges">
                                        <span
                                          className={`priority-badge priority-${task.priority}`}
                                        >
                                          {task.priority === "high"
                                            ? "🔴"
                                            : task.priority === "medium"
                                            ? "🟡"
                                            : "🟢"}
                                          {task.priority}
                                        </span>
                                        {task.requiredRole &&
                                          task.requiredRole !== "any" && (
                                            <span
                                              className={`role-badge ${task.requiredRole}`}
                                            >
                                              {task.requiredRole === "keyholder"
                                                ? "🔑 Keyholder"
                                                : "👔 Manager"}
                                            </span>
                                          )}
                                        {task.dueTime && (
                                          <span className="time-badge">
                                            ⏰ {formatTime(task.dueTime)}
                                          </span>
                                        )}
                                        <span
                                          className={`status-badge status-${task.status}`}
                                        >
                                          {task.status === "pending"
                                            ? "⏳"
                                            : task.status === "completed"
                                            ? "✅"
                                            : "⏰"}
                                          {task.status}
                                        </span>
                                      </div>
                                    </div>
                                  </div>
                                  <div className="task-actions">
                                    {task.status === "pending" && (
                                      <button
                                        className="btn btn--small btn--success"
                                        onClick={() =>
                                          onUpdateDailyTaskStatus(
                                            task.id,
                                            "completed"
                                          )
                                        }
                                        title="Complete task"
                                      >
                                        ✅
                                      </button>
                                    )}
                                    {task.status === "completed" && (
                                      <button
                                        className="btn btn--small btn--secondary"
                                        onClick={() =>
                                          onUpdateDailyTaskStatus(
                                            task.id,
                                            "pending"
                                          )
                                        }
                                        title="Reopen task"
                                      >
                                        ↩️
                                      </button>
                                    )}
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}

                        {/* To Do Tasks */}
                        {locationAdHocTasks.length > 0 && (
                          <div className="task-type-section">
                            <h4 className="task-type-header">
                              ⚡ To Do Tasks ({locationAdHocTasks.length})
                            </h4>
                            <div className="task-cards">
                              {sortTasksByPriorityAndName(
                                locationAdHocTasks
                              ).map((task) => (
                                <div key={task.id} className="adhoc-task-card">
                                  <div className="task-content">
                                    <div className="task-main">
                                      <h5 className="task-title">
                                        {task.title}
                                      </h5>
                                      {task.description && (
                                        <p className="task-description">
                                          {task.description}
                                        </p>
                                      )}
                                    </div>
                                    <div className="task-meta">
                                      <div className="task-badges">
                                        <span
                                          className={`priority-badge priority-${task.priority}`}
                                        >
                                          {task.priority === "high"
                                            ? "🔴"
                                            : task.priority === "medium"
                                            ? "🟡"
                                            : "🟢"}
                                          {task.priority}
                                        </span>
                                        {task.requiredRole &&
                                          task.requiredRole !== "any" && (
                                            <span
                                              className={`role-badge ${task.requiredRole}`}
                                            >
                                              {task.requiredRole === "keyholder"
                                                ? "🔑 Keyholder"
                                                : "👔 Manager"}
                                            </span>
                                          )}
                                        {task.dueTime && (
                                          <span className="time-badge">
                                            ⏰ {formatTime(task.dueTime)}
                                          </span>
                                        )}
                                        <span
                                          className={`status-badge status-${task.status}`}
                                        >
                                          {task.status === "pending"
                                            ? "⏳"
                                            : task.status === "completed"
                                            ? "✅"
                                            : "⏰"}
                                          {task.status}
                                        </span>
                                      </div>
                                    </div>
                                  </div>
                                  <div className="task-actions">
                                    {task.status === "pending" && (
                                      <button
                                        className="btn btn--small btn--success"
                                        onClick={() =>
                                          onUpdateAdHocTaskStatus(
                                            task.id,
                                            "completed"
                                          )
                                        }
                                        title="Complete task"
                                      >
                                        ✅
                                      </button>
                                    )}
                                    {task.status === "completed" && (
                                      <button
                                        className="btn btn--small btn--secondary"
                                        onClick={() =>
                                          onUpdateAdHocTaskStatus(
                                            task.id,
                                            "pending"
                                          )
                                        }
                                        title="Reopen task"
                                      >
                                        ↩️
                                      </button>
                                    )}
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              );
            }
          )}

          {/* Show locations with no tasks when viewing all */}
          {!selectedLocation &&
            locations
              .filter((location) => !tasksByLocation[location.id])
              .map((location) => (
                <div
                  key={location.id}
                  className="location-task-group location-task-group--empty"
                >
                  <div className="location-task-header">
                    <div className="location-info">
                      <h3>
                        <span className="location-icon">🏢</span>
                        {location.name}
                      </h3>
                      {location.address && (
                        <p className="location-address">{location.address}</p>
                      )}
                    </div>
                    <div className="location-stats">
                      <span className="task-count">0 tasks</span>
                    </div>
                  </div>
                  <div className="empty-location-tasks">
                    <p>
                      No tasks for this location today. Add a To Do task to get
                      started.
                    </p>
                    <button
                      className="btn btn--secondary btn--small"
                      onClick={onAddAdHocTask}
                    >
                      Add To Do Task
                    </button>
                  </div>
                </div>
              ))}
        </div>
      )}
    </div>
  );
};

// Roles Tab V2
interface RolesTabV2Props {
  tenantRoles: TenantRole[];
  onAddRole: () => void;
  onEditRole: (role: TenantRole) => void;
  onDeleteRole: (roleId: string) => void;
}

const RolesTabV2: React.FC<RolesTabV2Props & { userId?: string }> = ({
  tenantRoles,
  onAddRole,
  onEditRole,
  onDeleteRole,
  userId,
}) => {
  const { viewMode, setViewMode } = useViewMode({
    userId,
    page: "roles",
    defaultMode: "cards",
  });

  return (
    <div className="roles-tab">
      <div className="tab-header">
        <div className="tab-title">
          <h2>Employee Roles</h2>
          <p>
            Create role labels to organize tasks by employee type or department
          </p>
        </div>
        <div className="tab-header-actions">
          <div className="view-toggle">
            <button
              className={`view-toggle-btn ${
                viewMode === "cards" ? "active" : ""
              }`}
              onClick={() => setViewMode("cards")}
              title="Card View"
            >
              ⊞
            </button>
            <button
              className={`view-toggle-btn ${
                viewMode === "list" ? "active" : ""
              }`}
              onClick={() => setViewMode("list")}
              title="List View"
            >
              ☰
            </button>
          </div>
          <button className="btn btn-primary" onClick={onAddRole}>
            <span className="btn-icon">➕</span>
            Add Role
          </button>
        </div>
      </div>

      <div className="roles-content">
        {tenantRoles.length === 0 ? (
          <div className="empty-state">
            <div className="empty-icon">🔐</div>
            <h3>No roles defined yet</h3>
            <p>Create roles to organize task assignments by employee type</p>
            <button className="btn btn-primary" onClick={onAddRole}>
              Create First Role
            </button>
          </div>
        ) : viewMode === "cards" ? (
          <div className="roles-grid">
            {tenantRoles.map((role) => (
              <div key={role.id} className="role-card">
                <div className="role-header">
                  <h3 className="role-name">{role.name}</h3>
                  <div className="role-actions">
                    <button
                      className="btn btn-sm btn-secondary"
                      onClick={() => onEditRole(role)}
                      title="Edit role"
                    >
                      ✏️
                    </button>
                    <button
                      className="btn btn-sm btn-danger"
                      onClick={() => onDeleteRole(role.id)}
                      title="Delete role"
                    >
                      🗑️
                    </button>
                  </div>
                </div>

                {role.description && (
                  <p className="role-description">{role.description}</p>
                )}

                <div className="role-usage">
                  <p className="role-usage-text">
                    Tasks assigned to this role will be grouped together in the
                    kiosk interface
                  </p>
                </div>

                <div className="role-status">
                  <span className="role-created">
                    Created {new Date(role.createdAt).toLocaleDateString()}
                  </span>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="roles-table">
            <div className="table-header">
              <div className="table-cell header-cell">Role Name</div>
              <div className="table-cell header-cell">Description</div>
              <div className="table-cell header-cell">Status</div>
              <div className="table-cell header-cell">Created</div>
              <div className="table-cell header-cell">Actions</div>
            </div>
            {tenantRoles.map((role) => (
              <div key={role.id} className="table-row">
                <div className="table-cell" data-label="Role Name">
                  <span className="role-name-list">{role.name}</span>
                </div>
                <div className="table-cell" data-label="Description">
                  <span className="role-description-list">
                    {role.description || "No description"}
                  </span>
                </div>

                <div className="table-cell" data-label="Created">
                  <span className="role-created">
                    {new Date(role.createdAt).toLocaleDateString()}
                  </span>
                </div>
                <div className="table-cell" data-label="Actions">
                  <div className="role-actions">
                    <button
                      className="btn btn-sm btn-secondary"
                      onClick={() => onEditRole(role)}
                      title="Edit role"
                    >
                      ✏️
                    </button>
                    <button
                      className="btn btn-sm btn-danger"
                      onClick={() => onDeleteRole(role.id)}
                      title="Delete role"
                    >
                      🗑️
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

// Modal Components
const LocationModalV2: React.FC<{
  location?: Location;
  tenantId?: string;
  onSave: (data: Omit<Location, "id" | "createdAt" | "tenantId">) => void;
  onCancel: () => void;
  onRefreshLocations?: () => Promise<void>;
}> = ({ location, tenantId, onSave, onCancel, onRefreshLocations }) => {
  const [formData, setFormData] = useState({
    name: location?.name || "",
    address: location?.address || "",
  });

  // Auto-focus the first field when modal opens
  useEffect(() => {
    const nameInput = document.getElementById("location-name");
    if (nameInput) {
      nameInput.focus();
    }
  }, []);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (formData.name.trim() && formData.address.trim()) {
      onSave(formData);
    }
  };

  return (
    <div className="modal-overlay" onClick={onCancel}>
      <div className="modal" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h3>{location ? "Edit Location" : "Add New Location"}</h3>
          <div className="modal-header-actions">
            <button
              type="button"
              className="btn btn--secondary btn--sm"
              onClick={onCancel}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="btn btn--primary btn--sm"
              form="location-form"
            >
              {location ? "Update" : "Create"} Location
            </button>
          </div>
        </div>

        <form
          id="location-form"
          onSubmit={handleSubmit}
          className="modal-content"
        >
          <div className="form-field">
            <label htmlFor="location-name">Location Name *</label>
            <input
              type="text"
              id="location-name"
              value={formData.name}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, name: e.target.value }))
              }
              placeholder="e.g., Downtown Coffee Shop"
              required
            />
          </div>

          <div className="form-field">
            <label htmlFor="location-address">Address *</label>
            <input
              type="text"
              id="location-address"
              value={formData.address}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, address: e.target.value }))
              }
              placeholder="e.g., 123 Main St, Downtown"
              required
            />
          </div>

          {/* Kiosk Credentials - only show for existing locations with full data */}
          {location && tenantId && location.id && location.name && (
            <KioskCredentials
              location={location as any} // Cast to the expected Location type
              tenantId={tenantId}
              onUpdate={async () => {
                // Refresh location data after kiosk credentials update
                console.log(
                  "Kiosk credentials updated for location:",
                  location.id
                );
                if (onRefreshLocations) {
                  await onRefreshLocations();
                }
              }}
            />
          )}
        </form>
      </div>
    </div>
  );
};

const RoutineModalV2: React.FC<{
  routine?: Routine;
  onSave: (data: Omit<Routine, "id" | "createdAt" | "tenantId">) => void;
  onCancel: () => void;
}> = ({ routine, onSave, onCancel }) => {
  const [formData, setFormData] = useState({
    name: routine?.name || "",
    description: routine?.description || "",
  });

  // Auto-focus the first field when modal opens
  useEffect(() => {
    const nameInput = document.getElementById("routine-name");
    if (nameInput) {
      nameInput.focus();
    }
  }, []);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (formData.name.trim()) {
      onSave(formData);
    }
  };

  return (
    <div className="modal-overlay" onClick={onCancel}>
      <div className="modal" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h3>{routine ? "Edit Routine" : "Create New Routine"}</h3>
          <div className="modal-header-actions">
            <button
              type="button"
              className="btn btn--secondary btn--sm"
              onClick={onCancel}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="btn btn--primary btn--sm"
              form="routine-form"
            >
              {routine ? "Update" : "Create"} Routine
            </button>
          </div>
        </div>

        <form
          id="routine-form"
          onSubmit={handleSubmit}
          className="modal-content"
        >
          <div className="form-field">
            <label htmlFor="routine-name">Routine Name *</label>
            <input
              type="text"
              id="routine-name"
              value={formData.name}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, name: e.target.value }))
              }
              placeholder="e.g., Opening, Closing, Daily Maintenance"
              required
            />
          </div>

          <div className="form-field">
            <label htmlFor="routine-description">Description</label>
            <textarea
              id="routine-description"
              value={formData.description}
              onChange={(e) =>
                setFormData((prev) => ({
                  ...prev,
                  description: e.target.value,
                }))
              }
              placeholder="Brief description of this routine..."
              rows={3}
            />
          </div>
        </form>
      </div>
    </div>
  );
};

const RoutineTaskModalV2: React.FC<{
  routineTask?: RoutineTask;
  routines: Routine[];
  selectedRoutine: Routine | null;
  tenantRoles: TenantRole[];
  onSave: (data: Omit<RoutineTask, "id" | "createdAt" | "tenantId">) => void;
  onCancel: () => void;
}> = ({
  routineTask,
  routines,
  selectedRoutine,
  tenantRoles,
  onSave,
  onCancel,
}) => {
  const [formData, setFormData] = useState({
    routineId: routineTask?.routineId || selectedRoutine?.id || "",
    title: routineTask?.title || "",
    description: routineTask?.description || "",
    priority: routineTask?.priority || ("medium" as const),
    requiredRole: routineTask?.requiredRole || ("any" as const),
    dueTime: routineTask?.dueTime || "",
    expiryType: routineTask?.expiryType || ("next_report" as const),
    expiryDateTime: routineTask?.expiryDateTime || "",
  });

  // Auto-focus the first field when modal opens
  useEffect(() => {
    const titleInput = document.getElementById("routine-task-title");
    if (titleInput) {
      titleInput.focus();
    }
  }, []);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (formData.title.trim() && formData.routineId) {
      onSave({
        ...formData,
        expiryDateTime:
          formData.expiryType === "set_datetime"
            ? formData.expiryDateTime
            : undefined,
      });
    }
  };

  return (
    <div className="modal-overlay" onClick={onCancel}>
      <div className="modal" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h3>{routineTask ? "Edit Routine Task" : "Add New Routine Task"}</h3>
          <div className="modal-header-actions">
            <button
              type="button"
              className="btn btn--secondary btn--sm"
              onClick={onCancel}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="btn btn--primary btn--sm"
              form="routine-task-form"
            >
              {routineTask ? "Update" : "Create"} Task
            </button>
          </div>
        </div>

        <form
          id="routine-task-form"
          onSubmit={handleSubmit}
          className="modal-content"
        >
          <div className="form-row">
            <div className="form-field">
              <label htmlFor="routine-task-title">Task Title *</label>
              <input
                type="text"
                id="routine-task-title"
                value={formData.title}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, title: e.target.value }))
                }
                placeholder="e.g., Unlock front door"
                required
              />
            </div>

            <div className="form-field">
              <label htmlFor="routine-task-routine">Routine *</label>
              <select
                id="routine-task-routine"
                value={formData.routineId}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    routineId: e.target.value,
                  }))
                }
                required
              >
                <option value="">Select a routine</option>
                {routines.map((routine) => (
                  <option key={routine.id} value={routine.id}>
                    {routine.name}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <div className="form-field">
            <label htmlFor="routine-task-description">Description</label>
            <textarea
              id="routine-task-description"
              value={formData.description}
              onChange={(e) =>
                setFormData((prev) => ({
                  ...prev,
                  description: e.target.value,
                }))
              }
              placeholder="Detailed instructions for this task..."
              rows={2}
            />
          </div>

          <div className="form-row">
            <div className="form-field">
              <label>Priority</label>
              <div className="priority-radio-group">
                <label className="priority-radio">
                  <input
                    type="radio"
                    name="priority"
                    value="low"
                    checked={formData.priority === "low"}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        priority: e.target.value as any,
                      }))
                    }
                  />
                  <span className="priority-radio-label priority-low">Low</span>
                </label>
                <label className="priority-radio">
                  <input
                    type="radio"
                    name="priority"
                    value="medium"
                    checked={formData.priority === "medium"}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        priority: e.target.value as any,
                      }))
                    }
                  />
                  <span className="priority-radio-label priority-medium">
                    Medium
                  </span>
                </label>
                <label className="priority-radio">
                  <input
                    type="radio"
                    name="priority"
                    value="high"
                    checked={formData.priority === "high"}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        priority: e.target.value as any,
                      }))
                    }
                  />
                  <span className="priority-radio-label priority-high">
                    High
                  </span>
                </label>
              </div>
            </div>

            <div className="form-field">
              <label htmlFor="routine-task-role">Required Role</label>
              <select
                id="routine-task-role"
                value={formData.requiredRole}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    requiredRole: e.target.value as any,
                  }))
                }
              >
                {tenantRoles
                  .sort((a, b) => a.name.localeCompare(b.name))
                  .map((role) => (
                    <option key={role.id} value={role.name.toLowerCase()}>
                      {role.name}
                    </option>
                  ))}
              </select>
              <small>
                Tasks assigned to this role will be grouped together in the
                kiosk interface
              </small>
            </div>
          </div>

          <div className="form-row">
            <div className="form-field">
              <label htmlFor="routine-task-due-time">Due Time (optional)</label>
              <input
                type="time"
                id="routine-task-due-time"
                value={formData.dueTime}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, dueTime: e.target.value }))
                }
              />
            </div>
          </div>

          <div className="form-row">
            <div className="form-field">
              <label htmlFor="routine-task-expiry">Expiry Type</label>
              <select
                id="routine-task-expiry"
                value={formData.expiryType}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    expiryType: e.target.value as any,
                  }))
                }
              >
                <option value="no_expiry">No Expiry</option>
                <option value="next_report">Next Report</option>
                <option value="set_datetime">Set Time</option>
              </select>
            </div>

            {formData.expiryType === "set_datetime" && (
              <div className="form-field">
                <label htmlFor="routine-task-expiry-time">
                  Expiry Time (24-hour format)
                </label>
                <input
                  type="time"
                  id="routine-task-expiry-time"
                  value={formData.expiryDateTime}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      expiryDateTime: e.target.value,
                    }))
                  }
                  placeholder="e.g., 17:00"
                  required={formData.expiryType === "set_datetime"}
                />
                <small
                  style={{
                    color: "#6b7280",
                    fontSize: "0.8rem",
                    marginTop: "0.25rem",
                    display: "block",
                  }}
                >
                  Task will expire at this time each day
                </small>
              </div>
            )}
          </div>
        </form>
      </div>
    </div>
  );
};

const ScheduleModalV2: React.FC<{
  schedule?: LocationRoutineSchedule;
  locations: Location[];
  routines: Routine[];
  onSave: (
    data: Omit<LocationRoutineSchedule, "id" | "createdAt" | "tenantId">
  ) => void;
  onCancel: () => void;
}> = ({ schedule, locations, routines, onSave, onCancel }) => {
  const [formData, setFormData] = useState({
    locationId:
      schedule?.locationId || (locations.length === 1 ? locations[0].id : ""),
    routineId: schedule?.routineId || "",
    cadence: schedule?.cadence || ("weekly" as const),
    daysOfWeek: schedule?.daysOfWeek || [],
    visibleFromTime: schedule?.visibleFromTime || "",
  });

  // Auto-focus the first field when modal opens
  useEffect(() => {
    const locationSelect = document.getElementById("schedule-location");
    if (locationSelect) {
      locationSelect.focus();
    }
  }, []);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // For daily cadence, auto-set all days
    const finalFormData = {
      ...formData,
      daysOfWeek:
        formData.cadence === "daily"
          ? [0, 1, 2, 3, 4, 5, 6] // All days for daily cadence
          : formData.daysOfWeek,
    };

    if (
      finalFormData.locationId &&
      finalFormData.routineId &&
      (finalFormData.cadence === "daily" || finalFormData.daysOfWeek.length > 0)
    ) {
      onSave(finalFormData);
    }
  };

  const handleDayToggle = (dayNum: number) => {
    setFormData((prev) => ({
      ...prev,
      daysOfWeek: prev.daysOfWeek.includes(dayNum)
        ? prev.daysOfWeek.filter((d) => d !== dayNum)
        : [...prev.daysOfWeek, dayNum].sort((a, b) => a - b),
    }));
  };

  const dayNames = [
    "Sunday",
    "Monday",
    "Tuesday",
    "Wednesday",
    "Thursday",
    "Friday",
    "Saturday",
  ];

  return (
    <div className="modal-overlay" onClick={onCancel}>
      <div className="modal" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h3>{schedule ? "Edit Schedule" : "Create New Schedule"}</h3>
          <div className="modal-header-actions">
            <button
              type="button"
              className="btn btn--secondary btn--sm"
              onClick={onCancel}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="btn btn--primary btn--sm"
              form="schedule-form"
            >
              {schedule ? "Update" : "Create"} Schedule
            </button>
          </div>
        </div>

        <form
          id="schedule-form"
          onSubmit={handleSubmit}
          className="modal-content"
        >
          <div className="form-row">
            <div className="form-field">
              <label htmlFor="schedule-location">Location *</label>
              <select
                id="schedule-location"
                value={formData.locationId}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    locationId: e.target.value,
                  }))
                }
                required
              >
                <option value="">Select a location</option>
                {locations.map((location) => (
                  <option key={location.id} value={location.id}>
                    {location.name}
                  </option>
                ))}
              </select>
            </div>

            <div className="form-field">
              <label htmlFor="schedule-routine">Routine *</label>
              <select
                id="schedule-routine"
                value={formData.routineId}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    routineId: e.target.value,
                  }))
                }
                required
              >
                <option value="">Select a routine</option>
                {routines.map((routine) => (
                  <option key={routine.id} value={routine.id}>
                    {routine.name}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {formData.cadence !== "daily" && (
            <div className="form-field">
              <label>
                Days of Week *{" "}
                {formData.cadence === "monthly" && (
                  <small>(first occurrence each month)</small>
                )}
                {formData.cadence === "quarterly" && (
                  <small>(first occurrence each quarter)</small>
                )}
                {formData.cadence === "yearly" && (
                  <small>(first occurrence each year)</small>
                )}
              </label>
              <div className="days-of-week-selector">
                {dayNames.map((dayName, index) => (
                  <label key={index} className="day-checkbox">
                    <input
                      type="checkbox"
                      checked={formData.daysOfWeek.includes(index)}
                      onChange={() => handleDayToggle(index)}
                    />
                    <span className="checkbox-custom"></span>
                    <span className="day-name">{dayName.slice(0, 3)}</span>
                  </label>
                ))}
              </div>
              {formData.daysOfWeek.length === 0 && (
                <p className="field-error">Please select at least one day</p>
              )}
            </div>
          )}

          <div className="form-row">
            <div className="form-field">
              <label htmlFor="schedule-cadence">Cadence *</label>
              <select
                id="schedule-cadence"
                value={formData.cadence}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    cadence: e.target.value as any,
                  }))
                }
                required
              >
                <option value="daily">Daily</option>
                <option value="weekly">Weekly</option>
                <option value="biweekly">Bi-weekly</option>
                <option value="monthly">Monthly</option>
                <option value="quarterly">Quarterly</option>
                <option value="yearly">Yearly</option>
              </select>
            </div>
            <div className="form-field">
              <label htmlFor="schedule-visible-time">
                Visible From (optional)
              </label>
              <input
                type="time"
                id="schedule-visible-time"
                value={formData.visibleFromTime}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    visibleFromTime: e.target.value,
                  }))
                }
              />
              <small className="form-help">
                Tasks become visible at this time (e.g., closing tasks at 6 PM)
              </small>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

const AdHocTaskModalV2: React.FC<{
  adHocTask?: AdHocTask;
  locations: Location[];
  tenantRoles: TenantRole[];
  onSave: (data: Omit<AdHocTask, "id" | "createdAt" | "tenantId">) => void;
  onCancel: () => void;
}> = ({ adHocTask, locations, tenantRoles, onSave, onCancel }) => {
  // Helper function to get default expiry datetime (today at 11:59 PM in local time)
  const getDefaultExpiryDateTime = () => {
    const today = new Date();
    today.setHours(23, 59, 0, 0); // Set to 11:59 PM

    // Format for datetime-local input (YYYY-MM-DDTHH:MM) in local timezone
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, "0");
    const day = String(today.getDate()).padStart(2, "0");
    const hours = String(today.getHours()).padStart(2, "0");
    const minutes = String(today.getMinutes()).padStart(2, "0");

    return `${year}-${month}-${day}T${hours}:${minutes}`;
  };

  const [formData, setFormData] = useState({
    locationId:
      adHocTask?.locationId || (locations.length === 1 ? locations[0].id : ""),
    title: adHocTask?.title || "",
    description: adHocTask?.description || "",
    priority: adHocTask?.priority || ("medium" as const),
    requiredRole: adHocTask?.requiredRole || ("any" as const),
    dueDate: adHocTask?.dueDate || new Date().toISOString().split("T")[0],
    dueTime: adHocTask?.dueTime || "",
    expiryType: adHocTask?.expiryType || ("no_expiry" as const),
    expiryDateTime: adHocTask?.expiryDateTime || "",
    status: adHocTask?.status || ("pending" as const),
  });

  // Helper function to handle expiry type change with smart defaults
  const handleExpiryTypeChange = (
    newType: "next_report" | "set_datetime" | "no_expiry"
  ) => {
    setFormData((prev) => ({
      ...prev,
      expiryType: newType,
      expiryDateTime:
        newType === "set_datetime" && !prev.expiryDateTime
          ? getDefaultExpiryDateTime()
          : prev.expiryDateTime,
    }));
  };

  // Auto-focus the first field when modal opens
  useEffect(() => {
    const titleInput = document.getElementById("adhoc-task-title");
    if (titleInput) {
      titleInput.focus();
    }
  }, []);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (formData.title.trim() && formData.locationId && formData.dueDate) {
      onSave({
        ...formData,
        expiryDateTime:
          formData.expiryType === "set_datetime"
            ? formData.expiryDateTime
            : undefined,
      });
    }
  };

  return (
    <div className="modal-overlay" onClick={onCancel}>
      <div className="modal" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h3>{adHocTask ? "Edit To Do Task" : "Create New To Do Task"}</h3>
          <div className="modal-header-actions">
            <button
              type="button"
              className="btn btn--secondary btn--sm"
              onClick={onCancel}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="btn btn--primary btn--sm"
              form="adhoc-task-form"
            >
              {adHocTask ? "Update" : "Create"} Task
            </button>
          </div>
        </div>

        <form
          id="adhoc-task-form"
          onSubmit={handleSubmit}
          className="modal-content"
        >
          <div className="form-row">
            <div className="form-field">
              <label htmlFor="adhoc-task-location">Location *</label>
              <select
                id="adhoc-task-location"
                value={formData.locationId}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    locationId: e.target.value,
                  }))
                }
                required
              >
                <option value="">Select a location</option>
                {locations.map((location) => (
                  <option key={location.id} value={location.id}>
                    {location.name}
                  </option>
                ))}
              </select>
            </div>

            <div className="form-field">
              <label htmlFor="adhoc-task-due-date">Due Date *</label>
              <input
                type="date"
                id="adhoc-task-due-date"
                value={formData.dueDate}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, dueDate: e.target.value }))
                }
                required
              />
            </div>
          </div>

          <div className="form-field">
            <label htmlFor="adhoc-task-title">Task Title *</label>
            <input
              type="text"
              id="adhoc-task-title"
              value={formData.title}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, title: e.target.value }))
              }
              placeholder="e.g., Fix broken equipment, Special cleaning"
              required
            />
          </div>

          <div className="form-field">
            <label htmlFor="adhoc-task-description">Description</label>
            <textarea
              id="adhoc-task-description"
              value={formData.description}
              onChange={(e) =>
                setFormData((prev) => ({
                  ...prev,
                  description: e.target.value,
                }))
              }
              placeholder="Additional details about this task..."
              rows={3}
            />
          </div>

          <div className="form-row">
            <div className="form-field">
              <label htmlFor="adhoc-task-priority">Priority</label>
              <select
                id="adhoc-task-priority"
                value={formData.priority}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    priority: e.target.value as "low" | "medium" | "high",
                  }))
                }
              >
                <option value="low">Low</option>
                <option value="medium">Medium</option>
                <option value="high">High</option>
              </select>
            </div>

            <div className="form-field">
              <label htmlFor="adhoc-task-role">Required Role</label>
              <select
                id="adhoc-task-role"
                value={formData.requiredRole}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    requiredRole: e.target.value,
                  }))
                }
              >
                {tenantRoles
                  .sort((a, b) => a.name.localeCompare(b.name))
                  .map((role) => (
                    <option key={role.id} value={role.name.toLowerCase()}>
                      {role.name}
                    </option>
                  ))}
              </select>
              <small>
                Tasks assigned to this role will be grouped together in the
                kiosk interface
              </small>
            </div>
          </div>

          <div className="form-row">
            <div className="form-field">
              <label htmlFor="adhoc-task-due-time">Due Time (optional)</label>
              <input
                type="time"
                id="adhoc-task-due-time"
                value={formData.dueTime}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, dueTime: e.target.value }))
                }
              />
            </div>

            <div className="form-field">
              <label htmlFor="adhoc-task-expiry-type">Expiry Type</label>
              <select
                id="adhoc-task-expiry-type"
                value={formData.expiryType}
                onChange={(e) =>
                  handleExpiryTypeChange(
                    e.target.value as
                      | "next_report"
                      | "set_datetime"
                      | "no_expiry"
                  )
                }
              >
                <option value="no_expiry">No Expiry</option>
                <option value="next_report">Next Report</option>
                <option value="set_datetime">Set Date/Time</option>
              </select>
            </div>
          </div>

          {formData.expiryType === "set_datetime" && (
            <div className="form-field">
              <label htmlFor="adhoc-task-expiry-datetime">
                Expiry Date & Time
              </label>
              <input
                type="datetime-local"
                id="adhoc-task-expiry-datetime"
                value={formData.expiryDateTime}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    expiryDateTime: e.target.value,
                  }))
                }
              />
            </div>
          )}
        </form>
      </div>
    </div>
  );
};

// Role Modal V2
interface RoleModalV2Props {
  role?: TenantRole;
  onClose: () => void;
  onSubmit: (
    roleData: Omit<TenantRole, "id" | "createdAt" | "tenantId">
  ) => void;
}

const RoleModalV2: React.FC<RoleModalV2Props> = ({
  role,
  onClose,
  onSubmit,
}) => {
  const [formData, setFormData] = useState({
    name: role?.name || "",
    description: role?.description || "",
    permissions: role?.permissions || ["complete_tasks", "view_tasks"], // Keep for backend compatibility
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.name.trim()) {
      alert("Role name is required");
      return;
    }
    onSubmit(formData);
  };

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal-content" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h2>{role ? "Edit Role" : "Create New Role"}</h2>
          <button className="modal-close" onClick={onClose}>
            ×
          </button>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="role-name">Role Name *</label>
            <input
              type="text"
              id="role-name"
              value={formData.name}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, name: e.target.value }))
              }
              placeholder="e.g., Barista, Cashier, Shift Lead"
              required
              autoFocus
            />
          </div>

          <div className="form-group">
            <label htmlFor="role-description">Description</label>
            <textarea
              id="role-description"
              value={formData.description}
              onChange={(e) =>
                setFormData((prev) => ({
                  ...prev,
                  description: e.target.value,
                }))
              }
              placeholder="Brief description of this role's responsibilities"
              rows={3}
            />
          </div>

          <div className="form-group">
            <label>Role Purpose</label>
            <div className="role-purpose-info">
              <p>
                This role will be used to organize tasks in the kiosk interface.
                Tasks assigned to this role will be grouped together, making it
                easier for employees to find relevant work.
              </p>
              <p>
                <strong>Examples:</strong> Keyholder, Barista, Cashier, Grocery
                Department, Produce Department, etc.
              </p>
            </div>
          </div>

          <div className="modal-actions">
            <button
              type="button"
              className="btn btn-secondary"
              onClick={onClose}
            >
              Cancel
            </button>
            <button type="submit" className="btn btn-primary">
              {role ? "Update Role" : "Create Role"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

// Reports Tab - Display location reports
const ReportsTabV2: React.FC<{
  tenantId: string | null;
  locations: Location[];
}> = ({ tenantId, locations }) => {
  const [reports, setReports] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedLocation, setSelectedLocation] = useState<string>("all");
  const [routines, setRoutines] = useState<any[]>([]);
  const [routineTasks, setRoutineTasks] = useState<any[]>([]);
  const [expandedSections, setExpandedSections] = useState<
    Record<string, Record<string, boolean>>
  >({});
  const [expandedRoutineGroups, setExpandedRoutineGroups] = useState<
    Record<string, Record<string, boolean>>
  >({});

  // Toggle section expansion
  const toggleSection = (reportId: string, sectionType: string) => {
    setExpandedSections((prev) => ({
      ...prev,
      [reportId]: {
        ...prev[reportId],
        [sectionType]: !prev[reportId]?.[sectionType],
      },
    }));
  };

  // Check if section is expanded (default: false)
  const isSectionExpanded = (reportId: string, sectionType: string) => {
    return expandedSections[reportId]?.[sectionType] || false;
  };

  // Toggle routine group expansion within a report section
  const toggleRoutineGroup = (
    reportId: string,
    sectionType: string,
    routineName: string
  ) => {
    setExpandedRoutineGroups((prev) => ({
      ...prev,
      [reportId]: {
        ...prev[reportId],
        [`${sectionType}-${routineName}`]:
          !prev[reportId]?.[`${sectionType}-${routineName}`],
      },
    }));
  };

  // Check if routine group is expanded (default: false)
  const isRoutineGroupExpanded = (
    reportId: string,
    sectionType: string,
    routineName: string
  ) => {
    return (
      expandedRoutineGroups[reportId]?.[`${sectionType}-${routineName}`] ||
      false
    );
  };

  // Load reports and routine data on component mount and when tenantId changes
  useEffect(() => {
    if (tenantId) {
      loadData();
    }
  }, [tenantId, selectedLocation]);

  const loadData = async () => {
    if (!tenantId) return;

    try {
      setLoading(true);

      // First load routine data and get the actual data
      const routineData = await loadRoutineData();

      // Then load reports with the routine data
      await loadReports(routineData.routines, routineData.routineTasks);
    } catch (error) {
      console.error("❌ Error loading data:", error);
    } finally {
      setLoading(false);
    }
  };

  const loadRoutineData = async () => {
    if (!tenantId) return { routines: [], routineTasks: [] };

    try {
      const { getAllRoutines, getAllRoutineTasks } = await import(
        "../../services/adminBuddyFirebaseService"
      );

      const [routinesData, routineTasksData] = await Promise.all([
        getAllRoutines(tenantId),
        getAllRoutineTasks(tenantId),
      ]);

      setRoutines(routinesData);
      setRoutineTasks(routineTasksData);

      return { routines: routinesData, routineTasks: routineTasksData };
    } catch (error) {
      console.error("❌ Error loading routine data:", error);
      return { routines: [], routineTasks: [] };
    }
  };

  const loadReports = async (
    routinesData?: any[],
    routineTasksData?: any[]
  ) => {
    if (!tenantId) return;

    try {
      console.log("📊 Loading reports for tenant:", tenantId);

      const { queryDocuments } = await import(
        "../../services/firestoreRestApi"
      );

      // Build filters
      const filters: any[] = [
        { field: "tenantId", operator: "==", value: tenantId },
      ];

      // Add location filter if specific location selected
      if (selectedLocation !== "all") {
        filters.push({
          field: "locationId",
          operator: "==",
          value: selectedLocation,
        });
      }

      // Temporarily remove ordering until index is created
      const reportsData = await queryDocuments(
        "adminbuddy_location_reports",
        filters
        // Temporarily removing ordering: [{ field: "reportTimestamp", direction: "desc" }]
      );

      // Manually sort by reportTimestamp (most recent first) since we can't use ordering
      reportsData.sort(
        (a: any, b: any) =>
          new Date(b.reportTimestamp).getTime() -
          new Date(a.reportTimestamp).getTime()
      );

      // Enhance reports with routine names for legacy data
      const enhancedReports = await Promise.all(
        reportsData.map(async (report: any) => ({
          ...report,
          completedSinceLastReport: await enhanceTasksWithRoutineNames(
            report.completedSinceLastReport || [],
            routinesData || [],
            routineTasksData || []
          ),
          currentPendingTasks: await enhanceTasksWithRoutineNames(
            report.currentPendingTasks || [],
            routinesData || [],
            routineTasksData || []
          ),
        }))
      );

      console.log(`✅ Loaded ${enhancedReports.length} reports`);
      setReports(enhancedReports);
    } catch (error) {
      console.error("❌ Error loading reports:", error);
      setReports([]);
    }
  };

  // Helper function to enhance tasks with routine names by looking up original task data
  const enhanceTasksWithRoutineNames = async (
    tasks: any[],
    routinesData: any[],
    routineTasksData: any[]
  ): Promise<any[]> => {
    console.log("🔍 Enhancing tasks with routine names:", {
      tasksCount: tasks.length,
      routinesCount: routinesData.length,
      routineTasksCount: routineTasksData.length,
      tenantId,
    });

    if (
      !tenantId ||
      !tasks.length ||
      routinesData.length === 0 ||
      routineTasksData.length === 0
    ) {
      console.log("⚠️ Skipping enhancement - missing data");
      return tasks;
    }

    try {
      const { queryDocuments } = await import(
        "../../services/firestoreRestApi"
      );

      // Get all daily tasks and ad-hoc tasks for this tenant
      const [dailyTasks, adHocTasks] = await Promise.all([
        queryDocuments("adminbuddy_daily_tasks", [
          { field: "tenantId", operator: "==", value: tenantId },
        ]),
        queryDocuments("adminbuddy_ad_hoc_tasks", [
          { field: "tenantId", operator: "==", value: tenantId },
        ]),
      ]);

      // Create lookup maps
      const dailyTaskMap = new Map(
        dailyTasks.map((task: any) => [task.id, task])
      );
      const adHocTaskMap = new Map(
        adHocTasks.map((task: any) => [task.id, task])
      );

      // Enhance each task
      return tasks.map((task: any) => {
        console.log("🔍 Processing task:", {
          id: task.id,
          title: task.title,
          hasRoutineName: !!task.routineName,
        });

        if (task.routineName) {
          console.log("✅ Task already has routine name:", task.routineName);
          return task; // Already has routine name
        }

        // Look up original task
        const originalTask =
          dailyTaskMap.get(task.id) || adHocTaskMap.get(task.id);

        console.log("🔍 Original task lookup:", {
          taskId: task.id,
          foundInDaily: dailyTaskMap.has(task.id),
          foundInAdHoc: adHocTaskMap.has(task.id),
          originalTask: originalTask
            ? { id: originalTask.id, routineTaskId: originalTask.routineTaskId }
            : null,
        });

        if (!originalTask) {
          console.log("⚠️ No original task found, defaulting to 'To Do Tasks'");
          return { ...task, routineName: "To Do Tasks" };
        }

        // If it's an ad-hoc task, it goes to "To Do Tasks"
        if (adHocTaskMap.has(task.id)) {
          console.log("✅ Ad-hoc task, assigning to 'To Do Tasks'");
          return { ...task, routineName: "To Do Tasks" };
        }

        // For daily tasks, look up routine name
        if (originalTask.routineTaskId) {
          const routineTask = routineTasksData.find(
            (rt: any) => rt.id === originalTask.routineTaskId
          );
          console.log("🔍 Routine task lookup:", {
            routineTaskId: originalTask.routineTaskId,
            found: !!routineTask,
            routineTask: routineTask
              ? { id: routineTask.id, routineId: routineTask.routineId }
              : null,
          });

          if (routineTask) {
            const routine = routinesData.find(
              (r: any) => r.id === routineTask.routineId
            );
            console.log("🔍 Routine lookup:", {
              routineId: routineTask.routineId,
              found: !!routine,
              routine: routine ? { id: routine.id, name: routine.name } : null,
            });

            if (routine) {
              console.log("✅ Found routine name:", routine.name);
              return { ...task, routineName: routine.name };
            }
          }
        }

        console.log("⚠️ No routine found, defaulting to 'To Do Tasks'");
        return { ...task, routineName: "To Do Tasks" };
      });
    } catch (error) {
      console.error("❌ Error enhancing tasks with routine names:", error);
      return tasks;
    }
  };

  const formatDate = (timestamp: string) => {
    return new Date(timestamp).toLocaleString();
  };

  const formatPeriod = (fromTimestamp: string, toTimestamp: string) => {
    const from = new Date(fromTimestamp);
    const to = new Date(toTimestamp);

    if (fromTimestamp === "1970-01-01T00:00:00.000Z") {
      return `Beginning → ${to.toLocaleDateString()}`;
    }

    return `${from.toLocaleDateString()} → ${to.toLocaleDateString()}`;
  };

  // Helper function to group tasks by routine
  const groupTasksByRoutine = (tasks: any[]): [string, any[]][] => {
    const grouped = tasks.reduce((acc: Record<string, any[]>, task: any) => {
      const routineName = task.routineName || "To Do Tasks";

      if (!acc[routineName]) {
        acc[routineName] = [];
      }
      acc[routineName].push(task);
      return acc;
    }, {});

    // Sort routines alphabetically, but put "To Do Tasks" last
    const sortedEntries = Object.entries(grouped).sort(([a], [b]) => {
      if (a === "To Do Tasks") return 1;
      if (b === "To Do Tasks") return -1;
      return a.localeCompare(b);
    });

    return sortedEntries as [string, any[]][];
  };

  if (loading) {
    return (
      <div className="reports-tab">
        <div className="tab-header">
          <h2>📊 Location Reports</h2>
        </div>
        <div className="loading-spinner">Loading reports...</div>
      </div>
    );
  }

  return (
    <div className="reports-tab">
      <div className="tab-header">
        <div className="tab-title">
          <h2>📊 Location Reports</h2>
          <p>View reports sent from kiosk interfaces</p>
        </div>
        <div className="tab-header-actions">
          <select
            value={selectedLocation}
            onChange={(e) => setSelectedLocation(e.target.value)}
            className="filter-select"
          >
            <option value="all">All Locations</option>
            {locations.map((location) => (
              <option key={location.id} value={location.id}>
                {location.name}
              </option>
            ))}
          </select>
        </div>
      </div>

      {reports.length === 0 ? (
        <div className="empty-state">
          <div className="empty-state__icon">📊</div>
          <h3>No Reports Yet</h3>
          <p>
            Reports will appear here when your team sends them from the kiosk
            interface.
          </p>
          <div className="empty-state__help">
            <h4>How to generate reports:</h4>
            <ol>
              <li>Complete some tasks in the kiosk interface</li>
              <li>Click the "📊 Send Report" button</li>
              <li>Add any notes and submit</li>
              <li>The report will appear here</li>
            </ol>
          </div>
        </div>
      ) : (
        <div className="reports-list">
          {reports.map((report) => (
            <div key={report.id} className="report-card">
              <div className="report-card__header">
                <div className="report-info">
                  <h3>{report.locationName}</h3>
                  <p className="report-date">
                    {formatDate(report.reportTimestamp)}
                  </p>
                  <p className="report-period">
                    Period:{" "}
                    {formatPeriod(
                      report.reportPeriod.fromTimestamp,
                      report.reportPeriod.toTimestamp
                    )}
                  </p>
                </div>
                <div className="report-stats">
                  <div className="stat">
                    <span className="stat-number">
                      {report.taskSummary.completedInPeriod}
                    </span>
                    <span className="stat-label">Completed</span>
                  </div>
                  <div className="stat">
                    <span className="stat-number">
                      {report.taskSummary.currentPending}
                    </span>
                    <span className="stat-label">Pending</span>
                  </div>
                  <div className="stat">
                    <span className="stat-number">
                      {report.taskSummary.currentExpired}
                    </span>
                    <span className="stat-label">Expired</span>
                  </div>
                </div>
              </div>

              {report.reportNotes && (
                <div className="report-notes">
                  <h4>📝 Notes:</h4>
                  <p>{report.reportNotes}</p>
                </div>
              )}

              <div className="report-details">
                <div className="report-section">
                  <h4
                    className="collapsible-header"
                    onClick={() => toggleSection(report.id, "completed")}
                    style={{
                      cursor: "pointer",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "space-between",
                    }}
                  >
                    <span>
                      ✅ Completed Tasks (
                      {report.completedSinceLastReport.length})
                    </span>
                    <span className="expand-icon">
                      {isSectionExpanded(report.id, "completed") ? "▼" : "▶"}
                    </span>
                  </h4>
                  {isSectionExpanded(report.id, "completed") && (
                    <>
                      {report.completedSinceLastReport.length > 0 ? (
                        <div className="task-list">
                          {groupTasksByRoutine(
                            report.completedSinceLastReport
                          ).map(
                            ([routineName, routineTasks]: [string, any[]]) => {
                              const isRoutineExpanded = isRoutineGroupExpanded(
                                report.id,
                                "completed",
                                routineName
                              );

                              return (
                                <div
                                  key={routineName}
                                  className="routine-group"
                                >
                                  <h5
                                    className="routine-group-title routine-group-title--collapsible"
                                    onClick={() =>
                                      toggleRoutineGroup(
                                        report.id,
                                        "completed",
                                        routineName
                                      )
                                    }
                                  >
                                    <span className="routine-group-title-content">
                                      <span className="routine-collapse-icon">
                                        {isRoutineExpanded ? "▼" : "▶"}
                                      </span>
                                      📋 {routineName}
                                    </span>
                                    <span className="routine-task-count">
                                      {routineTasks.length}
                                    </span>
                                  </h5>
                                  {isRoutineExpanded && (
                                    <div className="routine-tasks">
                                      {routineTasks.map(
                                        (task: any, index: number) => (
                                          <div
                                            key={index}
                                            className="task-item task-item--completed"
                                          >
                                            <div className="task-main-info">
                                              <span className="task-title">
                                                {task.title}
                                              </span>
                                              <span className="task-time">
                                                {formatDate(task.completedAt)}
                                              </span>
                                            </div>
                                            {task.notes && (
                                              <div className="task-notes">
                                                <span className="task-notes-label">
                                                  📝 Notes:
                                                </span>
                                                <span className="task-notes-text">
                                                  {task.notes}
                                                </span>
                                              </div>
                                            )}
                                          </div>
                                        )
                                      )}
                                    </div>
                                  )}
                                </div>
                              );
                            }
                          )}
                        </div>
                      ) : (
                        <p className="no-tasks">
                          No tasks completed in this period
                        </p>
                      )}
                    </>
                  )}
                </div>

                {/* Expired Tasks Section */}
                {report.expiredTasks && report.expiredTasks.length > 0 && (
                  <div className="report-section">
                    <h4
                      className="collapsible-header"
                      onClick={() => toggleSection(report.id, "expired")}
                      style={{
                        cursor: "pointer",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "space-between",
                      }}
                    >
                      <span>
                        ⏰ Expired Tasks ({report.expiredTasks.length})
                      </span>
                      <span className="expand-icon">
                        {isSectionExpanded(report.id, "expired") ? "▼" : "▶"}
                      </span>
                    </h4>
                    {isSectionExpanded(report.id, "expired") && (
                      <>
                        <div className="task-list">
                          {report.expiredTasks.map(
                            (task: any, index: number) => (
                              <div
                                key={index}
                                className="task-item task-item--expired"
                              >
                                <div className="task-main-info">
                                  <span className="task-title">
                                    {task.title}
                                  </span>
                                  {task.routineName && (
                                    <span className="task-routine">
                                      ({task.routineName})
                                    </span>
                                  )}
                                  <span className="task-status task-status--expired">
                                    ⏰ Expired
                                  </span>
                                </div>
                                {task.notes && (
                                  <div className="task-notes">
                                    <span className="task-notes-label">
                                      📝 Notes:
                                    </span>
                                    <span className="task-notes-text">
                                      {task.notes}
                                    </span>
                                  </div>
                                )}
                              </div>
                            )
                          )}
                        </div>
                      </>
                    )}
                  </div>
                )}

                <div className="report-section">
                  <h4
                    className="collapsible-header"
                    onClick={() => toggleSection(report.id, "current")}
                    style={{
                      cursor: "pointer",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "space-between",
                    }}
                  >
                    <span>
                      ⏳ Current Pending ({report.currentPendingTasks.length})
                    </span>
                    <span className="expand-icon">
                      {isSectionExpanded(report.id, "current") ? "▼" : "▶"}
                    </span>
                  </h4>
                  {isSectionExpanded(report.id, "current") && (
                    <>
                      {report.currentPendingTasks.length > 0 ? (
                        <div className="task-list">
                          {groupTasksByRoutine(report.currentPendingTasks).map(
                            ([routineName, routineTasks]: [string, any[]]) => {
                              const isRoutineExpanded = isRoutineGroupExpanded(
                                report.id,
                                "current",
                                routineName
                              );

                              return (
                                <div
                                  key={routineName}
                                  className="routine-group"
                                >
                                  <h5
                                    className="routine-group-title routine-group-title--collapsible"
                                    onClick={() =>
                                      toggleRoutineGroup(
                                        report.id,
                                        "current",
                                        routineName
                                      )
                                    }
                                  >
                                    <span className="routine-group-title-content">
                                      <span className="routine-collapse-icon">
                                        {isRoutineExpanded ? "▼" : "▶"}
                                      </span>
                                      📋 {routineName}
                                    </span>
                                    <span className="routine-task-count">
                                      {routineTasks.length}
                                    </span>
                                  </h5>
                                  {isRoutineExpanded && (
                                    <div className="routine-tasks">
                                      {routineTasks.map(
                                        (task: any, index: number) => (
                                          <div
                                            key={index}
                                            className={`task-item task-item--${task.status}`}
                                          >
                                            <div className="task-main-info">
                                              <span className="task-title">
                                                {task.title}
                                              </span>
                                              <span
                                                className={`task-status task-status--${task.status}`}
                                              >
                                                {task.status === "pending"
                                                  ? "⏳ Pending"
                                                  : "⚠️ Expired"}
                                              </span>
                                            </div>
                                            {task.notes && (
                                              <div className="task-notes">
                                                <span className="task-notes-label">
                                                  📝 Notes:
                                                </span>
                                                <span className="task-notes-text">
                                                  {task.notes}
                                                </span>
                                              </div>
                                            )}
                                          </div>
                                        )
                                      )}
                                    </div>
                                  )}
                                </div>
                              );
                            }
                          )}
                        </div>
                      ) : (
                        <p className="no-tasks">All tasks completed! 🎉</p>
                      )}
                    </>
                  )}
                </div>
              </div>

              <div className="report-footer">
                <span className="reported-by">
                  Reported by: {report.reportedBy}
                </span>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

// Watchlist Modal Component
const WatchlistModalV2: React.FC<{
  task?: WatchlistTask;
  locations: Location[];
  tenantRoles: TenantRole[];
  onClose: () => void;
  onSubmit: (
    taskData: Omit<WatchlistTask, "id" | "createdAt" | "checkHistory">
  ) => void;
}> = ({ task, locations, tenantRoles, onClose, onSubmit }) => {
  const [formData, setFormData] = useState({
    tenantId: task?.tenantId || "",
    locationId: task?.locationId || "",
    title: task?.title || "",
    description: task?.description || "",
    priority: task?.priority || ("medium" as "low" | "medium" | "high"),
    requiredRole: task?.requiredRole || "any employee",
    frequencyMinutes: task?.frequencyMinutes || 120, // Default 2 hours
  });

  // Set tenant ID and default location if available
  React.useEffect(() => {
    if (!formData.tenantId && locations.length > 0) {
      setFormData((prev) => ({
        ...prev,
        tenantId: locations[0].tenantId,
        // Auto-select location if only one exists and no location is set
        locationId:
          prev.locationId || (locations.length === 1 ? locations[0].id : ""),
      }));
    }
  }, [locations, formData.tenantId]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.title.trim()) {
      alert("Task title is required");
      return;
    }
    if (!formData.locationId) {
      alert("Please select a location");
      return;
    }
    if (formData.frequencyMinutes <= 0) {
      alert("Frequency must be greater than 0");
      return;
    }

    onSubmit({
      ...formData,
      title: formData.title.trim(),
      description: formData.description.trim() || undefined,
    });
  };

  const formatFrequencyDisplay = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours === 0) return `${mins}m`;
    if (mins === 0) return `${hours}h`;
    return `${hours}h ${mins}m`;
  };

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal-content" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h2>{task ? "Edit Watchlist Task" : "Create Watchlist Task"}</h2>
          <button className="modal-close" onClick={onClose}>
            ×
          </button>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="watchlist-title">Task Title *</label>
            <input
              type="text"
              id="watchlist-title"
              value={formData.title}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, title: e.target.value }))
              }
              placeholder="e.g., Check restroom cleanliness"
              required
              autoFocus
            />
          </div>

          <div className="form-group">
            <label htmlFor="watchlist-description">Description</label>
            <textarea
              id="watchlist-description"
              value={formData.description}
              onChange={(e) =>
                setFormData((prev) => ({
                  ...prev,
                  description: e.target.value,
                }))
              }
              placeholder="What should be checked or monitored?"
              rows={3}
            />
          </div>

          <div className="form-row">
            <div className="form-field">
              <label htmlFor="watchlist-location">Location *</label>
              <select
                id="watchlist-location"
                value={formData.locationId}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    locationId: e.target.value,
                  }))
                }
                required
              >
                <option value="">Select Location</option>
                {locations.map((location) => (
                  <option key={location.id} value={location.id}>
                    {location.name}
                  </option>
                ))}
              </select>
            </div>

            <div className="form-field">
              <label htmlFor="watchlist-priority">Priority</label>
              <select
                id="watchlist-priority"
                value={formData.priority}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    priority: e.target.value as "low" | "medium" | "high",
                  }))
                }
              >
                <option value="low">Low</option>
                <option value="medium">Medium</option>
                <option value="high">High</option>
              </select>
            </div>
          </div>

          <div className="form-row">
            <div className="form-field">
              <label htmlFor="watchlist-role">Required Role</label>
              <select
                id="watchlist-role"
                value={formData.requiredRole}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    requiredRole: e.target.value,
                  }))
                }
              >
                {tenantRoles.map((role) => (
                  <option key={role.id} value={role.name.toLowerCase()}>
                    {role.name}
                  </option>
                ))}
              </select>
            </div>

            <div className="form-field">
              <label htmlFor="watchlist-frequency">
                Check Frequency (minutes) *
              </label>
              <input
                type="number"
                id="watchlist-frequency"
                value={formData.frequencyMinutes}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    frequencyMinutes: parseInt(e.target.value) || 0,
                  }))
                }
                min="1"
                required
              />
              <small className="form-help">
                Current: {formatFrequencyDisplay(formData.frequencyMinutes)}
              </small>
            </div>
          </div>

          <div className="form-group">
            <label>Frequency Examples</label>
            <div className="frequency-examples">
              <button
                type="button"
                className="btn btn-sm btn-outline"
                onClick={() =>
                  setFormData((prev) => ({ ...prev, frequencyMinutes: 60 }))
                }
              >
                1 hour
              </button>
              <button
                type="button"
                className="btn btn-sm btn-outline"
                onClick={() =>
                  setFormData((prev) => ({ ...prev, frequencyMinutes: 120 }))
                }
              >
                2 hours
              </button>
              <button
                type="button"
                className="btn btn-sm btn-outline"
                onClick={() =>
                  setFormData((prev) => ({ ...prev, frequencyMinutes: 240 }))
                }
              >
                4 hours
              </button>
              <button
                type="button"
                className="btn btn-sm btn-outline"
                onClick={() =>
                  setFormData((prev) => ({ ...prev, frequencyMinutes: 480 }))
                }
              >
                8 hours
              </button>
            </div>
          </div>

          <div className="modal-actions">
            <button
              type="button"
              className="btn btn-secondary"
              onClick={onClose}
            >
              Cancel
            </button>
            <button type="submit" className="btn btn-primary">
              {task ? "Update Task" : "Create Task"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

// Watchlist Tab Component
const WatchlistTabV2: React.FC<{
  watchlistTasks: WatchlistTask[];
  locations: Location[];
  tenantRoles: TenantRole[];
  onAddWatchlistTask: () => void;
  onEditWatchlistTask: (task: WatchlistTask) => void;
  onDeleteWatchlistTask: (taskId: string) => void;
  onAddCheck: (taskId: string, checkData: Omit<WatchlistCheck, "id">) => void;
  userId?: string;
}> = ({
  watchlistTasks,
  locations,
  tenantRoles,
  onAddWatchlistTask,
  onEditWatchlistTask,
  onDeleteWatchlistTask,
  onAddCheck,
  userId,
}) => {
  const [selectedLocation, setSelectedLocation] = useState<string>("all");
  const { viewMode, setViewMode } = useViewMode({
    userId,
    page: "watchlist",
    defaultMode: "grouped",
  });

  // Filter tasks by location
  const filteredTasks = watchlistTasks.filter((task) =>
    selectedLocation === "all" ? true : task.locationId === selectedLocation
  );

  // Sort tasks alphabetically by title
  const sortedTasks = [...filteredTasks].sort((a, b) =>
    a.title.localeCompare(b.title)
  );

  // Group tasks by location for grouped view
  const groupedTasks = locations.reduce((acc, location) => {
    const locationTasks = sortedTasks.filter(
      (task) => task.locationId === location.id
    );
    if (locationTasks.length > 0) {
      acc[location.id] = {
        location,
        tasks: locationTasks,
      };
    }
    return acc;
  }, {} as Record<string, { location: Location; tasks: WatchlistTask[] }>);

  return (
    <div className="watchlist-tab">
      <div className="tab-header">
        <div className="tab-title">
          <h2>📋 Watchlist</h2>
          <p>Recurring check-in tasks that never disappear</p>
        </div>
        <div className="tab-header-actions">
          <div className="view-toggle">
            <button
              className={`view-toggle-btn ${
                viewMode === "grouped" ? "active" : ""
              }`}
              onClick={() => setViewMode("grouped")}
              title="Grouped by Location"
            >
              📍
            </button>
            <button
              className={`view-toggle-btn ${
                viewMode === "list" ? "active" : ""
              }`}
              onClick={() => setViewMode("list")}
              title="List View"
            >
              ☰
            </button>
          </div>
          <select
            value={selectedLocation}
            onChange={(e) => setSelectedLocation(e.target.value)}
            className="filter-select"
          >
            <option value="all">All Locations</option>
            {locations.map((location) => (
              <option key={location.id} value={location.id}>
                {location.name}
              </option>
            ))}
          </select>
          <button className="btn btn-primary" onClick={onAddWatchlistTask}>
            + Add Watchlist Task
          </button>
        </div>
      </div>

      {sortedTasks.length === 0 ? (
        <div className="empty-state">
          <div className="empty-state__icon">📋</div>
          <h3>No Watchlist Tasks Yet</h3>
          <p>
            Create recurring check-in tasks that your team can update throughout
            the day.
          </p>
          <div className="empty-state__help">
            <h4>Perfect for:</h4>
            <ul>
              <li>Restroom cleanliness checks</li>
              <li>Product facing and organization</li>
              <li>Equipment status monitoring</li>
              <li>Safety rounds</li>
              <li>Temperature logs</li>
            </ul>
          </div>
          <button className="btn btn-primary" onClick={onAddWatchlistTask}>
            Create Your First Watchlist Task
          </button>
        </div>
      ) : viewMode === "list" ? (
        <div className="watchlist-list">
          {sortedTasks.map((task) => (
            <WatchlistTaskRow
              key={task.id}
              task={task}
              locations={locations}
              tenantRoles={tenantRoles}
              onEdit={() => onEditWatchlistTask(task)}
              onDelete={() => onDeleteWatchlistTask(task.id)}
              onAddCheck={onAddCheck}
            />
          ))}
        </div>
      ) : (
        <div className="watchlist-grouped">
          {Object.values(groupedTasks).map(({ location, tasks }) => (
            <div key={location.id} className="location-group">
              <div className="location-group__header">
                <h3>{location.name}</h3>
                <span className="task-count">{tasks.length} tasks</span>
              </div>
              <div className="watchlist-grid">
                {tasks.map((task) => (
                  <WatchlistTaskCard
                    key={task.id}
                    task={task}
                    locations={locations}
                    tenantRoles={tenantRoles}
                    onEdit={() => onEditWatchlistTask(task)}
                    onDelete={() => onDeleteWatchlistTask(task.id)}
                    onAddCheck={onAddCheck}
                    showLocation={false}
                  />
                ))}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

// Watchlist Task Card Component
const WatchlistTaskCard: React.FC<{
  task: WatchlistTask;
  locations: Location[];
  tenantRoles: TenantRole[];
  onEdit: () => void;
  onDelete: () => void;
  onAddCheck: (taskId: string, checkData: Omit<WatchlistCheck, "id">) => void;
  showLocation?: boolean;
}> = ({
  task,
  locations,
  tenantRoles,
  onEdit,
  onDelete,
  onAddCheck,
  showLocation = true,
}) => {
  const [showCheckModal, setShowCheckModal] = useState(false);
  const [showHistoryModal, setShowHistoryModal] = useState(false);
  const [checkerName, setCheckerName] = useState("");
  const [checkNotes, setCheckNotes] = useState("");
  const [recentChecks, setRecentChecks] = useState<WatchlistCheck[]>([]);
  const [fullHistory, setFullHistory] = useState<WatchlistCheck[]>([]);
  const [loadingChecks, setLoadingChecks] = useState(false);
  const [loadingHistory, setLoadingHistory] = useState(false);

  // Calculate status based on last check and frequency
  const getTaskStatus = () => {
    if (!task.lastCheckAt) {
      return "overdue"; // Never checked
    }

    const lastCheck = new Date(task.lastCheckAt);
    const now = new Date();
    const minutesSinceLastCheck =
      (now.getTime() - lastCheck.getTime()) / (1000 * 60);

    if (minutesSinceLastCheck <= task.frequencyMinutes * 0.8) {
      return "good"; // Recently checked (within 80% of frequency)
    } else if (minutesSinceLastCheck <= task.frequencyMinutes) {
      return "due-soon"; // Due soon (80-100% of frequency elapsed)
    } else {
      return "overdue"; // Overdue
    }
  };

  const status = getTaskStatus();
  const location = locations.find((l) => l.id === task.locationId);
  const role = tenantRoles.find(
    (r) => r.name.toLowerCase() === task.requiredRole
  );

  // Fetch recent checks when modal opens
  const fetchRecentChecks = async () => {
    if (!task.lastCheckAt) {
      return; // No checks to fetch
    }

    setLoadingChecks(true);
    try {
      const checks = await getWatchlistChecks(task.id, 3);
      setRecentChecks(checks);
    } catch (error) {
      console.error("Error fetching recent checks:", error);
    } finally {
      setLoadingChecks(false);
    }
  };

  // Fetch full history for the history modal
  const fetchFullHistory = async () => {
    if (!task.lastCheckAt) {
      return; // No checks to fetch
    }

    setLoadingHistory(true);
    try {
      const checks = await getWatchlistChecks(task.id, 20);
      setFullHistory(checks);
    } catch (error) {
      console.error("Error fetching full history:", error);
    } finally {
      setLoadingHistory(false);
    }
  };

  // Fetch checks when component mounts (for card display) and when modal opens
  React.useEffect(() => {
    if (task.lastCheckAt) {
      fetchRecentChecks();
    }
  }, [task.lastCheckAt, task.id]);

  React.useEffect(() => {
    if (showCheckModal) {
      fetchRecentChecks();
    }
  }, [showCheckModal]);

  const handleAddCheck = () => {
    if (!checkerName.trim()) {
      alert("Please enter your name");
      return;
    }

    const checkData: Omit<WatchlistCheck, "id"> = {
      watchlistTaskId: task.id,
      tenantId: task.tenantId,
      checkedBy: checkerName.trim(),
      checkedAt: new Date().toISOString(),
      notes: checkNotes.trim() || undefined,
    };

    onAddCheck(task.id, checkData);
    setCheckerName("");
    setCheckNotes("");
    setShowCheckModal(false);
  };

  return (
    <>
      <div className={`watchlist-card watchlist-card--${status}`}>
        <div className="watchlist-card__header">
          <div className="watchlist-card__title">
            <h3>{task.title}</h3>
            <div className="watchlist-card__meta">
              {showLocation && (
                <span className="location-name">{location?.name}</span>
              )}
              <span className="role-name">
                {role?.name || task.requiredRole}
              </span>
              <span className="frequency">
                Every {Math.floor(task.frequencyMinutes / 60)}h{" "}
                {task.frequencyMinutes % 60}m
              </span>
            </div>
          </div>
          <div className="watchlist-card__actions">
            <button
              className="btn btn-sm btn-primary"
              onClick={() => setShowCheckModal(true)}
            >
              ✓ Check In
            </button>
            <button className="btn btn-sm btn-secondary" onClick={onEdit}>
              Edit
            </button>
            <button className="btn btn-sm btn-danger" onClick={onDelete}>
              Delete
            </button>
          </div>
        </div>

        {task.description && (
          <div className="watchlist-card__description">
            <p>{task.description}</p>
          </div>
        )}

        <div className="watchlist-card__status">
          <div className={`status-indicator status-indicator--${status}`}>
            {status === "good" && "✅ Recently Checked"}
            {status === "due-soon" && "⚠️ Due Soon"}
            {status === "overdue" && "🔴 Overdue"}
          </div>
        </div>

        <div className="watchlist-card__history">
          <h4>Recent Check-ins</h4>
          {loadingChecks ? (
            <div className="loading-checks">Loading...</div>
          ) : recentChecks.length > 0 ? (
            <div className="check-history">
              {recentChecks.slice(0, 3).map((check) => (
                <div key={check.id} className="check-item">
                  <div className="check-info">
                    <span className="checker-name">{check.checkedBy}</span>
                    <span className="check-time">
                      {new Date(check.checkedAt).toLocaleString()}
                    </span>
                  </div>
                  {check.notes && (
                    <div className="check-notes">{check.notes}</div>
                  )}
                </div>
              ))}
              {recentChecks.length > 3 && (
                <div className="check-item check-item--more">
                  +{recentChecks.length - 3} more check-ins
                </div>
              )}
              {(recentChecks.length > 0 || task.lastCheckAt) && (
                <button
                  className="view-more-history-btn"
                  onClick={() => {
                    fetchFullHistory();
                    setShowHistoryModal(true);
                  }}
                  title="View full history"
                >
                  📋 View More
                </button>
              )}
            </div>
          ) : task.lastCheckAt ? (
            <div className="check-history">
              <div className="check-item">
                <div className="check-info">
                  <span className="checker-name">{task.lastCheckBy}</span>
                  <span className="check-time">
                    {new Date(task.lastCheckAt).toLocaleString()}
                  </span>
                </div>
              </div>
            </div>
          ) : (
            <p className="no-checks">No check-ins yet</p>
          )}
        </div>
      </div>

      {/* Check-in Modal */}
      {showCheckModal && (
        <div className="modal-overlay" onClick={() => setShowCheckModal(false)}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h2>Check In: {task.title}</h2>
              <button
                className="modal-close"
                onClick={() => setShowCheckModal(false)}
              >
                ×
              </button>
            </div>

            <div className="modal-body">
              <div className="form-group">
                <label htmlFor="checker-name">Your Name *</label>
                <input
                  type="text"
                  id="checker-name"
                  value={checkerName}
                  onChange={(e) => setCheckerName(e.target.value)}
                  placeholder="Enter your name"
                  autoFocus
                />
              </div>

              <div className="form-group">
                <label htmlFor="check-notes">Notes (optional)</label>
                <textarea
                  id="check-notes"
                  value={checkNotes}
                  onChange={(e) => setCheckNotes(e.target.value)}
                  placeholder="Any observations or notes..."
                  rows={3}
                />
              </div>

              {/* Recent Check-ins Section */}
              {task.lastCheckAt && (
                <div className="form-group">
                  <label>Recent Check-ins</label>
                  {loadingChecks ? (
                    <div className="loading-checks">
                      Loading recent check-ins...
                    </div>
                  ) : recentChecks.length > 0 ? (
                    <div className="recent-checks-list">
                      {recentChecks.map((check) => (
                        <div key={check.id} className="recent-check-item">
                          <div className="check-header">
                            <span className="check-name">
                              {check.checkedBy}
                            </span>
                            <span className="check-time">
                              {new Date(check.checkedAt).toLocaleString()}
                            </span>
                          </div>
                          {check.notes && (
                            <div className="check-notes">"{check.notes}"</div>
                          )}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="no-recent-checks">
                      Last check: {task.lastCheckBy} -{" "}
                      {new Date(task.lastCheckAt).toLocaleString()}
                    </div>
                  )}
                </div>
              )}
            </div>

            <div className="modal-actions">
              <button
                className="btn btn-secondary"
                onClick={() => setShowCheckModal(false)}
              >
                Cancel
              </button>
              <button className="btn btn-primary" onClick={handleAddCheck}>
                Record Check-in
              </button>
            </div>
          </div>
        </div>
      )}

      {/* History Modal */}
      {showHistoryModal && (
        <div
          className="modal-overlay"
          onClick={() => setShowHistoryModal(false)}
        >
          <div
            className="modal-content modal-content--large"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="modal-header">
              <h2>Check-in History: {task.title}</h2>
              <button
                className="modal-close"
                onClick={() => setShowHistoryModal(false)}
              >
                ×
              </button>
            </div>

            <div className="modal-body">
              {loadingHistory ? (
                <div className="loading-history">Loading history...</div>
              ) : fullHistory.length > 0 ? (
                <div className="full-history-list">
                  {fullHistory.map((check, index) => (
                    <div key={check.id} className="history-item">
                      <div className="history-item__header">
                        <span className="history-item__number">
                          #{fullHistory.length - index}
                        </span>
                        <span className="history-item__name">
                          {check.checkedBy}
                        </span>
                        <span className="history-item__time">
                          {new Date(check.checkedAt).toLocaleString()}
                        </span>
                      </div>
                      {check.notes && (
                        <div className="history-item__notes">
                          <span className="notes-label">Notes:</span>
                          <span className="notes-text">"{check.notes}"</span>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="no-history">
                  <p>No check-in history available</p>
                </div>
              )}
            </div>

            <div className="modal-actions">
              <button
                className="btn btn-secondary"
                onClick={() => setShowHistoryModal(false)}
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

// Watchlist Task Row Component (for list view)
const WatchlistTaskRow: React.FC<{
  task: WatchlistTask;
  locations: Location[];
  tenantRoles: TenantRole[];
  onEdit: () => void;
  onDelete: () => void;
  onAddCheck: (taskId: string, checkData: Omit<WatchlistCheck, "id">) => void;
}> = ({ task, locations, tenantRoles, onEdit, onDelete, onAddCheck }) => {
  const [showCheckModal, setShowCheckModal] = useState(false);
  const [showHistoryModal, setShowHistoryModal] = useState(false);
  const [checkerName, setCheckerName] = useState("");
  const [checkNotes, setCheckNotes] = useState("");
  const [recentChecks, setRecentChecks] = useState<WatchlistCheck[]>([]);
  const [fullHistory, setFullHistory] = useState<WatchlistCheck[]>([]);
  const [loadingChecks, setLoadingChecks] = useState(false);
  const [loadingHistory, setLoadingHistory] = useState(false);

  // Calculate status based on last check and frequency
  const getTaskStatus = () => {
    if (!task.lastCheckAt) {
      return "overdue"; // Never checked
    }

    const lastCheck = new Date(task.lastCheckAt);
    const now = new Date();
    const minutesSinceLastCheck =
      (now.getTime() - lastCheck.getTime()) / (1000 * 60);

    if (minutesSinceLastCheck <= task.frequencyMinutes * 0.8) {
      return "good"; // Recently checked (within 80% of frequency)
    } else if (minutesSinceLastCheck <= task.frequencyMinutes) {
      return "due-soon"; // Due soon (80-100% of frequency elapsed)
    } else {
      return "overdue"; // Overdue
    }
  };

  const status = getTaskStatus();
  const location = locations.find((l) => l.id === task.locationId);
  const role = tenantRoles.find(
    (r) => r.name.toLowerCase() === task.requiredRole
  );

  // Fetch recent checks when modal opens
  const fetchRecentChecks = async () => {
    if (!task.lastCheckAt) return; // No checks to fetch

    setLoadingChecks(true);
    try {
      const checks = await getWatchlistChecks(task.id, 3);
      setRecentChecks(checks);
    } catch (error) {
      console.error("Error fetching recent checks:", error);
    } finally {
      setLoadingChecks(false);
    }
  };

  // Fetch full history for the history modal
  const fetchFullHistory = async () => {
    if (!task.lastCheckAt) return; // No checks to fetch

    setLoadingHistory(true);
    try {
      const checks = await getWatchlistChecks(task.id, 20);
      setFullHistory(checks);
    } catch (error) {
      console.error("Error fetching full history:", error);
    } finally {
      setLoadingHistory(false);
    }
  };

  // Fetch checks when component mounts and when modal opens
  React.useEffect(() => {
    if (task.lastCheckAt) {
      fetchRecentChecks();
    }
  }, [task.lastCheckAt]);

  React.useEffect(() => {
    if (showCheckModal) {
      fetchRecentChecks();
    }
  }, [showCheckModal]);

  const handleAddCheck = () => {
    if (!checkerName.trim()) {
      alert("Please enter your name");
      return;
    }

    const checkData: Omit<WatchlistCheck, "id"> = {
      watchlistTaskId: task.id,
      tenantId: task.tenantId,
      checkedBy: checkerName.trim(),
      checkedAt: new Date().toISOString(),
      notes: checkNotes.trim() || undefined,
    };

    onAddCheck(task.id, checkData);
    setCheckerName("");
    setCheckNotes("");
    setShowCheckModal(false);
  };

  const getLastCheckInfo = () => {
    if (!task.lastCheckAt || !task.lastCheckBy) {
      return "Never checked";
    }
    return `${task.lastCheckBy} - ${new Date(
      task.lastCheckAt
    ).toLocaleString()}`;
  };

  return (
    <>
      <div className={`watchlist-row watchlist-row--${status}`}>
        <div className="watchlist-row__main">
          <div className="watchlist-row__title">
            <h4>{task.title}</h4>
            <div className="watchlist-row__meta">
              <span className="location-name">{location?.name}</span>
              <span className="role-name">
                {role?.name || task.requiredRole}
              </span>
              <span className="frequency">
                Every {Math.floor(task.frequencyMinutes / 60)}h{" "}
                {task.frequencyMinutes % 60}m
              </span>
            </div>
          </div>
          {task.description && (
            <div className="watchlist-row__description">
              <p>{task.description}</p>
            </div>
          )}
        </div>

        <div className="watchlist-row__status">
          <div className={`status-indicator status-indicator--${status}`}>
            {status === "good" && "✅ Recently Checked"}
            {status === "due-soon" && "⚠️ Due Soon"}
            {status === "overdue" && "🔴 Overdue"}
          </div>
          <div className="last-check">{getLastCheckInfo()}</div>
        </div>

        <div className="watchlist-row__actions">
          <button
            className="btn btn-sm btn-primary"
            onClick={() => setShowCheckModal(true)}
          >
            ✓ Check In
          </button>
          {task.lastCheckAt && (
            <button
              className="btn btn-sm btn-outline"
              onClick={() => {
                fetchFullHistory();
                setShowHistoryModal(true);
              }}
              title="View full history"
            >
              📋 History
            </button>
          )}
          <button className="btn btn-sm btn-secondary" onClick={onEdit}>
            Edit
          </button>
          <button className="btn btn-sm btn-danger" onClick={onDelete}>
            Delete
          </button>
        </div>
      </div>

      {/* Check-in Modal */}
      {showCheckModal && (
        <div className="modal-overlay" onClick={() => setShowCheckModal(false)}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h2>Check In: {task.title}</h2>
              <button
                className="modal-close"
                onClick={() => setShowCheckModal(false)}
              >
                ×
              </button>
            </div>

            <div className="modal-body">
              <div className="form-group">
                <label htmlFor="checker-name-row">Your Name *</label>
                <input
                  type="text"
                  id="checker-name-row"
                  value={checkerName}
                  onChange={(e) => setCheckerName(e.target.value)}
                  placeholder="Enter your name"
                  autoFocus
                />
              </div>

              <div className="form-group">
                <label htmlFor="check-notes-row">Notes (optional)</label>
                <textarea
                  id="check-notes-row"
                  value={checkNotes}
                  onChange={(e) => setCheckNotes(e.target.value)}
                  placeholder="Any observations or notes..."
                  rows={3}
                />
              </div>

              {/* Recent Check-ins Section */}
              {task.lastCheckAt && (
                <div className="form-group">
                  <label>Recent Check-ins</label>
                  {loadingChecks ? (
                    <div className="loading-checks">
                      Loading recent check-ins...
                    </div>
                  ) : recentChecks.length > 0 ? (
                    <div className="recent-checks-list">
                      {recentChecks.map((check) => (
                        <div key={check.id} className="recent-check-item">
                          <div className="check-header">
                            <span className="check-name">
                              {check.checkedBy}
                            </span>
                            <span className="check-time">
                              {new Date(check.checkedAt).toLocaleString()}
                            </span>
                          </div>
                          {check.notes && (
                            <div className="check-notes">"{check.notes}"</div>
                          )}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="no-recent-checks">
                      Last check: {task.lastCheckBy} -{" "}
                      {new Date(task.lastCheckAt).toLocaleString()}
                    </div>
                  )}
                </div>
              )}
            </div>

            <div className="modal-actions">
              <button
                className="btn btn-secondary"
                onClick={() => setShowCheckModal(false)}
              >
                Cancel
              </button>
              <button className="btn btn-primary" onClick={handleAddCheck}>
                Record Check-in
              </button>
            </div>
          </div>
        </div>
      )}

      {/* History Modal */}
      {showHistoryModal && (
        <div
          className="modal-overlay"
          onClick={() => setShowHistoryModal(false)}
        >
          <div
            className="modal-content modal-content--large"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="modal-header">
              <h2>Check-in History: {task.title}</h2>
              <button
                className="modal-close"
                onClick={() => setShowHistoryModal(false)}
              >
                ×
              </button>
            </div>

            <div className="modal-body">
              {loadingHistory ? (
                <div className="loading-history">Loading history...</div>
              ) : fullHistory.length > 0 ? (
                <div className="full-history-list">
                  {fullHistory.map((check, index) => (
                    <div key={check.id} className="history-item">
                      <div className="history-item__header">
                        <span className="history-item__number">
                          #{fullHistory.length - index}
                        </span>
                        <span className="history-item__name">
                          {check.checkedBy}
                        </span>
                        <span className="history-item__time">
                          {new Date(check.checkedAt).toLocaleString()}
                        </span>
                      </div>
                      {check.notes && (
                        <div className="history-item__notes">
                          <span className="notes-label">Notes:</span>
                          <span className="notes-text">"{check.notes}"</span>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="no-history">
                  <p>No check-in history available</p>
                </div>
              )}
            </div>

            <div className="modal-actions">
              <button
                className="btn btn-secondary"
                onClick={() => setShowHistoryModal(false)}
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default OwnerDashboard;
