/* Owner Dashboard - Business Management Interface */
.owner-dashboard {
  min-height: 100vh;
  background: #f8fafc;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  display: flex;
  flex-direction: column;
}

/* Sidebar Layout */
.owner-dashboard__body {
  display: flex;
  flex: 1;
  min-height: 0;
}

.owner-dashboard__sidebar {
  width: 280px;
  background: white;
  border-right: 2px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 10;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.owner-dashboard__sidebar.collapsed {
  width: 70px;
}

.sidebar-header {
  padding: 1.5rem 1rem 1rem 1rem;
  border-bottom: 2px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  min-height: 70px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  position: relative;
}

.sidebar-toggle {
  background: #0a2d69;
  border: none;
  color: white;
  font-size: 0.9rem;
  cursor: pointer;
  padding: 0.6rem;
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(10, 45, 105, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 36px;
  height: 36px;
  position: relative;
  overflow: hidden;
}

.sidebar-toggle:hover {
  background: #1e4a8c;
  transform: translateY(-1px) scale(1.05);
  box-shadow: 0 4px 12px rgba(10, 45, 105, 0.25);
}

.sidebar-toggle:active {
  transform: translateY(0) scale(0.98);
  transition: all 0.1s ease;
}

.sidebar-toggle::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.3s ease, height 0.3s ease;
}

.sidebar-toggle:hover::before {
  width: 100%;
  height: 100%;
}

.toggle-arrow {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1;
  position: relative;
}

.toggle-arrow.collapsed {
  transform: rotate(180deg);
}

.sidebar-title {
  font-weight: 700;
  font-size: 1rem;
  color: #0a2d69;
  margin-left: 1rem;
  opacity: 1;
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  white-space: nowrap;
}

.owner-dashboard__sidebar.collapsed .sidebar-title {
  opacity: 0;
  pointer-events: none;
}

.sidebar-nav {
  flex: 1;
  padding: 1.5rem 0;
  overflow-y: auto;
}

.nav-section {
  margin-bottom: 1.5rem;
  opacity: 1;
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.owner-dashboard__sidebar.collapsed .nav-section {
  margin-bottom: 0.5rem;
}

.nav-section-title {
  padding: 0 1rem 0.75rem 1rem;
  font-size: 0.75rem;
  font-weight: 700;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-bottom: 1px solid #f3f4f6;
  margin-bottom: 0.75rem;
  opacity: 1;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  white-space: nowrap;
}

.owner-dashboard__sidebar.collapsed .nav-section-title {
  opacity: 0;
  height: 0;
  padding: 0;
  margin: 0;
  border: none;
  overflow: hidden;
}

.sidebar-nav-btn {
  width: 100%;
  background: none;
  border: none;
  color: #374151;
  padding: 0.875rem 1rem;
  text-align: left;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.9rem;
  font-weight: 500;
  position: relative;
  border-radius: 0;
  margin: 0 0.5rem;
  border-radius: 8px;
}

.sidebar-nav-btn:hover {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  color: #0a2d69;
  transform: translateX(4px);
  box-shadow: 0 2px 8px rgba(10, 45, 105, 0.1);
}

.sidebar-nav-btn.active {
  background: linear-gradient(135deg, #0a2d69 0%, #1e4a8c 100%);
  color: white;
  transform: translateX(4px);
  box-shadow: 0 4px 12px rgba(10, 45, 105, 0.25);
}

.sidebar-nav-btn.active::before {
  content: "";
  position: absolute;
  left: -0.5rem;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 60%;
  background: #3b82f6;
  border-radius: 0 2px 2px 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-nav-btn.coming-soon {
  color: #9ca3af;
  cursor: not-allowed;
  opacity: 0.7;
}

.sidebar-nav-btn.coming-soon:hover {
  background: none;
  transform: none;
  color: #9ca3af;
  box-shadow: none;
}

.nav-icon {
  font-size: 1.1rem;
  min-width: 20px;
  text-align: center;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-nav-btn:hover .nav-icon {
  transform: scale(1.1);
}

.sidebar-nav-btn.active .nav-icon {
  transform: scale(1.05);
}

.nav-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
  opacity: 1;
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.owner-dashboard__sidebar.collapsed .nav-text {
  opacity: 0;
  width: 0;
  overflow: hidden;
}

.nav-badge {
  background: #f3f4f6;
  color: #6b7280;
  padding: 0.125rem 0.5rem;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 600;
  opacity: 1;
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  white-space: nowrap;
}

.owner-dashboard__sidebar.collapsed .nav-badge {
  opacity: 0;
  width: 0;
  padding: 0;
  overflow: hidden;
}

.nav-badge.coming-soon {
  background: #fef3c7;
  color: #d97706;
}

/* Header */
.owner-dashboard__header {
  background: linear-gradient(135deg, #0a2d69 0%, #1e4a8c 100%);
  color: white;
  padding: 2rem;
  margin-bottom: 2rem;
}

.owner-dashboard__header-content {
  max-width: 1200px;
  margin: 0 auto;
}

.owner-dashboard__header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
}

.owner-dashboard__header p {
  font-size: 1.1rem;
  color: #e8f4f8;
  margin: 0 0 1rem 0;
}

/* Header Info Styling */
.header-info {
  margin-top: 0.5rem;
}

.user-info {
  display: flex;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.user-badge,
.tenant-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
  color: #ffffff;
  backdrop-filter: blur(10px);
}

.user-badge {
  background: rgba(34, 197, 94, 0.2);
  border-color: rgba(34, 197, 94, 0.3);
}

.tenant-badge {
  background: rgba(59, 130, 246, 0.2);
  border-color: rgba(59, 130, 246, 0.3);
}

.dev-info {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.7);
  font-family: "Monaco", "Menlo", monospace;
}

/* Navigation */
.owner-dashboard__nav {
  max-width: 1200px;
  margin: 0 auto 2rem auto;
  padding: 0 2rem;
  display: flex;
  gap: 1rem;
  border-bottom: 2px solid #e5e7eb;
  overflow-x: auto;
}

.owner-dashboard__nav-btn {
  background: none;
  border: none;
  padding: 1rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  color: #6b7280;
  cursor: pointer;
  border-bottom: 3px solid transparent;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.owner-dashboard__nav-btn:hover {
  color: #0a2d69;
  background: #f3f4f6;
}

.owner-dashboard__nav-btn.active {
  color: #0a2d69;
  border-bottom-color: #0a2d69;
}

/* Content */
.owner-dashboard__content {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
  background-color: #f8fafc;
  max-width: none;
  margin: 0;
}

/* Loading */
.owner-dashboard__loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 50vh;
  color: #6b7280;
}

/* Common Button Styles */
.btn {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
}

.btn--primary {
  background: #0a2d69;
  color: white;
}

.btn--primary:hover {
  background: #1e4a8c;
}

.btn--secondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn--secondary:hover {
  background: #e5e7eb;
}

.btn--danger {
  background: #ef4444;
  color: white;
}

.btn--danger:hover {
  background: #dc2626;
}

.btn--disabled {
  background-color: #e2e8f0;
  color: #a0aec0;
  cursor: not-allowed;
  border: 1px solid #e2e8f0;
}

.btn--disabled:hover {
  background-color: #e2e8f0;
  transform: none;
}

.location-limit-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.limit-message {
  font-size: 12px;
  color: #e53e3e;
  text-align: center;
  margin: 0;
  font-weight: 500;
}

.btn--small {
  padding: 0.5rem 1rem;
  font-size: 0.8rem;
}

/* Tab Headers */
.tab-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.tab-header h2 {
  font-size: 1.8rem;
  font-weight: 700;
  color: #111827;
  margin: 0;
}

/* Overview Tab */
.overview-tab__stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  gap: 1rem;
}

.stat-card__icon {
  font-size: 2.5rem;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f3f4f6;
  border-radius: 12px;
}

.stat-card__content h3 {
  font-size: 2rem;
  font-weight: 700;
  color: #0a2d69;
  margin: 0 0 0.25rem 0;
}

.stat-card__content p {
  font-size: 0.9rem;
  color: #6b7280;
  margin: 0;
}

.overview-tab__quick-actions h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #111827;
  margin: 0 0 1rem 0;
}

.quick-actions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.quick-action-btn {
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  text-align: center;
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
}

.quick-action-btn:hover {
  border-color: #0a2d69;
  background: #f8fafc;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(10, 45, 105, 0.1);
}

.quick-action-btn span {
  font-size: 2rem;
}

/* Locations Tab */
.locations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

.location-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease;
}

.location-card:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.location-card__header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.location-card__header h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
  margin: 0;
  flex: 1;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-badge.active {
  background: #d1fae5;
  color: #065f46;
}

.status-badge.inactive {
  background: #fee2e2;
  color: #991b1b;
}

.location-card__address {
  color: #6b7280;
  font-size: 0.9rem;
  margin: 0 0 1.5rem 0;
}

.location-card__actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

/* Routines Tab */
.routines-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

.routine-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease;
}

.routine-card:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.routine-card__header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.routine-card__header h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
  margin: 0;
  flex: 1;
}

.task-count {
  background: #f3f4f6;
  color: #374151;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
}

.routine-card__description {
  color: #6b7280;
  font-size: 0.9rem;
  margin: 0 0 1rem 0;
  line-height: 1.5;
}

.routine-card__stats {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
}

.routine-stat {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.routine-stat__label {
  font-size: 0.8rem;
  color: #6b7280;
}

.routine-stat__value {
  font-size: 0.8rem;
  font-weight: 600;
  color: #111827;
  background: #f3f4f6;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.routine-card__actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

/* Tasks Tab */
.tasks-table {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.table-header {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr 1.5fr;
  gap: 1rem;
  padding: 1rem 1.5rem;
  background: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
  font-weight: 600;
  color: #374151;
  font-size: 0.9rem;
}

.table-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr 1.5fr;
  gap: 1rem;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #f3f4f6;
  align-items: center;
}

.table-row:last-child {
  border-bottom: none;
}

.table-row:hover {
  background: #f8fafc;
}

.table-col {
  display: flex;
  align-items: center;
}

.task-info h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #111827;
  margin: 0 0 0.25rem 0;
}

.task-info p {
  font-size: 0.8rem;
  color: #6b7280;
  margin: 0;
  line-height: 1.4;
}

.routine-badge {
  background: #dbeafe;
  color: #1d4ed8;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
}

.routine-badge.no-routine {
  background: #f3f4f6;
  color: #6b7280;
  font-style: italic;
}

.priority-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.priority-high {
  background: #fee2e2;
  color: #991b1b;
}

.priority-medium {
  background: #fef3c7;
  color: #92400e;
}

.priority-low {
  background: #d1fae5;
  color: #065f46;
}

.role-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
}

.role-badge.keyholder {
  background: #f3e8ff;
  color: #7c3aed;
}

.role-badge.any {
  background: #f3f4f6;
  color: #374151;
}

.task-actions {
  display: flex;
  gap: 0.5rem;
}

/* Today's Tasks Tab */
.daily-tasks-tab {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.task-filters {
  display: flex;
  gap: 2rem;
  margin-bottom: 2rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filter-group label {
  font-size: 0.9rem;
  font-weight: 600;
  color: #374151;
}

.filter-select {
  padding: 0.75rem 1rem;
  padding-right: 2.5rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  font-size: 0.9rem;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 150px;
  font-weight: 500;
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 1rem;
}

.filter-select:hover {
  border-color: #0a2d69;
  box-shadow: 0 2px 8px rgba(10, 45, 105, 0.1);
}

.filter-select:focus {
  outline: none;
  border-color: #0a2d69;
  box-shadow: 0 0 0 3px rgba(10, 45, 105, 0.1);
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%230a2d69' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
}

.task-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.summary-card {
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1.5rem;
}

.summary-card h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #111827;
  margin: 0 0 1rem 0;
}

.summary-stats {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.summary-stats .stat {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  color: white;
}

.summary-stats .stat.pending {
  background: #f59e0b;
}

.summary-stats .stat.completed {
  background: #10b981;
}

.summary-stats .stat.expired {
  background: #ef4444;
}

.task-section {
  margin-bottom: 3rem;
}

.task-section h3 {
  font-size: 1.3rem;
  font-weight: 600;
  color: #111827;
  margin: 0 0 1rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #e5e7eb;
}

.tasks-table {
  background: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.tasks-table .table-header {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr 1.5fr;
  gap: 1rem;
  padding: 1rem 1.5rem;
  background: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
  font-weight: 600;
  color: #374151;
  font-size: 0.9rem;
}

.tasks-table .table-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr 1.5fr;
  gap: 1rem;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #f3f4f6;
  align-items: center;
}

.tasks-table .table-row:last-child {
  border-bottom: none;
}

.tasks-table .table-row:hover {
  background: #f8fafc;
}

.location-badge {
  background: #e0e7ff;
  color: #3730a3;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
}

.priority-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  color: white;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  color: white;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.btn--success {
  background: #10b981;
  color: white;
}

.btn--success:hover {
  background: #059669;
}

.automation-status {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.automation-status .status-item.active {
  background: #d1fae5;
  color: #065f46;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
}

.kiosk-links {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.kiosk-link-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
}

.kiosk-link-item .location-name {
  font-weight: 600;
  color: #374151;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .table-header,
  .table-row {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .table-col {
    justify-content: space-between;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f3f4f6;
  }

  .table-col:last-child {
    border-bottom: none;
  }

  .table-header .table-col {
    display: none;
  }

  .table-row .table-col:before {
    content: attr(data-label);
    font-weight: 600;
    color: #6b7280;
    font-size: 0.8rem;
  }
}

@media (max-width: 768px) {
  .owner-dashboard__nav {
    padding: 0 1rem;
  }

  .owner-dashboard__content {
    padding: 0 1rem 2rem 1rem;
  }

  .overview-tab__stats {
    grid-template-columns: 1fr;
  }

  .quick-actions {
    grid-template-columns: repeat(2, 1fr);
  }

  .locations-grid,
  .routines-grid {
    grid-template-columns: 1fr;
  }

  .tab-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.modal {
  background: white;
  border-radius: 12px;
  max-width: 700px;
  width: 100%;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  flex-shrink: 0;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.25rem;
  color: #111827;
}

.modal-header-actions {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.btn--sm {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 600;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #6b7280;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.modal-close:hover {
  background: #f3f4f6;
}

.modal-content {
  padding: 1.5rem 2rem;
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

.modal-actions {
  display: flex;
  gap: 1rem;
  padding: 1rem 1.5rem;
  border-top: 1px solid #e5e7eb;
  justify-content: flex-end;
}

/* Form Styles */
.form-field {
  margin-bottom: 1.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.form-field label {
  display: block;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.25rem;
  font-size: 0.875rem;
}

.form-field input,
.form-field select,
.form-field textarea {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e5e7eb;
  border-radius: 6px;
  font-size: 1rem;
  transition: all 0.2s;
  font-family: inherit;
  background: white;
}

.form-field input:focus,
.form-field select:focus,
.form-field textarea:focus {
  outline: none;
  border-color: #0a2d69;
  box-shadow: 0 0 0 3px rgba(10, 45, 105, 0.1);
}

.form-field input:hover,
.form-field select:hover,
.form-field textarea:hover {
  border-color: #9ca3af;
}

/* Enhanced Select Styling */
.form-field select {
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 1rem;
  padding-right: 2.5rem;
  cursor: pointer;
}

.form-field select:focus {
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%230a2d69' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
}

.form-field textarea {
  resize: vertical;
  min-height: 80px;
}

/* Custom Checkbox Styling */
.checkbox-label {
  display: flex !important;
  align-items: center;
  gap: 0.75rem;
  font-weight: 400 !important;
  cursor: pointer;
  margin: 0 !important;
  position: relative;
  margin-top: 0.25rem !important;
}

.checkbox-label input[type="checkbox"] {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.checkbox-custom {
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid #e5e7eb;
  border-radius: 4px;
  background: white;
  transition: all 0.2s;
  flex-shrink: 0;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.checkbox-label:hover .checkbox-custom {
  border-color: #9ca3af;
}

.checkbox-label input[type="checkbox"]:checked + .checkbox-custom {
  background: #0a2d69;
  border-color: #0a2d69;
}

.checkbox-label input[type="checkbox"]:checked + .checkbox-custom::after {
  content: "✓";
  color: white;
  font-size: 0.875rem;
  font-weight: bold;
  line-height: 1;
}

.checkbox-label input[type="checkbox"]:focus + .checkbox-custom {
  box-shadow: 0 0 0 3px rgba(10, 45, 105, 0.1);
  outline: none;
}

/* Priority Radio Buttons */
.priority-radio-group {
  display: flex;
  gap: 0.5rem;
  margin-top: 0.25rem;
}

.priority-radio {
  position: relative;
  cursor: pointer;
  margin: 0 !important;
}

.priority-radio input[type="radio"] {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.priority-radio-label {
  display: inline-block;
  padding: 0.5rem 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 600;
  transition: all 0.2s;
  cursor: pointer;
  min-width: 60px;
  text-align: center;
}

.priority-radio input[type="radio"]:checked + .priority-radio-label {
  border-color: currentColor;
  background: currentColor;
  color: white !important;
}

.priority-radio input[type="radio"]:focus + .priority-radio-label {
  outline: 2px solid #0a2d69;
  outline-offset: 2px;
}

.priority-low {
  color: #059669;
}

.priority-radio input[type="radio"]:checked + .priority-low {
  background: #059669 !important;
  border-color: #059669 !important;
}

.priority-medium {
  color: #d97706;
}

.priority-radio input[type="radio"]:checked + .priority-medium {
  background: #d97706 !important;
  border-color: #d97706 !important;
}

.priority-high {
  color: #dc2626;
}

.priority-radio input[type="radio"]:checked + .priority-high {
  background: #dc2626 !important;
  border-color: #dc2626 !important;
}

.priority-radio:hover .priority-radio-label {
  border-color: currentColor;
  background: rgba(0, 0, 0, 0.05);
}

.priority-radio input[type="radio"]:checked + .priority-radio-label:hover {
  background: currentColor;
}

/* AdminBuddy V2 Workflow Styles */
.overview-workflow {
  margin-top: 2rem;
  padding: 2rem;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.overview-workflow h3 {
  margin: 0 0 1.5rem 0;
  color: #0a2d69;
  text-align: center;
}

.workflow-steps {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.workflow-step {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  border: 2px solid #e2e8f0;
  text-align: center;
  position: relative;
  transition: all 0.2s;
}

.workflow-step:hover {
  border-color: #0a2d69;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(10, 45, 105, 0.1);
}

.step-number {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  background: #0a2d69;
  color: white;
  border-radius: 50%;
  font-weight: bold;
  font-size: 0.875rem;
  margin-bottom: 1rem;
}

.workflow-step h4 {
  margin: 0 0 0.5rem 0;
  color: #1e293b;
  font-size: 1rem;
}

.workflow-step p {
  margin: 0;
  color: #64748b;
  font-size: 0.875rem;
  line-height: 1.4;
}

@media (max-width: 768px) {
  .modal {
    margin: 0.5rem;
    max-width: none;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .modal-header-actions {
    flex-direction: column;
    gap: 0.5rem;
  }

  .priority-radio-group {
    flex-direction: column;
    gap: 0.25rem;
  }

  .priority-radio-label {
    text-align: left;
    min-width: auto;
  }

  .workflow-steps {
    grid-template-columns: 1fr;
  }

  .overview-workflow {
    padding: 1rem;
  }
}

/* Empty State Styles */
.empty-state {
  grid-column: 1 / -1;
  text-align: center;
  padding: 3rem 2rem;
  background: #f8fafc;
  border: 2px dashed #e2e8f0;
  border-radius: 12px;
  color: #64748b;
}

.empty-state h3 {
  margin: 0 0 0.5rem 0;
  color: #475569;
  font-size: 1.25rem;
}

.empty-state p {
  margin: 0 0 1.5rem 0;
  font-size: 1rem;
  line-height: 1.5;
}

.empty-state .btn {
  margin: 0 auto;
}

.empty-state__help {
  text-align: left;
  max-width: 400px;
  margin: 1.5rem auto;
  padding: 1rem;
  background: white;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.empty-state__help h4 {
  margin: 0 0 0.75rem 0;
  color: #475569;
  font-size: 1rem;
  font-weight: 600;
  text-align: center;
}

.empty-state__help ul {
  margin: 0;
  padding-left: 1.25rem;
  list-style-type: disc;
}

.empty-state__help li {
  margin: 0.5rem 0;
  color: #64748b;
  line-height: 1.4;
}

/* Routine Card Styles */
.routines-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.routine-card {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.2s;
}

.routine-card:hover {
  border-color: #0a2d69;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(10, 45, 105, 0.1);
}

.routine-card__header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.routine-card__header h3 {
  margin: 0;
  color: #1e293b;
  font-size: 1.25rem;
  font-weight: 600;
}

.routine-card__description {
  color: #64748b;
  font-size: 0.875rem;
  line-height: 1.5;
  margin: 0 0 1rem 0;
}

.routine-card__stats {
  margin-bottom: 1.5rem;
}

.task-count {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: #f1f5f9;
  color: #475569;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
}

.routine-card__actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.routine-card__actions .btn {
  flex: 1;
  min-width: 80px;
}

/* Routine Tasks Tab Styles */
.tab-header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.tab-header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.routine-selector {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.routine-badge {
  background: #0a2d69;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
}

.routine-filter {
  padding: 0.75rem 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 0.9rem;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 180px;
  font-weight: 500;
  color: #374151;
}

.routine-filter:hover {
  border-color: #0a2d69;
  box-shadow: 0 2px 8px rgba(10, 45, 105, 0.1);
}

.routine-filter:focus {
  outline: none;
  border-color: #0a2d69;
  box-shadow: 0 0 0 3px rgba(10, 45, 105, 0.1);
}

.order-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  background: #f1f5f9;
  color: #475569;
  border-radius: 50%;
  font-weight: 600;
  font-size: 0.875rem;
}

.expiry-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
}

.expiry-next_report {
  background: #fef3c7;
  color: #92400e;
}

.expiry-set_datetime {
  background: #fecaca;
  color: #991b1b;
}

.expiry-no_expiry {
  background: #d1fae5;
  color: #065f46;
}

/* Schedule Tab Styles */
.days-badge {
  background: #e0e7ff;
  color: #3730a3;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.cadence-badge {
  background: #f3e8ff;
  color: #7c3aed;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: capitalize;
}

.location-info h4 {
  margin: 0 0 0.25rem 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: #1e293b;
}

.location-info p {
  margin: 0;
  font-size: 0.75rem;
  color: #64748b;
}

.schedule-actions {
  display: flex;
  gap: 0.5rem;
}

/* Days of Week Selector */
.days-of-week-selector {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  margin-top: 0.5rem;
}

.day-checkbox {
  display: flex !important;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
  padding: 0.5rem;
  border: 2px solid #e5e7eb;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  min-width: 60px;
  background: white;
  position: relative;
}

.day-checkbox:hover {
  border-color: #9ca3af;
}

/* Hide the actual checkbox */
.day-checkbox input[type="checkbox"] {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

/* Hide the custom checkbox when checked */
.day-checkbox input[type="checkbox"]:checked + .checkbox-custom {
  display: none;
}

/* Style the entire day box when checked */
.day-checkbox:has(input[type="checkbox"]:checked) {
  background: #0a2d69;
  border-color: #0a2d69;
}

.day-checkbox:has(input[type="checkbox"]:checked) .day-name {
  color: white;
  font-weight: 600;
}

.day-name {
  font-size: 0.75rem;
  font-weight: 500;
  color: #64748b;
  transition: all 0.2s;
}

.field-error {
  color: #dc2626;
  font-size: 0.75rem;
  margin: 0.25rem 0 0 0;
  font-weight: 500;
}

/* Location-Grouped Schedules Styles */
.schedules-by-location {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.location-schedule-group {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: all 0.2s ease;
}

.location-schedule-group:hover {
  border-color: #cbd5e1;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.location-schedule-group--empty {
  border-style: dashed;
  border-color: #cbd5e1;
  background: #f8fafc;
}

.location-header {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.location-info h3 {
  margin: 0 0 0.25rem 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #0f172a;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.location-info h3::before {
  content: "🏢";
  font-size: 1rem;
}

.location-info p {
  margin: 0;
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 400;
}

.location-stats {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.schedule-count {
  background: #0a2d69;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
  letter-spacing: 0.025em;
}

.location-schedule-group--empty .schedule-count {
  background: #94a3b8;
  color: white;
}

.location-schedules {
  padding: 0;
}

.location-schedules .schedules-table {
  border: none;
  border-radius: 0;
  box-shadow: none;
  background: transparent;
}

.location-schedules .table-header {
  background: #f8fafc;
  border-bottom: 2px solid #e2e8f0;
  padding: 1rem 2rem;
  grid-template-columns: 240px 140px 120px 100px 1fr;
  font-weight: 600;
  color: #475569;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.location-schedules .table-row {
  padding: 1.25rem 2rem;
  border-bottom: 1px solid #f1f5f9;
  grid-template-columns: 240px 140px 120px 100px 1fr;
  align-items: flex-start;
  transition: background-color 0.15s ease;
  min-height: 4rem;
}

.location-schedules .table-row:hover {
  background: #f8fafc;
}

.location-schedules .table-row:last-child {
  border-bottom: none;
}

.routine-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  align-items: flex-start;
  width: 100%;
  max-width: 220px;
  overflow: hidden;
}

.routine-description {
  font-size: 0.75rem;
  color: #94a3b8;
  margin: 0;
  line-height: 1.3;
  font-style: italic;
  width: 100%;
  max-width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.time-badge {
  background: #f1f5f9;
  color: #475569;
  padding: 0.375rem 0.75rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 600;
  font-family: "SF Mono", "Monaco", "Inconsolata", "Roboto Mono", monospace;
}

.empty-location-schedules {
  padding: 2rem;
  text-align: center;
  color: #64748b;
  background: #f8fafc;
}

.empty-location-schedules p {
  margin: 0 0 1rem 0;
  font-size: 0.875rem;
  line-height: 1.5;
}

/* Enhanced badges for better visual hierarchy */
.routine-badge {
  background: #dbeafe;
  color: #1e40af;
  padding: 0.25rem 0.625rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
  display: inline-block;
  width: 100%;
  max-width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  vertical-align: top;
  box-sizing: border-box;
}

.days-badge {
  background: #ecfdf5;
  color: #059669;
  padding: 0.375rem 0.75rem;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 500;
  border: 1px solid #a7f3d0;
}

.status-badge.active {
  background: #dcfce7;
  color: #166534;
  border: 1px solid #bbf7d0;
}

.status-badge.inactive {
  background: #fef2f2;
  color: #991b1b;
  border: 1px solid #fecaca;
}

/* Responsive adjustments for location groups */
@media (max-width: 1024px) {
  .location-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
    padding: 1.25rem 1.5rem;
  }

  .location-schedules .table-header,
  .location-schedules .table-row {
    grid-template-columns: 1fr;
    gap: 0.5rem;
    padding: 1rem 1.5rem;
  }

  .location-schedules .table-header {
    display: none;
  }

  .location-schedules .table-row {
    display: flex;
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
    padding: 1.5rem;
    border: 1px solid #f1f5f9;
    border-radius: 8px;
    margin: 0.5rem 1rem;
    background: white;
  }

  .location-schedules .table-col {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f8fafc;
  }

  .location-schedules .table-col:last-child {
    border-bottom: none;
    justify-content: flex-start;
  }

  .location-schedules .table-col::before {
    content: attr(data-label);
    font-weight: 600;
    color: #64748b;
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }
}

@media (max-width: 768px) {
  .schedules-by-location {
    gap: 1.5rem;
  }

  .location-header {
    padding: 1rem 1.25rem;
  }

  .location-info h3 {
    font-size: 1.125rem;
  }

  .empty-location-schedules {
    padding: 1.5rem 1rem;
  }
}

/* Routine Tasks Tab - Grouped by Routine Styles */
.routine-tasks-by-routine {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.routine-task-group {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: all 0.2s ease;
}

.routine-task-group:hover {
  border-color: #cbd5e1;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.routine-task-group--empty {
  border-style: dashed;
  border-color: #cbd5e1;
  background: #f8fafc;
}

.routine-task-header {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.routine-info h3 {
  margin: 0 0 0.25rem 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #0f172a;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.routine-icon {
  font-size: 1rem;
}

.routine-info .routine-description {
  margin: 0;
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 400;
  line-height: 1.4;
}

.routine-stats {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.routine-stats .task-count {
  background: #0a2d69;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
  letter-spacing: 0.025em;
}

.routine-task-group--empty .routine-stats .task-count {
  background: #94a3b8;
  color: white;
}

.routine-tasks {
  padding: 0;
}

.tasks-list {
  display: flex;
  flex-direction: column;
  gap: 0;
}

.task-card {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.25rem 2rem;
  border-bottom: 1px solid #f1f5f9;
  transition: background-color 0.15s ease;
  min-height: 4rem;
}

.task-card:hover {
  background: #f8fafc;
}

.task-card:last-child {
  border-bottom: none;
}

.task-order {
  flex-shrink: 0;
  margin-top: 0.25rem;
}

.order-number {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  background: #f1f5f9;
  color: #475569;
  border-radius: 50%;
  font-weight: 600;
  font-size: 0.875rem;
  border: 2px solid #e2e8f0;
}

.task-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  min-width: 0;
}

.task-main {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.task-title {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: #0f172a;
  line-height: 1.3;
}

.task-description {
  margin: 0;
  font-size: 0.875rem;
  color: #64748b;
  line-height: 1.4;
}

.task-meta {
  display: flex;
  align-items: flex-start;
}

.task-badges {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  align-items: center;
}

.task-badges .priority-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.625rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.task-badges .priority-badge.priority-high {
  background: #fef2f2;
  color: #991b1b;
  border: 1px solid #fecaca;
}

.task-badges .priority-badge.priority-medium {
  background: #fef3c7;
  color: #92400e;
  border: 1px solid #fde68a;
}

.task-badges .priority-badge.priority-low {
  background: #dcfce7;
  color: #166534;
  border: 1px solid #bbf7d0;
}

.task-badges .role-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.625rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.task-badges .role-badge.keyholder {
  background: #fef3c7;
  color: #92400e;
  border: 1px solid #fde68a;
}

.task-badges .role-badge.manager {
  background: #e0e7ff;
  color: #3730a3;
  border: 1px solid #c7d2fe;
}

.task-badges .role-badge.any {
  background: #f1f5f9;
  color: #475569;
  border: 1px solid #e2e8f0;
}

.task-badges .time-badge {
  background: #f1f5f9;
  color: #475569;
  padding: 0.25rem 0.625rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
  font-family: "SF Mono", "Monaco", "Inconsolata", "Roboto Mono", monospace;
  border: 1px solid #e2e8f0;
}

.task-badges .expiry-badge {
  padding: 0.25rem 0.625rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  border: 1px solid;
}

.task-badges .expiry-badge.expiry-next_report {
  background: #fef3c7;
  color: #92400e;
  border-color: #fde68a;
}

.task-badges .expiry-badge.expiry-set_datetime {
  background: #fecaca;
  color: #991b1b;
  border-color: #fca5a5;
}

.task-badges .expiry-badge.expiry-no_expiry {
  background: #d1fae5;
  color: #065f46;
  border-color: #a7f3d0;
}

.task-actions {
  flex-shrink: 0;
  display: flex;
  gap: 0.5rem;
  margin-top: 0.25rem;
}

.task-actions .btn {
  padding: 0.5rem;
  min-width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  font-size: 0.875rem;
}

.empty-routine-tasks {
  padding: 2rem;
  text-align: center;
  color: #64748b;
  background: #f8fafc;
}

.empty-routine-tasks p {
  margin: 0 0 1rem 0;
  font-size: 0.875rem;
  line-height: 1.5;
}

/* Responsive adjustments for routine task groups */
@media (max-width: 1024px) {
  .routine-task-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
    padding: 1.25rem 1.5rem;
  }

  .task-card {
    flex-direction: column;
    gap: 1rem;
    padding: 1.5rem;
    align-items: stretch;
  }

  .task-order {
    align-self: flex-start;
    margin-top: 0;
  }

  .task-badges {
    flex-wrap: wrap;
    gap: 0.375rem;
  }

  .task-actions {
    align-self: flex-end;
    margin-top: 0;
  }
}

@media (max-width: 768px) {
  .routine-tasks-by-routine {
    gap: 1.5rem;
  }

  .routine-task-header {
    padding: 1rem 1.25rem;
  }

  .routine-info h3 {
    font-size: 1.125rem;
  }

  .task-card {
    padding: 1.25rem;
  }

  .empty-routine-tasks {
    padding: 1.5rem 1rem;
  }

  .task-badges {
    gap: 0.25rem;
  }

  .task-badges .priority-badge,
  .task-badges .role-badge,
  .task-badges .time-badge,
  .task-badges .expiry-badge {
    font-size: 0.7rem;
    padding: 0.2rem 0.5rem;
  }

  .toast {
    top: 10px;
    right: 10px;
    left: 10px;
    min-width: auto;
    max-width: none;
  }
}

/* Toast Notifications */
.toast {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10000;
  min-width: 300px;
  max-width: 500px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  animation: slideInRight 0.3s ease-out;
}

.toast--success {
  background: #10b981;
  color: white;
}

.toast--error {
  background: #ef4444;
  color: white;
}

.toast--info {
  background: #3b82f6;
  color: white;
}

.toast__content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.25rem;
  gap: 1rem;
}

.toast__message {
  flex: 1;
  font-weight: 500;
  line-height: 1.4;
}

.toast__close {
  background: none;
  border: none;
  color: inherit;
  font-size: 1.25rem;
  font-weight: bold;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.toast__close:hover {
  background: rgba(255, 255, 255, 0.2);
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Daily Tasks Tab - Grouped by Location Styles */
.tasks-by-location {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.location-task-group {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: all 0.2s ease;
}

.location-task-group:hover {
  border-color: #cbd5e1;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.location-task-group--empty {
  border-style: dashed;
  border-color: #cbd5e1;
  background: #f8fafc;
}

.location-task-header {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.location-icon {
  font-size: 1rem;
}

.location-info h3 {
  margin: 0 0 0.25rem 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #0f172a;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.location-address {
  margin: 0;
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 400;
  line-height: 1.4;
}

.location-stats {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.location-stats .task-count {
  background: #0a2d69;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
  letter-spacing: 0.025em;
}

.location-task-group--empty .location-stats .task-count {
  background: #94a3b8;
  color: white;
}

.task-breakdown {
  background: #f1f5f9;
  color: #475569;
  padding: 0.375rem 0.75rem;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 500;
  border: 1px solid #e2e8f0;
}

.location-tasks {
  padding: 0;
}

.task-type-section {
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #f1f5f9;
}

.task-type-section:last-child {
  border-bottom: none;
}

.task-type-header {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #475569;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.task-cards {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.daily-task-card,
.adhoc-task-card {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: #fafbfc;
  transition: all 0.15s ease;
}

.daily-task-card:hover,
.adhoc-task-card:hover {
  background: #f8fafc;
  border-color: #cbd5e1;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.adhoc-task-card {
  border-left: 3px solid #f59e0b;
  background: #fffbeb;
}

.adhoc-task-card:hover {
  background: #fef3c7;
}

.daily-task-card {
  border-left: 3px solid #3b82f6;
}

.task-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  min-width: 0;
}

.task-main {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.task-title {
  margin: 0;
  font-size: 0.95rem;
  font-weight: 600;
  color: #0f172a;
  line-height: 1.3;
}

.task-description {
  margin: 0;
  font-size: 0.85rem;
  color: #64748b;
  line-height: 1.4;
}

.task-meta {
  display: flex;
  align-items: flex-start;
}

.task-badges {
  display: flex;
  flex-wrap: wrap;
  gap: 0.375rem;
  align-items: center;
}

.task-badges .priority-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.task-badges .priority-badge.priority-high {
  background: #fef2f2;
  color: #991b1b;
  border: 1px solid #fecaca;
}

.task-badges .priority-badge.priority-medium {
  background: #fef3c7;
  color: #92400e;
  border: 1px solid #fde68a;
}

.task-badges .priority-badge.priority-low {
  background: #dcfce7;
  color: #166534;
  border: 1px solid #bbf7d0;
}

.task-badges .role-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: 500;
}

.task-badges .role-badge.keyholder {
  background: #fef3c7;
  color: #92400e;
  border: 1px solid #fde68a;
}

.task-badges .role-badge.manager {
  background: #e0e7ff;
  color: #3730a3;
  border: 1px solid #c7d2fe;
}

.task-badges .time-badge {
  background: #f1f5f9;
  color: #475569;
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: 600;
  font-family: "SF Mono", "Monaco", "Inconsolata", "Roboto Mono", monospace;
  border: 1px solid #e2e8f0;
}

.task-badges .status-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: 500;
  border: 1px solid;
}

.task-badges .status-badge.status-pending {
  background: #fef3c7;
  color: #92400e;
  border-color: #fde68a;
}

.task-badges .status-badge.status-completed {
  background: #dcfce7;
  color: #166534;
  border-color: #bbf7d0;
}

.task-badges .status-badge.status-expired {
  background: #fef2f2;
  color: #991b1b;
  border-color: #fecaca;
}

.task-actions {
  flex-shrink: 0;
  display: flex;
  gap: 0.375rem;
  margin-top: 0.125rem;
}

.task-actions .btn {
  padding: 0.375rem;
  min-width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  font-size: 0.8rem;
}

.empty-location-tasks {
  padding: 2rem;
  text-align: center;
  color: #64748b;
  background: #f8fafc;
}

.empty-location-tasks p {
  margin: 0 0 1rem 0;
  font-size: 0.875rem;
  line-height: 1.5;
}

/* Filter styling for daily tasks */
.location-filter,
.status-filter {
  padding: 0.75rem 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 0.9rem;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 160px;
  font-weight: 500;
  color: #374151;
}

.location-filter:hover,
.status-filter:hover {
  border-color: #0a2d69;
  box-shadow: 0 2px 8px rgba(10, 45, 105, 0.1);
}

.location-filter:focus,
.status-filter:focus {
  outline: none;
  border-color: #0a2d69;
  box-shadow: 0 0 0 3px rgba(10, 45, 105, 0.1);
}

/* Collapsible Section Styles */
.section-toggle {
  width: 100%;
  background: none;
  border: none;
  padding: 1.5rem;
  cursor: pointer;
  text-align: left;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 12px 12px 0 0;
  position: relative;
  overflow: hidden;
}

.section-toggle:hover {
  background: linear-gradient(
    135deg,
    rgba(10, 45, 105, 0.03) 0%,
    rgba(30, 74, 140, 0.05) 100%
  );
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(10, 45, 105, 0.08);
}

.section-toggle:active {
  transform: translateY(0);
  transition: all 0.1s ease;
}

.section-toggle::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #0a2d69 0%, #3b82f6 100%);
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.section-toggle:hover::before {
  transform: scaleX(1);
}

.toggle-icon {
  min-width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8fafc;
  border-radius: 6px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid #e5e7eb;
}

.section-toggle:hover .toggle-icon {
  background: #0a2d69;
  color: white;
  transform: scale(1.1);
  border-color: #0a2d69;
}

.section-arrow {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.section-arrow.collapsed {
  transform: rotate(-90deg);
}

.section-toggle .location-info {
  flex: 1;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.section-toggle:hover .location-info {
  transform: translateX(2px);
}

.section-toggle .location-stats {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.section-toggle:hover .location-stats {
  transform: translateX(-2px);
}

/* Location task group improvements */
.location-task-group {
  margin-bottom: 0.75rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.location-task-group:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
}

.location-task-header {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-bottom: 1px solid #e5e7eb;
  padding: 0;
  position: relative;
  display: flex;
  align-items: center;
}

.location-focus-btn {
  margin-right: 1rem;
  flex-shrink: 0;
}

.location-tasks {
  max-height: 1000px;
  overflow: hidden;
  transition: max-height 0.4s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.3s ease;
  opacity: 1;
}

.location-task-group .location-tasks.collapsed {
  max-height: 0;
  opacity: 0;
  transition: max-height 0.3s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.2s ease;
}

/* Collapsible sections for schedules and routine tasks */
.location-schedule-group {
  margin-bottom: 0.75rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.location-schedule-group:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
}

.location-schedule-group .location-header {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-bottom: 1px solid #e5e7eb;
  padding: 0;
  position: relative;
}

.location-schedules {
  max-height: 1000px;
  overflow: hidden;
  transition: max-height 0.4s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.3s ease;
  opacity: 1;
}

.location-schedule-group .location-schedules.collapsed {
  max-height: 0;
  opacity: 0;
  transition: max-height 0.3s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.2s ease;
}

.routine-task-group {
  margin-bottom: 0.75rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.routine-task-group:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
}

.routine-task-group .routine-task-header {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-bottom: 1px solid #e5e7eb;
  padding: 0;
  position: relative;
}

.routine-tasks {
  max-height: 1000px;
  overflow: hidden;
  transition: max-height 0.4s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.3s ease;
  opacity: 1;
}

.routine-task-group .routine-tasks.collapsed {
  max-height: 0;
  opacity: 0;
  transition: max-height 0.3s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.2s ease;
}

/* Enhanced location card kiosk section */
.location-card__kiosk {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 1px solid #0ea5e9;
  border-radius: 8px;
  padding: 1rem;
  margin: 1rem 0;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 1rem;
  flex-wrap: wrap;
}

.kiosk-info {
  flex: 1;
  min-width: 0; /* Allow text to wrap */
}

.kiosk-info h4 {
  margin: 0 0 0.25rem 0;
  color: #0369a1;
  font-size: 0.9rem;
  font-weight: 600;
}

.kiosk-info p {
  margin: 0 0 0.5rem 0;
  color: #64748b;
  font-size: 0.8rem;
  line-height: 1.4;
}

.kiosk-url {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid #cbd5e1;
  border-radius: 4px;
  padding: 0.25rem 0.5rem;
  display: inline-block;
  font-size: 0.75rem;
  color: #64748b;
  font-style: italic;
  max-width: 100%;
  word-break: break-all;
}

.btn--kiosk {
  background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
  border: none;
  color: white;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(14, 165, 233, 0.3);
  transition: all 0.2s ease;
  flex-shrink: 0;
  white-space: nowrap;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.85rem;
  align-self: flex-start;
}

.btn--kiosk:hover {
  background: linear-gradient(135deg, #0284c7 0%, #0369a1 100%);
  box-shadow: 0 4px 12px rgba(14, 165, 233, 0.4);
  transform: translateY(-1px);
}

/* Responsive adjustments for smaller screens */
@media (max-width: 768px) {
  .location-card__kiosk {
    flex-direction: column;
    align-items: stretch;
  }

  .btn--kiosk {
    align-self: center;
    width: fit-content;
  }
}

.location-selector {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.location-badge {
  background: #0a2d69;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
}

/* Responsive adjustments for daily task groups */
@media (max-width: 1024px) {
  .location-task-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
    padding: 1.25rem 1.5rem;
  }

  .location-stats {
    align-self: stretch;
    justify-content: space-between;
  }

  .task-type-section {
    padding: 1.25rem 1.5rem;
  }

  .daily-task-card,
  .adhoc-task-card {
    flex-direction: column;
    gap: 0.75rem;
    padding: 1rem;
    align-items: stretch;
  }

  .task-actions {
    align-self: flex-end;
    margin-top: 0;
  }
}

@media (max-width: 768px) {
  /* Sidebar responsive */
  .owner-dashboard__sidebar {
    width: 70px;
  }

  .sidebar-title {
    display: none;
  }

  .nav-text {
    display: none;
  }

  .nav-badge {
    display: none;
  }

  .nav-section-title {
    display: none;
  }

  .owner-dashboard__content {
    padding: 1.5rem;
  }

  .tasks-by-location {
    gap: 1.5rem;
  }

  .location-task-header {
    padding: 1rem 1.25rem;
  }

  .location-info h3 {
    font-size: 1.125rem;
  }

  .task-type-section {
    padding: 1rem;
  }

  .daily-task-card,
  .adhoc-task-card {
    padding: 0.875rem;
  }

  .empty-location-tasks {
    padding: 1.5rem 1rem;
  }

  .task-badges {
    gap: 0.25rem;
  }

  .task-badges .priority-badge,
  .task-badges .role-badge,
  .task-badges .time-badge,
  .task-badges .status-badge {
    font-size: 0.65rem;
    padding: 0.15rem 0.4rem;
  }

  .location-filter,
  .status-filter {
    min-width: 140px;
    font-size: 0.85rem;
  }
}

/* Roles Tab Styles */
.roles-tab {
  max-width: 1200px;
  margin: 0 auto;
}

.roles-content {
  margin-top: 2rem;
}

/* Tab Header Actions */
.tab-header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* View Toggle */
.view-toggle {
  display: flex;
  background: #f3f4f6;
  border-radius: 8px;
  padding: 0.25rem;
  border: 1px solid #e5e7eb;
}

.view-toggle-btn {
  background: none;
  border: none;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  color: #6b7280;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
}

.view-toggle-btn:hover {
  color: #374151;
  background: rgba(255, 255, 255, 0.5);
}

.view-toggle-btn.active {
  background: white;
  color: #0a2d69;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.roles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

.role-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease;
}

.role-card:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.role-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.role-name {
  font-size: 1.25rem;
  font-weight: 700;
  color: #111827;
  margin: 0;
  flex: 1;
}

.role-actions {
  display: flex;
  gap: 0.5rem;
}

.role-description {
  color: #6b7280;
  font-size: 0.9rem;
  line-height: 1.5;
  margin: 0 0 1.5rem 0;
}

.role-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 1rem;
  border-top: 1px solid #f3f4f6;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 16px;
  font-size: 0.75rem;
  font-weight: 600;
}

.status-badge.active {
  background: #dcfce7;
  color: #166534;
}

.status-badge.inactive {
  background: #fef2f2;
  color: #991b1b;
}

.role-created {
  font-size: 0.75rem;
  color: #9ca3af;
}

.role-usage {
  margin-bottom: 1.5rem;
}

.role-usage-text {
  color: #6b7280;
  font-size: 0.9rem;
  line-height: 1.5;
  margin: 0;
  font-style: italic;
}

/* Table View Styles */
.roles-table,
.locations-table,
.routines-table,
.schedules-table {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
}

/* Roles table grid */
.roles-table .table-header {
  display: grid;
  grid-template-columns: 2fr 3fr 1fr 1.5fr 1.5fr;
  background: #f8fafc;
  border-bottom: 2px solid #e5e7eb;
}

.roles-table .table-row {
  display: grid;
  grid-template-columns: 2fr 3fr 1fr 1.5fr 1.5fr;
  border-bottom: 1px solid #f3f4f6;
  transition: background-color 0.2s ease;
}

/* Locations table grid */
.locations-table .table-header {
  display: grid;
  grid-template-columns: 2fr 3fr 1fr 1.5fr 2fr;
  background: #f8fafc;
  border-bottom: 2px solid #e5e7eb;
}

.locations-table .table-row {
  display: grid;
  grid-template-columns: 2fr 3fr 1fr 1.5fr 2fr;
  border-bottom: 1px solid #f3f4f6;
  transition: background-color 0.2s ease;
}

/* Routines table grid */
.routines-table .table-header {
  display: grid;
  grid-template-columns: 2.5fr 3fr 1.5fr 1fr 2fr;
  background: #f8fafc;
  border-bottom: 2px solid #e5e7eb;
}

.routines-table .table-row {
  display: grid;
  grid-template-columns: 2.5fr 3fr 1.5fr 1fr 2fr;
  border-bottom: 1px solid #f3f4f6;
  transition: background-color 0.2s ease;
}

/* Schedules table grid */
.schedules-table .table-header {
  display: grid;
  grid-template-columns: 2fr 2fr 2fr 1.5fr 1fr 2fr;
  background: #f8fafc;
  border-bottom: 2px solid #e5e7eb;
}

.schedules-table .table-row {
  display: grid;
  grid-template-columns: 2fr 2fr 2fr 1.5fr 1fr 2fr;
  border-bottom: 1px solid #f3f4f6;
  transition: background-color 0.2s ease;
}

.table-row:hover {
  background: #f8fafc;
}

.table-row:last-child {
  border-bottom: none;
}

.table-cell {
  padding: 1rem;
  display: flex;
  align-items: center;
  font-size: 0.9rem;
}

.header-cell {
  font-weight: 600;
  color: #374151;
  font-size: 0.85rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.role-name-list {
  font-weight: 600;
  color: #111827;
}

.role-description-list {
  color: #6b7280;
  line-height: 1.4;
}

/* Fix routine task header layout */
.routine-task-header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.routine-task-header-content .section-toggle {
  flex: 1;
  min-width: 0; /* Allow flex item to shrink */
}

.focus-btn {
  margin-left: 1rem;
  flex-shrink: 0;
}

/* Fix action button spacing in table views */
.location-actions,
.role-actions {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.location-actions .btn,
.role-actions .btn {
  min-width: auto;
  padding: 0.5rem 0.75rem;
}

/* Ensure table cells have proper spacing */
.table-cell .location-actions,
.table-cell .role-actions,
.table-cell .routine-actions,
.table-cell .schedule-actions {
  justify-content: flex-start;
}

/* Schedule actions styling */
.schedule-actions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.schedule-actions .btn {
  min-width: auto;
  padding: 0.4rem 0.6rem;
  font-size: 0.8rem;
}

/* Routine list view styling */
.routine-name-list {
  font-weight: 600;
  color: #111827;
}

.routine-description-list {
  color: #6b7280;
  line-height: 1.4;
}

.task-count-list {
  color: #374151;
  font-size: 0.9rem;
}

.routine-actions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.routine-actions .btn {
  min-width: auto;
  padding: 0.4rem 0.6rem;
  font-size: 0.8rem;
}

/* Improved typography for badges and time elements */
.time-badge {
  font-family: "SF Mono", "Monaco", "Inconsolata", "Roboto Mono", monospace;
  font-size: 0.9rem;
  font-weight: 500;
  color: #374151;
  background: #f3f4f6;
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  display: inline-block;
}

.days-badge {
  background: #f3f4f6;
  color: #374151;
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  font-size: 0.85rem;
  font-weight: 500;
  display: inline-block;
  border: 1px solid #d1d5db;
}

.routine-badge {
  background: #f0f9ff;
  color: #0369a1;
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  font-size: 0.85rem;
  font-weight: 500;
  display: inline-block;
}

/* Better spacing for list view elements */
.location-name-list,
.routine-name-list {
  font-weight: 600;
  color: #111827;
  font-size: 0.95rem;
}

.location-address-list,
.routine-description-list {
  color: #6b7280;
  line-height: 1.4;
  font-size: 0.9rem;
}

.task-count-list {
  color: #374151;
  font-size: 0.9rem;
  font-weight: 500;
}

/* Role Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.modal-header {
  padding: 1.5rem 1.5rem 0 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 1.5rem;
}

.modal-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: #111827;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #6b7280;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: color 0.2s ease;
}

.modal-close:hover {
  color: #374151;
}

.form-group {
  margin-bottom: 1.5rem;
  padding: 0 1.5rem;
}

.form-group label {
  display: block;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
  box-sizing: border-box;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #0a2d69;
  box-shadow: 0 0 0 3px rgba(10, 45, 105, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

.modal-actions {
  padding: 1.5rem;
  border-top: 1px solid #e5e7eb;
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  margin: 0;
}

.checkbox-label input[type="checkbox"] {
  margin: 0;
}

.checkbox-label span {
  font-weight: 500;
  color: #374151;
}

.checkbox-label + small {
  display: block;
  margin-top: 0.5rem;
  color: #6b7280;
  font-size: 0.85rem;
}

.role-purpose-info {
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1rem;
  margin-top: 0.5rem;
}

.role-purpose-info p {
  margin: 0 0 0.75rem 0;
  color: #374151;
  font-size: 0.9rem;
  line-height: 1.5;
}

.role-purpose-info p:last-child {
  margin-bottom: 0;
}

/* Button Variants */
.btn.btn-sm {
  padding: 0.5rem 0.75rem;
  font-size: 0.8rem;
  border-radius: 6px;
}

.btn.btn-primary {
  background: #0a2d69;
  color: white;
  border: none;
}

.btn.btn-primary:hover {
  background: #1e4a8c;
}

.btn.btn-secondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn.btn-secondary:hover {
  background: #e5e7eb;
}

.btn.btn-danger {
  background: #ef4444;
  color: white;
  border: none;
}

.btn.btn-danger:hover {
  background: #dc2626;
}

/* Responsive Design for Tables */
@media (max-width: 1200px) {
  /* Adjust grid columns for medium screens */
  .routines-table .table-header,
  .routines-table .table-row {
    grid-template-columns: 2fr 2.5fr 1.2fr 0.8fr 1.8fr;
  }

  .schedules-table .table-header,
  .schedules-table .table-row {
    grid-template-columns: 1.5fr 1.5fr 1.8fr 1.2fr 0.8fr 1.8fr;
  }
}

@media (max-width: 768px) {
  .tab-header-actions {
    flex-direction: column;
    gap: 0.75rem;
    align-items: stretch;
  }

  .view-toggle {
    align-self: center;
  }

  .roles-grid,
  .locations-grid,
  .routines-grid {
    grid-template-columns: 1fr;
  }

  .role-header,
  .location-header,
  .routine-header {
    flex-direction: column;
    gap: 1rem;
  }

  .role-actions,
  .location-actions,
  .routine-actions {
    align-self: flex-end;
  }

  /* Table responsive - stack on mobile */
  .roles-table .table-header,
  .locations-table .table-header,
  .routines-table .table-header,
  .schedules-table .table-header {
    display: none;
  }

  .roles-table .table-row,
  .locations-table .table-row,
  .routines-table .table-row,
  .schedules-table .table-row {
    display: block;
    padding: 1rem;
    margin-bottom: 0.5rem;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
    background: white;
  }

  .table-cell {
    padding: 0.25rem 0;
    display: block;
  }

  .table-cell::before {
    content: attr(data-label);
    font-weight: 600;
    color: #374151;
    display: block;
    margin-bottom: 0.25rem;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .header-cell::before {
    display: none;
  }
}

/* Help Tab Styles */
.help-tab {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  max-width: 1200px;
  margin: 0 auto;
}

.help-section {
  margin-bottom: 3rem;
}

.help-section h3 {
  color: #0a2d69;
  margin-bottom: 1rem;
  font-size: 1.5rem;
  font-weight: 600;
}

.help-section p {
  color: #64748b;
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.tips-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.tip-card {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 1.5rem;
  transition: all 0.2s ease;
}

.tip-card:hover {
  border-color: #0a2d69;
  box-shadow: 0 4px 12px rgba(10, 45, 105, 0.1);
}

.tip-card h4 {
  color: #0f172a;
  margin-bottom: 0.75rem;
  font-size: 1.1rem;
  font-weight: 600;
}

.tip-card p {
  color: #64748b;
  margin: 0;
  font-size: 0.9rem;
  line-height: 1.5;
}

.contact-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.contact-card {
  background: #f1f5f9;
  border: 1px solid #cbd5e1;
  border-radius: 8px;
  padding: 1.5rem;
  text-align: center;
}

.contact-card h4 {
  color: #0f172a;
  margin-bottom: 0.75rem;
  font-size: 1.1rem;
  font-weight: 600;
}

.contact-card p {
  color: #64748b;
  margin-bottom: 1rem;
  font-size: 0.9rem;
}

.contact-link {
  display: inline-block;
  background: #0a2d69;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s ease;
}

.contact-link:hover {
  background: #1e4a8c;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(10, 45, 105, 0.2);
}

.best-practices {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.practice-item {
  background: #fefefe;
  border: 1px solid #e5e7eb;
  border-left: 4px solid #0a2d69;
  border-radius: 6px;
  padding: 1.5rem;
}

.practice-item h4 {
  color: #0a2d69;
  margin-bottom: 0.75rem;
  font-size: 1rem;
  font-weight: 600;
}

.practice-item p {
  color: #64748b;
  margin: 0;
  font-size: 0.9rem;
  line-height: 1.5;
}

/* Watchlist Styles */
.watchlist-tab {
  padding: 1.5rem;
}

.watchlist-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(360px, 1fr));
  gap: 1rem;
  margin-top: 1.5rem;
}

.watchlist-card {
  background: white;
  border-radius: 8px;
  border: 2px solid #e2e8f0;
  padding: 1rem;
  transition: all 0.2s ease;
  display: flex;
  flex-direction: column;
  min-width: 0; /* Allow card to shrink */
  overflow: hidden; /* Prevent content overflow */
}

.watchlist-card--good {
  border-color: #10b981;
  background: linear-gradient(135deg, #f0fdf4 0%, #ffffff 100%);
}

.watchlist-card--due-soon {
  border-color: #f59e0b;
  background: linear-gradient(135deg, #fffbeb 0%, #ffffff 100%);
}

.watchlist-card--overdue {
  border-color: #ef4444;
  background: linear-gradient(135deg, #fef2f2 0%, #ffffff 100%);
}

.watchlist-card__header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.75rem;
  gap: 1rem;
}

.watchlist-card__title {
  flex: 1;
  min-width: 0; /* Allow title to shrink */
}

.watchlist-card__title h3 {
  margin: 0 0 0.25rem 0;
  color: #1e293b;
  font-size: 1rem;
  font-weight: 600;
  line-height: 1.3;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.watchlist-card__meta {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
  font-size: 0.75rem;
  color: #64748b;
  margin-top: 0.25rem;
}

.watchlist-card__meta span {
  color: #64748b;
  font-weight: 500;
  position: relative;
}

.watchlist-card__meta span:not(:last-child)::after {
  content: "•";
  margin-left: 0.75rem;
  color: #cbd5e1;
}

.watchlist-card__actions {
  display: flex;
  gap: 0.5rem;
  flex-shrink: 0;
  flex-wrap: wrap;
  min-width: 0;
}

.watchlist-card__actions .btn {
  white-space: nowrap;
  min-width: 0;
  font-size: 0.875rem;
  padding: 0.5rem 0.75rem;
}

.watchlist-card__description {
  margin-bottom: 0.75rem;
  color: #64748b;
  font-style: italic;
  font-size: 0.875rem;
  line-height: 1.4;
}

.watchlist-card__status {
  margin-bottom: 0.75rem;
}

.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 600;
}

.status-indicator--good {
  background: #dcfce7;
  color: #166534;
}

.status-indicator--due-soon {
  background: #fef3c7;
  color: #92400e;
}

.status-indicator--overdue {
  background: #fee2e2;
  color: #991b1b;
}

.watchlist-card__history h4 {
  margin: 0 0 0.5rem 0;
  color: #475569;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.check-history {
  space-y: 0.375rem;
}

.check-item {
  padding: 0.5rem;
  background: #f8fafc;
  border-radius: 4px;
  border-left: 2px solid #e2e8f0;
  margin-bottom: 0.375rem;
}

.check-item--more {
  text-align: center;
  color: #64748b;
  font-style: italic;
  border-left-color: #cbd5e1;
}

.check-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.125rem;
}

.checker-name {
  font-weight: 600;
  color: #1e293b;
  font-size: 0.875rem;
}

.check-time {
  font-size: 0.6875rem;
  color: #64748b;
}

.check-notes {
  font-size: 0.8125rem;
  color: #475569;
  font-style: italic;
  margin-top: 0.125rem;
}

.no-checks {
  color: #94a3b8;
  font-style: italic;
  text-align: center;
  padding: 1rem;
}

/* Frequency Examples */
.frequency-examples {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  margin-top: 0.5rem;
}

.btn-outline {
  background: transparent;
  border: 1px solid #d1d5db;
  color: #374151;
}

.btn-outline:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

.form-help {
  display: block;
  margin-top: 0.25rem;
  font-size: 0.75rem;
  color: #6b7280;
}

/* Watchlist List View */
.watchlist-list {
  margin-top: 1.5rem;
  space-y: 1rem;
}

.watchlist-row {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  padding: 1rem 1.5rem;
  margin-bottom: 1rem;
  transition: all 0.2s ease;
}

.watchlist-row--good {
  border-color: #10b981;
  background: linear-gradient(135deg, #f0fdf4 0%, #ffffff 100%);
}

.watchlist-row--due-soon {
  border-color: #f59e0b;
  background: linear-gradient(135deg, #fffbeb 0%, #ffffff 100%);
}

.watchlist-row--overdue {
  border-color: #ef4444;
  background: linear-gradient(135deg, #fef2f2 0%, #ffffff 100%);
}

.watchlist-row__main {
  flex: 1;
  min-width: 0;
}

.watchlist-row__title h4 {
  margin: 0 0 0.5rem 0;
  color: #1e293b;
  font-size: 1.125rem;
  font-weight: 600;
}

.watchlist-row__meta {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
  font-size: 0.875rem;
  color: #64748b;
}

.watchlist-row__meta span {
  background: #f1f5f9;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-weight: 500;
}

.watchlist-row__description {
  margin-top: 0.5rem;
  color: #64748b;
  font-style: italic;
}

.watchlist-row__status {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  text-align: center;
  min-width: 150px;
}

.last-check {
  font-size: 0.75rem;
  color: #64748b;
  text-align: center;
}

.watchlist-row__actions {
  display: flex;
  gap: 0.5rem;
  flex-shrink: 0;
}

/* Watchlist Grouped View */
.watchlist-grouped {
  margin-top: 1.5rem;
}

.location-group {
  margin-bottom: 2.5rem;
}

.location-group__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #e2e8f0;
}

.location-group__header h3 {
  margin: 0;
  color: #1e293b;
  font-size: 1.25rem;
  font-weight: 600;
}

.task-count {
  background: #f1f5f9;
  color: #64748b;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 500;
}

/* Recent Check-ins Styles */
.recent-checks-list {
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  background: #f8fafc;
}

.recent-check-item {
  padding: 0.75rem;
  border-bottom: 1px solid #e2e8f0;
}

.recent-check-item:last-child {
  border-bottom: none;
}

.check-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.25rem;
}

.check-name {
  font-weight: 600;
  color: #1e293b;
  font-size: 0.875rem;
}

.check-time {
  font-size: 0.75rem;
  color: #64748b;
}

.check-notes {
  font-size: 0.8125rem;
  color: #475569;
  font-style: italic;
  margin-top: 0.25rem;
  padding: 0.5rem;
  background: #ffffff;
  border-radius: 4px;
  border-left: 3px solid #cbd5e1;
}

.loading-checks {
  padding: 1rem;
  text-align: center;
  color: #64748b;
  font-style: italic;
}

.no-recent-checks {
  padding: 0.75rem;
  color: #64748b;
  font-style: italic;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
}

/* Routine grouping in reports */
.routine-group {
  margin-bottom: 1.5rem;
}

.routine-group-title {
  font-size: 0.95rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 0.75rem 0;
  padding: 0.5rem 0.75rem;
  background: #f8fafc;
  border-radius: 6px;
  border-left: 3px solid #3b82f6;
}

.routine-tasks {
  margin-left: 1rem;
  border-left: 2px solid #e2e8f0;
  padding-left: 1rem;
}

.routine-tasks .task-item {
  margin-bottom: 0.75rem;
}

.routine-tasks .task-item:last-child {
  margin-bottom: 0;
}

/* Collapsible routine group titles */
.routine-group-title--collapsible {
  cursor: pointer;
  user-select: none;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.2s ease;
  padding: 0.5rem 0.75rem;
  background: #f1f5f9;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

.routine-group-title--collapsible:hover {
  background: #e2e8f0;
  border-color: #cbd5e1;
}

.routine-group-title-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.routine-collapse-icon {
  font-size: 0.75rem;
  color: #64748b;
  transition: transform 0.2s ease;
  width: 0.875rem;
  text-align: center;
}

.routine-task-count {
  font-size: 0.875rem;
  color: #64748b;
  background: #ffffff;
  padding: 0.25rem 0.5rem;
  border-radius: 10px;
  font-weight: 500;
  border: 1px solid #e2e8f0;
}

/* Watchlist History View More Button */
.view-more-history-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.375rem 0.75rem;
  margin-top: 0.5rem;
  font-size: 0.75rem;
  color: #6366f1;
  background: transparent;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.view-more-history-btn:hover {
  background: #f8fafc;
  border-color: #6366f1;
  color: #4f46e5;
}

/* Large Modal for History */
.modal-content--large {
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
}

/* History Modal Styles */
.loading-history {
  text-align: center;
  padding: 2rem;
  color: #64748b;
}

.full-history-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.history-item {
  padding: 1rem;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.history-item:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

.history-item__header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
}

.history-item__number {
  font-size: 0.75rem;
  color: #64748b;
  background: #e2e8f0;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-weight: 600;
  min-width: 2rem;
  text-align: center;
}

.history-item__name {
  font-weight: 600;
  color: #1e293b;
  flex: 1;
}

.history-item__time {
  font-size: 0.875rem;
  color: #64748b;
}

.history-item__notes {
  display: flex;
  gap: 0.5rem;
  margin-top: 0.5rem;
  padding-top: 0.5rem;
  border-top: 1px solid #e2e8f0;
}

.notes-label {
  font-size: 0.75rem;
  color: #64748b;
  font-weight: 500;
  min-width: 3rem;
}

.notes-text {
  font-size: 0.875rem;
  color: #374151;
  font-style: italic;
  flex: 1;
}

.no-history {
  text-align: center;
  padding: 3rem 1rem;
  color: #64748b;
}

.no-history p {
  margin: 0;
  font-size: 1rem;
}
