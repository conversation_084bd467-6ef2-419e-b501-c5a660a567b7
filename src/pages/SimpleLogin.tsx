import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useTenant } from "../contexts/TenantContext";
import { sendPasswordReset } from "../services/userService";

const SimpleLogin: React.FC = () => {
  const navigate = useNavigate();
  const { login, signup, userProfile } = useTenant();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [isSignupMode, setIsSignupMode] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [resetLoading, setResetLoading] = useState(false);
  const [resetMessage, setResetMessage] = useState("");
  const [checkingAuth, setCheckingAuth] = useState(true);

  // Check if user is already authenticated on component mount
  useEffect(() => {
    console.log("🔐 SimpleLogin: useEffect triggered - starting auth check");

    const checkExistingAuth = async () => {
      try {
        console.log("🔐 SimpleLogin: Checking for existing authentication...");

        // Check localStorage for auth data (same as kiosk interface)
        const authData = localStorage.getItem("adminbuddy_auth");
        console.log(
          "🔐 SimpleLogin: Raw auth data from localStorage:",
          authData ? "Found" : "Not found"
        );

        if (!authData) {
          console.log("🔐 SimpleLogin: No auth data found in localStorage");
          setCheckingAuth(false);
          return;
        }

        const { uid, email } = JSON.parse(authData);
        console.log("🔐 SimpleLogin: Found auth data for:", email);
        console.log("🔐 SimpleLogin: User UID:", uid);

        // Get fresh user profile from Firestore
        console.log("🔐 SimpleLogin: Importing getUserProfile service...");
        const { getUserProfile } = await import(
          "../services/adminBuddyFirebaseService"
        );
        console.log("🔐 SimpleLogin: Calling getUserProfile for UID:", uid);
        const userProfile = await getUserProfile(uid);
        console.log("🔐 SimpleLogin: getUserProfile result:", userProfile);

        if (!userProfile) {
          console.log(
            "🔐 SimpleLogin: Could not load user profile from Firestore"
          );
          localStorage.removeItem("adminbuddy_auth");
          setCheckingAuth(false);
          return;
        }

        console.log(
          "🔐 SimpleLogin: User already authenticated, redirecting...",
          userProfile.email
        );
        console.log("🔐 SimpleLogin: User role:", userProfile.role);
        console.log(
          "🔐 SimpleLogin: User kioskLocationId:",
          userProfile.kioskLocationId
        );

        // Check if there's a stored redirect URL
        const redirectUrl = localStorage.getItem(
          "adminbuddy_redirect_after_login"
        );
        console.log(
          "📍 SimpleLogin: Checking for stored redirect URL:",
          redirectUrl
        );
        if (redirectUrl) {
          console.log(
            "📍 SimpleLogin: Found stored redirect URL:",
            redirectUrl
          );
          localStorage.removeItem("adminbuddy_redirect_after_login");
          console.log("📍 SimpleLogin: Navigating to stored URL:", redirectUrl);
          navigate(redirectUrl);
          return;
        }

        // Redirect based on user role
        console.log(
          "🔀 SimpleLogin: No stored redirect URL, using role-based redirect"
        );
        console.log(
          "🔀 SimpleLogin: User role for redirect:",
          userProfile.role
        );
        switch (userProfile.role) {
          case "owner":
            console.log(
              "👑 SimpleLogin: Owner user, redirecting to /dashboard"
            );
            navigate("/dashboard");
            break;
          case "manager":
            console.log(
              "👨‍💼 SimpleLogin: Manager user, redirecting to /manager"
            );
            navigate("/manager");
            break;
          case "kiosk":
            console.log(
              "🏪 SimpleLogin: Kiosk user detected, redirecting to kiosk interface"
            );
            // For kiosk users, redirect to their specific location if available
            if (userProfile.kioskLocationId) {
              const kioskUrl = `/kiosk/${userProfile.kioskLocationId}`;
              console.log(
                "🏪 SimpleLogin: Redirecting to specific kiosk location:",
                kioskUrl
              );
              navigate(kioskUrl);
            } else {
              console.log(
                "🏪 SimpleLogin: No specific location, redirecting to /kiosk"
              );
              navigate("/kiosk");
            }
            break;
          default:
            console.log("🏠 SimpleLogin: Default case, redirecting to /");
            navigate("/");
        }
      } catch (error) {
        console.log("🔐 SimpleLogin: Error checking authentication:", error);
        // Clear potentially corrupted auth data
        localStorage.removeItem("adminbuddy_auth");
      } finally {
        setCheckingAuth(false);
      }
    };

    checkExistingAuth();
  }, [navigate]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email.trim() || !password.trim()) return;

    setLoading(true);
    setError("");

    // Check if this is a kiosk login (for extended sessions)
    const storedRedirectUrl = localStorage.getItem(
      "adminbuddy_redirect_after_login"
    );
    const isKioskLogin = storedRedirectUrl?.includes("/kiosk/");

    try {
      if (isSignupMode) {
        console.log("Creating new user account:", email);

        // Use the proper signup method from TenantContext (Firebase Auth REST API)
        await signup(email.trim(), password);
        console.log("✅ User created and logged in successfully");

        // Check for stored redirect URL first
        const storedRedirectUrl = localStorage.getItem(
          "adminbuddy_redirect_after_login"
        );

        if (storedRedirectUrl) {
          console.log("🔄 New user - found stored URL:", storedRedirectUrl);
          localStorage.removeItem("adminbuddy_redirect_after_login");

          // New users should always go to onboarding, regardless of stored URL
          console.log(
            "🎉 New user - redirecting to onboarding (ignoring stored URL)"
          );
          navigate("/onboarding");
        } else {
          console.log("🎉 New user - redirecting to onboarding");
          navigate("/onboarding");
        }
      } else {
        console.log("🔐 Pure REST login:", email);
        await login(email.trim(), password, isKioskLogin); // Pass rememberMe for kiosk logins
        console.log("✅ Login successful, navigating based on role");

        // Check for stored redirect URL first
        const storedRedirectUrl = localStorage.getItem(
          "adminbuddy_redirect_after_login"
        );

        console.log("🔍 Checking for stored redirect URL:", storedRedirectUrl);

        if (storedRedirectUrl) {
          console.log("🔄 Found stored redirect URL:", storedRedirectUrl);
          localStorage.removeItem("adminbuddy_redirect_after_login");
          console.log("🧹 Removed stored redirect URL from localStorage");

          // Check if this is a kiosk user - they should never go to dashboard pages
          if (userProfile && userProfile.role === "kiosk") {
            console.log(
              "🏪 Kiosk user detected, ignoring stored redirect and going to kiosk"
            );
            navigate("/kiosk");
            return;
          } else {
            console.log(
              "🔄 Non-kiosk user, redirecting to stored URL:",
              storedRedirectUrl
            );
            navigate(storedRedirectUrl);
            return; // Important: return early to prevent default navigation
          }
        } else {
          console.log(
            "📍 No stored redirect URL found, using default navigation"
          );

          // Navigation based on user profile (TenantContext handles auth state)
          if (userProfile) {
            switch (userProfile.role) {
              case "owner":
                navigate("/dashboard");
                break;
              case "manager":
                navigate("/manager");
                break;
              case "employee":
                navigate("/employee");
                break;
              case "kiosk":
                console.log(
                  "🏪 Kiosk user detected, redirecting to kiosk interface"
                );
                navigate("/kiosk");
                break;
              default:
                navigate("/dashboard");
            }
          } else {
            // Default navigation if profile not immediately available
            navigate("/dashboard");
          }
        }
      }
    } catch (error: any) {
      console.error("Authentication error:", error);

      // Check for network connectivity issues
      if (
        error.message &&
        error.message.includes("Network connectivity issue")
      ) {
        setError(
          "Unable to connect to AdminBuddy servers. Please check your internet connection and try again."
        );
      } else if (error.message && error.message.includes("Load failed")) {
        setError(
          "Network connection failed. Please check your internet connection and try again."
        );
      } else {
        setError(
          error.message ||
            `Failed to ${
              isSignupMode ? "create account" : "login"
            }. Please try again.`
        );
      }
    } finally {
      setLoading(false);
    }
  };

  const handleForgotPassword = async () => {
    if (!email.trim()) {
      setError("Please enter your email address first");
      return;
    }

    // Check if this is a username (no @) - can't reset password for kiosk users
    if (!email.includes("@")) {
      setError(
        "Password reset is only available for email accounts. Contact your manager to reset kiosk passwords."
      );
      return;
    }

    setResetLoading(true);
    setError("");
    setResetMessage("");

    try {
      await sendPasswordReset(email.trim());
      setResetMessage(
        "Password reset email sent! Check your inbox and spam folder."
      );
    } catch (error: any) {
      setError(error.message || "Failed to send password reset email");
    } finally {
      setResetLoading(false);
    }
  };

  // Show loading state while checking authentication
  if (checkingAuth) {
    return (
      <div
        style={{
          minHeight: "100vh",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          backgroundColor: "#f5f5f5",
        }}
      >
        <div
          style={{
            backgroundColor: "white",
            padding: "40px",
            borderRadius: "8px",
            boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
            width: "100%",
            maxWidth: "400px",
            textAlign: "center",
          }}
        >
          <h1 style={{ color: "#2c3e50", marginBottom: "10px" }}>AdminBuddy</h1>
          <p style={{ color: "#666", marginBottom: "20px" }}>
            Checking Authentication...
          </p>
          <div
            style={{
              width: "40px",
              height: "40px",
              border: "4px solid #ecf0f1",
              borderTop: "4px solid #3498db",
              borderRadius: "50%",
              animation: "spin 1s linear infinite",
              margin: "0 auto",
            }}
          />
          <style>
            {`
              @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
              }
            `}
          </style>
        </div>
      </div>
    );
  }

  // No predefined tenants - each user gets their own

  return (
    <div
      style={{
        minHeight: "100vh",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        backgroundColor: "#f5f5f5",
      }}
    >
      <div
        style={{
          backgroundColor: "white",
          padding: "40px",
          borderRadius: "8px",
          boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
          width: "100%",
          maxWidth: "400px",
        }}
      >
        <div style={{ textAlign: "center", marginBottom: "30px" }}>
          <h1 style={{ color: "#2c3e50", marginBottom: "10px" }}>AdminBuddy</h1>
          <p style={{ color: "#666", margin: 0 }}>
            Simple Business Task Management
          </p>
        </div>

        <div style={{ textAlign: "center", marginBottom: "20px" }}>
          <button
            type="button"
            onClick={() => setIsSignupMode(false)}
            style={{
              padding: "8px 16px",
              marginRight: "10px",
              backgroundColor: !isSignupMode ? "#3498db" : "#ecf0f1",
              color: !isSignupMode ? "white" : "#2c3e50",
              border: "none",
              borderRadius: "4px",
              cursor: "pointer",
            }}
          >
            Login
          </button>
          <button
            type="button"
            onClick={() => setIsSignupMode(true)}
            style={{
              padding: "8px 16px",
              backgroundColor: isSignupMode ? "#3498db" : "#ecf0f1",
              color: isSignupMode ? "white" : "#2c3e50",
              border: "none",
              borderRadius: "4px",
              cursor: "pointer",
            }}
          >
            Sign Up
          </button>
        </div>

        <form onSubmit={handleSubmit}>
          <div style={{ marginBottom: "20px" }}>
            <label
              style={{
                display: "block",
                marginBottom: "5px",
                fontWeight: "bold",
                color: "#333",
              }}
            >
              Email / Username:
            </label>
            <input
              type="text"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Enter your email or kiosk username"
              required
              disabled={loading}
              style={{
                width: "100%",
                padding: "12px",
                border: "1px solid #ddd",
                borderRadius: "4px",
                fontSize: "16px",
                boxSizing: "border-box",
                opacity: loading ? 0.6 : 1,
              }}
            />
          </div>

          <div style={{ marginBottom: "20px" }}>
            <label
              style={{
                display: "block",
                marginBottom: "5px",
                fontWeight: "bold",
                color: "#333",
              }}
            >
              Password:
            </label>
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="Enter your password"
              required
              disabled={loading}
              style={{
                width: "100%",
                padding: "12px",
                border: "1px solid #ddd",
                borderRadius: "4px",
                fontSize: "16px",
                boxSizing: "border-box",
                opacity: loading ? 0.6 : 1,
              }}
            />
          </div>

          {error && (
            <div
              style={{
                marginBottom: "20px",
                padding: "10px",
                backgroundColor: "#fee",
                border: "1px solid #fcc",
                borderRadius: "4px",
                color: "#c33",
                fontSize: "14px",
              }}
            >
              {error}
            </div>
          )}

          {resetMessage && (
            <div
              style={{
                marginBottom: "20px",
                padding: "10px",
                backgroundColor: "#dcfce7",
                border: "1px solid #bbf7d0",
                borderRadius: "4px",
                color: "#16a34a",
                fontSize: "14px",
              }}
            >
              {resetMessage}
            </div>
          )}

          <button
            type="submit"
            disabled={loading}
            style={{
              width: "100%",
              padding: "12px",
              backgroundColor: loading ? "#bbb" : "#3498db",
              color: "white",
              border: "none",
              borderRadius: "4px",
              fontSize: "16px",
              fontWeight: "bold",
              cursor: loading ? "not-allowed" : "pointer",
            }}
          >
            {loading
              ? isSignupMode
                ? "🔄 Creating Account..."
                : "🔄 Logging in..."
              : isSignupMode
              ? "📝 Create Account"
              : "🔐 Login to AdminBuddy"}
          </button>

          {!isSignupMode && (
            <div style={{ textAlign: "center", marginTop: "15px" }}>
              <button
                type="button"
                onClick={handleForgotPassword}
                disabled={resetLoading}
                style={{
                  background: "none",
                  border: "none",
                  color: "#3498db",
                  fontSize: "14px",
                  cursor: resetLoading ? "not-allowed" : "pointer",
                  textDecoration: "underline",
                  padding: "0",
                }}
              >
                {resetLoading ? "Sending..." : "Forgot your password?"}
              </button>
            </div>
          )}
        </form>

        <div
          style={{
            marginTop: "20px",
            padding: "15px",
            backgroundColor: "#e8f4fd",
            borderRadius: "4px",
            fontSize: "14px",
            color: "#2c3e50",
          }}
        >
          <strong>Secure Authentication:</strong>
          <br />
          • Password-protected accounts
          <br />
          • Each user gets their own private business data
          <br />• Full access to all AdminBuddy features
        </div>
      </div>
    </div>
  );
};

export default SimpleLogin;
