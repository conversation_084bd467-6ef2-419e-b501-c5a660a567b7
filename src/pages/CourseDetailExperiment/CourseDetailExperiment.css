/* Course Detail Experiment Page */
.course-detail-experiment {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding: 2rem;
}

.course-detail-experiment__header {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  margin-bottom: 2rem;
  text-align: center;
}

.course-detail-experiment__header h1 {
  margin: 0 0 0.5rem 0;
  color: #0a2d69;
  font-size: 2rem;
}

.course-detail-experiment__header p {
  margin: 0 0 2rem 0;
  color: #666;
  font-size: 1.1rem;
}

.course-detail-experiment__controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.course-detail-experiment__controls label {
  font-weight: 600;
  color: #0a2d69;
}

.course-detail-experiment__select {
  padding: 0.5rem 1rem;
  border: 2px solid #d1e7f0;
  border-radius: 8px;
  background: white;
  color: #0a2d69;
  font-size: 1rem;
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.course-detail-experiment__select:focus {
  outline: none;
  border-color: #ffa300;
}

.course-detail-experiment__loading,
.course-detail-experiment__error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.course-detail-experiment__error h2 {
  color: #dc3545;
  margin-bottom: 1rem;
}

.course-detail-experiment__error a {
  color: #0a2d69;
  text-decoration: none;
  font-weight: 600;
  margin-top: 1rem;
}

.course-detail-experiment__error a:hover {
  text-decoration: underline;
}

.course-detail-experiment__message {
  padding: 1rem 1.5rem;
  border-radius: 8px;
  margin-bottom: 2rem;
  font-weight: 500;
}

.course-detail-experiment__message.success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.course-detail-experiment__message.error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.course-detail-experiment__content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.course-detail-experiment__variant {
  padding: 2rem;
}

.course-detail-experiment__variant-title {
  margin: 0 0 0.5rem 0;
  color: #0a2d69;
  font-size: 1.5rem;
}

.course-detail-experiment__variant-description {
  margin: 0 0 2rem 0;
  color: #666;
  font-size: 1rem;
  line-height: 1.6;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #ffa300;
}

/* Common breadcrumb styles */
.course-detail-honeycomb__breadcrumb,
.course-detail-sidebar__breadcrumb,
.course-detail-minimal__breadcrumb {
  font-size: 0.9rem;
  padding: 0.75rem 1.5rem;
  background-color: #f8f9fa;
  border: 1px solid #e8f4f8;
  border-radius: 8px;
  color: #0a2d69;
  margin-bottom: 1rem;
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
  align-items: center;
}

.course-detail-honeycomb__breadcrumb a,
.course-detail-sidebar__breadcrumb a,
.course-detail-minimal__breadcrumb a {
  color: #0a2d69;
  text-decoration: none;
  font-weight: bold;
}

.course-detail-honeycomb__breadcrumb a:hover,
.course-detail-sidebar__breadcrumb a:hover,
.course-detail-minimal__breadcrumb a:hover {
  text-decoration: underline;
}

/* Common category styles */
.course-detail-honeycomb__category,
.course-detail-sidebar__category,
.course-detail-minimal__category {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.course-detail-honeycomb__category-icon,
.course-detail-sidebar__category-icon,
.course-detail-minimal__category-icon {
  font-size: 1.5rem;
}

.course-detail-honeycomb__category-text,
.course-detail-sidebar__category-text,
.course-detail-minimal__category-text {
  background: rgba(255, 163, 0, 0.9);
  color: #0a2d69;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Common action button styles */
.course-detail-honeycomb__actions,
.course-detail-sidebar__actions,
.course-detail-minimal__actions {
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
  flex-wrap: wrap;
}

/* Honeycomb pattern base */
.course-detail-honeycomb__hero-pattern,
.course-detail-sidebar__pattern,
.course-detail-minimal__info-pattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.15;
  background-image: url("data:image/svg+xml,%3Csvg width='56' height='100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' stroke='rgba(255,255,255,0.8)' stroke-width='1'%3E%3Cpolygon points='28,1 53,15 53,43 28,57 3,43 3,15'/%3E%3C/g%3E%3C/svg%3E");
  background-size: 56px 100px;
  background-repeat: repeat;
}

.course-detail-honeycomb__hero-pattern::before,
.course-detail-sidebar__pattern::before,
.course-detail-minimal__info-pattern::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("data:image/svg+xml,%3Csvg width='56' height='100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' stroke='rgba(255,255,255,0.6)' stroke-width='0.8'%3E%3Cpolygon points='28,1 53,15 53,43 28,57 3,43 3,15'/%3E%3C/g%3E%3C/svg%3E");
  background-size: 56px 100px;
  background-repeat: repeat;
  background-position: 28px 50px;
}

/* ===== HONEYCOMB HEADER DESIGN ===== */
.course-detail-honeycomb__hero {
  position: relative;
  background: linear-gradient(135deg, #0a2d69 0%, #1e4a8c 100%);
  padding: 3rem 2rem;
  margin: -2rem -2rem 2rem -2rem;
  overflow: hidden;
}

.course-detail-honeycomb__hero-content {
  position: relative;
  z-index: 2;
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.course-detail-honeycomb__title {
  margin: 0 0 1rem 0;
  font-size: 2.5rem;
  font-weight: 700;
  color: white;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.6);
  line-height: 1.2;
}

.course-detail-honeycomb__description {
  margin: 0;
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
}

.course-detail-honeycomb__content {
  padding: 0 2rem 2rem 2rem;
}

.course-detail-honeycomb__info-grid {
  display: grid;
  gap: 1rem;
  margin-bottom: 2rem;
  max-width: 600px;
}

.course-detail-honeycomb__info-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #ffa300;
}

.course-detail-honeycomb__info-label {
  font-weight: 600;
  color: #0a2d69;
  min-width: 6rem;
}

.course-detail-honeycomb__info-value {
  color: #333;
  font-weight: 500;
}

.course-detail-honeycomb__info-item--price {
  border-top: 1px solid #e8f4f8;
  margin-top: 0.5rem;
}

.course-detail-honeycomb__info-item--price
  .course-detail-honeycomb__info-label {
  color: #666;
  font-size: 0.9rem;
}

.course-detail-honeycomb__info-item--price
  .course-detail-honeycomb__info-value {
  color: #666;
  font-size: 0.9rem;
}

/* ===== SIDEBAR PATTERN DESIGN ===== */
.course-detail-sidebar__layout {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 2rem;
  margin-top: 1rem;
}

.course-detail-sidebar__sidebar {
  position: relative;
  background: linear-gradient(135deg, #0a2d69 0%, #1e4a8c 100%);
  border-radius: 12px;
  padding: 2rem;
  overflow: hidden;
  height: fit-content;
}

.course-detail-sidebar__sidebar-content {
  position: relative;
  z-index: 2;
}

.course-detail-sidebar__info {
  margin: 2rem 0;
}

.course-detail-sidebar__info-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.course-detail-sidebar__info-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.course-detail-sidebar__info-label {
  font-weight: 600;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.course-detail-sidebar__info-value {
  color: white;
  font-weight: 500;
  font-size: 1.1rem;
}

.course-detail-sidebar__info-item--price {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.course-detail-sidebar__info-item--price .course-detail-sidebar__info-label,
.course-detail-sidebar__info-item--price .course-detail-sidebar__info-value {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
}

.course-detail-sidebar__main {
  padding: 2rem 0;
}

.course-detail-sidebar__title {
  margin: 0 0 1rem 0;
  font-size: 2.2rem;
  color: #0a2d69;
  line-height: 1.2;
}

.course-detail-sidebar__description {
  margin: 0;
  font-size: 1.1rem;
  color: #555;
  line-height: 1.6;
}

/* ===== MINIMAL ACCENT DESIGN ===== */
.course-detail-minimal__header {
  padding: 2rem 0;
  text-align: center;
  border-bottom: 1px solid #e8f4f8;
  margin-bottom: 2rem;
}

.course-detail-minimal__title {
  margin: 0 0 1rem 0;
  font-size: 2.2rem;
  color: #0a2d69;
  line-height: 1.2;
}

.course-detail-minimal__description {
  margin: 0;
  font-size: 1.1rem;
  color: #555;
  line-height: 1.6;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
}

.course-detail-minimal__content {
  padding: 0 2rem 2rem 2rem;
}

.course-detail-minimal__info-card {
  position: relative;
  background: linear-gradient(135deg, #0a2d69 0%, #1e4a8c 100%);
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
  overflow: hidden;
  max-width: 600px;
}

.course-detail-minimal__info-content {
  position: relative;
  z-index: 2;
}

.course-detail-minimal__info-content h3 {
  margin: 0 0 1.5rem 0;
  color: white;
  font-size: 1.3rem;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
}

.course-detail-minimal__info-grid {
  display: grid;
  gap: 1rem;
}

.course-detail-minimal__info-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.course-detail-minimal__info-item:last-child {
  border-bottom: none;
}

.course-detail-minimal__info-label {
  font-weight: 600;
  color: rgba(255, 255, 255, 0.8);
  min-width: 6rem;
  font-size: 0.9rem;
}

.course-detail-minimal__info-value {
  color: white;
  font-weight: 500;
}

.course-detail-minimal__info-item--price {
  margin-top: 0.5rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.course-detail-minimal__info-item--price .course-detail-minimal__info-label,
.course-detail-minimal__info-item--price .course-detail-minimal__info-value {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
}

/* Responsive design */
@media (max-width: 768px) {
  .course-detail-experiment {
    padding: 1rem;
  }

  .course-detail-experiment__header {
    padding: 1.5rem;
  }

  .course-detail-experiment__header h1 {
    font-size: 1.5rem;
  }

  .course-detail-experiment__controls {
    flex-direction: column;
    align-items: stretch;
  }

  .course-detail-experiment__variant {
    padding: 1.5rem;
  }

  .course-detail-honeycomb__hero {
    padding: 2rem 1rem;
    margin: -1.5rem -1.5rem 1.5rem -1.5rem;
  }

  .course-detail-honeycomb__title {
    font-size: 1.8rem;
  }

  .course-detail-honeycomb__content {
    padding: 0 1.5rem 1.5rem 1.5rem;
  }

  .course-detail-sidebar__layout {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .course-detail-sidebar__sidebar {
    order: 2;
  }

  .course-detail-sidebar__main {
    order: 1;
    padding: 0;
  }

  .course-detail-minimal__content {
    padding: 0 1.5rem 1.5rem 1.5rem;
  }

  .course-detail-minimal__title {
    font-size: 1.8rem;
  }

  .course-detail-honeycomb__actions,
  .course-detail-sidebar__actions,
  .course-detail-minimal__actions {
    flex-direction: column;
  }
}
