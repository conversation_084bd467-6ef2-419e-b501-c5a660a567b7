import React, { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, Link } from "react-router-dom";
import { EXTERNAL_LINKS, ROUTES } from "../../constants";
import Button from "../../components/ui/Button";
import LoadingSpinner from "../../components/ui/LoadingSpinner";
import ErrorBoundary from "../../components/ui/ErrorBoundary";
import RegistrationForm, {
  RegistrationData,
} from "../../components/course/RegistrationForm";
import { useCourses, usePerformance } from "../../hooks";
import { submitRegistration } from "../../services/registrationService";
import { getCourseCapacity } from "../../services/firebaseCourseCapacityService";
import { formatPrice } from "../../utils/priceUtils";
import "./CourseDetailExperiment.css";

type ExperimentVariant =
  | "honeycomb-header"
  | "sidebar-pattern"
  | "minimal-accent";

const CourseDetailExperiment: React.FC = () => {
  // Add performance monitoring
  usePerformance({
    componentName: "CourseDetailExperiment",
    threshold: 25,
  });

  const { slug } = useParams<{ slug: string }>();
  const { loading, error, getCourseBySlug } = useCourses();
  const [selectedVariant, setSelectedVariant] =
    useState<ExperimentVariant>("honeycomb-header");

  // Registration state
  const [showRegistrationForm, setShowRegistrationForm] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [registrationMessage, setRegistrationMessage] = useState<string | null>(
    null
  );
  const [capacity, setCapacity] = useState<any>(null);

  // Get course data
  const course = slug ? getCourseBySlug(slug) : null;

  useEffect(() => {
    const loadCapacity = async () => {
      if (!slug) return;
      try {
        const capacityData = await getCourseCapacity(slug);
        setCapacity(capacityData);
      } catch (error) {
        console.error("Error loading course capacity:", error);
      }
    };

    loadCapacity();
  }, [slug]);

  const handleRegistrationSubmit = async (formData: RegistrationData) => {
    if (!course) return;

    setIsSubmitting(true);
    setRegistrationMessage(null);

    try {
      const result = await submitRegistration(
        course.slug,
        course.title,
        course.price || "Contact for pricing",
        formData
      );

      if (result.success) {
        setShowRegistrationForm(false);
        setRegistrationMessage(result.message);
        // Scroll to top to show success message
        window.scrollTo({ top: 0, behavior: "smooth" });
      } else {
        setRegistrationMessage(result.message);
      }
    } catch (error) {
      setRegistrationMessage("An unexpected error occurred. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleRegistrationCancel = () => {
    setShowRegistrationForm(false);
    setRegistrationMessage(null);
  };

  if (loading) {
    return (
      <div className="course-detail-experiment__loading">
        <LoadingSpinner />
        <p>Loading course details...</p>
      </div>
    );
  }

  if (error || !course) {
    return (
      <div className="course-detail-experiment__error">
        <h2>Course Not Found</h2>
        <p>
          The course you're looking for doesn't exist or couldn't be loaded.
        </p>
        <Link to={ROUTES.COURSES}>← Back to Courses</Link>
      </div>
    );
  }

  // Get category icon
  const getCategoryIcon = (category?: string) => {
    const categoryIcons = {
      Python: "🐍",
      JavaScript: "⚡",
      "Web Development": "🌐",
      "Game Development": "🎮",
      "Data Science": "📊",
      "Mobile Development": "📱",
      default: "💻",
    };

    return (
      categoryIcons[category as keyof typeof categoryIcons] ||
      categoryIcons.default
    );
  };

  const categoryIcon = getCategoryIcon(course.category);

  return (
    <ErrorBoundary>
      <div className="course-detail-experiment">
        <div className="course-detail-experiment__header">
          <h1>Course Detail Design Experiment</h1>
          <p>
            Exploring different approaches to course detail pages without images
          </p>

          <div className="course-detail-experiment__controls">
            <label htmlFor="variant-select">Design Variant:</label>
            <select
              id="variant-select"
              value={selectedVariant}
              onChange={(e) =>
                setSelectedVariant(e.target.value as ExperimentVariant)
              }
              className="course-detail-experiment__select"
            >
              <option value="honeycomb-header">Honeycomb Header Design</option>
              <option value="sidebar-pattern">Sidebar Pattern Design</option>
              <option value="minimal-accent">Minimal Accent Design</option>
            </select>
          </div>
        </div>

        {registrationMessage && (
          <div
            className={`course-detail-experiment__message ${
              registrationMessage.includes("success") ? "success" : "error"
            }`}
          >
            {registrationMessage}
          </div>
        )}

        <div
          className={`course-detail-experiment__content course-detail-experiment__content--${selectedVariant}`}
        >
          {selectedVariant === "honeycomb-header" && (
            <div className="course-detail-experiment__variant">
              <h2 className="course-detail-experiment__variant-title">
                Honeycomb Header Design
              </h2>
              <p className="course-detail-experiment__variant-description">
                Features a prominent honeycomb pattern header that replaces the
                course image, creating visual interest while maintaining brand
                consistency.
              </p>

              <div className="course-detail-honeycomb">
                <nav className="course-detail-honeycomb__breadcrumb">
                  <Link to={ROUTES.HOME}>Home</Link>
                  <span>›</span>
                  <Link to={ROUTES.COURSES}>Courses</Link>
                  <span>›</span>
                  <span>{course.title}</span>
                </nav>

                <div className="course-detail-honeycomb__hero">
                  <div className="course-detail-honeycomb__hero-pattern"></div>
                  <div className="course-detail-honeycomb__hero-content">
                    <div className="course-detail-honeycomb__category">
                      <span className="course-detail-honeycomb__category-icon">
                        {categoryIcon}
                      </span>
                      <span className="course-detail-honeycomb__category-text">
                        {course.category}
                      </span>
                    </div>
                    <h1 className="course-detail-honeycomb__title">
                      {course.title}
                    </h1>
                    <p className="course-detail-honeycomb__description">
                      {course.description}
                    </p>
                  </div>
                </div>

                <div className="course-detail-honeycomb__content">
                  <div className="course-detail-honeycomb__main">
                    <div className="course-detail-honeycomb__info-grid">
                      <div className="course-detail-honeycomb__info-item">
                        <span className="course-detail-honeycomb__info-label">
                          Start Date:
                        </span>
                        <span className="course-detail-honeycomb__info-value">
                          {course.startDate}
                        </span>
                      </div>
                      <div className="course-detail-honeycomb__info-item">
                        <span className="course-detail-honeycomb__info-label">
                          Schedule:
                        </span>
                        <span className="course-detail-honeycomb__info-value">
                          {course.schedule
                            ? `${course.schedule.daysOfWeek.join(", ")} at ${
                                course.schedule.time
                              }`
                            : "Schedule TBD"}
                        </span>
                      </div>
                      {course.price && (
                        <div className="course-detail-honeycomb__info-item course-detail-honeycomb__info-item--price">
                          <span className="course-detail-honeycomb__info-label">
                            Course Fee:
                          </span>
                          <span className="course-detail-honeycomb__info-value">
                            {formatPrice(course.price)}
                          </span>
                        </div>
                      )}
                    </div>

                    <div className="course-detail-honeycomb__actions">
                      <Button
                        variant="primary"
                        size="large"
                        onClick={() => setShowRegistrationForm(true)}
                      >
                        {capacity?.status === "full"
                          ? "Join Waitlist"
                          : "Register Now"}
                      </Button>
                      <Button
                        href={EXTERNAL_LINKS.REGISTRATION_FORM}
                        target="_blank"
                        rel="noopener noreferrer"
                        variant="secondary"
                        size="medium"
                      >
                        Ask Questions
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {selectedVariant === "sidebar-pattern" && (
            <div className="course-detail-experiment__variant">
              <h2 className="course-detail-experiment__variant-title">
                Sidebar Pattern Design
              </h2>
              <p className="course-detail-experiment__variant-description">
                Uses a sidebar with honeycomb pattern for course info, creating
                a unique layout that separates content from course details.
              </p>

              <div className="course-detail-sidebar">
                <nav className="course-detail-sidebar__breadcrumb">
                  <Link to={ROUTES.HOME}>Home</Link>
                  <span>›</span>
                  <Link to={ROUTES.COURSES}>Courses</Link>
                  <span>›</span>
                  <span>{course.title}</span>
                </nav>

                <div className="course-detail-sidebar__layout">
                  <aside className="course-detail-sidebar__sidebar">
                    <div className="course-detail-sidebar__pattern"></div>
                    <div className="course-detail-sidebar__sidebar-content">
                      <div className="course-detail-sidebar__category">
                        <span className="course-detail-sidebar__category-icon">
                          {categoryIcon}
                        </span>
                        <span className="course-detail-sidebar__category-text">
                          {course.category}
                        </span>
                      </div>

                      <div className="course-detail-sidebar__info">
                        <div className="course-detail-sidebar__info-item">
                          <span className="course-detail-sidebar__info-label">
                            Start:
                          </span>
                          <span className="course-detail-sidebar__info-value">
                            {course.startDate}
                          </span>
                        </div>
                        <div className="course-detail-sidebar__info-item">
                          <span className="course-detail-sidebar__info-label">
                            Schedule:
                          </span>
                          <span className="course-detail-sidebar__info-value">
                            {course.schedule
                              ? `${course.schedule.daysOfWeek.join(", ")} at ${
                                  course.schedule.time
                                }`
                              : "Schedule TBD"}
                          </span>
                        </div>
                        {course.price && (
                          <div className="course-detail-sidebar__info-item course-detail-sidebar__info-item--price">
                            <span className="course-detail-sidebar__info-label">
                              Fee:
                            </span>
                            <span className="course-detail-sidebar__info-value">
                              {formatPrice(course.price)}
                            </span>
                          </div>
                        )}
                      </div>

                      <div className="course-detail-sidebar__actions">
                        <Button
                          variant="primary"
                          size="large"
                          onClick={() => setShowRegistrationForm(true)}
                        >
                          {capacity?.status === "full"
                            ? "Join Waitlist"
                            : "Register Now"}
                        </Button>
                        <Button
                          href={EXTERNAL_LINKS.REGISTRATION_FORM}
                          target="_blank"
                          rel="noopener noreferrer"
                          variant="secondary"
                          size="medium"
                        >
                          Ask Questions
                        </Button>
                      </div>
                    </div>
                  </aside>

                  <main className="course-detail-sidebar__main">
                    <h1 className="course-detail-sidebar__title">
                      {course.title}
                    </h1>
                    <p className="course-detail-sidebar__description">
                      {course.description}
                    </p>
                  </main>
                </div>
              </div>
            </div>
          )}

          {selectedVariant === "minimal-accent" && (
            <div className="course-detail-experiment__variant">
              <h2 className="course-detail-experiment__variant-title">
                Minimal Accent Design
              </h2>
              <p className="course-detail-experiment__variant-description">
                Clean, minimal approach with subtle honeycomb accents used
                sparingly for visual interest without overwhelming the content.
              </p>

              <div className="course-detail-minimal">
                <nav className="course-detail-minimal__breadcrumb">
                  <Link to={ROUTES.HOME}>Home</Link>
                  <span>›</span>
                  <Link to={ROUTES.COURSES}>Courses</Link>
                  <span>›</span>
                  <span>{course.title}</span>
                </nav>

                <div className="course-detail-minimal__header">
                  <div className="course-detail-minimal__category">
                    <span className="course-detail-minimal__category-icon">
                      {categoryIcon}
                    </span>
                    <span className="course-detail-minimal__category-text">
                      {course.category}
                    </span>
                  </div>
                  <h1 className="course-detail-minimal__title">
                    {course.title}
                  </h1>
                  <p className="course-detail-minimal__description">
                    {course.description}
                  </p>
                </div>

                <div className="course-detail-minimal__content">
                  <div className="course-detail-minimal__info-card">
                    <div className="course-detail-minimal__info-pattern"></div>
                    <div className="course-detail-minimal__info-content">
                      <h3>Course Details</h3>
                      <div className="course-detail-minimal__info-grid">
                        <div className="course-detail-minimal__info-item">
                          <span className="course-detail-minimal__info-label">
                            Start Date:
                          </span>
                          <span className="course-detail-minimal__info-value">
                            {course.startDate}
                          </span>
                        </div>
                        <div className="course-detail-minimal__info-item">
                          <span className="course-detail-minimal__info-label">
                            Schedule:
                          </span>
                          <span className="course-detail-minimal__info-value">
                            {course.schedule
                              ? `${course.schedule.daysOfWeek.join(", ")} at ${
                                  course.schedule.time
                                }`
                              : "Schedule TBD"}
                          </span>
                        </div>
                        {course.price && (
                          <div className="course-detail-minimal__info-item course-detail-minimal__info-item--price">
                            <span className="course-detail-minimal__info-label">
                              Course Fee:
                            </span>
                            <span className="course-detail-minimal__info-value">
                              {formatPrice(course.price)}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="course-detail-minimal__actions">
                    <Button
                      variant="primary"
                      size="large"
                      onClick={() => setShowRegistrationForm(true)}
                    >
                      {capacity?.status === "full"
                        ? "Join Waitlist"
                        : "Register Now"}
                    </Button>
                    <Button
                      href={EXTERNAL_LINKS.REGISTRATION_FORM}
                      target="_blank"
                      rel="noopener noreferrer"
                      variant="secondary"
                      size="medium"
                    >
                      Ask Questions
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {showRegistrationForm && (
          <RegistrationForm
            courseTitle={course.title}
            coursePrice={course.price || "Contact for pricing"}
            onSubmit={handleRegistrationSubmit}
            onCancel={handleRegistrationCancel}
            isSubmitting={isSubmitting}
            isWaitlist={capacity?.status === "full"}
          />
        )}
      </div>
    </ErrorBoundary>
  );
};

export default CourseDetailExperiment;
