import React, { useState, useEffect } from "react";
import InstructorLogin from "../../components/instructor/InstructorLogin";
import InstructorDashboard from "../../components/instructor/InstructorDashboard";
import {
  isInstructorAuthenticated,
  onInstructorAuthStateChanged,
} from "../../services/instructorService";
import { usePerformance } from "../../hooks";

const Instructor: React.FC = () => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Performance monitoring
  usePerformance({
    componentName: "Instructor Portal",
    threshold: 100,
  });

  useEffect(() => {
    // Check initial auth state
    const checkAuth = () => {
      const authenticated = isInstructorAuthenticated();
      setIsAuthenticated(authenticated);
      setIsLoading(false);
    };

    // Listen for auth state changes
    const unsubscribe = onInstructorAuthStateChanged((user) => {
      setIsAuthenticated(!!user);
      setIsLoading(false);
    });

    checkAuth();

    return unsubscribe;
  }, []);

  const handleLoginSuccess = () => {
    setIsAuthenticated(true);
  };

  const handleLogout = () => {
    setIsAuthenticated(false);
  };

  // Show loading state while checking authentication
  if (isLoading) {
    return (
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          minHeight: "100vh",
          background: "#f8f9fa",
        }}
      >
        <div
          style={{
            textAlign: "center",
            color: "#6b7280",
          }}
        >
          <div
            style={{
              width: "40px",
              height: "40px",
              border: "3px solid #e5e7eb",
              borderTop: "3px solid #0a2d69",
              borderRadius: "50%",
              animation: "instructorSpin 1s linear infinite",
              margin: "0 auto 1rem",
            }}
          ></div>
          <p>Loading instructor portal...</p>
        </div>
      </div>
    );
  }

  // Show login if not authenticated
  if (!isAuthenticated) {
    return <InstructorLogin onLoginSuccess={handleLoginSuccess} />;
  }

  // Show dashboard if authenticated
  return <InstructorDashboard onLogout={handleLogout} />;
};

export default Instructor;
