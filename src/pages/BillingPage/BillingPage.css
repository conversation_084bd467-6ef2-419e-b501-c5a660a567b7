.billing-page {
  min-height: 100vh;
  background: #f8fafc;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    sans-serif;
}

.billing-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

/* Subscription Status Section */
.subscription-status {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e5e9;
}

.subscription-status h3 {
  margin: 0 0 16px 0;
  color: #0a2d69;
  font-size: 18px;
  font-weight: 600;
}

.subscription-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.subscription-details p {
  margin: 0;
  font-size: 14px;
  color: #5a6c7d;
}

.subscription-details strong {
  color: #0a2d69;
  font-weight: 600;
}

.billing-header {
  text-align: center;
  margin-bottom: 3rem;
  padding: 2rem 0;
  background: linear-gradient(135deg, #0a2d69 0%, #1e40af 100%);
  color: white;
  margin: 0 -1rem 3rem -1rem;
  border-radius: 0 0 16px 16px;
}

.billing-header h1 {
  font-size: 2.5rem;
  color: white;
  margin-bottom: 0.5rem;
  font-weight: 700;
}

.billing-header p {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 1rem;
}

.location-info {
  display: inline-block;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  font-weight: 600;
  border: 2px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
  margin-bottom: 1.5rem;
}

.location-selector {
  margin-top: 1.5rem;
}

.location-selector label {
  display: block;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 600;
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
}

.location-select {
  padding: 0.75rem 1rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.9);
  color: #0a2d69;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 200px;
}

.location-select:focus {
  outline: none;
  border-color: white;
  background: white;
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.3);
}

.pricing-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.pricing-card {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 2px solid #e5e7eb;
  transition: all 0.3s ease;
  position: relative;
}

.pricing-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.pricing-card.selected {
  border-color: #0a2d69;
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(10, 45, 105, 0.2);
}

.plan-header {
  text-align: center;
  margin-bottom: 2rem;
  position: relative;
}

.savings-badge {
  position: absolute;
  top: -1rem;
  right: -1rem;
  background: #10b981;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  transform: rotate(15deg);
}

.plan-header h3 {
  font-size: 1.5rem;
  color: #0a2d69;
  margin-bottom: 1rem;
  font-weight: 600;
}

.price {
  display: flex;
  align-items: baseline;
  justify-content: center;
  margin-bottom: 0.5rem;
}

.currency {
  font-size: 1.5rem;
  color: #6b7280;
  margin-right: 0.25rem;
}

.amount {
  font-size: 3rem;
  font-weight: 700;
  color: #0a2d69;
}

.period {
  font-size: 1.2rem;
  color: #6b7280;
  margin-left: 0.25rem;
}

.price-breakdown {
  font-size: 0.9rem;
  color: #6b7280;
  margin-bottom: 1rem;
}

.plan-features {
  list-style: none;
  padding: 0;
  margin: 0 0 2rem 0;
}

.plan-features li {
  padding: 0.75rem 0;
  font-size: 1rem;
  color: #374151;
  border-bottom: 1px solid #f3f4f6;
}

.plan-features li:last-child {
  border-bottom: none;
}

.plan-button {
  width: 100%;
  padding: 1rem 2rem;
  border: 2px solid #0a2d69;
  background: white;
  color: #0a2d69;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.plan-button:hover {
  background: #0a2d69;
  color: white;
}

.plan-button.selected {
  background: #0a2d69;
  color: white;
}

.billing-actions {
  text-align: center;
}

.subscribe-button {
  background: linear-gradient(135deg, #0a2d69 0%, #1e40af 100%);
  color: white;
  border: none;
  padding: 1.25rem 3rem;
  border-radius: 12px;
  font-size: 1.2rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 2rem;
  box-shadow: 0 4px 15px rgba(10, 45, 105, 0.3);
}

.subscribe-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(10, 45, 105, 0.4);
}

.subscribe-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.billing-notes {
  color: #6b7280;
  font-size: 0.9rem;
}

.billing-notes p {
  margin: 0.25rem 0;
}

.loading-state {
  text-align: center;
  padding: 4rem 2rem;
}

.loading-state h2 {
  color: #0a2d69;
  font-size: 1.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .billing-page {
    padding: 1rem 0.5rem;
  }

  .billing-header h1 {
    font-size: 2rem;
  }

  .pricing-cards {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .pricing-card {
    padding: 1.5rem;
  }

  .amount {
    font-size: 2.5rem;
  }

  .subscribe-button {
    padding: 1rem 2rem;
    font-size: 1.1rem;
  }
}
