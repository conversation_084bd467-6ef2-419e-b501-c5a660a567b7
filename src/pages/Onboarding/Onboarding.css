.onboarding {
  min-height: 100vh;
  background: linear-gradient(135deg, #0a2d69 0%, #1e4a8c 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.onboarding__container {
  background: white;
  border-radius: 16px;
  padding: 3rem;
  width: 100%;
  max-width: 600px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  text-align: center;
}

.onboarding__header {
  margin-bottom: 3rem;
}

.onboarding__logo {
  font-size: 1.5rem;
  font-weight: bold;
  color: #0a2d69;
  margin-bottom: 2rem;
}

.onboarding__progress {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #e2e8f0;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #0a2d69 0%, #1e4a8c 100%);
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 0.9rem;
  color: #666;
  font-weight: 500;
}

.onboarding__content {
  margin: 3rem 0;
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.onboarding__step {
  width: 100%;
}

.onboarding__icon {
  font-size: 4rem;
  margin-bottom: 1.5rem;
}

.onboarding__step h2 {
  font-size: 2rem;
  color: #1a1a1a;
  margin-bottom: 1rem;
  line-height: 1.2;
}

.onboarding__step p {
  font-size: 1.1rem;
  color: #666;
  line-height: 1.6;
  margin-bottom: 2rem;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

.onboarding__features {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  max-width: 400px;
  margin: 0 auto;
}

.feature {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
  text-align: left;
}

.feature__icon {
  font-size: 1.5rem;
}

.onboarding__info {
  background: #f8fafc;
  border-radius: 12px;
  padding: 2rem;
  text-align: left;
  max-width: 400px;
  margin: 0 auto;
}

.info-item {
  margin-bottom: 1rem;
  font-size: 1rem;
  color: #333;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item strong {
  color: #0a2d69;
  display: inline-block;
  min-width: 120px;
}

.onboarding__next-steps {
  text-align: left;
  max-width: 500px;
  margin: 0 auto;
}

.next-step {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
}

.next-step:last-child {
  margin-bottom: 0;
}

.step-number {
  background: #0a2d69;
  color: white;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 0.9rem;
  flex-shrink: 0;
}

.next-step strong {
  color: #0a2d69;
  font-size: 1rem;
  margin-bottom: 0.25rem;
  display: block;
}

.next-step p {
  color: #666;
  font-size: 0.9rem;
  margin: 0;
  line-height: 1.4;
}

.onboarding__actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

/* Responsive Design */
@media (max-width: 768px) {
  .onboarding {
    padding: 1rem;
  }

  .onboarding__container {
    padding: 2rem;
  }

  .onboarding__features {
    grid-template-columns: 1fr;
  }

  .onboarding__step h2 {
    font-size: 1.5rem;
  }

  .onboarding__icon {
    font-size: 3rem;
  }

  .onboarding__actions {
    flex-direction: column;
    align-items: center;
  }

  .onboarding__actions .btn {
    width: 100%;
    max-width: 300px;
  }
}
