import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useTenant } from "../../contexts/TenantContext";
import Button from "../../components/ui/Button";
import "./Onboarding.css";

const Onboarding: React.FC = () => {
  const navigate = useNavigate();
  const { userProfile } = useTenant();
  const [currentStep, setCurrentStep] = useState(1);
  const [isCreatingSampleData, setIsCreatingSampleData] = useState(false);

  const handleSkipToApp = () => {
    navigate("/dashboard");
  };

  const handleNextStep = () => {
    if (currentStep < 3) {
      setCurrentStep(currentStep + 1);
    } else {
      navigate("/dashboard");
    }
  };

  const handleCreateSampleData = async () => {
    setIsCreatingSampleData(true);
    try {
      // We'll implement sample data creation later
      console.log("Creating sample data...");
      await new Promise((resolve) => setTimeout(resolve, 2000)); // Simulate API call
      navigate("/dashboard");
    } catch (error) {
      console.error("Error creating sample data:", error);
      setIsCreatingSampleData(false);
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="onboarding__step">
            <div className="onboarding__icon">🎉</div>
            <h2>Welcome to AdminBuddy!</h2>
            <p>
              Your account has been created successfully. You now have a 14-day
              free trial to explore all of AdminBuddy's features.
            </p>
            <div className="onboarding__features">
              <div className="feature">
                <span className="feature__icon">📱</span>
                <span>Shared tablet interface</span>
              </div>
              <div className="feature">
                <span className="feature__icon">🔄</span>
                <span>Recurring task routines</span>
              </div>
              <div className="feature">
                <span className="feature__icon">👥</span>
                <span>Team management</span>
              </div>
              <div className="feature">
                <span className="feature__icon">📊</span>
                <span>Progress tracking</span>
              </div>
            </div>
          </div>
        );

      case 2:
        return (
          <div className="onboarding__step">
            <div className="onboarding__icon">🏢</div>
            <h2>Your Business Setup</h2>
            <p>
              AdminBuddy has automatically created your business workspace. You
              can customize everything later in the dashboard.
            </p>
            <div className="onboarding__info">
              <div className="info-item">
                <strong>Business Owner:</strong> {userProfile?.email}
              </div>
              <div className="info-item">
                <strong>Company ID:</strong>{" "}
                {userProfile?.tenantId?.split("-")[1] || "Loading..."}
              </div>
              <div className="info-item">
                <strong>Trial Expires:</strong>{" "}
                {new Date(
                  Date.now() + 14 * 24 * 60 * 60 * 1000
                ).toLocaleDateString()}
              </div>
            </div>
          </div>
        );

      case 3:
        return (
          <div className="onboarding__step">
            <div className="onboarding__icon">🚀</div>
            <h2>Ready to Get Started!</h2>
            <p>
              Your AdminBuddy workspace is ready. Choose how you'd like to
              begin:
            </p>

            <div className="onboarding__setup-options">
              <div className="setup-option">
                <div className="setup-option__header">
                  <span className="setup-option__icon">🏗️</span>
                  <h3>Start from Scratch</h3>
                </div>
                <p>
                  Set up your locations, routines, and tasks manually for full
                  control
                </p>
                <div className="setup-steps">
                  <div className="setup-step">1. Add your locations</div>
                  <div className="setup-step">2. Create task routines</div>
                  <div className="setup-step">3. Set up your kiosk</div>
                </div>
              </div>

              <div className="setup-option setup-option--recommended">
                <div className="setup-option__header">
                  <span className="setup-option__icon">⚡</span>
                  <h3>Quick Start with Sample Data</h3>
                  <span className="recommended-badge">Recommended</span>
                </div>
                <p>
                  Get started immediately with pre-built locations, routines,
                  and tasks
                </p>
                <div className="setup-steps">
                  <div className="setup-step">
                    ✓ Sample coffee shop location
                  </div>
                  <div className="setup-step">✓ Opening & closing routines</div>
                  <div className="setup-step">
                    ✓ Ready-to-use task templates
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="onboarding">
      <div className="onboarding__container">
        <div className="onboarding__header">
          <div className="onboarding__logo">AdminBuddy</div>
          <div className="onboarding__progress">
            <div className="progress-bar">
              <div
                className="progress-fill"
                style={{ width: `${(currentStep / 3) * 100}%` }}
              />
            </div>
            <span className="progress-text">Step {currentStep} of 3</span>
          </div>
        </div>

        <div className="onboarding__content">{renderStep()}</div>

        <div className="onboarding__actions">
          {currentStep < 3 ? (
            <>
              <Button variant="secondary" onClick={handleSkipToApp}>
                Skip to Dashboard
              </Button>
              <Button variant="primary" onClick={handleNextStep}>
                Continue
              </Button>
            </>
          ) : (
            <>
              <Button variant="secondary" onClick={handleNextStep}>
                Start from Scratch
              </Button>
              <Button
                variant="primary"
                onClick={handleCreateSampleData}
                disabled={isCreatingSampleData}
              >
                {isCreatingSampleData
                  ? "Creating Sample Data..."
                  : "Quick Start with Sample Data"}
              </Button>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default Onboarding;
