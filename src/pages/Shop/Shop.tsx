import React from "react";
import { IMAGES, EXTERNAL_LINKS } from "../../constants";
import Button from "../../components/ui/Button";
import ErrorBoundary from "../../components/ui/ErrorBoundary";
import { usePerformance } from "../../hooks";
import "./Shop.css";

const Shop: React.FC = () => {
  // Add performance monitoring
  usePerformance({
    componentName: "Shop",
    threshold: 20, // Simple page, should be fast
  });

  return (
    <ErrorBoundary>
      <div className="shop">
        <div className="shop__image-section">
          <img
            src={IMAGES.MERCH}
            alt="Bit By Bit Merch"
            className="shop__image"
          />
        </div>
        <div className="shop__text-section">
          <h2>Shop Online!</h2>
          <p>
            Check out our online store with items for the whole family! Hoodies,
            mugs, stickers, and more.
          </p>
          <Button
            href={EXTERNAL_LINKS.MERCH_STORE}
            target="_blank"
            rel="noopener noreferrer"
          >
            Visit Shop
          </Button>
        </div>
      </div>
    </ErrorBoundary>
  );
};

export default Shop;
