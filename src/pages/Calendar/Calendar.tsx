import React from "react";
import CustomerSessionCalendar from "../../components/customer/CustomerSessionCalendar/CustomerSessionCalendar";
import { usePerformance } from "../../hooks";
import ErrorBoundary from "../../components/ui/ErrorBoundary";
import "./Calendar.css";

const Calendar: React.FC = () => {
  // Add performance monitoring
  usePerformance({
    componentName: "Calendar",
    threshold: 40, // Allow more time for data loading
  });

  return (
    <ErrorBoundary>
      <div className="calendar">
        <section className="calendar__header">
          <div className="calendar__header-content">
            <div className="calendar__title-section">
              <h2>Session Calendar</h2>
              <p className="calendar__subtitle">
                Find the perfect session timing that fits your schedule and
                learning goals.
              </p>
              <p>
                View all scheduled sessions for our active courses. All times
                are Pacific Time. Click on any session to learn more and
                register.
              </p>
            </div>
          </div>
        </section>

        <div className="calendar__content">
          <CustomerSessionCalendar viewMode="calendar" />
        </div>
      </div>
    </ErrorBoundary>
  );
};

export default Calendar;
