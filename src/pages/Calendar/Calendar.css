.calendar {
  /* Remove padding to allow full-width header */
}

.calendar__content {
  padding: 1rem;
  max-width: 1400px;
  margin: 0 auto;
}

@media (min-width: 768px) {
  .calendar__content {
    padding: 2rem;
  }
}

.calendar__header {
  background: linear-gradient(135deg, #0a2d69 0%, #1e4a8c 100%);
  color: white;
  padding: 4rem 2rem;
  margin-bottom: 0;
}

.calendar__header-content {
  max-width: 1200px;
  margin: 0 auto;
  text-align: center;
}

.calendar__title-section {
  text-align: center;
}

@media (max-width: 767px) {
  .calendar__header {
    padding: 2rem 1rem;
  }
}

@media (min-width: 768px) {
  .calendar__header {
    margin-bottom: 4rem;
  }
}

.calendar__header h2 {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: white;
}

@media (max-width: 767px) {
  .calendar__header h2 {
    font-size: 2rem;
  }
}

.calendar__subtitle {
  font-size: 1.4rem;
  color: #ffd46f;
  margin-bottom: 1.5rem;
  font-weight: 500;
}

@media (max-width: 767px) {
  .calendar__subtitle {
    font-size: 1.2rem;
  }
}

.calendar__header p {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #e8f4f8;
  margin: 0;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
}

.calendar__header .calendar__subtitle {
  color: #ffd46f !important;
}

@media (max-width: 767px) {
  .calendar__header p {
    font-size: 1rem;
  }
}

.calendar__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.calendar__loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

.calendar__loading p {
  margin-top: 1rem;
  color: #666;
  font-size: 1.1rem;
}

.calendar__error {
  text-align: center;
  padding: 3rem 2rem;
  background-color: white;
  border-radius: 12px;
  border-left: 4px solid #dc3545;
  margin: 2rem;
}

.calendar__error h2 {
  color: #dc3545;
  margin-bottom: 1rem;
}

.calendar__error p {
  color: #666;
}
