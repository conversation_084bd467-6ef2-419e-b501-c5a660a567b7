import React, { useState, useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import {
  isSignInWithEmailLink,
  signInWithEmailLink,
  createUserWithEmailAndPassword,
} from "firebase/auth";
import { doc, getDoc, updateDoc, setDoc, deleteDoc } from "firebase/firestore";
import { auth, db } from "../../config/firebase";
import Button from "../../components/ui/Button";
import Spinner from "../../components/ui/Spinner";
import "./InstructorSetup.css";

const InstructorSetup: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [instructorData, setInstructorData] = useState<any>(null);
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [settingUp, setSettingUp] = useState(false);

  const instructorId = searchParams.get("instructorId");

  useEffect(() => {
    const handleEmailLink = async () => {
      try {
        // Get instructor data
        if (!instructorId) {
          setError("Missing instructor information");
          setLoading(false);
          return;
        }

        console.log("📋 Loading instructor data for ID:", instructorId);
        const instructorRef = doc(db, "instructors", instructorId);
        const instructorDoc = await getDoc(instructorRef);

        if (!instructorDoc.exists()) {
          setError("Instructor invitation not found");
          setLoading(false);
          return;
        }

        const data = instructorDoc.data();
        if (data.status !== "invited") {
          setError(
            "This invitation has already been used or is no longer valid"
          );
          setLoading(false);
          return;
        }

        setInstructorData(data);
        setLoading(false);
      } catch (error: any) {
        console.error("Error handling email link:", error);
        setError("Failed to process invitation link");
        setLoading(false);
      }
    };

    handleEmailLink();
  }, [instructorId]);

  const handleSetupAccount = async () => {
    if (!password || !confirmPassword) {
      setError("Please fill in all fields");
      return;
    }

    if (password !== confirmPassword) {
      setError("Passwords do not match");
      return;
    }

    if (password.length < 6) {
      setError("Password must be at least 6 characters");
      return;
    }

    setSettingUp(true);
    setError(null);

    try {
      console.log("🔐 Setting up instructor account...");

      // Create Firebase Auth user with the password they set
      console.log("🔐 Creating Firebase Auth user...");
      const userCredential = await createUserWithEmailAndPassword(
        auth,
        instructorData.email,
        password
      );
      const firebaseUserId = userCredential.user.uid;

      console.log("✅ Firebase Auth user created, user ID:", firebaseUserId);

      // Update the instructor document with the real Firebase user ID and active status
      console.log("🔄 Updating instructor record...");

      // Create new document with Firebase user ID
      const updatedInstructorData = {
        ...instructorData,
        id: firebaseUserId,
        status: "active",
        activatedAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      await setDoc(
        doc(db, "instructors", firebaseUserId),
        updatedInstructorData
      );

      // Delete the old temporary document
      await deleteDoc(doc(db, "instructors", instructorId!));

      console.log("✅ Instructor account setup completed successfully");

      // Redirect to instructor portal
      navigate("/instructor");
    } catch (error: any) {
      console.error("❌ Error setting up account:", error);
      setError(error.message || "Failed to set up account");
    } finally {
      setSettingUp(false);
    }
  };

  if (loading) {
    return (
      <div className="instructor-setup">
        <div className="instructor-setup__container">
          <Spinner size="large" />
          <p>Loading invitation...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="instructor-setup">
        <div className="instructor-setup__container">
          <div className="instructor-setup__error">
            <h2>Setup Error</h2>
            <p>{error}</p>
            <Button onClick={() => navigate("/instructor")}>
              Go to Instructor Login
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="instructor-setup">
      <div className="instructor-setup__container">
        <div className="instructor-setup__header">
          <h1>Complete Your Instructor Account Setup</h1>
          <p>
            Welcome, {instructorData.name}! Please set your password to complete
            your account setup.
          </p>
        </div>

        <div className="instructor-setup__form">
          <div className="instructor-setup__field">
            <label>Email</label>
            <input
              type="email"
              value={instructorData.email}
              disabled
              className="instructor-setup__disabled-field"
            />
          </div>

          <div className="instructor-setup__field">
            <label>Full Name</label>
            <input
              type="text"
              value={instructorData.name}
              disabled
              className="instructor-setup__disabled-field"
            />
          </div>

          <div className="instructor-setup__field">
            <label>Password *</label>
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="Enter your password (min 6 characters)"
            />
          </div>

          <div className="instructor-setup__field">
            <label>Confirm Password *</label>
            <input
              type="password"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              placeholder="Confirm your password"
            />
          </div>

          {error && (
            <div className="instructor-setup__error-message">{error}</div>
          )}

          <div className="instructor-setup__actions">
            <Button
              onClick={handleSetupAccount}
              loading={settingUp}
              disabled={settingUp}
            >
              {settingUp ? "Setting up account..." : "Complete Setup"}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InstructorSetup;
