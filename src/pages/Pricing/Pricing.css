.pricing__hero {
  background: linear-gradient(135deg, #0a2d69 0%, #1e4a8c 100%);
  color: white;
  padding: 3rem 2rem;
  text-align: center;
}

.pricing__hero-content {
  max-width: 600px;
  margin: 0 auto;
}

.pricing__hero h1 {
  font-size: 3rem;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.pricing__hero p {
  font-size: 1.2rem;
  color: #e8f4f8;
  margin: 0;
}

.pricing__plans {
  padding: 4rem 2rem;
  background: #f8fafc;
}

.pricing__container {
  max-width: 800px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
}

.pricing__plan {
  background: white;
  padding: 3rem 2rem;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  position: relative;
}

.pricing__plan--featured {
  border: 2px solid #0a2d69;
  transform: scale(1.05);
}

.pricing__badge {
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background: #0a2d69;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
}

.pricing__plan h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: #1a1a1a;
}

.pricing__price {
  margin-bottom: 2rem;
}

.pricing__amount {
  font-size: 3rem;
  font-weight: 700;
  color: #0a2d69;
}

.pricing__period {
  font-size: 1rem;
  color: #666;
}

.pricing__features {
  list-style: none;
  padding: 0;
  margin: 0 0 2rem 0;
}

.pricing__features li {
  padding: 0.5rem 0;
  color: #666;
  position: relative;
  padding-left: 1.5rem;
}

.pricing__features li::before {
  content: "✓";
  position: absolute;
  left: 0;
  color: #22c55e;
  font-weight: bold;
}

.pricing__savings {
  position: absolute;
  top: -12px;
  right: 1rem;
  background: #22c55e;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.pricing__yearly-note {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 1.5rem;
  text-align: center;
}

.pricing__trial-note {
  font-size: 0.8rem;
  color: #666;
  margin-top: 1rem;
  text-align: center;
  font-style: italic;
}

.pricing__enterprise {
  max-width: 600px;
  margin: 4rem auto 0;
  text-align: center;
  padding: 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.pricing__enterprise-content h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: #1a1a1a;
}

.pricing__enterprise-content p {
  color: #666;
  margin-bottom: 2rem;
  line-height: 1.6;
}

@media (max-width: 768px) {
  .pricing__hero {
    padding: 2rem 1rem;
  }

  .pricing__hero h1 {
    font-size: 2rem;
  }

  .pricing__plans {
    padding: 3rem 1rem;
  }

  .pricing__container {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .pricing__enterprise {
    margin: 2rem auto 0;
    padding: 1.5rem;
  }

  .pricing__plan--featured {
    transform: none;
  }
}
