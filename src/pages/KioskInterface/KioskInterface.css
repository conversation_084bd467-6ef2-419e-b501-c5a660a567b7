/* Kiosk Interface - Optimized for Tablet Use */
.kiosk {
  min-height: 100vh;
  background: #f8fafc;
  padding: 1rem;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  user-select: none; /* Prevent text selection on tablet */
  -webkit-touch-callout: none; /* Disable callout on iOS */
}

/* Header */
.kiosk__header {
  background: linear-gradient(135deg, #0a2d69 0%, #1e4a8c 100%);
  color: white;
  padding: 1.5rem 2rem;
  border-radius: 12px;
  margin-bottom: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 4px 20px rgba(10, 45, 105, 0.2);
  position: relative;
}

/* Compact Header */
.kiosk__header-compact {
  background: linear-gradient(135deg, #0a2d69 0%, #1e4a8c 100%);
  color: white;
  padding: 1rem 1.5rem;
  border-radius: 12px;
  margin-bottom: 1rem;
  box-shadow: 0 4px 20px rgba(10, 45, 105, 0.2);
  position: relative;
}

.kiosk__header-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.kiosk__location-compact {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.kiosk__location-compact h1 {
  font-size: 1.5rem;
  margin: 0;
  font-weight: 700;
}

.kiosk__time-compact {
  font-size: 1.25rem;
  font-weight: 600;
  color: #e8f4f8;
}

.kiosk__header-buttons {
  position: absolute;
  top: 1rem;
  right: 1rem;
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.kiosk__logout-btn,
.kiosk__dashboard-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

.kiosk__logout-btn:hover,
.kiosk__dashboard-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

.kiosk__dashboard-btn {
  background: rgba(76, 175, 80, 0.2);
  border-color: rgba(76, 175, 80, 0.3);
}

.kiosk__dashboard-btn:hover {
  background: rgba(76, 175, 80, 0.3);
  border-color: rgba(76, 175, 80, 0.4);
}

/* Announcement Button in Header */
.kiosk__announcement-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
  border-radius: 50%;
  width: 44px;
  height: 44px;
  cursor: pointer;
  font-size: 1.2rem;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
  margin-right: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.kiosk__announcement-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* Manual Refresh Button in Header */
.kiosk__refresh-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
  border-radius: 50%;
  width: 44px;
  height: 44px;
  cursor: pointer;
  font-size: 1.2rem;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
  margin-right: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.kiosk__refresh-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-1px) rotate(180deg);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.kiosk__refresh-btn:active {
  transform: translateY(0) rotate(360deg);
}

/* ==================== TEAM ANNOUNCEMENTS ==================== */

.kiosk__announcements {
  background: white;
  border-radius: 12px;
  margin: 1rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 2px solid #e1e5e9;
  overflow: hidden;
}

.kiosk__announcements-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1.5rem;
  background: #f8f9fa;
  border-bottom: 3px solid #0a2d69;
  color: #0a2d69;
}

.kiosk__announcements-title {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.kiosk__announcements-edit-btn {
  background: #f8f9fa;
  border: 2px solid #0a2d69;
  color: #0a2d69;
  border-radius: 8px;
  padding: 8px 12px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.kiosk__announcements-edit-btn:hover {
  background: #e9ecef;
  border-color: #1e4a8c;
  color: #1e4a8c;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.kiosk__announcements-content {
  padding: 1.5rem;
}

.kiosk__announcements-display {
  min-height: 60px;
}

.kiosk__announcements-text {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #2c3e50;
}

.kiosk__announcements-text h1,
.kiosk__announcements-text h2,
.kiosk__announcements-text h3,
.kiosk__announcements-text h4,
.kiosk__announcements-text h5,
.kiosk__announcements-text h6 {
  margin: 1rem 0 0.5rem 0;
  color: #0a2d69;
  font-weight: 600;
}

.kiosk__announcements-text p {
  margin: 0.75rem 0;
}

.kiosk__announcements-text ul,
.kiosk__announcements-text ol {
  margin: 0.75rem 0;
  padding-left: 1.5rem;
}

.kiosk__announcements-text li {
  margin: 0.25rem 0;
}

.kiosk__announcements-text strong {
  font-weight: 600;
  color: #0a2d69;
}

.kiosk__announcements-empty {
  text-align: center;
  color: #666;
  font-style: italic;
  padding: 1rem 0;
}

.kiosk__announcements-hint {
  font-size: 0.9rem;
  color: #999;
  margin-top: 0.5rem;
}

.kiosk__announcements-meta {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e1e5e9;
  font-size: 0.85rem;
  color: #666;
  text-align: right;
}

.kiosk__announcements-editor {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.kiosk__announcements-actions {
  display: flex;
  gap: 0.75rem;
  justify-content: flex-end;
}

.kiosk__announcements-btn {
  padding: 10px 20px;
  border-radius: 8px;
  border: none;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.kiosk__announcements-btn--save {
  background: #28a745;
  color: white;
}

.kiosk__announcements-btn--save:hover {
  background: #218838;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

.kiosk__announcements-btn--cancel {
  background: #6c757d;
  color: white;
}

.kiosk__announcements-btn--cancel:hover {
  background: #5a6268;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(108, 117, 125, 0.3);
}

.kiosk__location h1 {
  font-size: 2rem;
  margin: 0 0 0.25rem 0;
  font-weight: 700;
}

.kiosk__location p {
  margin: 0;
  color: #e8f4f8;
  font-size: 1rem;
}

.kiosk__time {
  text-align: right;
}

.kiosk__current-time {
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 0.25rem;
}

.kiosk__current-date {
  font-size: 1rem;
  color: #e8f4f8;
}

/* Progress Section */
.kiosk__progress-section {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  gap: 2rem;
  flex-wrap: wrap;
}

.kiosk__progress {
  flex: 1;
  min-width: 300px;
}

.kiosk__progress-bar {
  width: 100%;
  height: 12px;
  background: #e5e7eb;
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 0.75rem;
}

.kiosk__progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #10b981, #22c55e);
  border-radius: 6px;
  transition: width 0.3s ease;
}

.kiosk__progress-text {
  text-align: center;
  font-size: 1.1rem;
  font-weight: 600;
  color: #374151;
}

/* Compact Controls */
.kiosk__controls-compact {
  background: white;
  padding: 1rem 1.5rem;
  border-radius: 12px;
  margin-bottom: 1rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.kiosk__progress-compact {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex: 1;
  min-width: 200px;
  max-width: 400px;
}

.kiosk__progress-compact .kiosk__progress-bar {
  flex: 1;
  min-width: 150px;
  height: 8px;
  margin-bottom: 0;
}

.kiosk__progress-compact .kiosk__progress-text {
  font-size: 0.9rem;
  text-align: left;
  margin: 0;
  white-space: nowrap;
}

.kiosk__add-task-btn-compact {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  border: none;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.kiosk__add-task-btn-compact:hover {
  background: linear-gradient(135deg, #047857 0%, #065f46 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(5, 150, 105, 0.3);
}

.kiosk__end-day-btn-compact {
  background: linear-gradient(135deg, #7c3aed 0%, #5b21b6 100%);
  border: none;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.kiosk__end-day-btn-compact:hover {
  background: linear-gradient(135deg, #5b21b6 0%, #4c1d95 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(124, 58, 237, 0.3);
}

/* Filters */
.kiosk__filters {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.kiosk__filter-group {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.kiosk__view-toggle {
  display: flex;
  gap: 0.25rem;
  background: #f3f4f6;
  border-radius: 8px;
  padding: 0.25rem;
  border: 1px solid #e5e7eb;
  margin-bottom: 1rem;
}

.kiosk__view-btn {
  background: none;
  border: none;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  color: #6b7280;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 100px;
  font-weight: 500;
  min-height: 40px;
}

.kiosk__view-btn:hover {
  color: #374151;
  background: rgba(255, 255, 255, 0.5);
}

.kiosk__view-btn.active {
  background: white;
  color: #0a2d69;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.kiosk__filter-btn {
  background: #f3f4f6;
  border: 2px solid transparent;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 40px; /* Touch-friendly but more compact */
  white-space: nowrap;
}

.kiosk__filter-btn:hover {
  background: #e5e7eb;
}

.kiosk__filter-btn.active {
  background: #0a2d69;
  color: white;
  border-color: #0a2d69;
}

.kiosk__routine-filters {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.kiosk__routine-btn {
  background: #f9fafb;
  border: 1px solid #d1d5db;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 40px; /* Touch-friendly */
}

.kiosk__routine-btn:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.kiosk__routine-btn.active {
  background: #dbeafe;
  border-color: #3b82f6;
  color: #1d4ed8;
}

/* Task List */
.kiosk__tasks {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* Routine Groups */
.kiosk__routine-group {
  background: white;
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.kiosk__routine-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #0a2d69;
  margin: 0 0 0.75rem 0;
  padding-bottom: 0.25rem;
  border-bottom: 1px solid #e5e7eb;
}

.kiosk__routine-title--collapsible {
  cursor: pointer;
  user-select: none;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.2s ease;
  padding: 0.75rem 1rem;
  margin: 0 0 0.75rem 0;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  border-bottom: 1px solid #e2e8f0;
}

.kiosk__routine-title--collapsible .kiosk__routine-title-content {
  flex: 1;
  text-align: left;
}

.kiosk__routine-title--collapsible:hover {
  background: #e2e8f0;
  border-color: #cbd5e1;
}

.kiosk__routine-title-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.kiosk__routine-collapse-icon {
  font-size: 0.875rem;
  color: #64748b;
  transition: transform 0.2s ease;
  width: 1rem;
  text-align: center;
}

.kiosk__routine-task-count {
  font-size: 0.875rem;
  color: #64748b;
  background: #ffffff;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-weight: 500;
  border: 1px solid #e2e8f0;
}

.kiosk__routine-tasks {
  display: grid;
  gap: 0.75rem;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
}

.kiosk__empty {
  grid-column: 1 / -1;
  text-align: center;
  padding: 3rem;
  background: white;
  border-radius: 12px;
  color: #6b7280;
}

.kiosk__empty h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
}

.kiosk__empty p {
  margin: 0;
  font-size: 1rem;
}

/* Loading State */
.kiosk__loading {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
  color: #6b7280;
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

/* Location Selector */
.location-selector {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
  width: 100%;
}

.location-btn {
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 16px;
  padding: 0;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  display: flex;
  align-items: center;
  min-height: 120px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.location-btn:hover {
  border-color: #0a2d69;
  box-shadow: 0 8px 25px rgba(10, 45, 105, 0.15);
  transform: translateY(-2px);
}

.location-btn__content {
  flex: 1;
  padding: 1.5rem;
}

.location-btn__content h3 {
  margin: 0 0 0.5rem 0;
  color: #0a2d69;
  font-size: 1.3rem;
  font-weight: 700;
}

.location-btn__content p {
  margin: 0 0 0.75rem 0;
  color: #6b7280;
  font-size: 0.95rem;
  line-height: 1.4;
}

.location-btn__meta {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.location-type {
  background: #f3f4f6;
  color: #6b7280;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.location-btn__arrow {
  padding: 1.5rem;
  color: #9ca3af;
  font-size: 1.5rem;
  font-weight: bold;
  transition: all 0.3s ease;
}

.location-btn:hover .location-btn__arrow {
  color: #0a2d69;
  transform: translateX(4px);
}

.empty-locations {
  margin-top: 2rem;
  padding: 2rem;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  color: #6b7280;
}

.empty-locations p {
  margin: 0 0 0.5rem 0;
}

.empty-locations p:last-child {
  margin-bottom: 0;
}

/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 44px; /* Touch-friendly */
}

.btn--primary {
  background: #0a2d69;
  color: white;
}

.btn--primary:hover {
  background: #1e4a8c;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(10, 45, 105, 0.3);
}

/* End Day Button */
.kiosk__end-day-btn {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 4px 15px rgba(139, 92, 246, 0.3);
  white-space: nowrap;
  flex-shrink: 0;
}

.kiosk__end-day-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(139, 92, 246, 0.4);
}

/* Task Cards */
.kiosk__task-card {
  background: white;
  border-radius: 12px;
  padding: 1.25rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 2px solid #e5e7eb;
  position: relative;
  overflow: hidden;
}

/* Clickable card styles */
.kiosk__task-card.clickable {
  cursor: pointer;
  transform: translateY(0);
}

.kiosk__task-card.clickable:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.12),
    0 4px 12px rgba(59, 130, 246, 0.15);
  border-color: #3b82f6;
  background: #fefeff;
}

.kiosk__task-card.clickable:active {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.12);
}

/* Add a subtle gradient overlay for clickable cards - use ::after for overlay */
.kiosk__task-card.clickable::after {
  content: "";
  position: absolute;
  top: 0;
  left: 4px; /* Start after the priority stripe */
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.03) 0%,
    rgba(147, 197, 253, 0.03) 100%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  z-index: 0;
}

.kiosk__task-card.clickable:hover::after {
  opacity: 1;
}

/* Remove explicit tap indicator - let the design speak for itself */

.kiosk__task-card.completed {
  background: #f0fdf4;
  border-color: #22c55e;
}

.kiosk__task-card.overdue {
  border-color: #ef4444;
  background: #fef2f2;
}

.kiosk__task-content {
  width: 100%;
  position: relative;
  z-index: 1; /* Ensure content is above the gradient overlay */
}

/* Add visual richness to task cards */
.kiosk__task-card .kiosk__task-content {
  min-height: 80px; /* Ensure cards have minimum height */
}

/* Add a priority stripe on the left */
.kiosk__task-card::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: #e5e7eb;
  transition: background-color 0.3s ease;
}

/* Priority-based left stripe colors */
.kiosk__task-card[data-priority="high"]::before {
  background: #ef4444;
}

.kiosk__task-card[data-priority="medium"]::before {
  background: #f59e0b;
}

.kiosk__task-card[data-priority="low"]::before {
  background: #10b981;
}

.kiosk__task-header {
  margin-bottom: 0.5rem;
}

.kiosk__task-title-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
  gap: 1rem;
}

.kiosk__task-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #111827;
  margin: 0;
  line-height: 1.3;
  flex: 1;
}

.kiosk__task-card.completed .kiosk__task-title {
  text-decoration: line-through;
  color: #6b7280;
}

.kiosk__task-meta {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  align-items: flex-start;
}

/* Description styling within meta */
.kiosk__task-description {
  color: #6b7280;
  font-size: 0.85rem;
  line-height: 1.4;
  font-style: italic;
  margin-top: 0.25rem;
  word-wrap: break-word;
  max-width: 100%;
}

.kiosk__task-priority {
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  flex-shrink: 0;
  align-self: flex-start;
}

.kiosk__task-routine {
  background: #f3f4f6;
  color: #374151;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
}

.kiosk__task-time {
  color: #6b7280;
  font-size: 0.75rem;
  font-weight: 500;
  white-space: nowrap;
}

.kiosk__task-time.overdue {
  color: #ef4444;
  font-weight: 600;
}

.kiosk__task-duration {
  color: #6b7280;
  font-size: 0.75rem;
  font-weight: 500;
}

.kiosk__task-note {
  background: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 4px;
  padding: 0.5rem;
  margin: 0.5rem 0;
  font-size: 0.8rem;
  color: #92400e;
  font-style: italic;
}

/* Task Context Display (Role/Routine info) */
.kiosk__task-context {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0.5rem 0;
  padding: 0.375rem 0.75rem;
  background: #f8fafc;
  border-radius: 6px;
  font-size: 0.85rem;
  border: 1px solid #e2e8f0;
}

.kiosk__task-context-label {
  font-weight: 600;
  color: #475569;
  white-space: nowrap;
}

.kiosk__task-context-value {
  color: #0a2d69;
  font-weight: 500;
  background: #dbeafe;
  padding: 0.125rem 0.5rem;
  border-radius: 4px;
  border: 1px solid #bfdbfe;
}

/* Description preview styling */
.kiosk__task-description-preview {
  background: #f8fafc !important;
  color: #475569 !important;
  border: 1px solid #e2e8f0 !important;
  font-weight: 400 !important;
  font-style: italic;
  line-height: 1.4;
  max-width: 100%;
  word-wrap: break-word;
}

/* Task Actions */
.kiosk__task-actions {
  margin-top: 0.75rem;
}

.kiosk__task-buttons {
  display: flex;
  gap: 0.5rem;
}

.kiosk__task-completed {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 0.5rem;
}

.kiosk__task-completed-by {
  color: #22c55e;
  font-size: 0.8rem;
  font-weight: 500;
  flex: 1;
}

/* New completion status styling */
.kiosk__task-completed-status {
  background: #dcfce7;
  border: 1px solid #22c55e;
  border-radius: 6px;
  padding: 0.5rem 0.75rem;
  margin-top: 0.75rem;
  font-size: 0.85rem;
  font-weight: 600;
  color: #15803d;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.kiosk__task-btn {
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.85rem;
  font-weight: 600;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 40px; /* Touch-friendly */
  white-space: nowrap;
}

.kiosk__task-btn--complete {
  background: #22c55e;
  color: white;
  flex: 1;
}

.kiosk__task-btn--complete:hover {
  background: #16a34a;
}

.kiosk__task-btn--note {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.kiosk__task-btn--note:hover {
  background: #e5e7eb;
}

.kiosk__task-btn--undo {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
  min-height: 32px;
}

.kiosk__task-btn--undo:hover {
  background: #e5e7eb;
}

/* Responsive Design */
@media (max-width: 768px) {
  .kiosk {
    padding: 0.5rem;
  }

  .kiosk__header {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .kiosk__header-compact {
    padding: 0.75rem 1rem;
  }

  .kiosk__location-compact {
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
  }

  .kiosk__location-compact h1 {
    font-size: 1.25rem;
  }

  .kiosk__time-compact {
    font-size: 1.1rem;
  }

  .kiosk__controls-compact {
    flex-direction: column;
    gap: 1rem;
    padding: 1rem;
  }

  .kiosk__progress-compact {
    justify-content: center;
    min-width: auto;
  }

  .kiosk__filter-group {
    justify-content: center;
    flex-wrap: wrap;
  }

  .kiosk__time {
    text-align: center;
  }

  .kiosk__current-time {
    font-size: 2rem;
  }

  .kiosk__progress-section {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .kiosk__routine-tasks {
    grid-template-columns: 1fr;
  }

  .kiosk__routine-filters {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .kiosk__location h1 {
    font-size: 1.5rem;
  }

  .kiosk__current-time {
    font-size: 1.75rem;
  }

  .kiosk__task-card {
    padding: 1rem;
  }

  .kiosk__task-title {
    font-size: 1.1rem;
  }
}

/* Modal Styles */
.kiosk__modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.kiosk__modal {
  background: white;
  border-radius: 12px;
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.kiosk__modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.kiosk__modal-header h3 {
  margin: 0;
  font-size: 1.25rem;
  color: #111827;
}

.kiosk__modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #6b7280;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.kiosk__modal-close:hover {
  background: #f3f4f6;
}

.kiosk__modal-content {
  padding: 1.5rem;
}

.kiosk__modal-description {
  color: #6b7280;
  margin-bottom: 1rem;
  line-height: 1.5;
}

.kiosk__modal-field {
  margin-bottom: 1rem;
}

.kiosk__modal-field label {
  display: block;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
}

.kiosk__modal-field input,
.kiosk__modal-field select,
.kiosk__modal-field textarea {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e5e7eb;
  border-radius: 6px;
  font-size: 1rem;
  font-family: inherit;
  box-sizing: border-box;
}

.kiosk__modal-field textarea {
  resize: vertical;
  min-height: 80px;
}

.kiosk__modal-field input:focus,
.kiosk__modal-field select:focus,
.kiosk__modal-field textarea:focus {
  outline: none;
  border-color: #0a2d69;
  box-shadow: 0 0 0 3px rgba(10, 45, 105, 0.1);
}

.kiosk__modal-actions {
  display: flex;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid #e5e7eb;
  justify-content: flex-end;
}

.kiosk__modal-btn {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  min-height: 48px; /* Touch-friendly */
}

.kiosk__modal-btn--primary {
  background: #0a2d69;
  color: white;
}

.kiosk__modal-btn--primary:hover {
  background: #1e4a8c;
}

.kiosk__modal-btn--secondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.kiosk__modal-btn--secondary:hover {
  background: #e5e7eb;
}

@media (max-width: 480px) {
  .kiosk__modal {
    margin: 0.5rem;
  }

  .kiosk__modal-actions {
    flex-direction: column;
  }

  .kiosk__modal-btn {
    width: 100%;
  }
}

/* Loading Spinner */
.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f4f6;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Watchlist Styles */
.kiosk__watchlist {
  margin: 1.5rem 0;
  padding: 0 1.5rem;
}

.kiosk__watchlist-title {
  color: #0a2d69;
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 0.75rem 0;
  padding-bottom: 0.25rem;
  border-bottom: 1px solid #e5e7eb;
}

.kiosk__watchlist-title--collapsible {
  cursor: pointer;
  user-select: none;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.2s ease;
  padding: 0.75rem 1rem;
  margin: 0 0 1rem 0;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  border-bottom: 1px solid #e2e8f0;
}

.kiosk__watchlist-title--collapsible .kiosk__watchlist-title-content {
  flex: 1;
  text-align: left;
}

.kiosk__watchlist-title--collapsible:hover {
  background: #e2e8f0;
  border-color: #cbd5e1;
}

.kiosk__watchlist-title-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.kiosk__watchlist-tasks {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 1rem;
}

.kiosk__watchlist-card {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  padding: 1rem;
  transition: all 0.2s ease;
}

.kiosk__watchlist-card--good {
  border-color: #10b981;
  background: linear-gradient(135deg, #f0fdf4 0%, #ffffff 100%);
}

.kiosk__watchlist-card--due-soon {
  border-color: #f59e0b;
  background: linear-gradient(135deg, #fffbeb 0%, #ffffff 100%);
}

.kiosk__watchlist-card--overdue {
  border-color: #ef4444;
  background: linear-gradient(135deg, #fef2f2 0%, #ffffff 100%);
}

.kiosk__watchlist-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.75rem;
}

.kiosk__watchlist-title h4 {
  margin: 0 0 0.25rem 0;
  color: #1e293b;
  font-size: 1.125rem;
  font-weight: 600;
}

.kiosk__watchlist-frequency {
  font-size: 0.75rem;
  color: #64748b;
  font-weight: 500;
}

.kiosk__watchlist-check-btn {
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.kiosk__watchlist-check-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.kiosk__watchlist-description {
  margin-bottom: 0.75rem;
  color: #64748b;
  font-style: italic;
  font-size: 0.875rem;
  line-height: 1.4;
}

.kiosk__watchlist-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.875rem;
}

.kiosk__watchlist-status-indicator {
  font-weight: 600;
}

.kiosk__watchlist-last-check {
  color: #64748b;
  font-size: 0.75rem;
}

/* Kiosk Modal Recent Check-ins */
.kiosk__modal-section {
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid #e2e8f0;
}

.kiosk__modal-section-title {
  margin: 0 0 0.75rem 0;
  color: #475569;
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.kiosk__recent-checkins {
  space-y: 0.5rem;
}

.kiosk__checkin-item {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 0.75rem;
  margin-bottom: 0.5rem;
}

.kiosk__checkin-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.25rem;
}

.kiosk__checkin-name {
  font-weight: 600;
  color: #1e293b;
  font-size: 0.875rem;
}

.kiosk__checkin-time {
  font-size: 0.75rem;
  color: #64748b;
}

.kiosk__checkin-notes {
  font-size: 0.8125rem;
  color: #475569;
  font-style: italic;
  margin-top: 0.25rem;
  padding: 0.5rem;
  background: #ffffff;
  border-radius: 4px;
  border-left: 3px solid #cbd5e1;
}

.kiosk__loading-checks {
  padding: 1rem;
  text-align: center;
  color: #64748b;
  font-style: italic;
}

/* Fix overlap between progress text and filter buttons - wider breakpoint */
@media (max-width: 900px) {
  .kiosk__controls-compact {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .kiosk__progress-compact {
    justify-content: center;
    order: 1;
    margin-bottom: 0.5rem;
  }

  .kiosk__filter-group {
    order: 2;
    justify-content: center;
    margin-top: 0.75rem;
  }

  .kiosk__view-toggle {
    order: 3;
    justify-content: center;
  }

  .kiosk__add-task-btn-compact,
  .kiosk__end-day-btn-compact {
    order: 4;
  }
}

/* Responsive Design Updates */
@media (max-width: 768px) {
  .kiosk__watchlist {
    padding: 0 1rem;
  }

  .kiosk__watchlist-tasks {
    grid-template-columns: 1fr;
  }

  .kiosk__watchlist-header {
    flex-direction: column;
    gap: 0.5rem;
    align-items: stretch;
  }

  .kiosk__watchlist-check-btn {
    align-self: flex-start;
  }
}

@media (max-width: 480px) {
  /* Further adjustments for very small screens */
  .kiosk__filter-group {
    flex-direction: column;
    gap: 0.5rem;
  }

  .kiosk__filter-btn {
    width: 100%;
    text-align: center;
  }

  .kiosk__progress-text {
    font-size: 0.875rem;
    white-space: nowrap;
  }
}

/* Drag and Drop Styles */
.kiosk__sections {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.kiosk__section {
  position: relative;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
  border: 2px solid transparent;
}

.kiosk__section--dragging {
  transform: rotate(2deg);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: #0a2d69;
  background: #f8fafc;
  z-index: 1000;
}

.kiosk__section-drag-handle {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(
    135deg,
    rgba(10, 45, 105, 0.05) 0%,
    rgba(30, 74, 140, 0.05) 100%
  );
  border-radius: 12px 12px 0 0;
  cursor: grab;
  transition: all 0.2s ease;
  opacity: 0.7;
  z-index: 10;
  border-bottom: 1px solid rgba(10, 45, 105, 0.1);
}

.kiosk__section-drag-handle:hover {
  background: linear-gradient(
    135deg,
    rgba(10, 45, 105, 0.1) 0%,
    rgba(30, 74, 140, 0.1) 100%
  );
  opacity: 1;
  border-bottom-color: rgba(10, 45, 105, 0.2);
}

.kiosk__section-drag-handle:active {
  cursor: grabbing;
  background: linear-gradient(
    135deg,
    rgba(10, 45, 105, 0.15) 0%,
    rgba(30, 74, 140, 0.15) 100%
  );
}

.kiosk__section:hover .kiosk__section-drag-handle {
  opacity: 1;
}

.kiosk__section-drag-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #0a2d69;
  font-weight: 600;
}

.kiosk__section-drag-dots {
  font-size: 16px;
  letter-spacing: -2px;
  opacity: 0.7;
}

.kiosk__section-drag-text {
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  opacity: 0.8;
}

/* Adjust existing section styles to work with drag container */
.kiosk__section .kiosk__watchlist,
.kiosk__section .kiosk__routine-group {
  background: transparent;
  box-shadow: none;
  border-radius: 0;
  padding: 1rem;
}

.kiosk__section .kiosk__watchlist {
  padding-top: 3rem; /* Make room for larger drag handle */
}

.kiosk__section .kiosk__routine-group {
  padding-top: 3rem; /* Make room for larger drag handle */
}

/* Collapsed section styles - minimize space when collapsed */
.kiosk__section--collapsed {
  padding: 0;
  margin-bottom: 0.5rem;
}

.kiosk__section--collapsed .kiosk__watchlist,
.kiosk__section--collapsed .kiosk__routine-group {
  padding: 0;
  margin: 0;
}

/* Mobile drag handle adjustments */
@media (max-width: 768px) {
  .kiosk__section-drag-handle {
    height: 48px; /* Larger for touch */
    opacity: 1; /* Always visible on mobile */
  }

  .kiosk__section-drag-text {
    font-size: 0.7rem;
  }

  .kiosk__section-drag-dots {
    font-size: 18px;
  }
}

/* Integrated drag handle styles for headers */
.kiosk__section-header--draggable {
  cursor: grab;
  user-select: none;
  transition: all 0.2s ease;
  position: relative;
}

.kiosk__section-header--draggable:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.kiosk__section-header--draggable:active {
  cursor: grabbing;
  transform: translateY(0);
}

.kiosk__section-header--draggable .kiosk__section-drag-dots {
  font-size: 1rem;
  opacity: 0.6;
  margin-right: 0.5rem;
  color: #6b7280;
  transition: opacity 0.2s ease;
}

.kiosk__section-header--draggable:hover .kiosk__section-drag-dots {
  opacity: 1;
  color: #3b82f6;
}
