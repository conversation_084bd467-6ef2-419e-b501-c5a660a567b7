import React, { useState, useEffect } from "react";
import { useParams } from "react-router-dom";
import {
  DragDropContext,
  Droppable,
  Draggable,
  DropResult,
} from "react-beautiful-dnd";
import "./KioskInterface.css";
import {
  getAllLocations,
  getAllDailyTasks,
  getAllAdHocTasks,
  updateDailyTaskStatus,
  updateAdHocTaskStatus,
  updateAdHocTask,
  createAdHocTask,
  getAllRoutines,
  getAllRoutineTasks,
  getTenantRoles,
  getWatchlistTasks,
  addWatchlistCheck,
  getWatchlistChecks,
  getLocationAnnouncement,
  createOrUpdateLocationAnnouncement,
  type Location,
  type Routine,
  type RoutineTask,
  type AdHocTask,
  type TenantRole,
  type WatchlistTask,
  type WatchlistCheck,
  type LocationAnnouncement,
} from "../../services/adminBuddyFirebaseService";
import WysiwygEditor from "../../components/WysiwygEditor/WysiwygEditor";

// Combined Task interface for kiosk functionality
interface KioskTask {
  id: string;
  title: string;
  description?: string;
  priority: "low" | "medium" | "high";
  requiredRole?: string;
  dueTime?: string;
  visibleFromTime?: string; // HH:MM format, when task becomes visible
  status: "pending" | "completed" | "expired";
  type: "daily" | "adhoc";
  locationId: string;
  notes?: string;
  routineName?: string; // For grouping by routine
  routineTaskId?: string; // For daily tasks
  expiryType?: "next_report" | "set_datetime" | "no_expiry"; // For expiry logic
}

const KioskInterface: React.FC = () => {
  const { locationId } = useParams<{ locationId?: string }>();
  const [currentLocation, setCurrentLocation] = useState<Location | null>(null);
  const [availableLocations, setAvailableLocations] = useState<Location[]>([]);
  const [currentUser, setCurrentUser] = useState<any>(null);
  const [forceLocationSelection, setForceLocationSelection] = useState(false);
  const [tasks, setTasks] = useState<KioskTask[]>([]);
  const [routines, setRoutines] = useState<Routine[]>([]);
  const [routineTasks, setRoutineTasks] = useState<RoutineTask[]>([]);
  const [tenantRoles, setTenantRoles] = useState<TenantRole[]>([]);
  const [watchlistTasks, setWatchlistTasks] = useState<WatchlistTask[]>([]);
  const [filter, setFilter] = useState<"all" | "pending" | "completed">(
    "pending"
  );
  const [viewMode, setViewMode] = useState<"routine" | "role">("routine");
  const [selectedPriority, setSelectedPriority] = useState<string>("all");
  const [currentTime, setCurrentTime] = useState(new Date());
  const [showTaskModal, setShowTaskModal] = useState(false);
  const [selectedTask, setSelectedTask] = useState<KioskTask | null>(null);
  const [taskNote, setTaskNote] = useState("");
  const [showEndDayModal, setShowEndDayModal] = useState(false);
  const [endDayNotes, setEndDayNotes] = useState("");
  const [authError, setAuthError] = useState<string | null>(null);

  // To Do task creation modal state
  const [showAddTaskModal, setShowAddTaskModal] = useState(false);
  const [newTaskTitle, setNewTaskTitle] = useState("");
  const [newTaskDescription, setNewTaskDescription] = useState("");
  const [newTaskPriority, setNewTaskPriority] = useState<
    "low" | "medium" | "high"
  >("medium");

  // Team announcement state
  const [announcement, setAnnouncement] = useState<LocationAnnouncement | null>(
    null
  );
  const [isEditingAnnouncement, setIsEditingAnnouncement] = useState(false);
  const [announcementContent, setAnnouncementContent] = useState("");
  const [newTaskRequiredRole, setNewTaskRequiredRole] =
    useState<string>("any employee");
  const [newTaskDueTime, setNewTaskDueTime] = useState("");
  const [newTaskExpiryType, setNewTaskExpiryType] = useState<
    "next_report" | "set_datetime" | "no_expiry"
  >("no_expiry");
  const [newTaskExpiryDateTime, setNewTaskExpiryDateTime] = useState("");
  const [isCreatingTask, setIsCreatingTask] = useState(false);
  const [collapsedSections, setCollapsedSections] = useState<Set<string>>(
    new Set()
  );

  // Section ordering state
  const [sectionOrder, setSectionOrder] = useState<string[]>([]);

  // Helper function to get default expiry datetime (today at 11:59 PM in local time)
  const getDefaultExpiryDateTime = () => {
    const today = new Date();
    today.setHours(23, 59, 0, 0); // Set to 11:59 PM

    // Format for datetime-local input (YYYY-MM-DDTHH:MM) in local timezone
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, "0");
    const day = String(today.getDate()).padStart(2, "0");
    const hours = String(today.getHours()).padStart(2, "0");
    const minutes = String(today.getMinutes()).padStart(2, "0");

    return `${year}-${month}-${day}T${hours}:${minutes}`;
  };

  // Helper function to handle expiry type change with smart defaults
  const handleExpiryTypeChange = (
    newType: "next_report" | "set_datetime" | "no_expiry"
  ) => {
    setNewTaskExpiryType(newType);
    if (newType === "set_datetime" && !newTaskExpiryDateTime) {
      // Set default to today at 11:59 PM if no value is set
      setNewTaskExpiryDateTime(getDefaultExpiryDateTime());
    }
  };

  // Toggle section collapse
  const toggleSectionCollapse = (sectionName: string) => {
    setCollapsedSections((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(sectionName)) {
        newSet.delete(sectionName);
      } else {
        newSet.add(sectionName);
      }
      return newSet;
    });
  };

  // Handle drag and drop for section reordering
  const handleSectionDragEnd = (result: DropResult) => {
    if (!result.destination) {
      return;
    }

    const items = Array.from(sectionOrder);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    setSectionOrder(items);

    // Save to localStorage
    try {
      const storageKey = `adminbuddy_kiosk_section_order_${viewMode}`;
      localStorage.setItem(storageKey, JSON.stringify(items));
    } catch (error) {
      console.warn("Failed to save section order to localStorage:", error);
    }
  };

  // Helper function to handle auth errors and redirect to login
  const handleAuthError = (error: any, context: string) => {
    console.error(`❌ Kiosk: Error in ${context}:`, error);

    // Check if it's specifically an auth-related error (not index or other errors)
    const isAuthError =
      error?.code === "permission-denied" ||
      error?.code === "unauthenticated" ||
      error?.message?.includes("UNAUTHENTICATED") ||
      error?.message?.includes("PERMISSION_DENIED") ||
      (error?.message?.includes("auth") &&
        !error?.message?.includes("index")) ||
      (error?.message?.includes("token") && !error?.message?.includes("index"));

    if (isAuthError) {
      console.log("🔐 Kiosk: Auth error detected, redirecting to login...");
      setAuthError("Your session has expired. Redirecting to login...");

      // Store current URL for redirect after login
      const currentUrl = window.location.pathname + window.location.search;
      localStorage.setItem("adminbuddy_redirect_after_login", currentUrl);
      console.log("💾 Stored redirect URL:", currentUrl);

      // Clear auth data and redirect after a short delay
      setTimeout(() => {
        localStorage.removeItem("adminbuddy_auth");
        window.location.href = "/login";
      }, 2000);

      return true; // Indicates auth error was handled
    }

    return false; // Not an auth error
  };

  // Load user authentication and locations
  useEffect(() => {
    const loadUserAndLocations = async () => {
      try {
        console.log("🔐 Kiosk: Loading user authentication...");

        // Check if we have auth data from our REST API login
        const authData = localStorage.getItem("adminbuddy_auth");

        if (!authData) {
          console.log("❌ Kiosk: No auth data found - user must login first");
          setAuthError("No authentication found. Redirecting to login...");
          setTimeout(() => {
            window.location.href = "/login";
          }, 2000);
          return;
        }

        const { uid, email } = JSON.parse(authData);
        console.log("👤 Kiosk: Found auth data for:", email);

        // Get fresh user profile from Firestore (don't trust localStorage for profile data)
        const { getUserProfile } = await import(
          "../../services/adminBuddyFirebaseService"
        );
        const userProfile = await getUserProfile(uid);

        if (!userProfile) {
          console.log("❌ Kiosk: Could not load user profile from Firestore");
          setAuthError("Could not load user profile. Redirecting to login...");
          setTimeout(() => {
            localStorage.removeItem("adminbuddy_auth");
            window.location.href = "/login";
          }, 2000);
          return;
        }

        console.log("👤 Kiosk: Loaded fresh user profile:", {
          email: userProfile.email,
          tenantId: userProfile.tenantId,
        });

        if (!userProfile.tenantId) {
          console.log(
            "❌ Kiosk: User profile has no tenant ID - invalid user profile"
          );
          setAuthError("Invalid user profile. Redirecting to login...");
          setTimeout(() => {
            localStorage.removeItem("adminbuddy_auth");
            window.location.href = "/login";
          }, 2000);
          return;
        }

        setCurrentUser(userProfile);

        // Get all locations for this tenant (with proper tenant filtering)
        console.log(
          "🏢 Kiosk: Loading locations for tenant:",
          userProfile.tenantId
        );
        const tenantLocations = await getAllLocations(userProfile.tenantId);
        const activeLocations = tenantLocations;
        setAvailableLocations(activeLocations);

        console.log(
          `✅ Kiosk: Found ${activeLocations.length} active locations for tenant ${userProfile.tenantId}`
        );
      } catch (error) {
        // Use the auth error handler
        if (!handleAuthError(error, "loadUserAndLocations")) {
          console.error(
            "❌ Kiosk: Non-auth error loading user/locations:",
            error
          );
        }
      }
    };

    loadUserAndLocations();
  }, []);

  // Function to reload tasks from server
  const reloadTasks = async () => {
    if (!currentUser?.tenantId || !locationId || !currentLocation) {
      console.log("⚠️ Cannot reload tasks - missing required data:", {
        tenantId: !!currentUser?.tenantId,
        locationId: !!locationId,
        currentLocation: !!currentLocation,
      });
      return;
    }

    try {
      console.log("🔄 Reloading tasks and watchlist from server...");

      // Get all tasks, watchlist, and fresh routine data for this tenant+location
      const [
        dailyTasks,
        adHocTasks,
        watchlistData,
        routinesData,
        routineTasksData,
      ] = await Promise.all([
        getAllDailyTasks(currentUser.tenantId, undefined, locationId),
        getAllAdHocTasks(currentUser.tenantId, locationId),
        getWatchlistTasks(currentUser.tenantId, locationId),
        getAllRoutines(currentUser.tenantId),
        getAllRoutineTasks(currentUser.tenantId),
      ]);

      // Update watchlist data and routine data
      setWatchlistTasks(watchlistData);
      setRoutines(routinesData);
      setRoutineTasks(routineTasksData);

      // Convert tasks to kiosk format and filter out reported tasks AND expired tasks
      const locationDailyTasks: KioskTask[] = dailyTasks
        .filter((task) => !task.reportedAt && task.status !== "expired")
        .map((task) => {
          const routineTask = routineTasksData.find(
            (rt) => rt.id === task.routineTaskId
          );
          const routine = routineTask
            ? routinesData.find((r) => r.id === routineTask.routineId)
            : null;

          return {
            id: task.id,
            title: task.title,
            description: task.description,
            priority: task.priority,
            requiredRole: task.requiredRole,
            dueTime: task.dueTime,
            visibleFromTime: task.visibleFromTime, // Add visibility control
            status: task.status,
            type: "daily" as const,
            locationId: task.locationId,
            notes: task.notes,
            routineName: routine?.name || "Unknown Routine",
            routineTaskId: task.routineTaskId,
            expiryType: task.expiryType,
          };
        });

      const locationAdHocTasks: KioskTask[] = adHocTasks
        .filter((task) => !task.reportedAt && task.status !== "expired")
        .map((task) => ({
          id: task.id,
          title: task.title,
          description: task.description,
          priority: task.priority,
          requiredRole: task.requiredRole,
          dueTime: task.dueTime,
          visibleFromTime: task.visibleFromTime, // Add visibility control
          status: task.status,
          type: "adhoc" as const,
          locationId: task.locationId,
          notes: task.notes,
          routineName: "To Do Tasks",
          expiryType: task.expiryType,
        }));

      const allTasks = [...locationDailyTasks, ...locationAdHocTasks];
      setTasks(allTasks);

      console.log(
        `✅ Reloaded ${allTasks.length} tasks and ${watchlistData.length} watchlist items from server`
      );
    } catch (error) {
      console.error("❌ Error reloading tasks:", error);
    }
  };

  // Load specific location data when locationId is provided
  useEffect(() => {
    const loadLocationData = async () => {
      if (!locationId || !currentUser) return;

      // Wait for available locations to be loaded
      if (availableLocations.length === 0) {
        console.log("⏳ Waiting for available locations to load...");
        return;
      }

      try {
        console.log("🔥 Loading kiosk data for location:", locationId);
        console.log("🔍 DEBUG: Current user:", currentUser);
        console.log("🔍 DEBUG: Available locations:", availableLocations);
        console.log("🔍 DEBUG: Looking for locationId:", locationId);

        // Find the location in our available locations
        const location = availableLocations.find(
          (loc) => loc.id === locationId
        );

        if (!location) {
          console.log("❌ Location not found or not accessible:", locationId);
          console.log(
            "🔄 Available locations for this user:",
            availableLocations.map((loc) => ({
              id: loc.id,
              name: loc.name,
              tenantId: loc.tenantId,
            }))
          );
          console.log("🔍 DEBUG: User tenantId:", currentUser?.tenantId);

          // TEMPORARILY DISABLED - Let's see what the debug logs show
          console.log("⚠️ TEMPORARILY ALLOWING ACCESS TO DEBUG THE ISSUE");

          // Try to fetch the location directly from the database
          console.log(
            "🔍 Attempting to fetch location directly from database..."
          );
          try {
            const allLocations = await getAllLocations(currentUser.tenantId);
            console.log("🔍 All locations from database:", allLocations);
            const directLocation = allLocations.find(
              (loc) => loc.id === locationId
            );
            if (directLocation) {
              console.log("✅ Found location in database:", directLocation);
              // Use the location from database
              setCurrentLocation(directLocation);
              // Continue with loading...
            } else {
              console.log(
                "❌ Location not found even in direct database query"
              );
              alert(
                `Location "${locationId}" not found in your tenant. Please select a valid location.`
              );
              window.history.replaceState({}, "", "/kiosk");
              return;
            }
          } catch (dbError) {
            console.error("❌ Error fetching location from database:", dbError);
            alert(`Error accessing location. Please try again.`);
            window.history.replaceState({}, "", "/kiosk");
            return;
          }
        } else {
          setCurrentLocation(location);
        }

        // Get the current location (either from availableLocations or database)
        const currentLoc = currentLocation || location;
        if (!currentLoc) {
          console.error("❌ No location available to load data");
          return;
        }

        // Get all tasks, routines, routine tasks, roles, and watchlist for this tenant+location (no date filtering)
        const [
          dailyTasks,
          adHocTasks,
          routinesData,
          routineTasksData,
          rolesData,
          watchlistData,
        ] = await Promise.all([
          getAllDailyTasks(currentLoc.tenantId, undefined, locationId), // No date filter - show all unreported tasks
          getAllAdHocTasks(currentLoc.tenantId, locationId),
          getAllRoutines(currentLoc.tenantId),
          getAllRoutineTasks(currentLoc.tenantId),
          getTenantRoles(currentLoc.tenantId),
          getWatchlistTasks(currentLoc.tenantId, locationId),
        ]);

        setRoutines(routinesData);
        setRoutineTasks(routineTasksData);
        setTenantRoles(rolesData);
        setWatchlistTasks(watchlistData);

        // Convert tasks to kiosk format and filter out reported tasks AND expired tasks
        const locationDailyTasks: KioskTask[] = dailyTasks
          .filter((task) => !task.reportedAt && task.status !== "expired") // Exclude reported and expired tasks
          .map((task) => {
            // Find the routine task to get routine information
            const routineTask = routineTasksData.find(
              (rt) => rt.id === task.routineTaskId
            );
            const routine = routineTask
              ? routinesData.find((r) => r.id === routineTask.routineId)
              : null;

            return {
              id: task.id,
              title: task.title,
              description: task.description,
              priority: task.priority,
              requiredRole: task.requiredRole,
              dueTime: task.dueTime,
              visibleFromTime: task.visibleFromTime, // Add visibility control
              status: task.status,
              type: "daily" as const,
              locationId: task.locationId,
              notes: task.notes,
              routineName: routine?.name || "Unknown Routine",
              routineTaskId: task.routineTaskId,
              expiryType: task.expiryType,
            };
          });

        // Filter To Do tasks to exclude reported tasks AND expired tasks (no date filtering)
        const locationAdHocTasks: KioskTask[] = adHocTasks
          .filter((task) => !task.reportedAt && task.status !== "expired") // Exclude reported and expired tasks
          .map((task) => ({
            id: task.id,
            title: task.title,
            description: task.description,
            priority: task.priority,
            requiredRole: task.requiredRole,
            dueTime: task.dueTime,
            visibleFromTime: task.visibleFromTime, // Add visibility control
            status: task.status,
            type: "adhoc" as const,
            locationId: task.locationId,
            notes: task.notes,
            routineName: "To Do Tasks", // Group all To Do tasks together
            expiryType: task.expiryType,
          }));

        const allTasks = [...locationDailyTasks, ...locationAdHocTasks];
        setTasks(allTasks);

        // Debug: Log task statuses
        const pendingTasks = allTasks.filter((t) => t.status === "pending");
        const completedTasks = allTasks.filter((t) => t.status === "completed");
        const expiredTasks = allTasks.filter((t) => t.status === "expired");

        console.log(
          `✅ Loaded ${allTasks.length} tasks for location ${currentLoc.name} (${locationDailyTasks.length} daily, ${locationAdHocTasks.length} to do)`
        );
        console.log(`📊 Task status breakdown:`, {
          pending: pendingTasks.length,
          completed: completedTasks.length,
          expired: expiredTasks.length,
          total: allTasks.length,
        });

        // Debug: Log completed tasks specifically
        if (completedTasks.length > 0) {
          console.log(
            `✅ Found ${completedTasks.length} completed tasks:`,
            completedTasks.map((t) => ({
              id: t.id,
              title: t.title,
              status: t.status,
            }))
          );
        }
      } catch (error) {
        // Use the auth error handler
        if (!handleAuthError(error, "loadLocationData")) {
          console.error(
            "❌ Kiosk: Non-auth error loading location data:",
            error
          );
        }
      }
    };

    loadLocationData();
  }, [locationId, currentUser, availableLocations]);

  // Load announcement for current location
  useEffect(() => {
    const loadAnnouncement = async () => {
      if (!currentLocation || !currentUser) return;

      try {
        console.log(
          "📢 Loading announcement for location:",
          currentLocation.id
        );
        const locationAnnouncement = await getLocationAnnouncement(
          currentLocation.id,
          currentUser.tenantId
        );

        setAnnouncement(locationAnnouncement);
        setAnnouncementContent(locationAnnouncement?.content || "");
        console.log(
          "✅ Announcement loaded:",
          locationAnnouncement ? "Found" : "None"
        );
      } catch (error) {
        console.error("❌ Error loading announcement:", error);
        setAnnouncement(null);
        setAnnouncementContent("");
      }
    };

    loadAnnouncement();
  }, [currentLocation, currentUser]);

  // Update time every minute and auto-refresh tasks every 1 minute
  useEffect(() => {
    const timeTimer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);

    // Only set up auto-refresh if we have the required data
    let refreshTimer: NodeJS.Timeout | null = null;
    if (currentUser?.tenantId && locationId && currentLocation) {
      refreshTimer = setInterval(() => {
        console.log("🔄 Auto-refreshing kiosk tasks...");
        reloadTasks();
      }, 60000);
    }

    return () => {
      clearInterval(timeTimer);
      if (refreshTimer) {
        clearInterval(refreshTimer);
      }
    };
  }, [currentUser?.tenantId, locationId, currentLocation]);

  const handleTaskClick = async (
    task: KioskTask,
    action: "complete" | "note"
  ) => {
    if (action === "complete") {
      // Toggle task completion and update in Firebase
      const newStatus = task.status === "completed" ? "pending" : "completed";

      try {
        // Update in Firebase
        if (task.type === "daily") {
          await updateDailyTaskStatus(task.id, newStatus);
        } else {
          await updateAdHocTaskStatus(task.id, newStatus);
        }

        // Update local state
        setTasks((prevTasks) =>
          prevTasks.map((t) =>
            t.id === task.id ? { ...t, status: newStatus } : t
          )
        );
      } catch (error) {
        // Use the auth error handler
        if (!handleAuthError(error, "updateTaskStatus")) {
          console.error("Error updating task status:", error);
          alert("Failed to update task. Please try again.");
        }
      }
    } else {
      // Open modal for notes
      setSelectedTask(task);
      setTaskNote(task.notes || "");
      setShowTaskModal(true);
    }
  };

  const handleTaskComplete = async () => {
    if (!selectedTask) return;

    try {
      // Update in Firebase first (including notes)
      const notes = taskNote.trim() || undefined;
      if (selectedTask.type === "daily") {
        await updateDailyTaskStatus(selectedTask.id, "completed", notes);
      } else {
        await updateAdHocTaskStatus(selectedTask.id, "completed", notes);
      }

      // Update local state
      setTasks((prevTasks) =>
        prevTasks.map((task) =>
          task.id === selectedTask.id
            ? {
                ...task,
                status: "completed",
                isCompleted: true,
                completedAt: new Date().toISOString(),
                completedBy: "Current User",
                notes: taskNote.trim() || undefined,
              }
            : task
        )
      );

      setShowTaskModal(false);
      setSelectedTask(null);
      setTaskNote("");
    } catch (error) {
      // Use the auth error handler
      if (!handleAuthError(error, "completeTask")) {
        console.error("Error completing task:", error);
        alert("Failed to complete task. Please try again.");
      }
    }
  };

  const handleTaskAddNote = async () => {
    if (!selectedTask) return;

    try {
      // Update notes in Firebase
      const notes = taskNote.trim() || undefined;
      if (selectedTask.type === "daily") {
        // For daily tasks, use updateDailyTaskStatus with current status to update notes
        await updateDailyTaskStatus(
          selectedTask.id,
          selectedTask.status,
          notes
        );
      } else {
        // For To Do tasks, use updateAdHocTask to update just the notes
        await updateAdHocTask(selectedTask.id, { notes });
      }

      // Update local state
      setTasks((prevTasks) =>
        prevTasks.map((task) =>
          task.id === selectedTask.id
            ? {
                ...task,
                notes: taskNote.trim() || undefined,
              }
            : task
        )
      );

      setShowTaskModal(false);
      setSelectedTask(null);
      setTaskNote("");
    } catch (error) {
      // Use the auth error handler
      if (!handleAuthError(error, "addTaskNote")) {
        console.error("Error adding task note:", error);
        alert("Failed to save note. Please try again.");
      }
    }
  };

  const handleEndDay = async () => {
    if (!currentUser?.tenantId || !locationId || !currentLocation) {
      console.error("❌ Missing required data for report creation");
      return;
    }

    try {
      console.log("📊 Creating location report...");

      // Import the report creation function
      const { createLocationReport } = await import(
        "../../services/adminBuddyFirebaseService"
      );

      // Create the report
      const reportId = await createLocationReport(
        currentUser.tenantId,
        locationId,
        currentLocation.name,
        currentUser.email,
        endDayNotes
      );

      console.log("✅ Report created successfully:", reportId);

      // Reload tasks from server to get updated status (expired tasks will be gone)
      // Note: Tasks with expiryType "next_report" are automatically expired when the report is created
      await reloadTasks();
      console.log(
        "🔄 Reloaded tasks from server - expired and completed tasks should be gone"
      );

      // Show success message
      setShowEndDayModal(false);
      setEndDayNotes("");

      // Show a toast notification
      const toast = document.createElement("div");
      toast.className = "toast-notification";
      toast.innerHTML = "📊 Report sent successfully!";
      toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #10b981;
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        font-weight: 500;
        z-index: 10000;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      `;
      document.body.appendChild(toast);

      // Remove toast after 3 seconds
      setTimeout(() => {
        if (document.body.contains(toast)) {
          document.body.removeChild(toast);
        }
      }, 3000);
    } catch (error) {
      console.error("❌ Error creating report:", error);

      // Use the auth error handler first
      if (!handleAuthError(error, "createLocationReport")) {
        // Show error message for non-auth errors
        const toast = document.createElement("div");
        toast.className = "toast-notification";
        toast.innerHTML = "❌ Failed to send report. Please try again.";
        toast.style.cssText = `
          position: fixed;
          top: 20px;
          right: 20px;
          background: #ef4444;
          color: white;
          padding: 12px 20px;
          border-radius: 8px;
          font-weight: 500;
          z-index: 10000;
          box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        `;
        document.body.appendChild(toast);

        // Remove toast after 5 seconds
        setTimeout(() => {
          if (document.body.contains(toast)) {
            document.body.removeChild(toast);
          }
        }, 5000);
      }
    }
  };

  // Function to create a new To Do task
  const handleCreateAdHocTask = async () => {
    if (!currentUser?.tenantId || !locationId || !newTaskTitle.trim()) {
      return;
    }

    setIsCreatingTask(true);
    try {
      const today = new Date().toISOString().split("T")[0]; // YYYY-MM-DD format

      const taskData: Omit<AdHocTask, "id" | "createdAt"> = {
        tenantId: currentUser.tenantId,
        locationId: locationId,
        title: newTaskTitle.trim(),
        description: newTaskDescription.trim() || undefined,
        priority: newTaskPriority,
        requiredRole: newTaskRequiredRole,
        dueDate: today, // Due today
        dueTime: newTaskDueTime || undefined,
        expiryType: newTaskExpiryType,
        expiryDateTime:
          newTaskExpiryType === "set_datetime"
            ? newTaskExpiryDateTime
            : undefined,
        status: "pending",
      };

      console.log("⚡ Creating To Do task:", taskData);
      console.log("📅 Today's date for task:", today);
      await createAdHocTask(taskData);

      // Clear form and close modal
      setNewTaskTitle("");
      setNewTaskDescription("");
      setNewTaskPriority("medium");
      setNewTaskRequiredRole("any employee");
      setNewTaskDueTime("");
      setNewTaskExpiryType("no_expiry");
      setNewTaskExpiryDateTime("");
      setShowAddTaskModal(false);

      // Reload tasks to show the new one
      console.log("🔄 Reloading tasks after creating To Do task...");
      await reloadTasks();

      // Debug: Check if the new task appears in the tasks list
      setTimeout(() => {
        console.log("🔍 Current tasks after reload:", tasks.length);
        console.log("🔍 Tasks data:", tasks);
      }, 1000);

      // Show success toast
      const toast = document.createElement("div");
      toast.className = "toast-notification";
      toast.innerHTML = "✅ Task added successfully!";
      toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #10b981;
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        font-weight: 500;
        z-index: 10000;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      `;
      document.body.appendChild(toast);

      setTimeout(() => {
        if (document.body.contains(toast)) {
          document.body.removeChild(toast);
        }
      }, 3000);
    } catch (error) {
      console.error("❌ Error creating To Do task:", error);

      // Show error toast
      const toast = document.createElement("div");
      toast.className = "toast-notification";
      toast.innerHTML = "❌ Failed to create task. Please try again.";
      toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #ef4444;
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        font-weight: 500;
        z-index: 10000;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      `;
      document.body.appendChild(toast);

      setTimeout(() => {
        if (document.body.contains(toast)) {
          document.body.removeChild(toast);
        }
      }, 5000);
    } finally {
      setIsCreatingTask(false);
    }
  };

  // Handle announcement save
  const handleSaveAnnouncement = async () => {
    if (!currentLocation || !currentUser) return;

    try {
      console.log("📢 Saving announcement for location:", currentLocation.id);

      const updatedAnnouncement = await createOrUpdateLocationAnnouncement(
        currentLocation.id,
        currentUser.tenantId,
        announcementContent,
        currentUser.displayName || currentUser.email
      );

      setAnnouncement(updatedAnnouncement);
      setIsEditingAnnouncement(false);
      console.log("✅ Announcement saved successfully");
    } catch (error) {
      console.error("❌ Error saving announcement:", error);
      alert("Failed to save announcement. Please try again.");
    }
  };

  // Handle announcement cancel
  const handleCancelAnnouncement = () => {
    setAnnouncementContent(announcement?.content || "");
    setIsEditingAnnouncement(false);
  };

  // Helper function to check if announcement content is empty or just whitespace/empty HTML
  const hasAnnouncementContent = (content: string | undefined): boolean => {
    if (!content) return false;

    // Remove HTML tags and check if there's actual text content
    const textContent = content.replace(/<[^>]*>/g, "").trim();
    return textContent.length > 0;
  };

  // Handle watchlist check-in
  const handleWatchlistCheckIn = async (
    taskId: string,
    checkData: Omit<WatchlistCheck, "id">
  ) => {
    if (!currentLocation) return;

    try {
      await addWatchlistCheck(taskId, checkData);

      // Refresh watchlist data immediately to show updated status
      const updatedWatchlistData = await getWatchlistTasks(
        currentLocation.tenantId,
        currentLocation.id
      );
      setWatchlistTasks(updatedWatchlistData);

      // Show success toast
      const toast = document.createElement("div");
      toast.className = "toast-notification";
      toast.innerHTML = "✅ Check-in recorded successfully!";
      toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #10b981;
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        font-weight: 500;
        z-index: 10000;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      `;
      document.body.appendChild(toast);

      setTimeout(() => {
        if (document.body.contains(toast)) {
          document.body.removeChild(toast);
        }
      }, 3000);
    } catch (error) {
      console.error("Error adding watchlist check:", error);

      // Show error toast
      const toast = document.createElement("div");
      toast.className = "toast-notification";
      toast.innerHTML = "❌ Failed to record check-in. Please try again.";
      toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #ef4444;
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        font-weight: 500;
        z-index: 10000;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      `;
      document.body.appendChild(toast);

      setTimeout(() => {
        if (document.body.contains(toast)) {
          document.body.removeChild(toast);
        }
      }, 5000);
    }
  };

  // Helper function to check if a task is currently visible
  const isTaskVisible = (task: KioskTask): boolean => {
    if (!task.visibleFromTime) {
      console.log(`✅ Task "${task.title}" is visible (no visibleFromTime)`);
      return true;
    }

    const now = new Date();
    const [visibleHour, visibleMinute] = task.visibleFromTime
      .split(":")
      .map(Number);
    const [currentHour, currentMinute] = [now.getHours(), now.getMinutes()];

    const visibleTimeInMinutes = visibleHour * 60 + visibleMinute;
    const currentTimeInMinutes = currentHour * 60 + currentMinute;
    const isVisible = currentTimeInMinutes >= visibleTimeInMinutes;

    console.log(`${isVisible ? "✅" : "❌"} Task "${task.title}" visibility:`, {
      visibleFromTime: task.visibleFromTime,
      currentTime: `${currentHour}:${currentMinute
        .toString()
        .padStart(2, "0")}`,
      isVisible,
    });

    return isVisible;
  };

  // Filter tasks to only include visible ones for all calculations
  const visibleTasks = tasks.filter(isTaskVisible);

  const priorities = [
    "all",
    ...Array.from(new Set(visibleTasks.map((task) => task.priority))),
  ];

  const filteredTasks = visibleTasks.filter((task) => {
    const matchesFilter =
      filter === "all" ||
      (filter === "pending" && task.status === "pending") ||
      (filter === "completed" && task.status === "completed");

    const matchesPriority =
      selectedPriority === "all" || task.priority === selectedPriority;

    return matchesFilter && matchesPriority;
  });

  // Helper function to get priority weight for sorting (higher number = higher priority)
  const getPriorityWeight = (priority: string): number => {
    switch (priority) {
      case "high":
        return 3;
      case "medium":
        return 2;
      case "low":
        return 1;
      default:
        return 0;
    }
  };

  // Helper function to sort tasks by priority (high to low) then alphabetically
  const sortTasksByPriorityAndName = (tasks: KioskTask[]): KioskTask[] => {
    return tasks.sort((a, b) => {
      // First sort by priority (high to low)
      const priorityDiff =
        getPriorityWeight(b.priority) - getPriorityWeight(a.priority);
      if (priorityDiff !== 0) return priorityDiff;

      // Then sort alphabetically by title
      return a.title.localeCompare(b.title);
    });
  };

  // Group tasks by routine or role based on view mode
  const groupedTasks = filteredTasks.reduce((groups, task) => {
    let groupKey: string;

    if (viewMode === "role") {
      // Group by role - capitalize first letter for display
      const role = task.requiredRole || "any employee";
      groupKey = role.charAt(0).toUpperCase() + role.slice(1);
    } else {
      // Group by routine (default)
      groupKey = task.routineName || "Unknown Routine";
    }

    if (!groups[groupKey]) {
      groups[groupKey] = [];
    }
    groups[groupKey].push(task);
    return groups;
  }, {} as Record<string, KioskTask[]>);

  // Sort groups by highest priority task in each group, then alphabetically
  const sortedGroupEntries = Object.entries(groupedTasks).sort(
    ([nameA, tasksA], [nameB, tasksB]) => {
      // Get highest priority in each group
      const maxPriorityA = Math.max(
        ...tasksA.map((task) => getPriorityWeight(task.priority))
      );
      const maxPriorityB = Math.max(
        ...tasksB.map((task) => getPriorityWeight(task.priority))
      );

      // Sort by priority first (highest first), then alphabetically
      if (maxPriorityA !== maxPriorityB) {
        return maxPriorityB - maxPriorityA;
      }
      return nameA.localeCompare(nameB);
    }
  );

  // Create a safe ID from a string by removing special characters
  const createSafeId = (str: string): string => {
    return str
      .replace(/[^\w]/g, "_") // Replace any non-word character with underscore
      .replace(/_+/g, "_") // Replace multiple underscores with single underscore
      .replace(/^_|_$/g, "") // Remove leading/trailing underscores
      .toLowerCase(); // Convert to lowercase for consistency
  };

  // Create current sections based on available data
  const getCurrentSections = (): string[] => {
    const sections: string[] = [];

    // Add watchlist section if there are watchlist tasks
    if (watchlistTasks.length > 0) {
      sections.push("watchlist");
    }

    // Add routine/role sections based on current view mode and grouped tasks
    Object.keys(groupedTasks).forEach((groupName) => {
      const safeId = `${viewMode}_${createSafeId(groupName)}`;
      sections.push(safeId);
    });

    return sections;
  };

  // Initialize section order when sections change
  useEffect(() => {
    const currentSections = getCurrentSections();

    // Only update if sections have actually changed or if sectionOrder is empty
    if (
      sectionOrder.length === 0 ||
      JSON.stringify(sectionOrder) !== JSON.stringify(currentSections)
    ) {
      // Try to load saved order from localStorage
      try {
        const storageKey = `adminbuddy_kiosk_section_order_${viewMode}`;
        const saved = localStorage.getItem(storageKey);

        if (saved) {
          const savedOrder = JSON.parse(saved) as string[];

          // Merge saved order with current sections
          const orderedSections: string[] = [];
          const remainingSections = new Set(currentSections);

          // Add sections from saved order that still exist
          savedOrder.forEach((sectionId) => {
            if (remainingSections.has(sectionId)) {
              orderedSections.push(sectionId);
              remainingSections.delete(sectionId);
            }
          });

          // Add any new sections that weren't in saved order
          remainingSections.forEach((sectionId) => {
            orderedSections.push(sectionId);
          });

          setSectionOrder(orderedSections);
        } else {
          setSectionOrder(currentSections);
        }
      } catch (error) {
        console.warn("Failed to load section order from localStorage:", error);
        setSectionOrder(currentSections);
      }
    }
  }, [
    viewMode,
    JSON.stringify(Object.keys(groupedTasks)),
    watchlistTasks.length,
  ]);

  const completedCount = visibleTasks.filter(
    (task) => task.status === "completed"
  ).length;
  const totalCount = visibleTasks.length;
  const pendingCount = visibleTasks.filter(
    (task) => task.status === "pending"
  ).length;

  // Fun empty state messages
  const getEmptyStateMessage = () => {
    const messages = [
      { title: "🎉 All done!", subtitle: "Woohoo!" },
      { title: "✨ You're crushing it!", subtitle: "Every task completed!" },
      { title: "🏆 Task master!", subtitle: "Nothing left on the list!" },
      { title: "🎯 Perfect score!", subtitle: "100% completion achieved!" },
      { title: "🌟 Stellar work!", subtitle: "All tasks knocked out!" },
    ];
    return messages[Math.floor(Math.random() * messages.length)];
  };

  // Show auth error if present
  if (authError) {
    return (
      <div className="kiosk">
        <div className="kiosk__loading">
          <h2>🔐 Authentication Error</h2>
          <p>{authError}</p>
          <div className="loading-spinner" style={{ marginTop: "1rem" }}>
            <div className="spinner"></div>
          </div>
        </div>
      </div>
    );
  }

  // Show location selector if no locationId provided
  if (!locationId) {
    if (!currentUser) {
      return (
        <div className="kiosk">
          <div className="kiosk__loading">
            <h2>🔐 Authentication Required</h2>
            <p>Please log in to access the kiosk interface.</p>
            <button
              className="btn btn--primary"
              onClick={() => (window.location.href = "/login")}
              style={{ marginTop: "1rem" }}
            >
              Go to Login
            </button>
          </div>
        </div>
      );
    }

    if (!currentUser.tenantId) {
      return (
        <div className="kiosk">
          <div className="kiosk__loading">
            <h2>🚫 Invalid User Profile</h2>
            <p>Your user profile is missing tenant information.</p>
            <p>Please contact your administrator or try logging in again.</p>
            <button
              className="btn btn--primary"
              onClick={() => {
                localStorage.removeItem("adminbuddy_auth");
                window.location.href = "/login";
              }}
              style={{ marginTop: "1rem" }}
            >
              Login Again
            </button>
          </div>
        </div>
      );
    }

    return (
      <div className="kiosk">
        <div className="kiosk__loading">
          <h2>🏢 Select Location</h2>
          <p>Choose a location to access the kiosk interface:</p>
          <div
            style={{
              fontSize: "0.9rem",
              color: "#6b7280",
              marginBottom: "1rem",
            }}
          >
            Logged in as: {currentUser.email} | Tenant: {currentUser.tenantId}
          </div>

          {availableLocations.length > 0 ? (
            <div className="location-selector">
              {availableLocations.map((location) => (
                <button
                  key={location.id}
                  className="location-btn"
                  onClick={() =>
                    (window.location.href = `/kiosk/${location.id}`)
                  }
                >
                  <div className="location-btn__content">
                    <h3>{location.name}</h3>
                    <p>{location.address}</p>
                    <div className="location-btn__meta">
                      <span className="location-type">📍 Location</span>
                    </div>
                  </div>
                  <div className="location-btn__arrow">→</div>
                </button>
              ))}
            </div>
          ) : (
            <div className="empty-locations">
              <p>No active locations found for your tenant.</p>
              <p>Please contact your administrator to set up locations.</p>
              <div
                style={{
                  marginTop: "1rem",
                  fontSize: "0.8rem",
                  color: "#9ca3af",
                }}
              >
                Tenant ID: {currentUser.tenantId}
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }

  if (!currentLocation) {
    return (
      <div className="kiosk">
        <div className="kiosk__loading">
          <h2>Loading location...</h2>
        </div>
      </div>
    );
  }

  return (
    <div className="kiosk">
      {/* Compact Header */}
      <header className="kiosk__header-compact">
        <div className="kiosk__header-top">
          <div className="kiosk__location-compact">
            <h1>{currentLocation.name}</h1>
            <span className="kiosk__time-compact">
              {currentTime.toLocaleTimeString([], {
                hour: "2-digit",
                minute: "2-digit",
              })}
            </span>
          </div>
          <div className="kiosk__header-buttons">
            {/* Owner Dashboard Button - Only visible to owners */}
            {currentUser?.role === "owner" && (
              <button
                className="kiosk__dashboard-btn"
                onClick={() => {
                  console.log("🏠 Owner navigating to dashboard from kiosk");
                  window.location.href = "/dashboard";
                }}
                title="Go to Owner Dashboard"
              >
                🏠
              </button>
            )}
            <button
              className="kiosk__logout-btn"
              onClick={() => {
                // Store current URL for redirect after login
                const currentUrl =
                  window.location.pathname + window.location.search;
                localStorage.setItem(
                  "adminbuddy_redirect_after_login",
                  currentUrl
                );
                console.log(
                  "💾 Manual logout - stored redirect URL:",
                  currentUrl
                );

                localStorage.removeItem("adminbuddy_auth");
                window.location.href = "/login";
              }}
              title="Logout"
            >
              🔓
            </button>
          </div>
        </div>
      </header>

      {/* Team Announcements Section */}
      {hasAnnouncementContent(announcement?.content) ||
      isEditingAnnouncement ? (
        <div className="kiosk__announcements">
          <div className="kiosk__announcements-header">
            <h2 className="kiosk__announcements-title">
              📢 Team Announcements
            </h2>
            {(currentUser?.role === "owner" ||
              currentUser?.role === "manager" ||
              currentUser?.role === "kiosk") && (
              <button
                className="kiosk__announcements-edit-btn"
                onClick={() => setIsEditingAnnouncement(!isEditingAnnouncement)}
                title={
                  isEditingAnnouncement ? "Cancel editing" : "Edit announcement"
                }
              >
                {isEditingAnnouncement ? "✕" : "✏️"}
              </button>
            )}
          </div>

          <div className="kiosk__announcements-content">
            {isEditingAnnouncement ? (
              <div className="kiosk__announcements-editor">
                <WysiwygEditor
                  value={announcementContent}
                  onChange={setAnnouncementContent}
                  placeholder="Enter team announcements here..."
                  className="kiosk-editor"
                />
                <div className="kiosk__announcements-actions">
                  <button
                    className="kiosk__announcements-btn kiosk__announcements-btn--save"
                    onClick={handleSaveAnnouncement}
                  >
                    💾 Save
                  </button>
                  <button
                    className="kiosk__announcements-btn kiosk__announcements-btn--cancel"
                    onClick={handleCancelAnnouncement}
                  >
                    Cancel
                  </button>
                </div>
              </div>
            ) : (
              <div className="kiosk__announcements-display">
                <div
                  className="kiosk__announcements-text"
                  dangerouslySetInnerHTML={{
                    __html: announcement?.content || "",
                  }}
                />
                {announcement?.lastUpdatedAt && (
                  <div className="kiosk__announcements-meta">
                    Last updated:{" "}
                    {new Date(announcement.lastUpdatedAt).toLocaleString()}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      ) : (
        // Compact "Add Announcement" button when no content exists
        (currentUser?.role === "owner" ||
          currentUser?.role === "manager" ||
          currentUser?.role === "kiosk") && (
          <div className="kiosk__announcements-compact">
            <button
              className="kiosk__announcements-add-btn"
              onClick={() => setIsEditingAnnouncement(true)}
            >
              📢 Post Team Announcement
            </button>
          </div>
        )
      )}

      {/* Compact Controls */}
      <div className="kiosk__controls-compact">
        <div className="kiosk__progress-compact">
          <div className="kiosk__progress-bar">
            <div
              className="kiosk__progress-fill"
              style={{ width: `${(completedCount / totalCount) * 100}%` }}
            />
          </div>
          <span className="kiosk__progress-text">
            {completedCount}/{totalCount} completed
          </span>
        </div>

        <div className="kiosk__filter-group">
          <button
            className={`kiosk__filter-btn ${
              filter === "pending" ? "active" : ""
            }`}
            onClick={() => setFilter("pending")}
          >
            To Do ({visibleTasks.filter((t) => t.status === "pending").length})
          </button>
          <button
            className={`kiosk__filter-btn ${
              filter === "completed" ? "active" : ""
            }`}
            onClick={() => setFilter("completed")}
          >
            Done ({completedCount})
          </button>
          <button
            className={`kiosk__filter-btn ${filter === "all" ? "active" : ""}`}
            onClick={() => setFilter("all")}
          >
            All ({totalCount})
          </button>
        </div>

        <div className="kiosk__view-toggle">
          <button
            className={`kiosk__view-btn ${
              viewMode === "routine" ? "active" : ""
            }`}
            onClick={() => setViewMode("routine")}
            title="Group by Routine"
          >
            📋 Routines
          </button>
          <button
            className={`kiosk__view-btn ${viewMode === "role" ? "active" : ""}`}
            onClick={() => setViewMode("role")}
            title="Group by Role"
          >
            👥 Roles
          </button>
        </div>

        <button
          className="kiosk__add-task-btn-compact"
          onClick={() => setShowAddTaskModal(true)}
          title="Add To Do Task"
        >
          ➕ Add Task
        </button>

        <button
          className="kiosk__end-day-btn-compact"
          onClick={() => setShowEndDayModal(true)}
        >
          📊 Send Report
        </button>
      </div>

      {/* Drag and Drop Section Container */}
      {sectionOrder.length > 0 ? (
        <DragDropContext onDragEnd={handleSectionDragEnd}>
          <Droppable droppableId="kiosk-sections">
            {(provided) => (
              <div
                className="kiosk__sections"
                {...provided.droppableProps}
                ref={provided.innerRef}
              >
                {sectionOrder.map((sectionId, index) => {
                  // Determine what content this section should have
                  let sectionContent: JSX.Element | null = null;

                  if (sectionId === "watchlist" && watchlistTasks.length > 0) {
                    sectionContent = (
                      <div className="kiosk__watchlist">
                        {!collapsedSections.has("Watchlist") && (
                          <div className="kiosk__watchlist-tasks">
                            {watchlistTasks.map((task) => (
                              <WatchlistKioskCard
                                key={task.id}
                                task={task}
                                onCheckIn={handleWatchlistCheckIn}
                              />
                            ))}
                          </div>
                        )}
                      </div>
                    );
                  } else if (sectionId.startsWith(`${viewMode}_`)) {
                    // Find the original group name that matches this safe ID
                    const originalGroupName = Object.keys(groupedTasks).find(
                      (groupName) =>
                        `${viewMode}_${createSafeId(groupName)}` === sectionId
                    );

                    if (originalGroupName && groupedTasks[originalGroupName]) {
                      const groupTasks = groupedTasks[originalGroupName];
                      const isCollapsed =
                        collapsedSections.has(originalGroupName);
                      const completedCount = groupTasks.filter(
                        (t) => t.status === "completed"
                      ).length;
                      const totalCount = groupTasks.length;

                      sectionContent = (
                        <div className="kiosk__routine-group">
                          {!isCollapsed && (
                            <div className="kiosk__routine-tasks">
                              {sortTasksByPriorityAndName(groupTasks).map(
                                (task) => (
                                  <TaskCard
                                    key={task.id}
                                    task={task}
                                    onTaskAction={handleTaskClick}
                                    viewMode={viewMode}
                                  />
                                )
                              )}
                            </div>
                          )}
                        </div>
                      );
                    }
                  }

                  // Only render if we have content
                  if (!sectionContent) {
                    return null;
                  }

                  // Determine if this section is collapsed
                  const isSectionCollapsed =
                    sectionId === "watchlist"
                      ? collapsedSections.has("Watchlist")
                      : sectionId.startsWith(`${viewMode}_`) &&
                        (() => {
                          const originalGroupName = Object.keys(
                            groupedTasks
                          ).find(
                            (groupName) =>
                              `${viewMode}_${createSafeId(groupName)}` ===
                              sectionId
                          );
                          return originalGroupName
                            ? collapsedSections.has(originalGroupName)
                            : false;
                        })();

                  return (
                    <Draggable
                      key={sectionId}
                      draggableId={sectionId}
                      index={index}
                    >
                      {(provided, snapshot) => (
                        <div
                          ref={provided.innerRef}
                          {...provided.draggableProps}
                          className={`kiosk__section ${
                            snapshot.isDragging
                              ? "kiosk__section--dragging"
                              : ""
                          } ${
                            isSectionCollapsed
                              ? "kiosk__section--collapsed"
                              : ""
                          }`}
                        >
                          {/* Section Header with integrated drag handle */}
                          {sectionId === "watchlist" && (
                            <h2
                              {...provided.dragHandleProps}
                              className="kiosk__watchlist-title kiosk__watchlist-title--collapsible kiosk__section-header--draggable"
                              onClick={() => toggleSectionCollapse("Watchlist")}
                            >
                              <span className="kiosk__section-drag-dots">
                                ⋮⋮
                              </span>
                              <span className="kiosk__watchlist-title-content">
                                <span className="kiosk__routine-collapse-icon">
                                  {collapsedSections.has("Watchlist")
                                    ? "▶"
                                    : "▼"}
                                </span>
                                📋 Watchlist
                              </span>
                              <span className="kiosk__routine-task-count">
                                {watchlistTasks.length}
                              </span>
                            </h2>
                          )}

                          {sectionId.startsWith(`${viewMode}_`) &&
                            (() => {
                              // Find the original group name that matches this safe ID
                              const originalGroupName = Object.keys(
                                groupedTasks
                              ).find(
                                (groupName) =>
                                  `${viewMode}_${createSafeId(groupName)}` ===
                                  sectionId
                              );

                              if (
                                originalGroupName &&
                                groupedTasks[originalGroupName]
                              ) {
                                const groupTasks =
                                  groupedTasks[originalGroupName];
                                const isCollapsed =
                                  collapsedSections.has(originalGroupName);
                                const completedCount = groupTasks.filter(
                                  (t) => t.status === "completed"
                                ).length;
                                const totalCount = groupTasks.length;

                                return (
                                  <h3
                                    {...provided.dragHandleProps}
                                    className="kiosk__routine-title kiosk__routine-title--collapsible kiosk__section-header--draggable"
                                    onClick={() =>
                                      toggleSectionCollapse(originalGroupName)
                                    }
                                  >
                                    <span className="kiosk__section-drag-dots">
                                      ⋮⋮
                                    </span>
                                    <span className="kiosk__routine-title-content">
                                      <span className="kiosk__routine-collapse-icon">
                                        {isCollapsed ? "▶" : "▼"}
                                      </span>
                                      {viewMode === "role" ? "👥 " : "📋 "}
                                      {originalGroupName}
                                    </span>
                                    <span className="kiosk__routine-task-count">
                                      {completedCount}/{totalCount}
                                    </span>
                                  </h3>
                                );
                              }
                              return null;
                            })()}

                          {/* Section Content */}
                          {sectionContent}
                        </div>
                      )}
                    </Draggable>
                  );
                })}
                {provided.placeholder}

                {/* Empty State */}
                {filteredTasks.length === 0 && watchlistTasks.length === 0 && (
                  <div className="kiosk__empty">
                    {pendingCount === 0 && filter === "pending" ? (
                      (() => {
                        const message = getEmptyStateMessage();
                        return (
                          <>
                            <h3>{message.title}</h3>
                            <p>{message.subtitle}</p>
                          </>
                        );
                      })()
                    ) : (
                      <>
                        <h3>No tasks found</h3>
                        <p>Try adjusting your filters</p>
                      </>
                    )}
                  </div>
                )}
              </div>
            )}
          </Droppable>
        </DragDropContext>
      ) : (
        <div className="kiosk__empty">
          <h3>Loading sections...</h3>
          <p>Please wait while we set up your kiosk interface.</p>
        </div>
      )}

      {/* Task Modal */}
      {showTaskModal && selectedTask && (
        <div
          className="kiosk__modal-overlay"
          onClick={() => setShowTaskModal(false)}
        >
          <div className="kiosk__modal" onClick={(e) => e.stopPropagation()}>
            <div className="kiosk__modal-header">
              <h3>{selectedTask.title}</h3>
              <button
                className="kiosk__modal-close"
                onClick={() => setShowTaskModal(false)}
              >
                ✕
              </button>
            </div>

            <div className="kiosk__modal-content">
              {selectedTask.description && (
                <p className="kiosk__modal-description">
                  {selectedTask.description}
                </p>
              )}

              <div className="kiosk__modal-field">
                <label htmlFor="taskNote">Add a note (optional):</label>
                <textarea
                  id="taskNote"
                  value={taskNote}
                  onChange={(e) => setTaskNote(e.target.value)}
                  placeholder="e.g., Partially completed, ran out of time..."
                  rows={3}
                />
              </div>
            </div>

            <div className="kiosk__modal-actions">
              <button
                className="kiosk__modal-btn kiosk__modal-btn--secondary"
                onClick={handleTaskAddNote}
              >
                💬 Add Note Only
              </button>
              <button
                className="kiosk__modal-btn kiosk__modal-btn--primary"
                onClick={handleTaskComplete}
              >
                ✅ Mark Complete
              </button>
            </div>
          </div>
        </div>
      )}

      {/* End Day Modal */}
      {showEndDayModal && (
        <div
          className="kiosk__modal-overlay"
          onClick={() => setShowEndDayModal(false)}
        >
          <div className="kiosk__modal" onClick={(e) => e.stopPropagation()}>
            <div className="kiosk__modal-header">
              <h3>📊 Send Report</h3>
              <button
                className="kiosk__modal-close"
                onClick={() => setShowEndDayModal(false)}
              >
                ✕
              </button>
            </div>

            <div className="kiosk__modal-content">
              <p>
                This will send a summary of completed tasks and current status
                to your manager.
              </p>

              <div className="kiosk__modal-field">
                <label htmlFor="endDayNotes">Report notes:</label>
                <textarea
                  id="endDayNotes"
                  value={endDayNotes}
                  onChange={(e) => setEndDayNotes(e.target.value)}
                  placeholder="Any issues, observations, or notes for the manager..."
                  rows={4}
                />
              </div>
            </div>

            <div className="kiosk__modal-actions">
              <button
                className="kiosk__modal-btn kiosk__modal-btn--secondary"
                onClick={() => setShowEndDayModal(false)}
              >
                Cancel
              </button>
              <button
                className="kiosk__modal-btn kiosk__modal-btn--primary"
                onClick={handleEndDay}
              >
                🚀 Send Report
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Add Task Modal */}
      {showAddTaskModal && (
        <div
          className="kiosk__modal-overlay"
          onClick={() => setShowAddTaskModal(false)}
        >
          <div className="kiosk__modal" onClick={(e) => e.stopPropagation()}>
            <div className="kiosk__modal-header">
              <h3>➕ Add New Task</h3>
              <button
                className="kiosk__modal-close"
                onClick={() => setShowAddTaskModal(false)}
              >
                ✕
              </button>
            </div>

            <div className="kiosk__modal-content">
              <div className="kiosk__modal-field">
                <label htmlFor="newTaskTitle">Task Title *</label>
                <input
                  id="newTaskTitle"
                  type="text"
                  value={newTaskTitle}
                  onChange={(e) => setNewTaskTitle(e.target.value)}
                  placeholder="e.g., Clean spill in aisle 3"
                  autoFocus
                />
              </div>

              <div className="kiosk__modal-field">
                <label htmlFor="newTaskDescription">
                  Description (optional)
                </label>
                <textarea
                  id="newTaskDescription"
                  value={newTaskDescription}
                  onChange={(e) => setNewTaskDescription(e.target.value)}
                  placeholder="Additional details about the task..."
                  rows={2}
                />
              </div>

              <div className="kiosk__modal-field">
                <label htmlFor="newTaskPriority">Priority</label>
                <select
                  id="newTaskPriority"
                  value={newTaskPriority}
                  onChange={(e) =>
                    setNewTaskPriority(
                      e.target.value as "low" | "medium" | "high"
                    )
                  }
                >
                  <option value="low">Low</option>
                  <option value="medium">Medium</option>
                  <option value="high">High</option>
                </select>
              </div>

              <div className="kiosk__modal-field">
                <label htmlFor="newTaskRequiredRole">Required Role</label>
                <select
                  id="newTaskRequiredRole"
                  value={newTaskRequiredRole}
                  onChange={(e) => setNewTaskRequiredRole(e.target.value)}
                >
                  {tenantRoles.map((role) => (
                    <option key={role.id} value={role.name.toLowerCase()}>
                      {role.name}
                    </option>
                  ))}
                </select>
              </div>

              <div className="kiosk__modal-field">
                <label htmlFor="newTaskDueTime">Due Time (optional)</label>
                <input
                  id="newTaskDueTime"
                  type="time"
                  value={newTaskDueTime}
                  onChange={(e) => setNewTaskDueTime(e.target.value)}
                />
              </div>

              <div className="kiosk__modal-field">
                <label htmlFor="newTaskExpiryType">Expiry Type</label>
                <select
                  id="newTaskExpiryType"
                  value={newTaskExpiryType}
                  onChange={(e) =>
                    handleExpiryTypeChange(
                      e.target.value as
                        | "next_report"
                        | "set_datetime"
                        | "no_expiry"
                    )
                  }
                >
                  <option value="no_expiry">♾️ No Expiry</option>
                  <option value="next_report">📋 Next Report</option>
                  <option value="set_datetime">⏰ Set Date/Time</option>
                </select>
              </div>

              {newTaskExpiryType === "set_datetime" && (
                <div className="kiosk__modal-field">
                  <label htmlFor="newTaskExpiryDateTime">
                    Expiry Date & Time
                  </label>
                  <input
                    id="newTaskExpiryDateTime"
                    type="datetime-local"
                    value={newTaskExpiryDateTime}
                    onChange={(e) => setNewTaskExpiryDateTime(e.target.value)}
                    required={newTaskExpiryType === "set_datetime"}
                  />
                  <small
                    style={{
                      color: "#6b7280",
                      fontSize: "0.8rem",
                      marginTop: "0.25rem",
                      display: "block",
                    }}
                  >
                    Task will expire at this specific date and time
                  </small>
                </div>
              )}
            </div>

            <div className="kiosk__modal-actions">
              <button
                className="kiosk__modal-btn kiosk__modal-btn--secondary"
                onClick={() => setShowAddTaskModal(false)}
                disabled={isCreatingTask}
              >
                Cancel
              </button>
              <button
                className="kiosk__modal-btn kiosk__modal-btn--primary"
                onClick={handleCreateAdHocTask}
                disabled={!newTaskTitle.trim() || isCreatingTask}
              >
                {isCreatingTask ? "Creating..." : "➕ Add Task"}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// TaskCard Component
interface TaskCardProps {
  task: KioskTask;
  onTaskAction: (task: KioskTask, action: "complete" | "note") => void;
  viewMode: "routine" | "role";
}

const TaskCard: React.FC<TaskCardProps> = ({
  task,
  onTaskAction,
  viewMode,
}) => {
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "#ef4444";
      case "medium":
        return "#f59e0b";
      case "low":
        return "#10b981";
      default:
        return "#6b7280";
    }
  };

  const formatTime = (timeString?: string) => {
    if (!timeString) return null;
    const [hours, minutes] = timeString.split(":");
    const hour = parseInt(hours);
    const ampm = hour >= 12 ? "PM" : "AM";
    const displayHour = hour % 12 || 12;
    return `${displayHour}:${minutes} ${ampm}`;
  };

  const isOverdue = () => {
    if (!task.dueTime || task.status === "completed") return false;
    const now = new Date();
    const [hours, minutes] = task.dueTime.split(":");
    const dueDate = new Date();
    dueDate.setHours(parseInt(hours), parseInt(minutes), 0, 0);
    return now > dueDate;
  };

  const formatRoleName = (role?: string) => {
    if (!role) return "Any Employee";
    return role.charAt(0).toUpperCase() + role.slice(1);
  };

  return (
    <div
      className={`kiosk__task-card ${
        task.status === "completed" ? "completed" : ""
      } ${isOverdue() ? "overdue" : ""} clickable`}
      data-priority={task.priority}
      onClick={() => onTaskAction(task, "note")}
    >
      <div className="kiosk__task-content">
        <div className="kiosk__task-header">
          <div className="kiosk__task-title-row">
            <h3 className="kiosk__task-title">{task.title}</h3>
            <span
              className="kiosk__task-priority"
              style={{ backgroundColor: getPriorityColor(task.priority) }}
            >
              {task.priority}
            </span>
          </div>

          {(task.dueTime || (task.description && task.description.trim())) && (
            <div className="kiosk__task-meta">
              {task.dueTime && (
                <span
                  className={`kiosk__task-time ${isOverdue() ? "overdue" : ""}`}
                >
                  Due: {formatTime(task.dueTime)}
                </span>
              )}
              {task.description && task.description.trim() && (
                <div className="kiosk__task-description">
                  {task.description.length > 80
                    ? `${task.description.substring(0, 80)}...`
                    : task.description}
                </div>
              )}
            </div>
          )}
        </div>

        {/* Show role when in routine view, routine when in role view */}
        {viewMode === "routine" && task.requiredRole && (
          <div className="kiosk__task-context">
            <span className="kiosk__task-context-label">👥 Role:</span>
            <span className="kiosk__task-context-value">
              {formatRoleName(task.requiredRole)}
            </span>
          </div>
        )}
        {viewMode === "role" && task.routineName && (
          <div className="kiosk__task-context">
            <span className="kiosk__task-context-label">📋 Routine:</span>
            <span className="kiosk__task-context-value">
              {task.routineName}
            </span>
          </div>
        )}

        {task.notes && <div className="kiosk__task-note">💬 {task.notes}</div>}

        {/* Show completion status */}
        {task.status === "completed" && (
          <div className="kiosk__task-completed-status">✅ Task Completed</div>
        )}
      </div>
    </div>
  );
};

// Watchlist Kiosk Card Component
interface WatchlistKioskCardProps {
  task: WatchlistTask;
  onCheckIn: (taskId: string, checkData: Omit<WatchlistCheck, "id">) => void;
}

const WatchlistKioskCard: React.FC<WatchlistKioskCardProps> = ({
  task,
  onCheckIn,
}) => {
  const [showCheckModal, setShowCheckModal] = useState(false);
  const [checkerName, setCheckerName] = useState("");
  const [checkNotes, setCheckNotes] = useState("");
  const [recentChecks, setRecentChecks] = useState<WatchlistCheck[]>([]);
  const [loadingChecks, setLoadingChecks] = useState(false);

  // Calculate status based on last check and frequency
  const getTaskStatus = () => {
    if (!task.lastCheckAt) {
      return "overdue"; // Never checked
    }

    const lastCheck = new Date(task.lastCheckAt);
    const now = new Date();
    const minutesSinceLastCheck =
      (now.getTime() - lastCheck.getTime()) / (1000 * 60);

    if (minutesSinceLastCheck <= task.frequencyMinutes * 0.8) {
      return "good"; // Recently checked (within 80% of frequency)
    } else if (minutesSinceLastCheck <= task.frequencyMinutes) {
      return "due-soon"; // Due soon (80-100% of frequency elapsed)
    } else {
      return "overdue"; // Overdue
    }
  };

  const status = getTaskStatus();

  const getStatusDisplay = () => {
    switch (status) {
      case "good":
        return { icon: "✅", text: "Recently Checked", color: "#10b981" };
      case "due-soon":
        return { icon: "⚠️", text: "Due Soon", color: "#f59e0b" };
      case "overdue":
        return { icon: "🔴", text: "Overdue", color: "#ef4444" };
      default:
        return { icon: "❓", text: "Unknown", color: "#6b7280" };
    }
  };

  const statusDisplay = getStatusDisplay();

  // Fetch recent checks when modal opens
  const fetchRecentChecks = async () => {
    if (!task.lastCheckAt) return; // No checks to fetch

    setLoadingChecks(true);
    try {
      const checks = await getWatchlistChecks(task.id, 3);
      setRecentChecks(checks);
    } catch (error) {
      console.error("Error fetching recent checks:", error);
    } finally {
      setLoadingChecks(false);
    }
  };

  // Fetch checks when component mounts and when modal opens
  React.useEffect(() => {
    if (task.lastCheckAt) {
      fetchRecentChecks();
    }
  }, [task.lastCheckAt]);

  React.useEffect(() => {
    if (showCheckModal) {
      fetchRecentChecks();
    }
  }, [showCheckModal]);

  const handleCheckIn = () => {
    if (!checkerName.trim()) {
      alert("Please enter your name");
      return;
    }

    const checkData: Omit<WatchlistCheck, "id"> = {
      watchlistTaskId: task.id,
      tenantId: task.tenantId,
      checkedBy: checkerName.trim(),
      checkedAt: new Date().toISOString(),
      notes: checkNotes.trim() || undefined,
    };

    onCheckIn(task.id, checkData);
    setCheckerName("");
    setCheckNotes("");
    setShowCheckModal(false);
  };

  const getLastCheckInfo = () => {
    if (!task.lastCheckAt || !task.lastCheckBy) {
      return "Never checked";
    }
    const checkTime = new Date(task.lastCheckAt);
    const now = new Date();
    const diffMinutes = Math.floor(
      (now.getTime() - checkTime.getTime()) / (1000 * 60)
    );

    if (diffMinutes < 60) {
      return `${task.lastCheckBy} - ${diffMinutes}m ago`;
    } else if (diffMinutes < 1440) {
      const hours = Math.floor(diffMinutes / 60);
      return `${task.lastCheckBy} - ${hours}h ago`;
    } else {
      return `${task.lastCheckBy} - ${checkTime.toLocaleDateString()}`;
    }
  };

  return (
    <>
      <div className={`kiosk__watchlist-card kiosk__watchlist-card--${status}`}>
        <div className="kiosk__watchlist-header">
          <div className="kiosk__watchlist-title">
            <h4>{task.title}</h4>
            <div className="kiosk__watchlist-frequency">
              Every {Math.floor(task.frequencyMinutes / 60)}h{" "}
              {task.frequencyMinutes % 60}m
            </div>
          </div>
          <button
            className="kiosk__watchlist-check-btn"
            onClick={() => setShowCheckModal(true)}
            style={{ backgroundColor: statusDisplay.color }}
          >
            ✓ Check In
          </button>
        </div>

        {task.description && (
          <div className="kiosk__watchlist-description">{task.description}</div>
        )}

        <div className="kiosk__watchlist-status">
          <span
            className="kiosk__watchlist-status-indicator"
            style={{ color: statusDisplay.color }}
          >
            {statusDisplay.icon} {statusDisplay.text}
          </span>
          <span className="kiosk__watchlist-last-check">
            {getLastCheckInfo()}
          </span>
        </div>
      </div>

      {/* Check-in Modal */}
      {showCheckModal && (
        <div
          className="kiosk__modal-overlay"
          onClick={() => setShowCheckModal(false)}
        >
          <div className="kiosk__modal" onClick={(e) => e.stopPropagation()}>
            <div className="kiosk__modal-header">
              <h3>Check In: {task.title}</h3>
              <button
                className="kiosk__modal-close"
                onClick={() => setShowCheckModal(false)}
              >
                ✕
              </button>
            </div>

            <div className="kiosk__modal-content">
              <div className="kiosk__modal-field">
                <label htmlFor="checker-name">Your Name *</label>
                <input
                  type="text"
                  id="checker-name"
                  value={checkerName}
                  onChange={(e) => setCheckerName(e.target.value)}
                  placeholder="Enter your name"
                  autoFocus
                />
              </div>

              <div className="kiosk__modal-field">
                <label htmlFor="check-notes">Notes (optional)</label>
                <textarea
                  id="check-notes"
                  value={checkNotes}
                  onChange={(e) => setCheckNotes(e.target.value)}
                  placeholder="Any observations or notes..."
                  rows={3}
                />
              </div>

              {/* Recent Check-ins Section */}
              {task.lastCheckAt && (
                <div className="kiosk__modal-section">
                  <h4 className="kiosk__modal-section-title">
                    Recent Check-ins
                  </h4>
                  {loadingChecks ? (
                    <div className="kiosk__loading-checks">
                      Loading recent check-ins...
                    </div>
                  ) : recentChecks.length > 0 ? (
                    <div className="kiosk__recent-checkins">
                      {recentChecks.map((check) => (
                        <div key={check.id} className="kiosk__checkin-item">
                          <div className="kiosk__checkin-header">
                            <span className="kiosk__checkin-name">
                              {check.checkedBy}
                            </span>
                            <span className="kiosk__checkin-time">
                              {new Date(check.checkedAt).toLocaleString()}
                            </span>
                          </div>
                          {check.notes && (
                            <div className="kiosk__checkin-notes">
                              "{check.notes}"
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="kiosk__recent-checkins">
                      <div className="kiosk__checkin-item">
                        <div className="kiosk__checkin-header">
                          <span className="kiosk__checkin-name">
                            {task.lastCheckBy}
                          </span>
                          <span className="kiosk__checkin-time">
                            {new Date(task.lastCheckAt).toLocaleString()}
                          </span>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>

            <div className="kiosk__modal-actions">
              <button
                className="kiosk__modal-btn kiosk__modal-btn--secondary"
                onClick={() => setShowCheckModal(false)}
              >
                Cancel
              </button>
              <button
                className="kiosk__modal-btn kiosk__modal-btn--primary"
                onClick={handleCheckIn}
              >
                ✅ Record Check-in
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default KioskInterface;
