rules_version = '2';

// Firebase Storage Security Rules for Bit by Bit
service firebase.storage {
  match /b/{bucket}/o {
    // Public read access to all images (for course display)
    // Admin-only write access for uploading/managing images
    match /{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null && 
        request.auth.token.email == '<EMAIL>';
    }
    
    // Specific rules for course images
    match /courses/{imageId} {
      allow read: if true;
      allow write: if request.auth != null && 
        request.auth.token.email == '<EMAIL>' &&
        request.resource.size < 5 * 1024 * 1024 && // 5MB limit
        request.resource.contentType.matches('image/.*'); // Images only
    }
    
    // Specific rules for calendar images
    match /calendar/{imageId} {
      allow read: if true;
      allow write: if request.auth != null && 
        request.auth.token.email == '<EMAIL>' &&
        request.resource.size < 5 * 1024 * 1024 && // 5MB limit
        request.resource.contentType.matches('image/.*'); // Images only
    }
  }
}
