{"indexes": [{"collectionGroup": "registrations", "queryScope": "COLLECTION", "fields": [{"fieldPath": "courseId", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "ASCENDING"}]}, {"collectionGroup": "registrations", "queryScope": "COLLECTION", "fields": [{"fieldPath": "email", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "adminbuddy_locations", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "adminbuddy_routines", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "adminbuddy_routine_tasks", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "adminbuddy_routine_tasks", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}]}, {"collectionGroup": "adminbuddy_routine_tasks", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "order", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "adminbuddy_daily_tasks", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "date", "order": "ASCENDING"}, {"fieldPath": "dueTime", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "adminbuddy_daily_tasks", "queryScope": "COLLECTION", "fields": [{"fieldPath": "date", "order": "ASCENDING"}, {"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "dueTime", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "adminbuddy_ad_hoc_tasks", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "dueDate", "order": "ASCENDING"}, {"fieldPath": "dueTime", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "adminbuddy_daily_tasks", "queryScope": "COLLECTION", "fields": [{"fieldPath": "date", "order": "ASCENDING"}, {"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}, {"fieldPath": "__name__", "order": "DESCENDING"}]}, {"collectionGroup": "adminbuddy_location_routine_schedules", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "adminbuddy_tenant_roles", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "name", "order": "ASCENDING"}]}, {"collectionGroup": "adminbuddy_ad_hoc_tasks", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "locationId", "order": "ASCENDING"}, {"fieldPath": "dueDate", "order": "ASCENDING"}, {"fieldPath": "dueTime", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "adminbuddy_location_reports", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "locationId", "order": "ASCENDING"}, {"fieldPath": "reportTimestamp", "order": "DESCENDING"}]}, {"collectionGroup": "adminbuddy_daily_tasks", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "locationId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "completedAt", "order": "DESCENDING"}]}, {"collectionGroup": "adminbuddy_ad_hoc_tasks", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "locationId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "completedAt", "order": "DESCENDING"}]}, {"collectionGroup": "adminbuddy_watchlist_checks", "queryScope": "COLLECTION", "fields": [{"fieldPath": "watchlistTaskId", "order": "ASCENDING"}, {"fieldPath": "checkedAt", "order": "DESCENDING"}]}], "fieldOverrides": []}